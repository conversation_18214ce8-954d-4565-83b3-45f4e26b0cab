@charset "UTF-8";
:root {
  --theme-deafult: #e83f3f;
}

:root {
  --theme-deafult: #e83f3f;
}

/**=====================
    Typography css start
==========================**/
body {
  font-family: Lato, sans-serif;
  position: relative;
  background: #ffffff;
  font-size: 14px;
}
body.christmas {
  font-family: Philosopher, sans-serif;
}
body.christmas .dark-light {
  display: none;
}
body.christmas section {
  overflow: hidden;
}

h1 {
  font-size: 60px;
  color: #222222;
  font-weight: 700;
  text-transform: uppercase;
}
h1 span {
  font-size: 107px;
  font-weight: 700;
  color: var(--theme-deafult);
}

h2 {
  font-size: 36px;
  color: #222222;
  text-transform: uppercase;
  font-weight: 700;
  line-height: 1;
  letter-spacing: 0.02em;
}

h3 {
  font-size: 24px;
  font-weight: 400;
  color: #777777;
  letter-spacing: 0.03em;
}

h4 {
  font-size: 18px;
  text-transform: capitalize;
  font-weight: 400;
  letter-spacing: 0.03em;
  line-height: 1;
}

h5 {
  font-size: 16px;
  font-weight: 400;
  color: #222222;
  line-height: 24px;
  letter-spacing: 0.05em;
}

h6 {
  font-size: 14px;
  font-weight: 400;
  color: #777777;
  line-height: 24px;
}

ul {
  padding-left: 0;
  margin-bottom: 0;
}

li {
  display: inline-block;
}

p {
  font-size: 14px;
  color: #777777;
  line-height: 1;
}

a {
  transition: 0.5s ease;
  text-decoration: none;
}
a:hover {
  text-decoration: none;
  transition: 0.5s ease;
}
a:focus {
  outline: none;
}

button:focus {
  outline: none;
}

.btn-close:focus {
  box-shadow: none;
}

label {
  margin-bottom: 0.5rem;
}

:focus {
  outline: none;
}

.form-control:focus {
  box-shadow: none;
}

.font-cormorant {
  font-family: Cormorant, sans-serif;
}

.font-fraunces {
  font-family: "Fraunces", serif;
}

.font-courgette {
  font-family: "Courgette", cursive; 
  text-transform: capitalize !important;
}


.form-check-input:checked {
  background-color: var(--theme-deafult);
  border-color: var(--theme-deafult);
}

.h-100vh {
  height: calc(100% - 30px);
}

/**=====================
     Reset css start
==========================**/
section,
.section-t-space {
  padding-top: 70px;
}

.section-b-space {
  padding-bottom: 70px;
}

.large-section {
  padding-top: 120px;
  padding-bottom: 120px;
}

hr.style1 {
  width: 75px;
  height: 3px;
  margin-top: 13px;
  background-color: var(--theme-deafult);
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  opacity: 1;
}

.table thead th {
  border-bottom: 1px solid #dee2e6 !important;
}

.form-control {
  border-radius: 0;
}

.small-section {
  padding-top: 35px;
  padding-bottom: 35px;
}

.banner-padding {
  padding-top: 30px;
}

.border-section {
  border-top: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
}

.border-b {
  border-bottom: 1px solid #38352f;
}

.border-bottom-grey {
  border-bottom: 1px solid #efefef;
}

.border-top-grey {
  border-top: 1px solid #efefef;
}

.darken-layout {
  background-color: #393230;
}

.dark-layout {
  background-color: #2d2a25;
}

.light-layout {
  background-color: #f9f9f9;
}

.white-layout {
  background-color: #ffffff;
}

.bg-light0 {
  background-color: #d0edff;
}

.bg-light1 {
  background-color: #f1e7e6;
}

.bg-light2 {
  background-color: #bfbfbf;
}

.bg-color1 {
  background-color: #c6c6c6;
}

.bg-color2 {
  background-color: #ab5252;
}

.bg-color3 {
  background-color: #6d6d6d;
}

.bg-blog {
  background-color: #eeeeee;
}

.bg-grey {
  background-color: #f7f7f7;
}

.bg_cls {
  background-color: #fafafa;
}

.bg-loader {
  background-color: #f3f3f3;
}

.badge-theme-color {
  background-color: var(--theme-deafult);
  color: white;
}

.badge-grey-color {
  background-color: #929292;
  color: white;
}

.overflow-hidden {
  overflow: hidden;
}

del {
  font-size: 14px;
  color: #aaaaaa;
  font-weight: 400;
}

.position-unset {
  position: unset !important;
}

[data-notify=progressbar] {
  margin-bottom: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
}

.progress-bar {
  background-color: #19a340;
}

.progress-bar-info {
  background-color: #00829a;
}

.container-fluid.custom-container {
  padding-left: 90px;
  padding-right: 90px;
}

.left-sidebar_space {
  padding-left: 300px;
}

.space_sm {
  padding-left: 240px;
  padding-top: 74px !important;
}

.left-sidebar_space-xl {
  padding-left: 380px;
}

.background-transparent {
  background-color: transparent !important;
}

.bg-overlay {
  background-blend-mode: overlay;
  background-color: rgba(250, 250, 250, 0.98);
}

.bg-blue-light {
  background-color: rgba(40, 115, 239, 0.06);
}

.bg-theme {
  background-color: var(--theme-deafult);
  background-color: var(--theme-deafult2);
}

/*Lazy load */
.blur-up {
  filter: blur(5px);
  transition: filter 400ms;
}
.blur-up.lazyloaded {
  filter: blur(0);
}

.margin-default {
  margin-bottom: -30px;
}
.margin-default > div {
  margin-bottom: 30px;
}
.margin-default.no-slider .product-box {
  margin-bottom: 30px !important;
}

.pl-section {
  padding-left: 310px;
}

.section-white section {
  background-color: white;
}

.w-80 {
  width: 80% !important;
}

.fz-16 {
  font-size: 16px;
}

.form-group {
  margin-bottom: 15px;
}

.form-check-input:focus {
  box-shadow: none;
}

.media {
  display: flex;
  align-items: flex-start;
}
.media .media-body {
  flex: 1;
}

.form-check {
  display: flex;
}

.input-group-prepend,
.input-group-append {
  display: flex;
}

.breadcrumb {
  padding: 0.75rem 1rem;
}

.row div[class*=col-],
.row .col {
  position: relative;
}

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}

.sticky-top-section {
  position: -webkit-sticky;
  position: sticky;
  top: 150px;
  z-index: 1;
}

/**=====================
     Animation css start
==========================**/
@-webkit-keyframes animate {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 0 500px;
  }
}
@keyframes animate {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 0 500px;
  }
}
@-webkit-keyframes ring-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes ring-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes up-down {
  0% {
    transform: translateY(-10px);
  }
  50% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
@keyframes up-down {
  0% {
    transform: translateY(-10px);
  }
  50% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
@-webkit-keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
/*=====================
 button css start
==========================*/
button {
  cursor: pointer;
}

.btn {
  line-height: 20px;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 700;
  border-radius: 0;
  transition: 0.3s ease-in-out;
}
.btn:hover {
  transition: 0.3s ease-in-out;
}
.btn:focus {
  box-shadow: none;
}

.btn-solid {
  padding: 13px 29px;
  color: #ffffff;
  letter-spacing: 0.05em;
  border: 2px solid var(--theme-deafult);
  background-image: linear-gradient(30deg, var(--theme-deafult) 50%, transparent 50%);
  background-size: 850px;
  background-repeat: no-repeat;
  background-position: 0;
  transition: background 300ms ease-in-out;
}
.btn-solid:hover {
  background-position: 100%;
  color: #000000;
  background-color: #ffffff;
}
.btn-solid.black-btn {
  background-image: linear-gradient(30deg, #222222 50%, transparent 50%);
  border: 2px solid #222222;
}
.btn-solid:focus {
  color: #000000;
}
.btn-solid.btn-gradient {
  background: var(--theme-deafult);
  background-image: linear-gradient(to right, var(--theme-deafult), var(--theme-deafult2), var(--theme-deafult), var(--theme-deafult2));
  transition: all 0.4s ease-in-out;
  background-size: 300% 100%;
  border: none;
}
.btn-solid.btn-gradient:hover {
  background-position: 100% 0;
  color: white;
  transition: all 0.4s ease-in-out;
}
.btn-solid.btn-green {
  background-image: linear-gradient(to right, var(--theme-deafult), var(--theme-deafult2));
  border: none;
  background-color: var(--theme-deafult);
}
.btn-solid.btn-green:hover {
  background-color: var(--theme-deafult);
  background-image: none;
  color: white;
}
.btn-solid.btn-sm {
  padding: 9px 16px;
}
.btn-solid.btn-xs {
  padding: 5px 8px;
  text-transform: capitalize;
}
.btn-solid.hover-solid {
  transition: none;
}
.btn-solid.hover-solid:hover, .btn-solid.hover-solid:focus {
  background-color: var(--theme-deafult);
  opacity: 0.92;
  color: #ffffff;
  transition: none;
}

.btn-outline {
  display: inline-block;
  padding: 13px 29px;
  letter-spacing: 0.05em;
  border: 2px solid var(--theme-deafult);
  position: relative;
  color: #000000;
}
.btn-outline:before {
  transition: 0.5s all ease;
  position: absolute;
  top: 0;
  left: 50%;
  right: 50%;
  bottom: 0;
  opacity: 0;
  content: "";
  background-color: var(--theme-deafult);
  z-index: -2;
}
.btn-outline:hover, .btn-outline:focus {
  color: #ffffff !important;
}
.btn-outline:hover i, .btn-outline:focus i {
  color: #ffffff !important;
}
.btn-outline:hover:before, .btn-outline:focus:before {
  transition: 0.5s all ease;
  left: 0;
  right: 0;
  opacity: 1;
}
.btn-outline.btn-sm {
  padding: 9px 16px;
  font-size: 12px;
}

.btn-rounded {
  border-radius: 30px;
}

button.btn.btn-solid:active, button.btn.btn-outline:active {
  background-image: linear-gradient(30deg, var(--theme-deafult) 50%, transparent 50%);
  color: #ffffff;
  background: var(--theme-deafult);
}

/*=====================
 Image-ratio start
==========================*/
.ratio_40 .bg-size:before {
  padding-top: 40%;
  content: "";
  display: block;
}

.ratio_45 .bg-size:before {
  padding-top: 45%;
  content: "";
  display: block;
}

.ratio2_1 .bg-size:before {
  padding-top: 50%;
  content: "";
  display: block;
}

.ratio2_3 .bg-size:before {
  padding-top: 60%;
  content: "";
  display: block;
}

.ratio3_2 .bg-size:before {
  padding-top: 66.66%;
  content: "";
  display: block;
}

.ratio_landscape .bg-size:before {
  padding-top: 75%;
  content: "";
  display: block;
}

.ratio_square .bg-size:before {
  padding-top: 100%;
  content: "";
  display: block;
}

.ratio_115 .bg-size:before {
  padding-top: 115%;
  content: "";
  display: block;
}

.ratio_125 .bg-size:before {
  padding-top: 125%;
  content: "";
  display: block;
}

.ratio_asos .bg-size:before {
  padding-top: 127.7777778%;
  content: "";
  display: block;
}

.ratio_portrait .bg-size:before {
  padding-top: 150%;
  content: "";
  display: block;
}

.ratio1_2 .bg-size:before {
  padding-top: 200%;
  content: "";
  display: block;
}

.b-top {
  background-position: top !important;
}

.b-bottom {
  background-position: bottom !important;
}

.b-center {
  background-position: center !important;
}

.b_size_content {
  background-size: contain !important;
  background-repeat: no-repeat;
}

/**=====================
    Timer css start
==========================**/
.timer {
  padding-top: 15px;
  padding-bottom: 15px;
  padding-left: 40px;
  margin-top: 30px;
  background-color: #2d2a25;
  display: inline-block;
}
.timer p {
  font-size: 18px;
  color: #ffffff;
  margin-bottom: 0;
}
.timer span {
  width: 70px;
  display: inline-block;
}
.timer span .timer-cal {
  font-size: 12px;
  color: #777777;
}
.timer span .padding-l {
  padding-left: 22px;
  display: inline;
}

.modal-card {
  position: relative;
}
.modal-card .right-dropdown {
  position: absolute;
  top: 12px;
  right: 12px;
  transition: all 0.5s ease;
  opacity: 0;
  z-index: 1;
}
.modal-card .right-dropdown .option-dropdown {
  right: 0 !important;
}
.modal-card .right-dropdown button {
  padding: 0;
  background-color: rgba(82, 82, 82, 0.72);
  width: 23px;
  height: 23px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.modal-card .right-dropdown button:after {
  content: none;
}
.modal-card .right-dropdown .dropdown-box {
  background-color: rgba(82, 82, 82, 0.72);
  width: 23px;
  height: 23px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.modal-card .right-dropdown .dropdown-menu li {
  padding: 0 12px;
  width: 100%;
}
.modal-card .right-dropdown .dropdown-menu li .list-box {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 8px;
  white-space: nowrap;
  color: #2b2b2b;
  opacity: 0.6;
  font-size: 14px;
}
.modal-card:hover .right-dropdown {
  opacity: 1;
}

.qty-box .input-group {
  justify-content: center;
}
.qty-box .input-group span button {
  background: #ffffff !important;
  border: 1px solid #ced4da;
}
.qty-box .input-group .form-control {
  text-align: center;
  width: 80px;
  flex: unset;
}
.qty-box .input-group button {
  background-color: transparent;
  border: 0;
  color: #777777;
  cursor: pointer;
  padding-left: 12px;
  font-size: 12px;
  font-weight: 900;
  line-height: 1;
}
.qty-box .input-group button i {
  font-weight: 900;
  color: #222222;
}
.qty-box .input-group .icon {
  padding-right: 0;
}

.city-progress {
  height: 10px;
  border-radius: 50px;
}

.custom-height {
  height: 294px;
  overflow: hidden auto;
}
.custom-height::-webkit-scrollbar {
  width: 5px;
}
.custom-height::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px transparent;
  border-radius: 10px;
}
.custom-height::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}
.custom-height .list-unstyled .media + .media {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding-top: 14px;
  margin-top: 14px;
}
.custom-height .list-unstyled .media {
  flex-wrap: nowrap;
  gap: calc(7px + (15 - 7) * ((100vw - 320px) / (1920 - 320)));
  align-items: center;
}
.custom-height .list-unstyled .media .media-image {
  width: calc(80px + (110 - 80) * ((100vw - 320px) / (1920 - 320)));
  height: calc(67px + (77 - 67) * ((100vw - 320px) / (1920 - 320)));
  border-radius: calc(8px + (12 - 8) * ((100vw - 320px) / (1920 - 320)));
  overflow: hidden;
}
.custom-height .list-unstyled .media .media-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.custom-height .list-unstyled .media .media-body {
  width: calc(100% - calc(80px + (110 - 80) * ((100vw - 320px) / (1920 - 320))) - calc(7px + (15 - 7) * ((100vw - 320px) / (1920 - 320))));
}
.custom-height .list-unstyled .media .media-body a h5 {
  font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));
  color: #000000;
  font-weight: 500;
  margin-bottom: 3px;
  line-height: 1.5;
}
.custom-height .list-unstyled .media .media-body .blog-date h6 {
  margin-bottom: 0;
  line-height: 1;
}

.popular-table .table {
  border-radius: $border-radius;
  overflow: hidden;
}
.popular-table .table thead tr th {
  background-color: #e2e8f0;
  padding: calc(12px + (16 - 12) * ((100vw - 320px) / (1920 - 320))) calc(17px + (24 - 17) * ((100vw - 320px) / (1920 - 320)));
  font-size: calc(14px + (15 - 14) * ((100vw - 320px) / (1920 - 320)));
  white-space: nowrap;
}
.popular-table .table tbody tr td {
  vertical-align: middle;
  padding: 10px 14px;
  font-size: 14px;
  color: #586082;
}
.popular-table .table tbody tr td.avatar-name {
  color: #222;
  font-weight: 500;
  font-size: 15px;
  white-space: nowrap;
}
.popular-table .table tbody tr:last-child td {
  padding-bottom: 0;
  border: none;
}
.popular-table .table tbody tr td.image-td {
  width: 70px;
  height: 70px;
}

.table-hover > tbody > tr:hover {
  background-color: #e2e8f066;
  --bs-table-accent-bg: transparent;
}

.activity-date-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 21px;
}
.activity-date-list li {
  width: 100%;
  position: relative;
  padding-left: calc(20px + (25 - 20) * ((100vw - 320px) / (1920 - 320)));
}
.activity-date-list li::before {
  content: "";
  position: absolute;
  top: 2px;
  left: 0;
  width: calc(7px + (10 - 7) * ((100vw - 320px) / (1920 - 320)));
  height: calc(7px + (10 - 7) * ((100vw - 320px) / (1920 - 320)));
  background-color: #5f57ea;
  border-radius: 100%;
}
.activity-date-list li + li::after {
  content: "";
  position: absolute;
  top: -11px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(120, 120, 120, 0.12);
  border-radius: 100%;
}
.activity-date-list li .activity-content {
  width: 100%;
}
.activity-date-list li .activity-content h5 {
  margin: 0;
  line-height: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.activity-date-list li .activity-date-box {
  font-size: 14px;
  font-weight: 400;
  color: #777777;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  line-height: 1;
  gap: 7px;
}
.activity-date-list li .activity-date-box .activity-date {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 15px;
}
.activity-date-list li .activity-date-box .activity-date .date-left i,
.activity-date-list li .activity-date-box .activity-date .date-right i {
  font-size: 14px;
}
.activity-date-list li .activity-date-box .activity-date .date-left span,
.activity-date-list li .activity-date-box .activity-date .date-right span {
  font-size: 14px;
}
.activity-date-list li .activity-date-box .activity-date .date-left {
  position: relative;
}
.activity-date-list li .activity-date-box .activity-date .date-left::before {
  content: "";
  position: absolute;
  top: 0;
  right: -8px;
  width: 2px;
  height: 100%;
  background-color: #77777780;
}

.visitors-date-list {
  align-items: center;
  flex-wrap: wrap;
  gap: 21px;
  display: grid;
  grid-template-columns: 1fr;
}
@media (max-width: 1199px) {
  .visitors-date-list {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 575px) {
  .visitors-date-list {
    grid-template-columns: 1fr;
  }
}
.visitors-date-list li {
  width: 100%;
  position: relative;
}
@media (max-width: 1199px) {
  .visitors-date-list li:nth-child(2)::after {
    content: none;
  }
}
@media (max-width: 575px) {
  .visitors-date-list li:nth-child(2)::after {
    content: "";
  }
}
.visitors-date-list li + li::after {
  content: "";
  position: absolute;
  top: -11px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(120, 120, 120, 0.12);
  border-radius: 100%;
}
.visitors-date-list li .visitors-date-box {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 7px;
}
.visitors-date-list li .visitors-date-box.bg-red .visitors-icon {
  background-color: rgba(231, 80, 90, 0.2);
  color: #e7505a;
}
.visitors-date-list li .visitors-date-box.bg-yellow .visitors-icon {
  background-color: rgba(196, 159, 71, 0.2);
  color: #c49f47;
}
.visitors-date-list li .visitors-date-box.bg-blue .visitors-icon {
  background-color: rgba(53, 152, 220, 0.2);
  color: #3598dc;
}
.visitors-date-list li .visitors-date-box.bg-orange .visitors-icon {
  background-color: rgba(242, 120, 75, 0.2);
  color: #f2784b;
}
.visitors-date-list li .visitors-date-box .visitors-icon {
  width: calc(40px + (50 - 40) * ((100vw - 320px) / (1920 - 320)));
  height: calc(40px + (50 - 40) * ((100vw - 320px) / (1920 - 320)));
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
}
.visitors-date-list li .visitors-date-box .visitors-icon svg {
  width: calc(20px + (26 - 20) * ((100vw - 320px) / (1920 - 320)));
  stroke-width: 1.6px;
}
.visitors-date-list li .visitors-date-box .visitors-content {
  width: calc(100% - calc(40px + (50 - 40) * ((100vw - 320px) / (1920 - 320))) - 7px);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.visitors-date-list li .visitors-date-box .visitors-content .left h5 {
  margin: 0;
  line-height: 1;
}
.visitors-date-list li .visitors-date-box .visitors-content .right h6 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #222;
}

.product-right .product-count {
  background-color: #f8f8f8;
  padding: 10px 12px;
  margin-bottom: 12px;
}
.product-right .product-count img {
  margin-right: 6px;
}
.product-right .product-count ul {
  margin-bottom: -4px;
}
.product-right .product-count ul li {
  margin-bottom: 4px;
}
.product-right .product-count ul li:first-child {
  margin-right: 14px;
}
.product-right p {
  margin-bottom: 0;
  line-height: 1.5em;
}
.product-right .product-title {
  color: #222222;
  text-transform: capitalize;
  font-weight: 700;
  margin-bottom: 3px;
  font-size: 16px;
}
.product-right .shipping-info li {
  display: block;
  font-size: 16px;
  color: #777777;
  line-height: 1.8;
}
.product-right .border-product {
  padding-top: 15px;
  padding-bottom: 20px;
  border-top: 1px dashed #dddddd;
}
.product-right h2 {
  text-transform: uppercase;
  margin-bottom: 15px;
  font-size: 25px;
  line-height: 1.2em;
}
.product-right .price-detail span {
  font-size: 16px;
  color: var(--theme-deafult);
  padding-left: 10px;
}
.product-right h3 {
  font-size: 26px;
  color: #222222;
  margin-bottom: 15px;
}
.product-right h4 {
  font-size: 16px;
  margin-bottom: 7px;
}
.product-right h4 del {
  color: #777777;
}
.product-right h4 span {
  padding-left: 5px;
  color: var(--theme-deafult);
}
.product-right .color-variant {
  margin-bottom: 10px;
}
.product-right .color-variant li {
  height: 30px;
  width: 30px;
  cursor: pointer;
}
.product-right .color-variant li.active {
  position: relative;
}
.product-right .color-variant li.active:after {
  content: "";
  background-image: url("data:image/svg+xml;charset=utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='%23000' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/></svg>");
  top: 10px;
  right: 4px;
  height: 15px;
  width: 15px;
  background-size: 70%;
  background-repeat: no-repeat;
  position: absolute;
  opacity: 0.8;
}
.product-right .product-buttons {
  margin-bottom: 20px;
}
.product-right .product-buttons .btn-solid,
.product-right .product-buttons .btn-outline {
  padding: 7px 25px;
}
.product-right .product-buttons a:last-child {
  margin-left: 10px;
}
.product-right .product-description h6 span {
  float: right;
}
.product-right .product-description .qty-box {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
.product-right .product-description .qty-box .input-group {
  justify-content: unset;
  width: unset;
}
.product-right .product-description .qty-box .input-group .form-control {
  border-right: none;
}
.product-right .size-box {
  margin-top: 10px;
  margin-bottom: 10px;
}
.product-right .size-box ul li {
  height: 35px;
  width: 35px;
  border-radius: 50%;
  margin-right: 10px;
  cursor: pointer;
  border: 1px solid #efefef;
  text-align: center;
}
.product-right .size-box ul li a {
  color: #222222;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.product-right .size-box ul li.active {
  background-color: #efefef;
}
.product-right .product-icon {
  display: flex;
}
.product-right .product-icon .product-social {
  margin-top: 5px;
}
.product-right .product-icon .product-social li {
  padding-right: 30px;
}
.product-right .product-icon .product-social li a {
  color: #333333;
  transition: all 0.3s ease;
}
.product-right .product-icon .product-social li a i {
  font-size: 18px;
}
.product-right .product-icon .product-social li a:hover {
  color: var(--theme-deafult);
}
.product-right .product-icon .product-social li:last-child {
  padding-right: 0;
}
.product-right .product-icon .wishlist-btn {
  background-color: transparent;
  border: none;
}
.product-right .product-icon .wishlist-btn i {
  border-left: 1px solid #dddddd;
  font-size: 18px;
  padding-left: 10px;
  margin-left: 5px;
  transition: all 0.5s ease;
}
.product-right .product-icon .wishlist-btn span {
  padding-left: 10px;
  font-size: 18px;
}
.product-right .product-icon .wishlist-btn:hover i {
  color: var(--theme-deafult);
  transition: all 0.5s ease;
}
.product-right .payment-card-bottom {
  margin-top: 10px;
}
.product-right .payment-card-bottom ul li {
  padding-right: 10px;
}
.product-right .timer {
  margin-top: 10px;
  background-color: #f7f7f7;
}
.product-right .timer p {
  color: #222222;
}
.product-right .rating-section {
  margin-bottom: 8px;
  margin-top: -6px;
  display: flex;
  align-items: center;
}
.product-right .rating-section h6 {
  margin-bottom: 0;
  margin-left: 10px;
  color: #323232;
  font-weight: 700;
  font-size: 15px;
}
.product-right .rating-section .rating i {
  padding-right: 2px;
  font-size: 18px;
}
.product-right .label-section {
  margin-bottom: 15px;
}
.product-right .label-section .badge {
  padding: 6px 11px;
  font-size: 12px;
}
.product-right .label-section .label-text {
  text-transform: capitalize;
  padding-left: 5px;
}
.product-right.product-form-box {
  text-align: center;
  border: 1px solid #dddddd;
  padding: 20px;
}
.product-right.product-form-box .product-description .qty-box {
  margin-bottom: 5px;
}
.product-right.product-form-box .product-description .qty-box .input-group {
  justify-content: center;
  width: 100%;
}
.product-right.product-form-box .product-buttons {
  margin-bottom: 0;
}
.product-right.product-form-box .timer {
  margin-bottom: 10px;
  text-align: left;
}

.rating {
  margin-top: 0;
}
.rating i {
  padding-right: 5px;
}
.rating i:nth-child(-n+4) {
  color: #ffa200;
}
.rating i:last-child {
  color: #dddddd;
}
.rating .three-star {
  padding-bottom: 5px;
}
.rating .three-star i {
  color: #acacac;
}
.rating .three-star i:nth-child(-n+3) {
  color: #ffd200;
}

@-webkit-keyframes buttons-shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-2px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(2px, 0, 0);
  }
}

@keyframes buttons-shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-2px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(2px, 0, 0);
  }
}
.color-variant li {
  display: inline-block;
  height: 20px;
  width: 20px;
  border-radius: 100%;
  margin-right: 5px;
  transition: all 0.1s ease;
  vertical-align: middle;
}

.product-box .image-swatch {
  margin-top: 15px;
}
.product-box .image-swatch li img {
  width: 28px;
  height: 28px;
}

.product-box,
.product-wrap {
  position: relative;
  transition: all 0.5s ease;
  vertical-align: middle;
}
.product-box .img-block,
.product-wrap .img-block {
  background-color: #f9f9f9;
  position: relative;
  overflow: hidden;
}
.product-box .img-block .front,
.product-wrap .img-block .front {
  opacity: 1;
  top: 0;
  left: 0;
  transition: all 0.5s ease;
}
.product-box .img-block .front a,
.product-wrap .img-block .front a {
  display: block;
  width: 100%;
}
.product-box .img-block .back,
.product-wrap .img-block .back {
  opacity: 0;
  position: absolute;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  top: 0;
  left: 0;
  transition: all 0.5s ease;
  transform: translateX(-100px);
  width: 100%;
}
.product-box .img-block .back a,
.product-wrap .img-block .back a {
  display: block;
  width: 100%;
}
.product-box .img-block .lable-wrapper,
.product-wrap .img-block .lable-wrapper {
  margin: 0 auto;
  top: 40px;
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  transition: all 0.5s ease;
  z-index: 2;
}
.product-box .img-block .lable-wrapper .lable1,
.product-box .img-block .lable-wrapper .lable2,
.product-wrap .img-block .lable-wrapper .lable1,
.product-wrap .img-block .lable-wrapper .lable2 {
  font-size: 14px;
  padding: 10px 14px 10px 20px;
  display: inline-block;
  text-transform: uppercase;
  text-align: center;
}
.product-box .img-block .lable-wrapper .lable1,
.product-wrap .img-block .lable-wrapper .lable1 {
  background-color: var(--theme-deafult);
  color: #ffffff;
  border-bottom-left-radius: 25px;
  border-top-left-radius: 25px;
}
.product-box .img-block .lable-wrapper .lable2,
.product-wrap .img-block .lable-wrapper .lable2 {
  background-color: #ffffff;
  color: #000000;
  border-bottom-right-radius: 25px;
  border-top-right-radius: 25px;
}
.product-box .img-wrapper,
.product-wrap .img-wrapper {
  position: relative;
  overflow: hidden;
  z-index: 0;
}
.product-box .img-wrapper .front,
.product-wrap .img-wrapper .front {
  opacity: 1;
  top: 0;
  left: 0;
  transition: all 0.5s ease;
}
.product-box .img-wrapper .back,
.product-wrap .img-wrapper .back {
  opacity: 0;
  position: absolute;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  top: 0;
  left: 0;
  transition: all 0.5s ease;
  transform: translateX(-100px);
  width: 100%;
}
.product-box .img-wrapper .cart-box,
.product-wrap .img-wrapper .cart-box {
  position: absolute;
  margin: 0 auto;
  display: inline-block;
  right: 0;
  left: 0;
  border-radius: 50px;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  padding: 12px 15px;
  box-shadow: 0 0 12px 0 #dddddd;
  bottom: 30px;
  background-color: #ffffff;
  opacity: 0;
  transition: all 0.2s ease;
}
.product-box .img-wrapper .cart-box button,
.product-wrap .img-wrapper .cart-box button {
  background: none;
  box-shadow: none;
  border: none;
  padding: 0;
}
.product-box .img-wrapper .cart-box i,
.product-wrap .img-wrapper .cart-box i {
  color: #6f6f6f;
  font-size: 18px;
  padding-left: 8px;
  padding-right: 8px;
  transition: all 0.2s ease;
  display: inline-block;
}
.product-box .img-wrapper .cart-box i:hover,
.product-wrap .img-wrapper .cart-box i:hover {
  color: var(--theme-deafult);
  transition: all 0.2s ease;
}
.product-box .img-wrapper .cart-box.cart-box-bottom,
.product-wrap .img-wrapper .cart-box.cart-box-bottom {
  bottom: 0;
  border-radius: 0;
  display: flex;
  right: unset;
}
.product-box .img-wrapper .lable-block .lable3,
.product-wrap .img-wrapper .lable-block .lable3 {
  border-radius: 100%;
  background-color: var(--theme-deafult);
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  position: absolute;
  padding: 12px 6px;
  text-transform: uppercase;
  color: #ffffff;
  top: 7px;
  left: 7px;
  z-index: 1;
}
.product-box .img-wrapper .lable-block .lable4,
.product-wrap .img-wrapper .lable-block .lable4 {
  position: absolute;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  -ms-writing-mode: tb-rl;
      writing-mode: vertical-rl;
  transform: rotate(-180deg);
  top: 7px;
  right: 7px;
  letter-spacing: 0.1em;
  z-index: 1;
  color: #212529;
}
.product-box .cart-info,
.product-box .cart-wrap,
.product-wrap .cart-info,
.product-wrap .cart-wrap {
  position: absolute;
  bottom: 40px;
  text-align: center;
  margin: 0 auto;
  display: inline-block;
  right: 0;
  left: 0;
  justify-content: center;
  opacity: 0;
  transition: all 0.5s ease;
}
.product-box .cart-info a [class^=ti-],
.product-box .cart-info a [class*=" ti-"],
.product-box .cart-wrap a [class^=ti-],
.product-box .cart-wrap a [class*=" ti-"],
.product-wrap .cart-info a [class^=ti-],
.product-wrap .cart-info a [class*=" ti-"],
.product-wrap .cart-wrap a [class^=ti-],
.product-wrap .cart-wrap a [class*=" ti-"] {
  display: inline-block;
}
.product-box .cart-info.cart-wrap,
.product-box .cart-wrap.cart-wrap,
.product-wrap .cart-info.cart-wrap,
.product-wrap .cart-wrap.cart-wrap {
  bottom: 0;
  text-align: right;
  left: unset;
}
.product-box .cart-info.cart-wrap i,
.product-box .cart-wrap.cart-wrap i,
.product-wrap .cart-info.cart-wrap i,
.product-wrap .cart-wrap.cart-wrap i {
  display: block;
  padding-bottom: 10px;
  padding-top: 10px;
}
.product-box .cart-info.cart-wrap.cart-effect-left,
.product-box .cart-wrap.cart-wrap.cart-effect-left,
.product-wrap .cart-info.cart-wrap.cart-effect-left,
.product-wrap .cart-wrap.cart-wrap.cart-effect-left {
  left: 0;
  right: unset;
}
.product-box .cart-info button,
.product-box .cart-wrap button,
.product-wrap .cart-info button,
.product-wrap .cart-wrap button {
  background: none;
  box-shadow: none;
  border: none;
  padding: 0;
}
.product-box .cart-info i,
.product-box .cart-wrap i,
.product-wrap .cart-info i,
.product-wrap .cart-wrap i {
  color: #6f6f6f;
  font-size: 18px;
  padding-right: 10px;
  padding-left: 10px;
}
.product-box .cart-info i:hover,
.product-box .cart-wrap i:hover,
.product-wrap .cart-info i:hover,
.product-wrap .cart-wrap i:hover {
  color: var(--theme-deafult);
}
.product-box .cart-detail,
.product-wrap .cart-detail {
  position: absolute;
  top: 15px;
  right: 20px;
  opacity: 0;
}
.product-box .cart-detail i,
.product-wrap .cart-detail i {
  color: #6f6f6f;
  font-size: 18px;
  display: flex;
  padding-top: 8px;
  padding-bottom: 8px;
}
.product-box .cart-detail button,
.product-wrap .cart-detail button {
  background: none;
  box-shadow: none;
  border: none;
  padding: 0;
}
.product-box .product-detail,
.product-box .product-info,
.product-wrap .product-detail,
.product-wrap .product-info {
  padding-left: 5px;
  margin-top: 15px;
}
.product-box .product-detail .rating i,
.product-box .product-info .rating i,
.product-wrap .product-detail .rating i,
.product-wrap .product-info .rating i {
  padding-right: 5px;
}
.product-box .product-detail .rating i:nth-child(-n+4),
.product-box .product-info .rating i:nth-child(-n+4),
.product-wrap .product-detail .rating i:nth-child(-n+4),
.product-wrap .product-info .rating i:nth-child(-n+4) {
  color: #ffa200;
}
.product-box .product-detail .rating i:last-child,
.product-box .product-info .rating i:last-child,
.product-wrap .product-detail .rating i:last-child,
.product-wrap .product-info .rating i:last-child {
  color: #dddddd;
}
.product-box .product-detail h6,
.product-box .product-info h6,
.product-wrap .product-detail h6,
.product-wrap .product-info h6 {
  line-height: 1;
  margin-bottom: 0;
  padding-top: 2px;
  padding-bottom: 5px;
  transition: all 0.5s ease;
  font-size: 16px;
  text-transform: capitalize;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.product-box .product-detail h4,
.product-box .product-info h4,
.product-wrap .product-detail h4,
.product-wrap .product-info h4 {
  font-size: 18px;
  color: #222222;
  font-weight: 700;
  margin-bottom: 0;
  transition: all 0.5s ease;
}
.product-box .product-detail .color-variant,
.product-box .product-info .color-variant,
.product-wrap .product-detail .color-variant,
.product-wrap .product-info .color-variant {
  padding-top: 15px;
}
.product-box .product-detail .color-variant li,
.product-box .product-info .color-variant li,
.product-wrap .product-detail .color-variant li,
.product-wrap .product-info .color-variant li {
  display: inline-block;
  height: 20px;
  width: 20px;
  border-radius: 100%;
  margin-right: 5px;
  transition: all 0.1s ease;
  cursor: pointer;
}
.product-box .product-info,
.product-wrap .product-info {
  padding: 0;
  text-align: center;
  position: relative;
}
.product-box .product-info .add-btn,
.product-wrap .product-info .add-btn {
  position: absolute;
  bottom: 110px;
  margin: 0 auto;
  left: 0;
  right: 0;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 1;
}
.product-box .product-info .add-btn i,
.product-wrap .product-info .add-btn i {
  color: var(--theme-deafult);
}
.product-box .product-info .add-btn .btn-outline,
.product-wrap .product-info .add-btn .btn-outline {
  transition: all 0.2s ease;
  color: var(--theme-deafult);
}
.product-box .product-info .add-btn .btn-outline:hover,
.product-wrap .product-info .add-btn .btn-outline:hover {
  color: #ffffff;
}
.product-box .product-info .add-btn .btn-outline:hover i,
.product-wrap .product-info .add-btn .btn-outline:hover i {
  color: #ffffff;
}
.product-box.effect-center .front img,
.product-wrap.effect-center .front img {
  transition: all 0.5s ease;
}
.product-box.effect-center .img-wrapper .cart-box,
.product-wrap.effect-center .img-wrapper .cart-box {
  bottom: 20%;
  transition: all 0.5s ease;
}
.product-box:hover.effect-center .front img,
.product-wrap:hover.effect-center .front img {
  opacity: 0.3;
  transition: all 0.5s ease;
}
.product-box:hover.effect-center .img-wrapper .cart-box,
.product-wrap:hover.effect-center .img-wrapper .cart-box {
  bottom: 35%;
  transition: all 0.5s ease;
}
.product-box:hover .img-block .first,
.product-box:hover .img-wrapper .first,
.product-wrap:hover .img-block .first,
.product-wrap:hover .img-wrapper .first {
  opacity: 0;
  transition: all 0.5s ease;
}
.product-box:hover .img-block .back,
.product-box:hover .img-wrapper .back,
.product-wrap:hover .img-block .back,
.product-wrap:hover .img-wrapper .back {
  opacity: 1;
  transition: all 0.5s ease;
  transform: translateX(0);
}
.product-box:hover .cart-info,
.product-wrap:hover .cart-info {
  opacity: 1;
  transition: all 0.5s ease;
}
.product-box:hover .cart-info button,
.product-wrap:hover .cart-info button {
  -webkit-animation: fadeInUp 300ms ease-in-out;
          animation: fadeInUp 300ms ease-in-out;
}
.product-box:hover .cart-info a:nth-child(2) i,
.product-wrap:hover .cart-info a:nth-child(2) i {
  -webkit-animation: fadeInUp 500ms ease-in-out;
          animation: fadeInUp 500ms ease-in-out;
}
.product-box:hover .cart-info a:nth-child(3) i,
.product-wrap:hover .cart-info a:nth-child(3) i {
  -webkit-animation: fadeInUp 700ms ease-in-out;
          animation: fadeInUp 700ms ease-in-out;
}
.product-box:hover .cart-info a:nth-child(4) i,
.product-wrap:hover .cart-info a:nth-child(4) i {
  -webkit-animation: fadeInUp 1000ms ease-in-out;
          animation: fadeInUp 1000ms ease-in-out;
}
.product-box:hover .cart-wrap button,
.product-wrap:hover .cart-wrap button {
  -webkit-animation: fadeInRight 300ms ease-in-out;
          animation: fadeInRight 300ms ease-in-out;
}
.product-box:hover .cart-wrap a:nth-child(2) i,
.product-wrap:hover .cart-wrap a:nth-child(2) i {
  -webkit-animation: fadeInRight 500ms ease-in-out;
          animation: fadeInRight 500ms ease-in-out;
}
.product-box:hover .cart-wrap a:nth-child(3) i,
.product-wrap:hover .cart-wrap a:nth-child(3) i {
  -webkit-animation: fadeInRight 700ms ease-in-out;
          animation: fadeInRight 700ms ease-in-out;
}
.product-box:hover .cart-wrap a:nth-child(4) i,
.product-wrap:hover .cart-wrap a:nth-child(4) i {
  -webkit-animation: fadeInRight 1000ms ease-in-out;
          animation: fadeInRight 1000ms ease-in-out;
}
.product-box:hover .cart-wrap.cart-effect-left button,
.product-wrap:hover .cart-wrap.cart-effect-left button {
  -webkit-animation: fadeInLeft 300ms ease-in-out;
          animation: fadeInLeft 300ms ease-in-out;
}
.product-box:hover .cart-wrap.cart-effect-left a:nth-child(2) i,
.product-wrap:hover .cart-wrap.cart-effect-left a:nth-child(2) i {
  -webkit-animation: fadeInLeft 500ms ease-in-out;
          animation: fadeInLeft 500ms ease-in-out;
}
.product-box:hover .cart-wrap.cart-effect-left a:nth-child(3) i,
.product-wrap:hover .cart-wrap.cart-effect-left a:nth-child(3) i {
  -webkit-animation: fadeInLeft 700ms ease-in-out;
          animation: fadeInLeft 700ms ease-in-out;
}
.product-box:hover .cart-wrap.cart-effect-left a:nth-child(4) i,
.product-wrap:hover .cart-wrap.cart-effect-left a:nth-child(4) i {
  -webkit-animation: fadeInLeft 1000ms ease-in-out;
          animation: fadeInLeft 1000ms ease-in-out;
}
.product-box:hover .cart-detail,
.product-wrap:hover .cart-detail {
  opacity: 1;
  transition: all 0.5s ease;
}
.product-box:hover .cart-detail button,
.product-wrap:hover .cart-detail button {
  -webkit-animation: fadeInRight 300ms ease-in-out;
          animation: fadeInRight 300ms ease-in-out;
}
.product-box:hover .cart-detail a:nth-child(2) i,
.product-wrap:hover .cart-detail a:nth-child(2) i {
  -webkit-animation: fadeInRight 500ms ease-in-out;
          animation: fadeInRight 500ms ease-in-out;
}
.product-box:hover .cart-detail a:nth-child(3) i,
.product-wrap:hover .cart-detail a:nth-child(3) i {
  -webkit-animation: fadeInRight 700ms ease-in-out;
          animation: fadeInRight 700ms ease-in-out;
}
.product-box:hover .cart-detail a:nth-child(4) i,
.product-wrap:hover .cart-detail a:nth-child(4) i {
  -webkit-animation: fadeInRight 1000ms ease-in-out;
          animation: fadeInRight 1000ms ease-in-out;
}
.product-box:hover .product-info .add-btn,
.product-wrap:hover .product-info .add-btn {
  opacity: 1;
  transition: all 0.2s ease;
  -webkit-animation: fadeInUp 500ms ease-in-out;
          animation: fadeInUp 500ms ease-in-out;
}
.product-box:hover .img-wrapper .cart-box,
.product-wrap:hover .img-wrapper .cart-box {
  opacity: 1;
  transition: all 0.2s ease;
  -webkit-animation: fadeInUp 400ms ease-in-out;
          animation: fadeInUp 400ms ease-in-out;
}

.product-box .cart-info.bg-color-cls {
  right: -40px;
}
.product-box .cart-info.bg-color-cls button {
  background-color: white;
  -webkit-animation: none !important;
          animation: none !important;
}
.product-box .cart-info.bg-color-cls a i {
  background-color: white;
  -webkit-animation: none !important;
          animation: none !important;
}
.product-box .cart-info.bg-color-cls.sm-box i {
  padding-bottom: 7px;
  padding-top: 7px;
  font-size: 16px;
}
.product-box:hover .cart-info.bg-color-cls {
  right: 0;
}

.toast-top-right {
  top: 70px !important;
}
.toast-top-right > div {
  opacity: 1 !important;
}

.trafic-section {
  background: transparent;
  display: flex;
  align-items: flex-start;
  flex-direction: unset;
  padding: 30px;
  flex-wrap: nowrap;
  gap: 16px;
}
.trafic-section .trafic-icon {
  width: 50px;
  height: 50px;
  border-radius: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 27px;
}
.trafic-section .trafic-content span {
  font-size: 15px;
  margin-bottom: 6px;
}
.trafic-section .trafic-content h3 {
  margin-bottom: 0;
}
.trafic-section.bg-danger {
  background: #e7505a3b !important;
}
.trafic-section.bg-danger .trafic-icon {
  color: #e7505a;
}
.trafic-section.bg-danger .trafic-content span {
  color: #e7505a;
}

.progress-box-list {
  display: flex;
  flex-wrap: wrap;
  gap: 26px;
}
.progress-box-list li {
  width: 100%;
}
.progress-box-list li .progress-box .progress-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.progress-box-list li .progress-box .progress-name h5 {
  margin: 0;
  line-height: 1;
}
.progress-box-list li .progress-box .city-progress .progress-bar.bg-green {
  background-color: #10b981;
}
.progress-box-list li .progress-box .city-progress .progress-bar.bg-orange {
  background-color: #ff9800;
}
.progress-box-list li .progress-box .city-progress .progress-bar.bg-pink {
  background-color: #4f46e5;
}
.progress-box-list li .progress-box .city-progress .progress-bar.bg-grey {
  background-color: #64748b;
}
.progress-box-list li .progress-box .city-progress .progress-bar.bg-sky {
  background-color: #0ea5e9;
}

.color-form .fill-color {
  display: inline-block;
  width: 56px;
  height: 37px;
  padding: 4px;
  overflow: hidden;
}

.form-check {
  padding: 0;
}
.form-check .form-check-input {
  margin: 0;
  float: unset;
}

.jvectormap-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.jvectormap-tip {
  position: absolute;
  display: none;
  border: solid 1px #4F5467;
  border-radius: 2px;
  background: #4F5467;
  color: white;
  font-family: sans-serif, Verdana;
  font-size: smaller;
  padding: 3px;
}

.jvectormap-zoomin {
  position: absolute;
  left: 10px;
  border-radius: 2px;
  background: #4F5467;
  padding: 5px;
  color: white;
  cursor: pointer;
  line-height: 20px;
  text-align: center;
  box-sizing: content-box;
  width: 20px;
  height: 20px;
  top: 10px;
}

.jvectormap-zoomout {
  position: absolute;
  left: 10px;
  border-radius: 2px;
  background: #4F5467;
  padding: 5px;
  color: white;
  cursor: pointer;
  line-height: 20px;
  text-align: center;
  box-sizing: content-box;
  width: 20px;
  height: 20px;
  top: 50px;
}

.jvectormap-goback {
  position: absolute;
  left: 10px;
  border-radius: 2px;
  background: #4F5467;
  padding: 5px;
  color: white;
  cursor: pointer;
  line-height: 20px;
  text-align: center;
  box-sizing: content-box;
  bottom: 10px;
  z-index: 1000;
  padding: 6px;
}

.jvectormap-spinner {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: center no-repeat url(data:image/gif;base64,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);
}

.jvectormap-legend-title {
  font-weight: bold;
  font-size: 14px;
  text-align: center;
}

.jvectormap-legend-cnt {
  position: absolute;
}

.jvectormap-legend-cnt-h {
  bottom: 0;
  right: 0;
}
.jvectormap-legend-cnt-h .jvectormap-legend {
  float: left;
  margin: 0 10px 10px 0;
  padding: 3px 3px 1px 3px;
}
.jvectormap-legend-cnt-h .jvectormap-legend .jvectormap-legend-tick {
  float: left;
}
.jvectormap-legend-cnt-h .jvectormap-legend-tick {
  width: 40px;
}
.jvectormap-legend-cnt-h .jvectormap-legend-tick-sample {
  height: 15px;
}
.jvectormap-legend-cnt-h .jvectormap-legend-tick-text {
  text-align: center;
}

.jvectormap-legend-cnt-v {
  top: 0;
  right: 0;
}
.jvectormap-legend-cnt-v .jvectormap-legend {
  margin: 10px 10px 0 0;
  padding: 3px;
}
.jvectormap-legend-cnt-v .jvectormap-legend-tick-sample {
  height: 20px;
  width: 20px;
  display: inline-block;
  vertical-align: middle;
}
.jvectormap-legend-cnt-v .jvectormap-legend-tick-text {
  display: inline-block;
  vertical-align: middle;
  line-height: 20px;
  padding-left: 3px;
}

.jvectormap-legend {
  background: black;
  color: white;
  border-radius: 3px;
}

.jvectormap-legend-tick-text {
  font-size: 12px;
}

/**=====================
    Dark css start
==========================**/
body.dark {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  color: #cfd4da;
}

.dark h1 {
  color: #ffffff;
}
.dark h2 {
  color: #f1f3f5;
}
.dark h3 {
  color: #eaedef;
}
.dark h4 {
  color: #e7eaec;
}
.dark h5 {
  color: #dee2e6;
}
.dark h6 {
  color: #cfd4da;
}
.dark p {
  color: #cfd4da;
}
.dark li {
  color: #cfd4da;
}
.dark a {
  color: #cbcbcb;
}
.dark .bg_cls {
  background-color: #232323;
}
.dark .white-bg {
  background-color: #2b2b2b !important;
}
.dark .grey-bg {
  background-color: #232323;
}
.dark .border-top {
  border-color: #404040 !important;
}
.dark .border-top-grey {
  border-color: #404040;
}
.dark .bg-light {
  background-color: #232323 !important;
}
.dark .blog-section .blog-details p {
  color: #cfd4da;
}
.dark .blog-section .review-box {
  background-color: #2b2b2b;
}
.dark .blog-section .review-box .review-content h6 {
  color: #cfd4da;
}
.dark .blog-section .review-box .slick-prev:after {
  background-color: #404040;
}
.dark .page-link {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .breadcrumb-section {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .light-layout {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .form-control {
  color: #cfd4da;
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  border: 1px solid #404040;
}
.dark mark, .dark .mark {
  background-color: #232323;
  color: #ffffff;
}
.dark .product-style-1.product-box {
  border-color: #404040;
}
.dark .addtocart_count .product-box.product-style-1 .add-button {
  background-color: #232323;
  color: #cbcbcb;
}
.dark .addtocart_count .product-box .add-button {
  background-color: #232323;
  color: #ffffff;
}
.dark .addtocart_count .addtocart_btn .cart_qty.qty-box .input-group button {
  background: #232323 !important;
}
.dark .addtocart_count .addtocart_btn .cart_qty.qty-box .input-group button i {
  color: #ffffff;
}
.dark .btn-solid {
  color: #ffffff !important;
}
.dark .btn-solid:hover {
  color: #222222 !important;
}
.dark select {
  color: #cfd4da;
}
.dark option {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark header {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark header.header-5 .pixelstrap a, .dark header.header-5 .pixelstrap:hover, .dark header.header-5 .pixelstrap:active {
  color: #cfd4da;
}
.dark header.header-5.left-sidebar-header {
  background-color: #2b2b2b;
}
.dark header.header-5.left-sidebar-header .top-header .header-dropdown > li img {
  filter: invert(1);
}
.dark header.header-5.left-sidebar-header .form_search {
  box-shadow: 0 0 0 1px #545454;
}
.dark header.header-5.left-sidebar-header .onhover-div > div img {
  filter: invert(1);
}
.dark header.header-metro .metro {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark header.header-metro .top-header .header-contact li {
  color: #999999;
}
.dark header.header-metro .top-header .header-dropdown li {
  color: #999999;
}
.dark header.header-metro .top-header .header-dropdown li a i {
  color: #999999;
}
.dark header.header-tools {
  background-color: transparent;
  transition: all 0.3s ease;
}
.dark header.header-tools .logo-menu-part > .container {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark header.header-tools.header-style.top-relative {
  background-color: #2b2b2b;
}
.dark header.left-header .top-header {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark header.left-header .main-menu .menu-right .header-dropdown > li img {
  filter: invert(100);
}
.dark header.left-header .onhover-div > div img {
  filter: invert(100);
}
.dark header.left-header .sidenav nav {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark header.left-header .sidenav .left-sidebar_center {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark header.left-header .sidenav .pixelstrap > li > a {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark header.left-header.left-header-sm .sidenav .left-sidebar_center .pixelstrap > li {
  border-color: #404040;
}
.dark header.left-header.left-header-sm .sidenav .left-sidebar_center .pixelstrap > li > a img {
  background-color: transparent;
  filter: invert(1);
}
.dark header.green-gradient {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark header.green-gradient .top-header {
  background-color: #232323;
  transition: all 0.3s ease;
  background: #232323;
  background-image: none;
}
.dark header.header-gym .pixelstrap > li > a {
  color: white !important;
}
.dark header.header-style .top-header .header-dropdown > li img {
  filter: invert(1);
}
.dark header.header-style .onhover-div > div img {
  filter: invert(1);
}
.dark header.left-header.left-header-relative .pixelstrap > li > a {
  background-color: #2b2b2b;
}
.dark header.left-header .main-menu .brand-logo {
  border-color: #404040;
}
.dark header.left-header .sidenav .leftside_social {
  border-color: #404040;
}
.dark .header-options span {
  color: #dee2e6;
}
.dark .header-style-1 .navbar .nav-link {
  color: #cbcbcb;
}
.dark .header-style-5 .bottom-part {
  background-color: #232323;
}
.dark .header-style-5 .bottom-part.bottom-light {
  border-color: #404040;
  background-color: transparent;
}
.dark .header-style-5 .bottom-part.bottom-light .pixelstrap > li > a {
  color: #cfd4da;
}
.dark .header-style-5 .bottom-part.bottom-light .pixelstrap > li > a:hover, .dark .header-style-5 .bottom-part.bottom-light .pixelstrap > li > a:focus {
  color: #cfd4da;
}
.dark .header-style-5.style-light .bottom-part .category-menu .toggle-sidebar {
  background-color: #2b2b2b;
  color: #cfd4da;
  border-color: #38352f;
}
.dark .header-style-5.style-light .bottom-part .category-menu .toggle-sidebar h5 {
  color: #cfd4da;
}
.dark .header-style-5.color-style {
  background-color: #2b2b2b;
}
.dark .header-style-5.color-style .top-header.top-header-theme {
  background-color: #2b2b2b;
  border-color: #404040;
}
.dark .header-style-5.color-style .bottom-part .pixelstrap.sm-vertical > li > a {
  color: #cbcbcb;
}
.dark .header-style-5.color-style .bottom-part .pixelstrap.sm-vertical > li > a:hover, .dark .header-style-5.color-style .bottom-part .pixelstrap.sm-vertical > li > a:focus, .dark .header-style-5.color-style .bottom-part .pixelstrap.sm-vertical > li > a:active {
  color: #cbcbcb;
}
.dark .header-style-5.color-style.style-classic .bottom-part .pixelstrap > li > a {
  color: #cbcbcb;
}
.dark .header-style-5.color-style.style-classic .bottom-part .pixelstrap > li > a:hover, .dark .header-style-5.color-style.style-classic .bottom-part .pixelstrap > li > a:focus, .dark .header-style-5.color-style.style-classic .bottom-part .pixelstrap > li > a:active {
  color: #cbcbcb;
}
.dark .header-style-5.color-style.style-classic .bottom-part .container {
  background-color: #232323;
}
.dark .bg-theme {
  background-color: #232323;
}
.dark.section-white section {
  background-color: #2b2b2b;
}
.dark .marketplace-sidebar.sidenav.fixed-sidebar .sm-vertical {
  background-color: #2b2b2b;
  border-color: #404040;
}
.dark .marketplace-sidebar.sidenav .sm-vertical {
  background-color: #232323;
}
.dark .svg-icon-menu.wo-bg .pixelstrap > li > a svg {
  fill: white;
}
.dark .svg-icon-menu .pixelstrap > li > a img {
  background-color: #2b2b2b;
  border-color: #404040;
}
.dark .ajax-search .typeahead {
  background-color: #2b2b2b !important;
}
.dark .ajax-search .tt-menu {
  background-color: #2b2b2b;
}
.dark .ajax-search .description-section h4 {
  color: #e7eaec;
}
.dark .product-vertical .full-box .theme-card .offer-slider {
  background-color: #2b2b2b;
}
.dark .top-header {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .sub-footer {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .border-section {
  border-color: #404040;
}
.dark .pixelstrap a, .dark .pixelstrap:hover, .dark .pixelstrap:active {
  color: #f1f3f5;
}
.dark .sm-vertical {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .sidenav nav {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .sidenav .sidebar-back {
  color: #f1f3f5;
  border-color: #404040;
}
.dark .pixelstrap ul {
  background: #232323;
  box-shadow: none;
}
.dark .pixelstrap ul a {
  color: #e7eaec;
}
.dark .pixelstrap ul a:hover, .dark .pixelstrap ul a:focus, .dark .pixelstrap ul a:active, .dark .pixelstrap ul a.highlighted {
  color: #e7eaec;
}
.dark .pixelstrap .home-menu,
.dark .pixelstrap .feature-menu,
.dark .pixelstrap .category-menu,
.dark .pixelstrap .full-mega-menu,
.dark .pixelstrap .clothing-menu {
  box-shadow: 0 0 1px 0 #2b2b2b;
}
.dark .onhover-div > div img {
  filter: brightness(100);
}
.dark .onhover-div .show-div {
  background-color: #232323;
  transition: all 0.3s ease;
  box-shadow: none;
}
.dark .error-section h1 {
  color: #e6e6e6;
}
.dark .breadcrumb-item.active {
  color: #e7eaec;
}
.dark .breadcrumb-section .breadcrumb a {
  color: #f7f7f7;
}
.dark .main-menu .menu-left .navbar i {
  color: #f7f7f7;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.setting h6 {
  color: #cfd4da;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.setting ul li a {
  color: #cfd4da;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .media .media-body h4 {
  color: #e7eaec;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .media .media-body h4 span {
  color: #dee2e6;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .close-circle i:hover {
  color: #ffffff;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .total {
  border-color: #404040;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .total h5 {
  color: #dee2e6;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .buttons a {
  color: #cbcbcb;
}
.dark .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .buttons a:hover {
  color: var(--theme-deafult);
}
.dark .footer-theme .sub-title li {
  color: #cfd4da;
}
.dark .footer-theme .sub-title li a {
  color: #cfd4da;
}
.dark .footer-theme .sub-title h4 {
  color: #e7eaec;
}
.dark .footer-social i,
.dark .social-white i {
  color: #cfd4da;
}
.dark .footer-light .subscribe {
  border-color: #404040;
}
.dark .footer-theme2 .contact-details li {
  color: #cfd4da;
}
.dark .footer-theme2 .contact-details li a {
  color: #e7eaec;
}
.dark .footer-theme2 .footer-link a,
.dark .footer-theme2 .footer-link-b a {
  color: #cbcbcb;
}
.dark .footer-theme2 .footer-link a:hover,
.dark .footer-theme2 .footer-link-b a:hover {
  color: #ffffff;
}
.dark .top-header .header-contact li {
  color: #cfd4da;
}
.dark .top-header .header-dropdown li {
  color: #cfd4da;
}
.dark .top-header .header-dropdown li a i {
  color: #cfd4da;
}
.dark .top-header .header-dropdown .onhover-dropdown .onhover-show-div li a {
  color: #cbcbcb;
}
.dark .top-header.top-header-dark .header-dropdown li a {
  color: #cbcbcb;
}
.dark .top-header.top-header-dark .header-contact li i {
  color: #cbcbcb;
}
.dark .top-header.top-header-dark2 .header-dropdown li a {
  color: #cfd4da;
}
.dark .top-header.top-header-dark2 .header-contact li i {
  color: #cfd4da;
}
.dark .top-header.top-header-dark3 .header-dropdown li a {
  color: #cbcbcb;
}
.dark .top-header.top-header-dark3 .header-contact li i {
  color: #cbcbcb;
}
.dark .onhover-dropdown .onhover-show-div {
  background-color: #232323;
  transition: all 0.3s ease;
  box-shadow: none;
}
.dark .testimonial {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .testimonial .testimonial-slider .slick-track .slick-slide:nth-child(even) .media {
  border-color: #404040;
}
.dark .testimonial .testimonial-slider .media img {
  border-color: #404040;
}
.dark .team h2 {
  border-color: #404040;
}
.dark .service-block + .service-block {
  border-color: #404040;
}
.dark .pixelstrap.light-font-menu li > a {
  color: #f1f3f5;
}
.dark .theme-tab .tab-title a,
.dark .theme-tab .tab-title2 a {
  color: #cbcbcb;
}
.dark .theme-tab .tab-title .current a,
.dark .theme-tab .tab-title2 .current a {
  color: var(--theme-deafult);
}
.dark .theme-tab .tab-title2:after {
  border-color: #404040;
}
.dark .theme-tab .tab-title2 .current {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .theme-tab .tab-title2 .current a {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .product-box .cart-info.bg-color-cls button {
  background-color: #2b2b2b;
}
.dark .product-box .cart-info.bg-color-cls a i {
  background-color: #2b2b2b;
}
.dark .product-box .product-detail h4,
.dark .product-box .product-info h4,
.dark .product-wrap .product-detail h4,
.dark .product-wrap .product-info h4 {
  color: #e7eaec;
}
.dark .theme-card .offer-slider .media .media-body h4 {
  color: #e7eaec;
}
.dark .theme-card .slick-prev:before,
.dark .theme-card .slick-next:before {
  color: #ffffff;
}
.dark .theme-card h5.title-border {
  border-color: #404040;
}
.dark .theme-card.card-border {
  border-color: #404040;
}
.dark .dark-layout {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .sub-footer.darker-subfooter {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .sub-footer.darker-subfooter p {
  color: #cfd4da;
}
.dark .blog-page .blog-media .blog-right h4 {
  color: #e7eaec;
}
.dark .blog-page .blog-media .blog-right ul {
  color: #cfd4da;
}
.dark .blog-page .blog-media .blog-right ul li + li {
  border-color: #404040;
}
.dark .blog-page .blog-sidebar .theme-card {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .blog-page .blog-sidebar h4 {
  color: #e7eaec;
}
.dark .blog-page .blog-sidebar h6 {
  color: #cfd4da;
}
.dark .blog-page .blog-sidebar p {
  color: #cfd4da;
}
.dark .blog-detail-page .blog-detail h3 {
  color: #eaedef;
}
.dark .blog-detail-page .blog-detail p {
  color: #cfd4da;
}
.dark .blog-detail-page .post-social {
  color: #cfd4da;
  border-color: #404040;
}
.dark .blog-detail-page .post-social li + li {
  border-color: #404040;
}
.dark .blog-detail-page .comment-section {
  border-color: #404040;
}
.dark .blog-detail-page .comment-section li {
  border-color: #404040;
}
.dark .blog-detail-page .comment-section li h6 {
  color: #cfd4da;
}
.dark .blog-detail-page .comment-section li h6 span {
  color: #929292;
}
.dark .blog-detail-page .blog-contact .theme-form label {
  color: #cfd4da;
}
.dark .blog-detail-page .blog-contact .theme-form input,
.dark .blog-detail-page .blog-contact .theme-form textarea {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .form_search {
  background-color: #232323;
  box-shadow: none;
}
.dark .img-category .img-sec {
  border-color: #404040;
}
.dark .dark-overlay {
  background-blend-mode: overlay;
  background-color: #232323;
}
.dark .vector-category .container {
  background-color: #2b2b2b;
  box-shadow: 0 0 8px #0a0a0a;
}
.dark .vector-category .category-slide .slick-next, .dark .vector-category .category-slide .slick-prev {
  background-color: #2b2b2b;
  box-shadow: 0 0 8px #0a0a0a;
}
.dark .vector-category .category-slide .slick-next:before, .dark .vector-category .category-slide .slick-prev:before {
  color: #ffffff;
}
.dark .vector-category .category-slide .img-category .img-sec {
  background-color: #232323;
}
.dark .vector-category .category-slide .img-category .img-sec img {
  filter: invert(0.6);
}
.dark .cart_counter {
  background: #232323;
  box-shadow: none;
  color: #cbcbcb;
}
.dark .cart-section .cart-table thead th,
.dark .wishlist-section .cart-table thead th {
  color: #f1f3f5;
}
.dark .cart-section tbody tr td,
.dark .wishlist-section tbody tr td {
  border-color: #404040 !important;
}
.dark .cart-section tbody tr td a,
.dark .wishlist-section tbody tr td a {
  color: #cbcbcb;
}
.dark .cart-section tbody tr td h2,
.dark .wishlist-section tbody tr td h2 {
  color: #f1f3f5;
}
.dark .table tbody + tbody {
  border-color: #404040;
}
.dark .table thead th {
  border-color: #404040 !important;
}
.dark .irs-from, .dark .irs-to, .dark .irs-single {
  color: #cbcbcb;
}
.dark .irs-line {
  background: #2b2b2b;
}
.dark .top-banner-wrapper .top-banner-content h4 {
  color: #e7eaec;
}
.dark .top-banner-wrapper .top-banner-content h5 {
  color: #dee2e6;
}
.dark .top-banner-wrapper .top-banner-content p {
  color: #cfd4da;
}
.dark .collection-product-wrapper .product-top-filter {
  border-color: #404040;
}
.dark .collection-product-wrapper .product-top-filter .product-filter-content .search-count,
.dark .collection-product-wrapper .product-top-filter .product-filter-content .sidebar-popup,
.dark .collection-product-wrapper .product-top-filter .popup-filter .search-count,
.dark .collection-product-wrapper .product-top-filter .popup-filter .sidebar-popup {
  border-color: #404040;
}
.dark .collection-product-wrapper .product-top-filter .product-filter-content .collection-grid-view,
.dark .collection-product-wrapper .product-top-filter .popup-filter .collection-grid-view {
  border-color: #404040;
}
.dark .collection-product-wrapper .product-top-filter .product-filter-content .product-page-per-view select,
.dark .collection-product-wrapper .product-top-filter .popup-filter .product-page-per-view select {
  border-color: #404040;
}
.dark .collection-product-wrapper .product-top-filter .popup-filter .sidebar-popup a {
  color: #cbcbcb;
}
.dark .collection-product-wrapper .product-top-filter .popup-filter .open-popup {
  border-color: #404040;
  box-shadow: none;
}
.dark .product-pagination {
  border-color: #404040;
}
.dark .product-pagination .pagination .page-item.active a {
  background-color: #232323;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .product-pagination .product-search-count-bottom {
  border-color: #404040;
}
.dark .product-pagination .product-search-count-bottom h5 {
  color: #dee2e6;
}
.dark .portfolio-section.metro-section .product-box .product-detail {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .portfolio-section.metro-section .product-box .product-detail h4 {
  color: #e7eaec;
}
.dark .portfolio-section.metro-section .product-box .product-detail h6 {
  color: #cfd4da;
}
.dark .portfolio-section.metro-section .product-box .cart-wrap button {
  border-color: #404040;
  background-color: rgba(43, 43, 43, 0.8);
  transition: all 0.3s ease;
}
.dark .portfolio-section.metro-section .product-box .cart-wrap button i {
  color: #cbcbcb;
}
.dark .portfolio-section.metro-section .product-box .cart-wrap a i {
  border-color: #404040;
  background-color: rgba(43, 43, 43, 0.8);
  transition: all 0.3s ease;
  color: #cbcbcb;
}
.dark .collection-filter-block {
  border-color: #404040;
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .collection-collapse-block .collapse-block-title {
  color: #eaedef;
}
.dark .collection-collapse-block .collapse-block-title:after {
  color: #eaedef;
}
.dark .collection-collapse-block .collection-collapse-block-content .collection-brand-filter .collection-filter-checkbox label {
  color: #929292;
}
.dark .load-more-sec a {
  border-color: #404040;
}
.dark .checkout-page .checkout-title h3 {
  color: #eaedef;
}
.dark .checkout-page .checkout-form .form-group .field-label {
  color: #cfd4da;
}
.dark .checkout-page .checkout-form input[type=text], .dark .checkout-page .checkout-form input[type=email], .dark .checkout-page .checkout-form input[type=password], .dark .checkout-page .checkout-form input[type=tel], .dark .checkout-page .checkout-form input[type=number], .dark .checkout-page .checkout-form input[type=url] {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .checkout-page .checkout-form select,
.dark .checkout-page .checkout-form textarea {
  border-color: #404040;
}
.dark .checkout-page .checkout-form .checkout-details {
  background-color: #232323;
  transition: all 0.3s ease;
  border: 1px solid #404040;
}
.dark .order-box .title-box {
  color: #eaedef;
  border-color: #404040;
}
.dark .order-box .qty {
  border-color: #404040;
}
.dark .order-box .qty li {
  color: #cfd4da;
}
.dark .order-box .qty li span {
  color: #dee2e6;
}
.dark .order-box .sub-total {
  border-color: #404040;
}
.dark .order-box .sub-total li {
  color: #cfd4da;
}
.dark .order-box .sub-total .shopping-option label {
  color: #929292;
}
.dark .order-box .total li {
  color: #cfd4da;
}
.dark .payment-box .payment-options li .radio-option label {
  color: #929292;
}
.dark .collection .collection-block .collection-content h4 {
  color: #e7eaec;
}
.dark .collection .collection-block .collection-content h3 {
  color: #eaedef;
}
.dark .table th,
.dark .table td {
  border-color: #404040;
}
.dark .compare-page .table-wrapper .table {
  color: #f1f3f5;
  border-color: #404040;
}
.dark .compare-page .table-wrapper .table thead .th-compare td {
  background: #232323;
  border-color: #404040;
}
.dark .compare-page .table-wrapper .table thead .th-compare th {
  border-color: #404040;
}
.dark .compare-page .table-wrapper .table thead .th-compare th .remove-compare {
  color: #cfd4da;
}
.dark .compare-page .table-wrapper .table tbody tr th {
  border-color: #404040;
  background: #232323;
}
.dark .compare-page .table-wrapper .table tbody tr td {
  border-color: #404040;
}
.dark .compare-page .table-wrapper .table tbody tr p {
  color: #cfd4da;
}
.dark .compare-section .compare-part .detail-part .title-detail {
  background-color: #232323;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .compare-section .compare-part .btn-part {
  border-color: #404040;
}
.dark .compare-section .compare-part .close-btn {
  color: #e7eaec;
}
.dark .compare-section .slick-slide > div {
  border-color: #404040;
}
.dark .compare-section .slick-slide:first-child {
  border-color: #404040;
}
.dark .contact-page .theme-form label {
  color: #cfd4da;
}
.dark .contact-page .theme-form input {
  border-color: #404040;
}
.dark .contact-page .theme-form textarea {
  border-color: #404040;
}
.dark .contact-page .contact-right ul li .contact-icon {
  border-color: #404040;
}
.dark .dashboard .box-head h2 {
  color: #f1f3f5;
}
.dark .dashboard .box .box-title {
  border-color: #404040;
}
.dark .dashboard .box .box-title h3 {
  color: #eaedef;
}
.dark .dashboard-left .block-content {
  border-color: #404040;
}
.dark .dashboard-left .block-content ul li a {
  color: #cbcbcb;
}
.dark .dashboard-right .dashboard {
  border-color: #404040;
}
.dark .white-bg {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .border-bottom-grey {
  border-color: #404040;
}
.dark .layout-8 .layout-8-bg {
  background-blend-mode: overlay;
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .card {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .card .card-header {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .faq-section .accordion.theme-accordion .card {
  border-color: #404040;
}
.dark .faq-section .accordion.theme-accordion .card .card-header {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .faq-section .accordion.theme-accordion .card .card-header button {
  color: #dee2e6;
}
.dark .faq-section .accordion.theme-accordion .card .card-header button[aria-expanded=true]:before {
  border-bottom-color: #404040;
}
.dark .faq-section .accordion.theme-accordion .card .card-header button:before {
  border-top-color: #404040;
}
.dark .faq-section .accordion.theme-accordion .card .card-body p {
  color: #cfd4da;
}
.dark.box-layout-body .box-layout-header {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark.box-layout-body .box-layout.bg-image {
  background-color: #232323;
  transition: all 0.3s ease;
  background-image: none;
  box-shadow: none;
}
.dark.box-layout-body .light-layout {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark.box-layout-body .sub-footer {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .bg-title.wo-bg .theme-tab .bg-title-part {
  border-color: #404040;
}
.dark .bg-title.wo-bg .theme-tab .bg-title-part .title-border {
  color: #ffffff;
}
.dark .bg-title.wo-bg .theme-tab .tab-title a {
  color: #cfd4da;
}
.dark .full-box .theme-card .offer-slider .product-box2 + .product-box2 {
  border-top: none;
}
.dark .center-slider {
  border-color: #404040;
}
.dark .bg-block {
  background-color: #232323;
  transition: all 0.3s ease;
  background-image: none;
}
.dark .theme-tab .tab-content .product-tab .tab-box {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .theme-tab .tab-content .product-tab .tab-box .product-box2 .media .media-body h4 {
  color: #e7eaec;
}
.dark .flower-bg {
  background-color: #232323;
  transition: all 0.3s ease;
  background-image: none;
}
.dark .pwd-page .theme-form input {
  border-color: #404040;
}
.dark .full-scroll-menu {
  background-color: transparent;
  transition: all 0.3s ease;
}
.dark .full-scroll-footer .sub-footer {
  background-color: transparent;
  transition: all 0.3s ease;
}
.dark .portfolio-section .filter-button {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  color: #cfd4da;
}
.dark .portfolio-section .filter-button.active {
  background-color: var(--theme-deafult);
  transition: all 0.3s ease;
  color: #ffffff;
}
.dark .portfolio-section .isotopeSelector:hover .overlay-background {
  border-color: #dddddd;
}
.dark .portfolio-section .isotopeSelector:hover .overlay-background i {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .portfolio-section .isotopeSelector .overlay {
  border-color: #404040;
}
.dark .category-m.w-bg .category-wrapper {
  background-color: #2b2b2b;
}
.dark .banner-timer {
  background-blend-mode: overlay;
  background-color: rgba(0, 0, 0, 0.77);
}
.dark .bg-img-gym .dark-layout {
  background-color: transparent;
  transition: all 0.3s ease;
}
.dark .bg-img-gym .sub-footer.darker-subfooter {
  background-color: #000000;
  transition: all 0.3s ease;
}
.dark .category-block .category-image {
  border-color: #404040;
}
.dark .category-block .category-image.svg-image {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .j-box .product-box {
  border-color: #404040;
}
.dark .j-box .product-box .product-detail {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .j-box .product-box .cart-info {
  background-color: rgba(35, 35, 35, 0.84);
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .j-box .product-box .cart-info i {
  color: #cfd4da;
}
.dark .j-box .product-box .cart-info a {
  border-color: #404040;
}
.dark .white-layout {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .footer-theme2.footer-border {
  border-color: #404040;
}
.dark .footer-theme2.section-light .footer-block .subscribe-white {
  border-color: #404040;
}
.dark .footer-theme2.section-light .footer-block h4 {
  color: #e7eaec;
}
.dark .footer-theme2.section-light .footer-block .contact-details li a {
  color: #e7eaec;
}
.dark .footer-theme2 .footer-link.link-white h4 {
  color: #e7eaec;
}
.dark .jewel-footer .sub-footer.black-subfooter {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .jewel-footer .sub-footer p {
  color: #cfd4da;
}
.dark .absolute-banner .absolute-bg {
  background-color: #232323;
  transition: all 0.3s ease;
  box-shadow: none;
}
.dark .login-page .theme-card {
  border-color: #404040;
  background-color: #232323;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
}
.dark .login-page .theme-card .theme-form input {
  border-color: #404040;
}
.dark .login-page .theme-card .theme-form label {
  color: #929292;
}
.dark .login-page .authentication-right h6,
.dark .login-page .authentication-right p {
  color: #cfd4da;
}
.dark .lookbook .lookbook-block .lookbook-dot:before {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .lookbook .lookbook-block .lookbook-dot .dot-showbox .dot-info {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .blog-left .blog-details h4 {
  color: #e7eaec;
}
.dark .blog-left .blog-details h6 {
  color: #cfd4da;
}
.dark footer.footer-black .below-section {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark footer.footer-black .sub-footer {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark footer.footer-black .upside .small-section {
  background-color: #383838;
  transition: all 0.3s ease;
}
.dark footer.footer-black .subscribe h4 {
  color: #e7eaec;
}
.dark footer.footer-black .subscribe p {
  color: #cfd4da;
}
.dark footer.footer-black.footer-light .subscribe {
  border-color: #929292;
}
.dark footer.pet-layout-footer .white-layout {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark footer.pet-layout-footer .sub-footer.black-subfooter {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark footer.footer-classic .sub-footer {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark footer.footer-5 {
  background-image: unset;
}
.dark footer.footer-5 .footer-theme2 .subscribe-block {
  border-color: #404040;
}
.dark footer.footer-5 .sub-footer > .container {
  border-color: #404040;
}
.dark footer.footer-theme-color .darken-layout {
  background-color: #232323;
}
.dark footer.footer-theme-color .darken-layout .footer-social i {
  background-color: #2b2b2b;
  border-color: #404040;
}
.dark footer.footer-theme-color .sub-footer.dark-subfooter {
  background-color: #2b2b2b;
}
.dark .logo-block img {
  filter: invert(100);
}
.dark .logo-block img:hover {
  filter: invert(100);
}
.dark .bg-white {
  background-color: #2b2b2b !important;
  transition: all 0.3s ease;
}
.dark .product-box .img-wrapper .cart-box,
.dark .product-wrap .img-wrapper .cart-box {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  box-shadow: none;
}
.dark .tab-bg.tab-grey-bg {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .order-success-sec {
  background-color: #232323;
}
.dark .tracking-page .border-part {
  border-color: #404040;
}
.dark .tracking-page .wrapper .arrow-steps .step {
  background-color: #232323;
  color: #cbcbcb;
}
.dark .tracking-page .wrapper .arrow-steps .step:after {
  border-left-color: #232323;
}
.dark .tracking-page .wrapper .arrow-steps .step:before {
  border-left-color: #2b2b2b;
}
.dark .tracking-page .wrapper .arrow-steps .step.current {
  color: #fff;
  background-color: var(--theme-deafult);
}
.dark .tracking-page .wrapper .arrow-steps .step.current:after {
  border-left-color: var(--theme-deafult);
}
.dark .tracking-page .table-striped tbody tr:nth-of-type(odd) {
  background-color: #232323;
  color: #cbcbcb;
}
.dark .tracking-page .table {
  color: #cbcbcb;
}
.dark .tracking-page .order-map iframe {
  border-color: #232323;
}
.dark .product-order .total-sec ul li span {
  color: #dee2e6;
}
.dark .product-order .total-sec {
  border-color: #404040;
}
.dark .blog.blog_box .blog-details p {
  color: #cfd4da;
}
.dark .blog.blog_box .blog-details a p {
  color: #cfd4da;
}
.dark .blog.blog_box .blog-details .read-cls {
  color: #cbcbcb;
}
.dark .btn-close {
  color: #ffffff;
}
.dark .sticky-bottom-cart {
  background-color: #2b2b2b;
}
.dark .sticky-bottom-cart .selection-section .form-control {
  background-color: #232323;
  border-color: #232323;
}
.dark .sticky-bottom-cart .product-image .content h6 {
  color: #cfd4da;
}
.dark .recently-purchase {
  background: #2b2b2b;
  box-shadow: 0 0 4px 0 #0c0c0c;
}
.dark .recently-purchase .close-popup {
  color: #ffffff;
}
.dark .single-product-tables table tr td:nth-child(2) {
  color: white;
}
.dark .product-right .product-title {
  color: #cfd4da;
}
.dark .product-right .border-product {
  border-color: #404040;
}
.dark .product-right .product-icon .product-social li a {
  color: #cbcbcb;
}
.dark .product-right .product-icon .wishlist-btn {
  color: #cbcbcb;
}
.dark .product-right .product-icon .wishlist-btn i {
  border-color: #404040;
}
.dark .product-right.product-form-box {
  border-color: #404040;
}
.dark .product-right.product-form-box .timer {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .product-right .timer {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .product-right .timer p {
  color: #cfd4da;
}
.dark .product-right .size-box ul li {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .product-right .size-box ul li.active {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .product-right .size-box ul li a {
  color: #cbcbcb;
}
.dark .product-right h4 del {
  color: #929292;
}
.dark .product-right .product-count {
  background-color: #232323;
}
.dark .timer span .timer-cal {
  color: #929292;
}
.dark .qty-box .input-group span button {
  background: #2b2b2b !important;
  border-color: #404040;
}
.dark .qty-box .input-group button i {
  color: #929292;
}
.dark .nav-tabs {
  border-color: #404040;
}
.dark .nav-tabs .nav-link.active {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .tab-product .nav-material.nav-tabs .nav-item .nav-link {
  color: #cbcbcb;
}
.dark .tab-product .nav-material.nav-tabs .nav-link.active {
  color: var(--theme-deafult);
}
.dark .product-full-tab .nav-material.nav-tabs .nav-link.active {
  color: var(--theme-deafult);
}
.dark .tab-product .theme-form input,
.dark .tab-product .theme-form textarea,
.dark .product-full-tab .theme-form input,
.dark .product-full-tab .theme-form textarea {
  border-color: #404040;
}
.dark .product-related h2 {
  border-color: #404040;
}
.dark .product-accordion .btn-link {
  color: #e7eaec;
}
.dark .product-accordion .card-header {
  border-color: rgba(64, 64, 64, 0.125);
}
.dark .theme_checkbox label {
  color: #929292;
}
.dark .theme_checkbox label .checkmark {
  background-color: #232323;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .theme_checkbox label .checkmark:after {
  border-color: #ffffff;
}
.dark .bundle .bundle_detail .price_product {
  color: #ffffff;
}
.dark .modal-content {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark button.close {
  color: #ffffff;
}
.dark .modal-header {
  border-color: #404040;
}
.dark .collection-filter-block .product-service .media {
  border-color: #404040;
}
.dark .pro_sticky_info {
  border-color: #404040;
}
.dark .tab-border {
  border-color: #404040;
}
.dark .register-page .theme-card {
  border-color: #404040;
  background-color: #232323;
  box-shadow: none;
}
.dark .register-page .theme-card .theme-form input {
  border-color: #404040;
}
.dark .register-page .theme-card .theme-form label {
  color: #929292;
}
.dark .category-border {
  background-color: #404040;
  transition: all 0.3s ease;
}
.dark .category-border div .category-banner .category-box h2 {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .background {
  background-color: #404040;
  transition: all 0.3s ease;
}
.dark .background .contain-bg {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .background .contain-bg h4 {
  color: #e7eaec;
}
.dark .background .contain-bg:hover h4 {
  color: var(--theme-deafult);
}
.dark .blog-bg {
  background-color: #404040;
  transition: all 0.3s ease;
}
.dark .sub-footer.black-subfooter {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark.tools-bg {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark.tools-bg section {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .category-tools .category-m .category-wrapper {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .category-m .category-wrapper {
  border-color: #404040;
}
.dark .category-m .category-wrapper h4 {
  color: #e7eaec;
}
.dark .category-m .category-wrapper .category-link li a {
  color: #cbcbcb;
}
.dark .tools-grey .product-box .product-info {
  background-color: #232323;
  transition: all 0.3s ease;
  border-color: rgba(64, 64, 64, 0.2);
}
.dark .tools-grey .product-box .cart-info button {
  background-color: #232323;
  transition: all 0.3s ease;
  color: #cbcbcb;
}
.dark .tools-grey .product-box .cart-info a {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .tools-grey .product-box .cart-info a i {
  color: #cbcbcb;
}
.dark .tools-grey .product-box .img-wrapper .front {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .tools-brand .row {
  background-color: #232323;
  transition: all 0.3s ease;
  box-shadow: none;
}
.dark .typography_section .typography-box .headings {
  background-color: rgba(35, 35, 35, 0.5);
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .typography_section .typography-box .headings h3 {
  color: #eaedef;
}
.dark .typography_section .typography-box .headings span {
  color: #929292;
}
.dark .typography_section .typography-box .headings span code {
  color: #929292;
}
.dark .typography_section .typography-box .typo-content .sub-title {
  color: #f1f3f5;
  border-color: rgba(64, 64, 64, 0.7);
}
.dark .typography_section .typo-content.heading_content h1,
.dark .typography_section .typo-content.heading_content h2,
.dark .typography_section .typo-content.heading_content h3,
.dark .typography_section .typo-content.heading_content h4,
.dark .typography_section .typo-content.heading_content h5,
.dark .typography_section .typo-content.heading_content h6 {
  color: white;
}
.dark .typography_section .typo-content.product-pagination .pagination .page-item {
  border-color: #404040;
}
.dark .typography_section code {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .absolute_banner .collection-banner .absolute-contain {
  background-color: #232323;
  transition: all 0.3s ease;
  box-shadow: none;
}
.dark .absolute_banner .collection-banner .absolute-contain h4 {
  color: #e7eaec;
}
.dark .collection-banner .contain-banner.content-bg h2 {
  background-color: #2b2b2b;
  color: #f1f3f5;
}
.dark .absolute-product .product-box {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .absolute-product .product-box .product-detail .cart-bottom {
  border-color: #404040;
}
.dark .absolute-product .product-box .product-detail .cart-bottom i {
  color: #cbcbcb;
}
.dark .absolute-product .product-box .product-detail .cart-bottom i:hover {
  color: #ffffff;
}
.dark .box-product .full-box .theme-card .offer-slider .product-box2 {
  box-shadow: none;
}
.dark .box-product .full-box .theme-card .offer-slider .product-box2 .media {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .box-product .full-box .theme-card .offer-slider .product-box2 .cart-bottom {
  border-color: #404040;
}
.dark .pets-box .product-box .img-wrapper .cart-info i {
  background-color: #2b2b2b;
}
.dark .insta-title {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .tab-bg {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .darken-layout {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .darken-layout p {
  color: #cfd4da;
}
.dark .darken-layout .sub-title .contact-list li {
  color: #cfd4da;
}
.dark .darken-layout .sub-title .contact-list i {
  color: #cfd4da;
}
.dark .darken-layout .footer-social i {
  color: #cfd4da;
}
.dark .sub-footer.dark-subfooter p {
  color: #cfd4da;
}
.dark .cart-section tbody tr td a,
.dark .cart-section tbody tr td p,
.dark .wishlist-section tbody tr td a,
.dark .wishlist-section tbody tr td p {
  color: #cbcbcb;
}
.dark .footer-title {
  border-color: #404040;
}
.dark .category-bg {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .category-bg .contain-block {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .category-bg .contain-block h2 {
  color: #f1f3f5;
}
.dark .category-bg .contain-block h6 span {
  color: #929292;
}
.dark .service_slide .service-home .service-block1 {
  background-color: rgba(35, 35, 35, 0.9);
  transition: all 0.3s ease;
}
.dark .service_slide .service-home .service-block1:nth-child(even) {
  background-color: rgba(35, 35, 35, 0.7);
  transition: all 0.3s ease;
}
.dark .service_slide .service-home .service-block1 svg path {
  fill: #ffffff;
}
.dark .bg-grey {
  background-color: #232323;
  transition: all 0.3s ease;
}
.dark .detail-cannabis .detail_section > div {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  box-shadow: none;
}
.dark .detail-cannabis .detail_section svg {
  fill: #ffffff;
}
.dark .border-box.tools-grey .product-box {
  border-color: #404040;
}
.dark .demo-card.theme-card {
  background-color: #232323;
}
.dark .theme-modal .modal-dialog .modal-content .modal-body {
  background-image: linear-gradient(135deg, var(--theme-deafult) 5.77%, #232323 5.77%, #232323 25%, #f1f3f5 25%, #f1f3f5 30.77%, #232323 30.77%, #232323 50%, var(--theme-deafult) 50%, var(--theme-deafult) 55.77%, #232323 55.77%, #232323 75%, #f1f3f5 75%, #f1f3f5 80.77%, #232323 80.77%, #232323 100%);
}
.dark .theme-modal .modal-dialog .modal-content .modal-body .modal-bg {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .theme-modal .modal-dialog .modal-content .modal-body .modal-bg .age-content h4 {
  color: #e7eaec;
}
.dark .theme-modal .modal-dialog .modal-content .modal-body .modal-bg .btn-close span {
  color: #ffffff;
}
.dark .theme-modal .modal-dialog .modal-content .modal-body .modal-bg .offer-content h2 {
  color: #f1f3f5;
}
.dark .theme-modal.demo-modal .modal-dialog .modal-content .modal-body {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .theme-modal.demo-modal .modal-dialog .modal-content .modal-body .demo-section .demo-effects > div .layout-container {
  box-shadow: none;
}
.dark .theme-modal.demo-modal .modal-dialog .modal-content .modal-body .demo-section .demo-effects > div .demo-text h4 {
  color: #e7eaec;
}
.dark .theme-modal.demo-modal .modal-dialog .modal-content .modal-body .demo-section .demo-effects > div .demo-text .demo-btn .btn {
  border-color: #404040;
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  color: #cbcbcb;
}
.dark .theme-modal.demo-modal .modal-dialog .modal-content .modal-body .demo-section .title-text h3 {
  color: #eaedef;
}
.dark .theme-modal.cart-modal .modal-dialog .modal-content .modal-body .modal-bg.addtocart #upsell_product .product-box .product-detail h6 a {
  color: #cbcbcb;
}
.dark .theme-modal.exit-modal .media .media-body h5 {
  color: #dee2e6;
}
.dark .theme-modal.exit-modal .stop {
  filter: invert(100);
}
.dark #quick-view .modal-dialog .modal-content .modal-body {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
}
.dark .addcart_btm_popup {
  background-color: #232323;
  transition: all 0.3s ease;
  border-color: #404040;
}
.dark .addcart_btm_popup .fixed_cart i {
  color: #cbcbcb;
}
.dark .review-page .comnt-sec li a {
  color: #cbcbcb;
}
.dark .sitemap_page ul li a {
  color: #cbcbcb;
}
.dark .sitemap_page ul li a:hover {
  color: var(--theme-deafult);
}
.dark .sitemap_page ul ul li a {
  color: #dee2e6;
}
.dark .sitemap_page .row > div:nth-child(odd) {
  background-color: #232323;
}
.dark .effect-cls:before, .dark .effect-cls:after {
  filter: invert(0.83);
}
.dark .game-product .product-box .cart-info a i {
  background-color: #2b2b2b;
  transition: all 0.3s ease;
  color: #cbcbcb;
}
.dark .demo-right a {
  background-color: #000000;
  transition: all 0.3s ease;
}
.dark .rtl-btn,
.dark .dark-light {
  background-color: #232323;
  box-shadow: none;
}
.dark .color-picker a.handle {
  background-color: #000000;
  transition: all 0.3s ease;
  color: #cbcbcb;
}
.dark .setting-box .setting-title h4 {
  color: #333333;
}
.dark .vendor-profile .profile-left {
  background-color: #232323;
}
.dark .vendor-profile .profile-left .profile-image h3 {
  color: #e7eaec;
}
.dark .vendor-profile .profile-left .profile-detail {
  border-color: #404040;
}
.dark .vendor-profile .profile-left .vendor-contact {
  border-color: #404040;
}
.dark .vendor-profile .profile-left .vendor-contact h6 {
  color: #cfd4da;
}
.dark .vendor-profile .collection-product-wrapper .product-top-filter {
  background-color: #232323;
}
.dark .vendor-profile .product-pagination {
  background-color: #232323;
}
.dark .vendor-profile .page-link {
  background-color: #232323;
}
.dark .vendor-profile .profile-title h3 {
  color: #eaedef;
}
.dark .vendor-profile .profile-title h4 {
  color: #e7eaec;
}
.dark .vendor-profile .collection-filter-block {
  border-color: transparent;
  background-color: #232323;
}
.dark .become-vendor {
  background-color: #232323;
}
.dark .become-vendor .step-bg .step-box {
  background-color: #2b2b2b;
}
.dark .become-vendor .step-bg .step-box .steps {
  background-color: #232323;
}
.dark .dashboard-section .apexcharts-svg text,
.dark .dashboard-section .apexcharts-svg .apexcharts-legend-text {
  fill: #eaedef;
  color: #eaedef !important;
}
.dark .dashboard-section .apexcharts-tooltip.apexcharts-theme-light {
  background: #2b2b2b;
  border-color: #404040;
  box-shadow: none;
}
.dark .dashboard-section .apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  background: #232323;
  border-color: #404040;
}
.dark .dashboard-section .dashboard-sidebar {
  background-color: #232323;
}
.dark .dashboard-section .faq-content .card {
  background-color: #232323;
}
.dark .dashboard-section .counter-section .counter-box {
  background-color: #232323;
}
.dark .dashboard-section .dashboard-table h3 {
  color: #eaedef;
}
.dark .dashboard-section .dashboard-table .table th, .dark .dashboard-section .dashboard-table .table td {
  color: #ffffff;
}
.dark .dashboard-section .dashboard-box .dashboard-detail ul li .details .left h6 {
  color: #9c9c9c;
}
.dark .faq-tab .nav-tabs .nav-item .nav-link {
  background-color: #232323;
  color: #cfd4da;
}
.dark .faq-tab .nav-tabs .nav-item .nav-link.active {
  background-color: #232323;
}
.dark .search-overlay {
  background-color: #2b2b2b;
}
.dark .category-img-wrapper .category-wrap .category-content h3 {
  color: #eaedef;
}
.dark .add_to_cart {
  background-color: #2b2b2b;
}
.dark .add_to_cart .cart-inner .cart_top {
  border-color: #404040;
}
.dark .add_to_cart .cart-inner .cart_top h3 {
  color: #eaedef;
}
.dark .add_to_cart .cart-inner .cart_top .close-cart i {
  color: #cbcbcb;
}
.dark .add_to_cart .cart-inner .cart_media li .total {
  border-color: #404040;
}
.dark .add_to_cart .cart-inner .cart_media li .media .media-body h4 {
  color: #e7eaec;
}
.dark .add_to_cart.top .cart-inner .cart_top, .dark .add_to_cart.bottom .cart-inner .cart_top {
  background-color: #232323;
}
.dark .add_to_cart .cart-inner {
  background-color: #2b2b2b;
}
.dark .theme-settings ul li {
  background-color: #232323;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.54);
}
.dark .setting-sidebar h5 {
  color: #ffffff;
}
.dark .search-section select.form-control {
  background: url(../images/dropdown.png) 95% center no-repeat #2b2b2b;
}
.dark .cycle-box {
  background-color: #2b2b2b;
}
.dark .cycle-box .bottom-detail h4 {
  color: #ffffff;
}
.dark .cycle-box .product-detail h4 {
  color: #e7eaec;
}
.dark .cycle-box .product-detail .details li {
  color: rgba(255, 255, 255, 0.63);
}
.dark .cycle-box .product-detail .add-wish {
  background-color: #232323;
}
.dark .cycle-box .cart-detail {
  border-color: #404040;
}
.dark .cycle-box .cart-detail li button, .dark .cycle-box .cart-detail li a {
  color: rgba(255, 255, 255, 0.5);
}
.dark .cycle-box .cart-detail li svg {
  color: rgba(255, 255, 255, 0.5);
}
.dark .cycle-box .cart-detail li + li {
  border-color: rgba(255, 255, 255, 0.2);
}
.dark .cycle-box .img-wrapper:after {
  background-color: rgba(43, 43, 43, 0.77);
}
.dark .center-object-banner .full-banner {
  background-blend-mode: overlay;
  background-color: #2b2b2b;
}
.dark .center-object-banner .full-banner.feature-banner h2 {
  color: #f1f3f5;
}
.dark .center-object-banner .full-banner.feature-banner .feature-object li .media h4, .dark .center-object-banner .full-banner.feature-banner .feature-object li .media p, .dark .center-object-banner .full-banner.feature-banner .feature-object-right li .media h4, .dark .center-object-banner .full-banner.feature-banner .feature-object-right li .media p {
  color: #e7eaec;
}
.dark .cookie-bar.left-bottom {
  box-shadow: 0 19px 38px rgba(0, 0, 0, 0.12), 0 15px 12px rgba(0, 0, 0, 0.13);
  background-color: #2b2b2b;
}
.dark .cookie-bar.left-bottom p {
  color: #cfd4da;
}
.dark .cookie-bar.left-bottom .btn-close i {
  color: #cbcbcb;
}
.dark .mordern-box {
  background-color: #232323;
}
.dark .mordern-box .mordern-content h6 {
  color: #cfd4da;
}
.dark .mordern-box .mordern-content .mordern-bottom .right .add-extent .animated-btn {
  background-color: #2b2b2b;
  color: #cbcbcb;
}
.dark .mordern-box .mordern-content .mordern-bottom .right .add-extent .options {
  background-color: #2b2b2b;
}
.dark .mordern-box .mordern-content .mordern-bottom .right .add-extent .options li i {
  color: rgba(203, 203, 203, 0.6);
}
.dark .service-w-bg .service-block .media {
  background-color: #232323;
}
.dark .title-basic .timer {
  background-color: #232323;
}
.dark .title-basic .timer p {
  color: #cfd4da;
}
.dark .product-parallax .theme-card .offer-slider > div .media {
  background-color: #2b2b2b;
}
.dark .load-more-button .btn.loading {
  border-color: #404040 #404040 #404040 #999;
  background: #2b2b2b;
}
.dark .footer-style-1 .footer-social i {
  background-color: #2b2b2b;
  border-color: #404040;
}
.dark .category-width .category-block .category-image img {
  filter: invert(0.6);
}
.dark .category-width .category-block:hover .category-image img {
  filter: invert(1);
}
.dark .image-swatch li img {
  border-color: #404040;
}
.dark .image-swatch li.active img {
  border-color: var(--theme-deafult);
}
.dark .added-notification {
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.63);
}
.dark .product-image-360 .nav_bar {
  background-color: #2b2b2b;
}
.dark .gradient-category .gradient-border .img-sec {
  background-color: #232323;
}
.dark .gradient-category .gradient-border .img-sec:after {
  filter: invert(0.9);
}
.dark .deal-category .deal-content {
  background-color: #2b2b2b;
}

@media (max-width: 1430px) {
  .dark header.left-header .sidenav .sidebar-back {
    color: #cfd4da;
    border-color: #404040;
  }
  .dark .collection-product-wrapper .product-top-filter .product-filter-content .collection-view {
    border-color: #404040;
  }
  .dark .collection-product-wrapper .product-top-filter .product-filter-content .product-page-filter {
    border-color: #404040;
  }
}
@media (max-width: 1199px) {
  .dark .sm-horizontal {
    background-color: #2b2b2b;
    border-color: #404040;
  }
  .dark .sm-horizontal .mobile-back {
    border-color: #404040;
    color: #e7eaec;
  }
  .dark .sm-horizontal.pixelstrap ul {
    background-color: #2b2b2b;
  }
  .dark .tab-product .nav-material.nav-tabs {
    background-color: #2b2b2b;
    border-color: #404040;
  }
  .dark header.left-header .pixelstrap ul {
    background-color: #232323;
  }
  .dark header.left-header.left-header-relative .pixelstrap > li > a {
    background-color: #232323;
  }
  .dark header.header-christmas .pixelstrap > li > a, .dark header.header-christmas .pixelstrap > li > a:hover, .dark header.header-christmas .pixelstrap > li > a:focus {
    color: #cbcbcb !important;
  }
  .dark .header-style-1 .bg-light-xl {
    background-color: #232323 !important;
  }
  .dark .header-style-5 .bottom-part .pixelstrap > li > a {
    color: #cbcbcb;
  }
  .dark .header-style-5 .bottom-part .pixelstrap > li > a:hover, .dark .header-style-5 .bottom-part .pixelstrap > li > a:focus, .dark .header-style-5 .bottom-part .pixelstrap > li > a:active {
    color: #cbcbcb;
  }
  .dark .header-style-5 .bottom-part.bottom-light .marketplace-sidebar.sidenav.fixed-sidebar {
    background-color: #232323;
  }
  .dark .header-style-5 .bottom-part.bottom-light .marketplace-sidebar.sidenav.fixed-sidebar .sm-vertical {
    background-color: #232323;
  }
  .dark .header-style-5.color-style .marketplace-sidebar.sidenav.fixed-sidebar {
    background-color: #232323;
  }
  .dark .header-style-5.color-style .marketplace-sidebar.sidenav.fixed-sidebar .sm-vertical {
    background-color: #232323;
  }
  .dark .header-style-5.color-style.style-classic .bottom-part .container {
    background-color: transparent;
  }
  .dark .marketplace-sidebar.sidenav.wo-bg {
    background-color: #232323;
  }
}
@media (max-width: 991px) {
  .dark .collection-filter {
    background-color: #2b2b2b;
  }
  .dark .collection-product-wrapper .product-top-filter .product-filter-content .product-page-per-view select,
.dark .collection-product-wrapper .product-top-filter .product-filter-content .product-page-filter select {
    border-color: #404040;
  }
  .dark .collection-product-wrapper .product-top-filter .popup-filter .product-page-filter, .dark .collection-product-wrapper .product-top-filter .popup-filter .product-page-per-view {
    border-color: #404040;
  }
  .dark .collection-mobile-back {
    border-color: #404040;
  }
  .dark .collection-mobile-back span {
    color: #929292;
  }
  .dark .collection-collapse-block {
    border-color: #404040 !important;
  }
  .dark .collection-filter {
    box-shadow: none;
  }
  .dark .contact-page .contact-right ul li {
    border-color: #404040;
  }
  .dark .dashboard-left {
    background-color: #232323;
    box-shadow: none;
  }
  .dark header.header-tools .logo-menu-part {
    background-color: #2b2b2b;
  }
  .dark header.header-style.header-tools .top-header {
    background-color: #2b2b2b;
  }
  .dark .service_slide .service-home .service-block1 {
    background-color: #2b2b2b;
  }
  .dark .service_slide .service-home .service-block1:nth-child(even) {
    background-color: #2b2b2b;
  }
  .dark .dashboard-section .dashboard-sidebar .faq-tab .nav-tabs {
    background-color: #232323;
  }
}
@media (max-width: 767px) {
  .dark .collection-product-wrapper .product-pagination .theme-paggination-block nav {
    border-color: #404040;
  }
  .dark .product-pagination .pagination {
    border-color: #404040;
  }
  .dark .product-wrapper-grid.list-view .product-box {
    border-color: #404040;
  }
  .dark .banner-timer .banner-text {
    background-color: rgba(0, 0, 0, 0.65);
  }
  .dark .border-box.tools-grey .product-box .cart-info a, .dark .border-box.tools-grey .product-box .cart-info button {
    background-color: #2b2b2b;
  }
  .dark .border-box.tools-grey .product-box .cart-info a i {
    opacity: 0.4;
  }
  .dark footer.footer-5 .dark-layout .footer-title {
    border-color: #404040;
  }
}
@media (max-width: 577px) {
  .dark header.header-style .top-header .header-dropdown > li img {
    filter: invert(0.55);
  }
  .dark .header-5 .top-header .header-dropdown .mobile-wishlist img, .dark .header-5 .top-header .header-dropdown .mobile-account img {
    filter: invert(1);
    opacity: 0.7;
  }
  .dark .collection-product-wrapper .product-top-filter .product-filter-content .product-page-per-view {
    border-color: #404040 !important;
  }
  .dark .tools-service.absolute-banner .absolute-bg {
    box-shadow: none;
  }
  .dark .full-banner .banner-contain {
    background-color: rgba(0, 0, 0, 0.2);
  }
  .dark .mobile-fix-option {
    background-color: #2b2b2b;
  }
}
@media (max-width: 420px) {
  .dark .full-box .theme-card .offer-slider .sec-1 .product-box2 {
    border-color: #404040;
  }
  .dark .full-box .center-slider .offer-slider .product-box {
    border-color: #404040;
  }
}
/*=====================
    29.Responsive CSS start
==========================*/
@media (min-width: 1630px) {
  body.christmas .container {
    max-width: 1600px;
  }
}
@media (max-width: 1630px) {
  body.christmas .home-slider .slider-details {
    width: 400px;
    height: 400px;
  }
}

@media (min-width: 1430px) {
  .container {
    max-width: 1400px;
  }
}
@media (max-width: 1430px) {
  .product-right .product-icon .product-social li {
    padding-right: 10px;
  }
}
@media (max-width: 1367px) {
  h2 {
    font-size: 32px;
  }

  tle1.title5 hr[role=tournament6] {
    margin: 10px auto 30px auto;
  }

  hr.style1 {
    height: 1px;
    margin-top: 7px;
    margin-bottom: 7px;
  }

  .product-box .product-detail .rating i,
.product-box .product-info .rating i,
.product-wrap .product-detail .rating i,
.product-wrap .product-info .rating i {
    padding-right: 0;
  }
}
@media (max-width: 1199px) {
  section,
.section-t-space {
    padding-top: 60px;
  }

  .section-b-space {
    padding-bottom: 60px;
  }

  .product-right h2 {
    font-size: 20px;
  }

  .product-description-box .border-product {
    padding-top: 10px;
    padding-bottom: 15px;
  }
  .product-description-box .product-icon .product-social li {
    padding-right: 4px;
  }
  .product-description-box .product-icon .wishlist-btn span {
    padding-left: 4px;
  }

  .single-product-tables.detail-section table {
    width: 75%;
  }
}
@media (max-width: 991px) {
  h2 {
    font-size: 28px;
  }

  section,
.section-t-space {
    padding-top: 50px;
  }

  .section-b-space {
    padding-bottom: 50px;
  }

  h4 {
    font-size: 16px;
  }

  .product-box .cart-info {
    bottom: 20px;
  }

  .product-right {
    text-align: center;
    margin: 20px 0 10px 0;
  }
  .product-right h2 {
    margin-top: 15px;
  }
  .product-right .rating-section {
    justify-content: center;
  }
  .product-right .detail-section,
.product-right .product-icon {
    justify-content: center;
  }
  .product-right .product-description .qty-box {
    justify-content: center;
  }
  .product-right .size-text {
    text-align: left;
  }
  .product-right .timer {
    text-align: left;
  }
  .product-right .product-icon .product-social li {
    padding-right: 20px;
  }

  .product-form-box {
    margin-bottom: 10px;
  }
}
@media (max-width: 767px) {
  .container-fluid.custom-container {
    padding-left: 30px;
    padding-right: 30px;
  }

  section,
.section-t-space {
    padding-top: 40px;
  }

  .section-b-space {
    padding-bottom: 40px;
  }

  .product-box .cart-detail,
.product-wrap .cart-detail {
    top: 5px;
    right: 10px;
  }
  .product-box:hover .product-info .add-btn,
.product-wrap:hover .product-info .add-btn {
    -webkit-animation: none;
            animation: none;
  }
  .product-box .img-wrapper .cart-box,
.product-wrap .img-wrapper .cart-box {
    padding: 8px 10px;
    bottom: 20px;
  }
  .product-box .img-wrapper .cart-box.style-1,
.product-wrap .img-wrapper .cart-box.style-1 {
    padding: 7px 4px;
    bottom: 7px;
  }
  .product-box .img-wrapper .cart-box.style-1 i,
.product-wrap .img-wrapper .cart-box.style-1 i {
    padding-left: 4px;
    padding-right: 4px;
  }
  .product-box .img-wrapper .cart-box i,
.product-wrap .img-wrapper .cart-box i {
    font-size: 15px;
    padding-left: 4px;
    padding-right: 4px;
  }
  .product-box .product-info .add-btn,
.product-wrap .product-info .add-btn {
    opacity: 1;
  }

  .product-box.single-product .product-detail .btn-solid {
    padding: 8px 36px;
  }
  .product-box.single-product .color-variant {
    padding-top: 10px;
  }
  .product-box.single-product .color-variant .nav-item {
    width: 20px !important;
    height: 20px !important;
    margin: 0 6px;
  }
  .product-box .cart-info.bg-color-cls {
    right: 0;
  }
  .product-box .cart-info,
.product-box .cart-detail {
    opacity: 1;
  }
  .product-box .img-wrapper .cart-box {
    opacity: 1;
  }
  .product-box:hover .img-wrapper .cart-box {
    -webkit-animation: none;
            animation: none;
  }
  .product-box:hover .cart-info button,
.product-box:hover .cart-detail button {
    -webkit-animation: none;
            animation: none;
  }
  .product-box:hover .cart-info a i,
.product-box:hover .cart-detail a i {
    -webkit-animation: none;
            animation: none;
  }
  .product-box:hover .cart-info a:nth-child(2) i,
.product-box:hover .cart-detail a:nth-child(2) i {
    -webkit-animation: none;
            animation: none;
  }
  .product-box:hover .cart-info a:nth-child(3) i,
.product-box:hover .cart-detail a:nth-child(3) i {
    -webkit-animation: none;
            animation: none;
  }
  .product-box:hover .cart-info a:nth-child(4) i,
.product-box:hover .cart-detail a:nth-child(4) i {
    -webkit-animation: none;
            animation: none;
  }
}
@media (max-width: 577px) {
  .section-b-space {
    padding-bottom: 30px;
  }

  section,
.section-t-space {
    padding-top: 30px;
  }

  h2 {
    font-size: 24px;
  }

  .btn-solid,
.btn-outline {
    padding: 7px 15px;
  }

  .product-box .product-detail .color-variant,
.product-box .product-info .color-variant,
.product-wrap .product-detail .color-variant,
.product-wrap .product-info .color-variant {
    padding-top: 10px;
  }

  .product-box .img-wrapper .lable-block .lable3 {
    font-size: 12px;
    padding: 13px 8px;
  }
  .product-box .img-block .lable-wrapper .lable1,
.product-box .img-block .lable-wrapper .lable2 {
    padding: 6px 9px 8px 15px;
    font-size: 12px;
  }
}
@media (max-width: 420px) {
  h2 {
    font-size: 22px;
  }

  footer p {
    line-height: 25px;
  }

  .product-box .cart-detail i,
.product-wrap .cart-detail i {
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 14px;
  }
  .product-box .cart-info i,
.product-box .cart-wrap i,
.product-wrap .cart-info i,
.product-wrap .cart-wrap i {
    padding: 5px;
    font-size: 13px;
  }
  .product-box .img-wrapper .lable-block .lable4,
.product-wrap .img-wrapper .lable-block .lable4 {
    font-size: 10px;
  }
  .product-box .img-wrapper .cart-box i,
.product-wrap .img-wrapper .cart-box i {
    font-size: 14px;
    padding-left: 4px;
    padding-right: 4px;
  }
  .product-box .product-detail,
.product-box .product-info,
.product-wrap .product-detail,
.product-wrap .product-info {
    margin-top: 5px;
  }
  .product-box .product-detail h6,
.product-box .product-info h6,
.product-wrap .product-detail h6,
.product-wrap .product-info h6 {
    font-size: 14px;
  }
  .product-box .product-detail h4,
.product-box .product-info h4,
.product-wrap .product-detail h4,
.product-wrap .product-info h4 {
    font-size: 15px;
  }
  .product-box .product-detail .color-variant,
.product-box .product-info .color-variant,
.product-wrap .product-detail .color-variant,
.product-wrap .product-info .color-variant {
    padding-top: 3px;
  }
  .product-box .product-detail .color-variant li,
.product-box .product-info .color-variant li,
.product-wrap .product-detail .color-variant li,
.product-wrap .product-info .color-variant li {
    height: 14px;
    width: 14px;
    margin-right: 3px;
  }
  .product-box .cart-info.cart-wrap i,
.product-box .cart-wrap.cart-wrap i,
.product-wrap .cart-info.cart-wrap i,
.product-wrap .cart-wrap.cart-wrap i {
    padding: 5px;
    font-size: 13px;
  }

  .timer span .padding-l {
    padding-left: 5px;
  }

  .product-box .img-wrapper .lable-block .lable3 {
    font-size: 10px;
    padding: 10px 5px;
  }
  .product-box .img-wrapper .cart-box {
    padding: 4px 8px;
    bottom: 10px;
  }
  .product-box .img-block .lable-wrapper {
    top: 20px;
  }
  .product-box .img-block .lable-wrapper .lable1,
.product-box .img-block .lable-wrapper .lable2 {
    padding: 5px 5px 5px 8px;
    font-size: 12px;
  }
  .product-box .cart-info {
    bottom: 10px;
  }

  .product-right .product-buttons .btn-solid,
.product-right .product-buttons .btn-outline {
    padding: 7px 8px;
  }
  .product-right.product-form-box .timer {
    padding-left: 29px;
  }
  .product-right.product-form-box .timer span {
    width: 45px;
  }
  .product-right .timer {
    padding-left: 35px;
  }
  .product-right .timer span {
    width: 45px;
  }
  .product-right .product-icon .product-social {
    margin-top: 0;
  }
  .product-right .product-icon .product-social li {
    padding-right: 5px;
  }
  .product-right .product-icon .product-social li a i {
    font-size: 14px;
  }
  .product-right .product-icon .wishlist-btn i {
    font-size: 14px;
    padding-left: 10px;
    margin-left: 5px;
  }
  .product-right .product-icon .wishlist-btn span {
    font-size: 14px;
  }
}
@media (max-width: 360px) {
  .btn-solid,
.btn-outline {
    padding: 10px 15px;
  }

  h2 {
    font-size: 25px;
  }

  footer .btn-solid {
    padding: 7px 8px;
  }

  .product-buttons .btn-solid,
.product-buttons .btn-outline {
    padding: 7px 16px;
  }

  .product-right .timer {
    padding-left: 25px;
  }
  .product-right .timer span .padding-l {
    padding-left: 10px;
  }
  .product-right .product-buttons a:last-child {
    margin-left: 6px;
  }
}
/*=====================
    RTL CSS start
==========================*/
body.rtl {
  direction: rtl;
}

.rtl .slick-slider {
  direction: ltr;
}
.rtl .slick-slide {
  float: left;
}
.rtl ul {
  -webkit-padding-start: 0;
}
.rtl .pre-2,
.rtl .px-2 {
  padding-right: 0 !important;
  padding-left: 0.5rem !important;
}
.rtl .me-1,
.rtl .mx-1 {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}
.rtl .me-2,
.rtl .mx-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}
.rtl .ms-2,
.rtl .mx-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}
.rtl .me-3,
.rtl .mx-3 {
  margin-right: 0 !important;
  margin-left: 1rem !important;
}
.rtl .ms-3,
.rtl .mx-3 {
  margin-left: 0 !important;
  margin-right: 1rem !important;
}
.rtl .ms-auto,
.rtl .mx-auto {
  margin-left: unset !important;
  margin-right: auto !important;
}
.rtl .pe-0,
.rtl .px-0 {
  padding-right: unset !important;
  padding-left: 0 !important;
}
.rtl .pe-2 {
  padding-left: 0.5rem !important;
  padding-right: 0 !important;
}
.rtl .text-end {
  text-align: left !important;
}
.rtl .text-start {
  text-align: right !important;
}
.rtl .theme-form .form-group {
  text-align: right;
}

.rtl .form-check {
  padding-left: 0;
  padding-right: 1.5em;
}
.rtl .form-check .form-check-input {
  float: right;
  margin-right: -1.5em;
  margin-left: 0;
}
.rtl .pixelstrap ul a {
  text-align: right;
}
.rtl .pixelstrap a .sub-arrow {
  right: auto;
  left: 10px;
}
.rtl .pixelstrap .clothing-menu .link-section {
  text-align: right;
}
.rtl .sm, .rtl .sm ul, .rtl .sm li {
  direction: rtl;
}
.rtl .onhover-dropdown .onhover-show-div {
  right: unset;
  left: 0;
}
.rtl .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .media .media-body {
  text-align: right;
}
.rtl .main-menu .menu-left.category-nav-right .navbar {
  padding: 40px 45px 40px 0;
}
.rtl .header-options {
  text-align: left;
}
.rtl .snow-slider .slick-slide img {
  width: 100%;
}
.rtl .effect-cls:before, .rtl .effect-cls:after {
  right: 0;
}
.rtl .left-header .sidenav .pixelstrap > li > a {
  text-align: right;
}
.rtl .left-header .sidenav .pixelstrap li ul li a {
  text-align: right;
}
.rtl .top-header .header-contact li {
  padding-right: 0;
  padding-left: 25px;
}
.rtl .onhover-dropdown:before {
  left: 3px;
  right: unset;
}
.rtl .top-header .header-dropdown > li:nth-child(2) {
  padding: 15px 25px;
  padding-left: 20px;
}
.rtl .top-header .header-dropdown li i {
  padding-right: 0;
  padding-left: 5px;
}
.rtl .top-header .header-dropdown li:first-child {
  padding-right: 20px;
  padding-left: 0;
}
.rtl .top-header .header-contact {
  text-align: right;
}
.rtl .top-header .header-contact li i {
  padding-right: 0;
  padding-left: 10px;
}
.rtl .main-menu .menu-left .navbar {
  padding: 40px 0 40px 45px;
}
.rtl .main-menu .menu-right {
  float: left;
}
.rtl .main-menu .menu-right .icon-nav li {
  padding-left: 0;
  padding-right: 20px;
}
.rtl .main-menu .menu-right .icon-nav .onhover-div .show-div.setting {
  right: unset;
  left: 0;
}
.rtl .main-menu .menu-right .icon-nav .onhover-div .show-div.setting h6 {
  text-align: right;
}
.rtl .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart {
  right: unset;
  left: 0;
}
.rtl .pixelstrap .full-mega-menu ul a:before {
  right: 0;
}
.rtl .cart_qty_cls {
  right: unset;
  left: -8px;
}
.rtl .header-style-1 .cart_qty_cls {
  left: -14px;
  right: unset;
}
.rtl header .main-navbar .nav-menu > li {
  float: right;
}
.rtl header .main-navbar .nav-menu > li > a {
  padding-right: 0;
  padding-left: 45px;
}
.rtl header .main-navbar .nav-menu > li > a .sub-arrow {
  right: auto;
  left: 7px;
}
.rtl header .main-navbar .nav-menu > li .mega-menu-container .mega-box {
  text-align: right;
}
.rtl header .main-navbar .nav-menu > li .mega-menu-container .mega-box .link-section .menu-content ul li a:before {
  right: 0;
}
.rtl header .main-navbar .nav-menu > li .nav-submenu {
  text-align: right;
}
.rtl header .main-navbar .nav-menu > li .nav-submenu li a i {
  float: left;
}
.rtl header .main-navbar .nav-menu > li .nav-submenu li .nav-sub-childmenu {
  left: -203px;
  right: unset;
}
.rtl header .sidenav .sidebar-menu li {
  direction: rtl;
  text-align: right;
}
.rtl header.left-header .sidenav {
  right: 0;
}
.rtl header.left-header .sidenav .pixelstrap li .mega-menu-container {
  right: 0px;
  margin-right: 270px;
  left: unset;
  margin-left: unset;
}
.rtl header.left-header .sidenav .pixelstrap li .nav-submenu {
  right: 0px;
  margin-right: 270px;
  margin-left: 0;
  left: unset;
}
.rtl header.left-header .sidenav .pixelstrap li .nav-submenu li .nav-sub-childmenu {
  left: unset;
  margin-left: 0;
  right: 0px;
  margin-right: 228px;
}
.rtl header.left-header .sidenav .pixelstrap li .nav-submenu li a i {
  float: left;
}
.rtl header.left-header .sidenav .left-sidebar_center {
  padding: 40px 30px 30px 0;
}
.rtl header.left-header .sidenav .left-sidebar_center .pixelstrap li a .sub-arrow {
  left: 7px;
  right: unset;
}
.rtl header.left-header.left-header-sm .sidenav .left-sidebar_center .pixelstrap > li > a img {
  margin-left: auto !important;
}
.rtl header .layout3-menu .main-navbar .nav-menu > li > a {
  padding-left: 34px;
}
.rtl header .layout3-menu .main-navbar .nav-menu > li > a .sub-arrow {
  left: -5px;
}
.rtl header.header-christmas .main-menu .menu-left .navbar {
  padding: 20px 0 20px 45px;
}
.rtl header.header-tools .main-menu .menu-left .navbar {
  padding: 25px 0 25px 45px;
}
.rtl .header-style-5 .bottom-part .category-menu .toggle-sidebar i {
  margin-right: 0;
  margin-left: 10px;
}
.rtl .addcart_btm_popup {
  left: 0;
  right: unset;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.rtl .addcart_btm_popup .fixed_cart {
  padding-right: 8px;
  padding-left: 0;
}
.rtl .dark-light {
  right: unset;
  left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.rtl .blog-left .blog-details {
  text-align: right;
}
.rtl .setting-sidebar {
  right: unset;
  left: 0;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.rtl .setting-sidebar i {
  margin-left: 10px;
  margin-right: 10px;
}
.rtl .setting-sidebar.open-icon {
  right: unset;
  left: 320px;
}
.rtl .sidenav {
  right: -300px;
  left: unset;
}
.rtl .sidenav.open-side {
  right: 0;
}
.rtl .sidenav.marketplace-sidebar {
  right: unset;
}
.rtl .bg-title .theme-tab .bg-title-part .tab-title {
  margin-right: auto;
  margin-left: unset;
}
.rtl .product-left-title .slick-custom-arrow li.left-arrow {
  margin-left: 16px;
  margin-right: 0;
}
.rtl .setting-box {
  right: unset;
  left: -660px;
}
.rtl .setting-box.open-setting {
  right: unset;
  left: -4px;
}
.rtl .full-slider {
  direction: ltr;
}
.rtl .full-banner .santa-img {
  left: unset;
  right: 28px;
}
.rtl .full-banner.feature-banner .feature-object,
.rtl .full-banner.feature-banner .feature-object-right {
  text-align: left;
}
.rtl .full-banner.feature-banner .feature-object li,
.rtl .full-banner.feature-banner .feature-object-right li {
  margin-right: 0;
  margin-left: 50px;
}
.rtl .full-banner.feature-banner .feature-object li .media img,
.rtl .full-banner.feature-banner .feature-object-right li .media img {
  margin-left: 0;
  margin-right: 15px;
}
.rtl .full-banner.feature-banner .feature-object li .media .media-body,
.rtl .full-banner.feature-banner .feature-object-right li .media .media-body {
  text-align: left;
}
.rtl .full-banner.feature-banner .feature-object li:nth-child(2),
.rtl .full-banner.feature-banner .feature-object-right li:nth-child(2) {
  padding-right: 0;
  padding-left: 30px;
}
.rtl .full-banner.feature-banner .feature-object li:nth-child(3),
.rtl .full-banner.feature-banner .feature-object-right li:nth-child(3) {
  padding-right: 0;
  padding-left: 60px;
}
.rtl .full-banner.feature-banner .feature-object-right {
  text-align: right;
  margin-left: 0;
  margin-right: 50px;
}
.rtl .full-banner.feature-banner .feature-object-right li .media img {
  margin-left: 15px;
  margin-right: 0;
}
.rtl .full-banner.feature-banner .feature-object-right li .media .media-body {
  text-align: right;
}
.rtl .full-banner.feature-banner .feature-object-right li:nth-child(2) {
  padding-right: 30px;
  padding-left: 0;
}
.rtl .full-banner.feature-banner .feature-object-right li:nth-child(3) {
  padding-right: 60px;
  padding-left: 0;
}
.rtl .blog-section .review-box .santa-img {
  transform: scaleX(-1);
  left: -116px;
  right: unset;
}
.rtl .home-slider {
  direction: ltr;
}
.rtl .beauty-about {
  text-align: right;
}
.rtl .beauty-about .about-text p {
  text-align: right;
}
.rtl .form_search button {
  left: 5px;
  right: unset;
}
.rtl .cart-qty-cls {
  right: unset;
  left: -8px;
}
.rtl .search-overlay > div .overlay-content button {
  left: 10px;
  right: unset;
}
.rtl .search-overlay > div .closebtn {
  right: unset;
  left: 25px;
}
.rtl .collection-banner.p-right .contain-banner {
  justify-content: flex-start;
}
.rtl .collection-banner.p-left .contain-banner {
  justify-content: flex-end;
}
.rtl .theme-modal .modal-dialog .modal-content .modal-body .modal-bg .btn-close {
  right: unset;
  left: 17px;
}
.rtl .cart_counter .cart_checkout {
  margin-left: 0;
  margin-right: 10px;
}
.rtl .product-box .product-detail,
.rtl .product-box .product-info,
.rtl .product-wrap .product-detail,
.rtl .product-wrap .product-info {
  text-align: right;
  padding-left: 0;
  padding-right: 5px;
}
.rtl .product-box .img-wrapper .product-thumb-list,
.rtl .product-wrap .img-wrapper .product-thumb-list {
  right: 0;
  left: unset;
}
.rtl .product-box .cart-info.cart-wrap,
.rtl .product-box .cart-wrap.cart-wrap,
.rtl .product-wrap .cart-info.cart-wrap,
.rtl .product-wrap .cart-wrap.cart-wrap {
  text-align: left;
  left: 0;
  right: unset;
}
.rtl .product-box .product-info,
.rtl .product-wrap .product-info {
  text-align: center;
}
.rtl .product-box .cart-detail,
.rtl .product-wrap .cart-detail {
  right: unset;
  left: 20px;
}
.rtl .product-box .back,
.rtl .product-wrap .back {
  right: 0;
  left: unset;
  transform: translateX(100px);
}
.rtl .j-box .product-box .img-wrapper .cart-info {
  width: 100%;
}
.rtl .j-box.style-box .product-box .cart-info a {
  border: none;
}
.rtl .portfolio-section.metro-section .product-box .cart-wrap {
  left: 7px;
}
.rtl .tools-grey .product-box .img-wrapper .cart-info {
  width: 100%;
}
.rtl .tools-parallax-product .tools-description {
  text-align: right;
}
.rtl .service-block + .service-block {
  border-right: 1px solid #dddddd;
  border-left: none;
}
.rtl .service-block svg {
  margin-right: 0;
  margin-left: 12px;
}
.rtl .service-block .media .media-body {
  text-align: right;
}
.rtl .footer-light .subscribe {
  border-right: none;
  border-left: 1px solid #dddddd;
  text-align: right;
}
.rtl .footer-theme {
  text-align: right;
}
.rtl .footer-theme .sub-title .contact-list li {
  padding-left: 0;
  padding-right: 25px;
}
.rtl .footer-theme .sub-title .contact-list i {
  left: unset;
  right: 0;
}
.rtl .footer-social li,
.rtl .social-white li {
  padding-left: 45px;
  padding-right: 0;
}
.rtl .social-white li {
  padding-left: 22px;
  padding-right: 22px;
}
.rtl .sub-footer {
  text-align: right;
}
.rtl .product-right .product-buttons a:last-child,
.rtl .product-right .product-buttons button:last-child {
  margin-left: 0;
  margin-right: 10px;
}
.rtl .theme-modal.modal-dialog .modal-content .modal-body .modal-bg .close {
  left: 36px;
  right: unset;
}
.rtl .theme-card .slick-next {
  right: unset;
  left: 25px;
}
.rtl .theme-card .slick-prev {
  left: 0;
  right: unset;
}
.rtl .theme-card h5.title-border {
  text-align: right;
}
.rtl .theme-card .offer-slider .media {
  direction: rtl;
}
.rtl .theme-card .offer-slider .media img {
  padding: 15px 0 15px 15px;
}
.rtl .theme-card .offer-slider .media .media-body {
  text-align: right;
}
.rtl .theme-card .offer-slider .media .media-body a h6 {
  margin-right: 0;
  margin-left: 0;
}
.rtl .theme-card.card-border h5 {
  padding-right: 50px;
  padding-left: 0;
}
.rtl .theme-card.card-border .slick-prev {
  left: 30px;
}
.rtl .theme-card.card-border .slick-next {
  left: 50px;
}
.rtl .theme-tab .tab-content .product-tab .tab-box {
  text-align: right;
}
.rtl .theme-tab .tab-content .product-tab .tab-box .product-box2 .media .media-body a h6 {
  margin-left: 35px;
  margin-right: 0;
}
.rtl .full-box .theme-card .offer-slider .product-box2 .media .media-body a h6 {
  margin-right: 0;
  margin-left: 20px;
}
.rtl .layout7-product .product-box .details-product {
  right: 15px;
  text-align: right;
  left: unset;
}
.rtl .game-product .product-box .cart-info {
  left: 10px;
}
.rtl .game-product .product-box .product-detail {
  text-align: center;
}
.rtl .gym-product .product-box .img-wrapper .cart-info {
  width: 100%;
}
.rtl .gym-product .product-box .product-detail {
  text-align: center;
}
.rtl .gym-blog .blog-details {
  text-align: right;
}
.rtl .pets-box .product-box .product-detail {
  text-align: center;
}
.rtl .blog.blog_box .blog-details {
  text-align: right;
}
.rtl .tab-left .theme-tab .left-side {
  text-align: right;
}
.rtl .tab-left .theme-tab .left-side .tab-title {
  margin-right: 0;
  margin-left: 100px;
  text-align: right;
}
.rtl .left-sidebar_space {
  padding-left: 0;
  padding-right: 300px;
}
.rtl .left-header .top-header {
  padding-left: 0;
  padding-right: 300px;
}
.rtl .absolute-product .product-box .product-detail {
  text-align: center;
}
.rtl .box-product .full-box .theme-card .offer-slider .product-box2 .media {
  padding-right: 0;
  padding-left: 15px;
}
.rtl .box-product .full-box .theme-card .offer-slider .product-box2 .media img {
  padding: 0 0 0 15px;
}
.rtl .layout3-menu .main-menu .menu-left .main-menu-right .toggle-nav {
  left: unset;
  right: 35px;
}
.rtl .breadcrumb-item + .breadcrumb-item {
  padding-left: 0;
  padding-right: 0.5rem;
}
.rtl .breadcrumb-item + .breadcrumb-item:before {
  padding-left: 0.5rem;
  padding-right: 0;
}
.rtl .custom-control {
  padding-left: 0;
  padding-right: 1.5rem;
  text-align: right;
}
.rtl .custom-control-input {
  right: 0;
  left: unset;
}
.rtl .custom-control-label:after {
  left: unset;
  right: -1.5rem;
}
.rtl .custom-control-label:before {
  right: -1.5rem;
  left: unset;
}
.rtl .collection-collapse-block .collapse-block-title {
  text-align: right;
}
.rtl .collection-collapse-block .collapse-block-title:after {
  right: unset;
  left: -3px;
}
.rtl .collection-collapse-block .collection-collapse-block-content .color-selector ul {
  text-align: right;
}
.rtl .collection-collapse-block .collection-collapse-block-content .collection-brand-filter .collection-filter-checkbox label {
  padding-left: 0;
  padding-right: 10px;
}
.rtl .top-banner-wrapper .top-banner-content {
  text-align: right;
}
.rtl .product-filter-tags {
  text-align: right;
}
.rtl .product-filter-tags li a i {
  margin-left: 0;
  margin-right: 5px;
}
.rtl .collection-product-wrapper .product-top-filter .product-filter-content .search-count,
.rtl .collection-product-wrapper .product-top-filter .product-filter-content .sidebar-popup,
.rtl .collection-product-wrapper .product-top-filter .popup-filter .search-count,
.rtl .collection-product-wrapper .product-top-filter .popup-filter .sidebar-popup {
  border-right: unset;
  border-left: 1px solid #dddddd;
  text-align: right;
}
.rtl .collection-product-wrapper .product-top-filter .product-filter-content .collection-view ul li:first-child,
.rtl .collection-product-wrapper .product-top-filter .popup-filter .collection-view ul li:first-child {
  margin-right: 0;
  margin-left: 14px;
}
.rtl .collection-product-wrapper .product-top-filter .product-filter-content .product-page-per-view select,
.rtl .collection-product-wrapper .product-top-filter .product-filter-content .product-page-filter select,
.rtl .collection-product-wrapper .product-top-filter .popup-filter .product-page-per-view select,
.rtl .collection-product-wrapper .product-top-filter .popup-filter .product-page-filter select {
  background-position: 5%;
  border-right: none;
  border-left: 1px solid #dddddd;
}
.rtl .collection-product-wrapper .product-top-filter .product-filter-content .product-page-filter select,
.rtl .collection-product-wrapper .product-top-filter .popup-filter .product-page-filter select {
  border-left: none;
}
.rtl .collection-product-wrapper .product-top-filter .popup-filter .sidebar-popup {
  background: url(../images/dropdown.png) no-repeat 5%;
}
.rtl .collection-product-wrapper .product-top-filter .popup-filter .open-popup {
  left: unset;
  right: 0;
}
.rtl .product-pagination .product-search-count-bottom {
  padding-left: 15px;
  padding-right: 0;
}
.rtl .collection-filter-block .product-service .media .media-body {
  padding-right: 10px;
  padding-left: 0;
  text-align: right;
}
.rtl .product-right {
  text-align: right;
}
.rtl .product-right h4 span {
  padding-left: 0;
  padding-right: 5px;
}
.rtl .product-right .product-description h6 span {
  float: left;
}
.rtl .product-right .size-box ul li {
  margin-right: 0;
  margin-left: 10px;
}
.rtl .product-right .product-buttons a:last-child {
  margin-left: 0;
  margin-right: 10px;
}
.rtl .product-right .product-icon .product-social li {
  padding-right: 0;
  padding-left: 30px;
}
.rtl .product-right .product-icon .product-social li:last-child {
  padding-left: 0;
}
.rtl .product-right .product-icon .wishlist-btn span {
  padding-left: 0;
  padding-right: 10px;
}
.rtl .product-right .product-icon .wishlist-btn i {
  padding-left: 0;
  margin-left: 0;
  padding-right: 10px;
  margin-right: 5px;
  border-left: none;
  border-right: 1px solid #dddddd;
}
.rtl .product-right.product-form-box {
  text-align: center;
}
.rtl .product-right.product-form-box .timer p {
  text-align: right;
}
.rtl .product-right.product-form-box .product-description {
  text-align: center;
}
.rtl .product-right .product-count img {
  margin-right: 0;
  margin-left: 6px;
}
.rtl .product-right .product-count ul li:first-child {
  margin-right: 0;
  margin-left: 14px;
}
.rtl .product-right .rating-section h6 {
  margin-left: 0;
  margin-right: 10px;
}
.rtl .product-right .label-section .label-text {
  padding-left: 0;
  padding-right: 5px;
}
.rtl .recently-purchase {
  text-align: right;
}
.rtl .recently-purchase .close-popup {
  left: 7px;
  right: unset;
}
.rtl .sticky-bottom-cart .product-image .content {
  margin-left: 0;
  margin-right: 12px;
}
.rtl .sticky-bottom-cart .product-image .content h6 del {
  margin-left: 0;
  margin-right: 7px;
}
.rtl .sticky-bottom-cart .product-image .content h6 span {
  margin-left: 0;
  margin-right: 5px;
}
.rtl .sticky-bottom-cart .selection-section .form-group:nth-child(2) {
  margin-left: 0;
  margin-right: 40px;
}
.rtl .add_to_cart .cart-inner .cart_media li .buttons .checkout {
  float: left;
}
.rtl .add_to_cart .cart-inner .cart_media li .media .media-body {
  text-align: right;
}
.rtl .add_to_cart .cart-inner .cart_media .close-circle {
  left: 0;
  right: unset;
}
.rtl .add_to_cart .cart-inner .cart_top .close-cart {
  margin-right: auto;
  margin-left: unset;
}
.rtl .add_to_cart.top .cart_media .cart_product li, .rtl .add_to_cart.bottom .cart_media .cart_product li {
  margin-left: 18px;
  margin-right: 0;
}
.rtl .timer {
  padding-left: 0;
  padding-right: 40px;
  text-align: right;
}
.rtl .timer span .padding-l {
  padding-left: 0;
  padding-right: 22px;
}
.rtl .tab-product .tab-content.nav-material p,
.rtl .product-full-tab .tab-content.nav-material p {
  text-align: right;
}
.rtl .product-related h2 {
  text-align: right;
}
.rtl .border-product {
  text-align: right;
}
.rtl .theme_checkbox label {
  padding-left: 0;
  padding-right: 30px;
}
.rtl .theme_checkbox .checkmark {
  left: unset;
  right: 0;
}
.rtl .element-detail {
  text-align: right;
}
.rtl .dashboard-section .radio_animated {
  margin: 0 0 0 1rem;
}
.rtl .dashboard-section .dashboard-box .dashboard-title span {
  margin-left: 0;
  margin-right: 30px;
}
.rtl .dashboard-section .top-sec .btn {
  margin-left: unset;
  margin-right: auto;
}
.rtl .dashboard-section .counter-section .counter-box {
  text-align: right;
  border-left: none;
  border-right: 3px solid var(--theme-deafult);
}
.rtl .dashboard-section .counter-section .counter-box img {
  margin-right: 0;
  margin-left: 20px;
}
.rtl .dashboard-section .faq-content .card {
  text-align: right;
}
.rtl .dashboard-section .dashboard-sidebar .faq-tab .nav-tabs {
  text-align: right;
}
.rtl .offer-box {
  display: none;
}
.rtl .faq-tab .nav-tabs .nav-item .nav-link.active, .rtl .faq-tab .nav-tabs .nav-item .nav-link:hover {
  border-right: none;
  border-left: 2px solid var(--theme-deafult);
}
.rtl .modal-header .close {
  margin: -1rem auto -1rem -1rem;
}
.rtl .modal-dialog .modal-content .modal-body {
  text-align: right;
}
.rtl .vendor-profile .profile-left .profile-detail,
.rtl .vendor-profile .profile-left .vendor-contact {
  border-left: none;
  padding-left: 0;
  margin-left: 0;
  border-right: 1px solid #efefef;
  padding-right: 20px;
  margin-right: 20px;
}
.rtl .vendor-profile .profile-left .profile-detail {
  text-align: right;
}
.rtl .vendor-profile .profile-left .profile-image img {
  margin: 0 auto;
}
.rtl .about-page {
  text-align: right;
}
.rtl .become-vendor {
  text-align: right;
}
.rtl .start-selling {
  text-align: right;
}
.rtl .cart-section .wishlist-buttons,
.rtl .wishlist-section .wishlist-buttons {
  text-align: left;
}
.rtl .cart-section tfoot tr td,
.rtl .wishlist-section tfoot tr td {
  text-align: left;
  padding-right: 0;
  padding-left: 63px;
}
.rtl .cart-section tfoot tr td:first-child,
.rtl .wishlist-section tfoot tr td:first-child {
  padding-right: 0;
  padding-left: 10px;
}
.rtl .cart-section .cart-buttons > div:first-child,
.rtl .wishlist-section .cart-buttons > div:first-child {
  text-align: right;
}
.rtl .cart-section .cart-buttons > div:nth-child(2),
.rtl .wishlist-section .cart-buttons > div:nth-child(2) {
  text-align: left;
}
.rtl .dashboard-right {
  text-align: right;
}
.rtl .dashboard .box .box-title > a {
  right: unset;
  left: 0;
}
.rtl .dashboard-left .block-content ul li {
  justify-content: end;
}
.rtl .login-page {
  text-align: right;
}
.rtl .register-page {
  text-align: right;
}
.rtl .contact-page .theme-form {
  text-align: right;
}
.rtl .contact-page .contact-right {
  text-align: right;
}
.rtl .contact-page .contact-right ul li {
  padding-left: 0;
  padding-right: 150px;
}
.rtl .contact-page .contact-right ul li .contact-icon {
  right: 0;
  left: unset;
  border-right: none;
  border-left: 1px solid #dddddd;
}
.rtl .checkout-page {
  text-align: right;
}
.rtl .order-box .title-box span {
  float: left;
}
.rtl .order-box .qty li span {
  float: left;
}
.rtl .order-box .sub-total .shipping {
  float: left;
}
.rtl .order-box .sub-total li .count {
  float: left;
}
.rtl .order-box .sub-total .shopping-option label {
  padding-right: 10px;
  padding-left: 0;
}
.rtl .order-box .total li span {
  float: left;
}
.rtl .payment-box .payment-options li .radio-option input[type=radio] {
  left: unset;
  right: 0;
}
.rtl .payment-box .payment-options li .radio-option label {
  padding-left: 0;
  padding-right: 30px;
}
.rtl .about-page {
  text-align: right;
}
.rtl .testimonial .testimonial-slider .slick-track .slick-slide:nth-child(even) .media {
  padding-left: 0;
  padding-right: 50px;
}
.rtl .testimonial .testimonial-slider .slick-track .slick-slide:nth-child(even) .media .media {
  padding-right: 0;
}
.rtl .testimonial .testimonial-slider .slick-track .slick-slide:nth-child(odd) .media {
  padding-right: 50px;
}
.rtl .testimonial .testimonial-slider .slick-track .slick-slide:nth-child(odd) .media .media {
  padding-right: 0;
}
.rtl .testimonial .testimonial-slider .media {
  direction: rtl;
}
.rtl .testimonial .testimonial-slider .media .media-body {
  padding: 30px 30px 30px 75px;
  text-align: right;
}
.rtl .typography_section {
  text-align: right;
}
.rtl .typography_section .typography-box .typo-content.input_button label {
  padding-left: 0;
  padding-right: 5px;
}
.rtl .review-page .comment-section .media {
  text-align: right;
}
.rtl .review-page .comnt-sec li {
  margin-right: 0;
  margin-left: 15px;
}
.rtl .comnt-sec li a i {
  margin-right: 0;
  margin-left: 5px;
}
.rtl .mordern-box .mordern-content .mordern-bottom .right {
  margin-left: 0;
  margin-right: auto;
}
.rtl footer .subscribe-form.rounded-input .form-control {
  border-radius: 0 25px 25px 0;
}
.rtl footer .subscribe-form.rounded-input .btn-solid {
  border-radius: 25px 0 0 25px;
}
.rtl .subscribe-form.classic-form .form-control {
  margin-left: 0;
  margin-right: 15px;
}
.rtl .blog-detail-page .comment-section li img {
  margin-right: 0;
  margin-left: 20px;
}
.rtl .blog-detail-page .comment-section li h6 span {
  margin-left: 0;
  margin-right: 20px;
}
.rtl .product-order {
  text-align: right;
}
.rtl .product-order .total-sec ul li span {
  float: left;
}
.rtl .product-order .final-total h3 span {
  float: left;
}
.rtl .order-success-sec {
  text-align: right;
}
.rtl .compare-section .compare-part {
  text-align: right;
}
.rtl .compare-section .compare-part .close-btn {
  right: unset;
  left: 0;
}
.rtl .sitemap_page {
  text-align: right;
}
.rtl .faq-section {
  text-align: right;
}
.rtl .faq-section .accordion.theme-accordion .card .card-header button {
  text-align: right;
}
.rtl .faq-section .accordion.theme-accordion .card .card-header button:before {
  right: unset;
  left: 20px;
}
.rtl .blog-sidebar {
  text-align: right;
}
.rtl .blog-sidebar .theme-card .recent-blog li .media img {
  margin-right: 0;
  margin-left: 10px;
}
.rtl .blog-page .blog-media .blog-right {
  text-align: right;
}
.rtl .blog-page .blog-media .blog-right ul li + li {
  padding-left: 0;
  margin-left: 0;
  border-left: none;
  padding-right: 15px;
  margin-right: 15px;
  border-right: 1px solid #dddddd;
}
.rtl .blog-page .blog-sidebar .theme-card .popular-blog li .blog-date {
  margin: 0 0 0 15px;
}
.rtl .blog-detail-page {
  text-align: right;
}
.rtl .blog-detail-page .blog-detail .post-social {
  text-align: right;
}
.rtl .blog-detail-page .blog-detail .post-social li + li {
  padding-left: 0;
  margin-left: 0;
  border-left: none;
  padding-right: 15px;
  margin-right: 15px;
  border-right: 1px solid #dddddd;
}
.rtl .product-christmas .product-box .cart-wrap {
  width: 100%;
}
.rtl .main-menu .menu-right .icon-nav .onhover-div .show-div.shopping-cart li .close-circle {
  left: 0;
  right: unset;
}
.rtl footer.footer-black.footer-light .subscribe {
  border-right: none;
  border-left: 1px solid rgba(0, 0, 0, 0.8);
}
.rtl .product-wrapper-grid.list-view .product-box .product-detail {
  padding-right: 15px;
  padding-left: 0;
  text-align: right !important;
}
.rtl .dashboard-section .apexcharts-svg {
  direction: ltr;
}
.rtl .theme-settings {
  left: 0;
  right: unset;
}
.rtl .theme-settings ul li {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-right: auto;
  margin-left: 0;
}
.rtl .search-section select.form-control {
  background: url(../images/dropdown.png) no-repeat 5%;
}
.rtl .cycle-box {
  direction: rtl;
}
.rtl .cycle-box .product-detail .add-wish {
  right: unset;
  left: 22px;
}
.rtl .cycle-box .product-detail .details li + li {
  margin-left: 0;
  margin-right: 12px;
}
.rtl .cycle-box .cart-detail li svg {
  margin-right: 0;
  margin-left: 6px;
}
.rtl .cycle-box .cart-detail li + li {
  border-left: none;
  border-right: 1px solid rgba(0, 0, 0, 0.2);
}
.rtl .filter-bottom-title:after {
  right: unset;
  left: -3px;
}
.rtl .color-w-name ul li span {
  margin-right: 0;
  margin-left: 10px;
}
.rtl .checkout-page .checkout-form select {
  background: url(../images/dropdown.png) no-repeat 5%;
}
.rtl.christmas {
  overflow-x: hidden !important;
}
.rtl .compare-page .table-wrapper .table thead .th-compare th .remove-compare {
  float: right;
}
.rtl .sale-box {
  right: -2px;
  left: unset;
}
.rtl .sale-box .heading-right h3 {
  left: -250px;
  right: unset;
  transform: translate(50%, -50%) rotate(90deg);
}
.rtl.modal-open {
  overflow-x: hidden !important;
}
.rtl .blackfriday-modal .btn-close {
  left: 20px;
  right: unset;
}
.rtl.dark .service-block + .service-block {
  border-color: #404040;
}
.rtl .cookie-bar .btn {
  margin-left: 0;
  margin-right: 20px;
}
.rtl .product-box .img-wrapper .lable-block .lable3, .rtl .product-wrap .img-wrapper .lable-block .lable3 {
  right: 7px;
  left: unset;
}
.rtl .product-box .img-wrapper .lable-block .lable4, .rtl .product-wrap .img-wrapper .lable-block .lable4 {
  left: 7px;
  right: unset;
}
.rtl .j-box .product-box {
  direction: rtl;
}
.rtl .j-box .product-box .cart-info a {
  border-right: 1px solid #dddddd;
  border-left: unset;
}
.rtl .service-w-bg .service-block + .service-block {
  border: none;
}
.rtl .service-style-border .service-block .media-body {
  border-right: 1px solid #dddddd;
  padding-right: 20px;
  border-left: none;
  padding-left: 0;
}
.rtl .service-style-border .service-block + .service-block {
  border-right: none;
}
.rtl .ajax-search .typeahead {
  text-align: right;
}
.rtl .product-vertical .theme-card.center-align .offer-slider .product-box2 .media .media-body {
  text-align: right;
}
.rtl .footer-style-1 .footer-social li {
  padding-left: 15px;
}
.rtl .title-basic .timer {
  margin-right: 15px;
  margin-left: 0;
}
.rtl .tracking-page .wrapper {
  margin-left: 0;
  margin-right: -5px;
}
.rtl .tracking-page .wrapper .arrow-steps .step {
  padding: 12px 30px 12px 10px;
  float: right;
}
.rtl .tracking-page .wrapper .arrow-steps .step span:before {
  left: unset;
  right: -20px;
}
.rtl .tracking-page .wrapper .arrow-steps .step:before, .rtl .tracking-page .wrapper .arrow-steps .step:after {
  right: unset;
  left: -17px;
  border-left: 0;
  border-right: 17px solid #f8f8f8;
}
.rtl .tracking-page .wrapper .arrow-steps .step:before {
  right: 0;
  left: auto;
  border-left: 0;
  border-right: 17px solid #fff;
}
.rtl .tracking-page .wrapper .arrow-steps .step.current:after {
  border-left: 0;
  border-right: 17px solid var(--theme-deafult);
}
.rtl .theme-tab .tab-title.border-title li, .rtl .theme-tab .tab-title2.border-title li {
  border-left: 0;
  border-right: 1px solid #bbbbbb;
}
.rtl .theme-tab .tab-title.border-title li:first-child, .rtl .theme-tab .tab-title2.border-title li:first-child {
  border-right: 0;
}
.rtl .product-left-title.right-content .slick-custom-arrow {
  justify-content: flex-start;
}
.rtl .added-notification {
  right: unset;
  left: 30px;
}
.rtl .image-360 {
  right: unset;
  left: 20px;
}
.rtl .tab-border .nav-border {
  border-left: 1px solid #dddddd;
  border-right: none;
}
.rtl .search-block .form-header .input-group i {
  padding-right: 0;
  padding-left: 10px;
}
.rtl .space_sm {
  padding-left: 0;
  padding-right: 240px;
}
.rtl .lable-gradient {
  right: 7px;
  left: unset;
}
.rtl .detail-inline h4 {
  margin-left: unset;
  margin-right: auto;
}
.rtl .gradient-slider .product-box {
  direction: rtl;
}

.custom-theme {
  position: fixed;
  right: 0;
  width: 40px;
  height: 38px;
  display: flex;
  z-index: 1;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  text-decoration: none;
  border-radius: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  background-color: #f7f7f7;
  box-shadow: 0px 0px 5px 0px rgba(154, 154, 154, 0.54);
  top: calc(50% + 34px);
  cursor: pointer;
}

body.rtl .m-r-15 {
  margin-left: 15px;
  margin-right: unset;
}
body.rtl .me-2, body.rtl .mx-2 {
  margin-left: 0.5rem !important;
  margin-right: unset !important;
}
body.rtl .custom-select {
  padding: 0.375rem 0.75rem 0.375rem 1.75rem;
}
body.rtl .ps-0, body.rtl .px-0 {
  padding-right: 0 !important;
  padding-left: unset !important;
}
body.rtl .m-r-10 {
  margin-left: 10px;
  margin-right: unset;
}
body.rtl .radio_animated, body.rtl .checkbox_animated {
  margin: 0 0 0 1rem;
}
body.rtl .digital-add .form-group .radio_animated {
  margin-left: 8px;
  margin-right: unset;
}
body.rtl .needs-validation .form-group {
  text-align: right;
}
body.rtl .needs-validation .form-group .checkbox {
  padding-right: 0;
  padding-left: unset;
}
body.rtl .needs-validation .permission-block .attribute-blocks {
  padding-right: 15px;
  padding-left: unset;
  text-align: right;
}
body.rtl .needs-validation .permission-block .attribute-blocks .row {
  padding-right: 20px;
  padding-left: unset;
}
body.rtl .needs-validation .checkbox {
  right: 0;
  margin-left: -16px;
}
body.rtl .needs-validation .checkbox label {
  padding-right: 16px;
  padding-left: unset;
}
body.rtl .needs-validation .checkbox label::before {
  right: 0;
  left: unset;
  margin-left: unset;
  margin-right: -16px;
}
body.rtl .needs-validation .radio-animated label {
  margin-left: 20px;
  margin-right: unset;
}
body.rtl .modal-footer > :not(:last-child) {
  margin-left: 0.25rem;
  margin-right: unset;
}
body.rtl .jsgrid-button + .jsgrid-button {
  margin-right: 5px;
  margin-left: unset;
}
body.rtl .custom-theme {
  left: 0;
  right: unset;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
body.rtl .text-right {
  text-align: left !important;
}
body.rtl .br-wrapper {
  text-align: right;
}
body.rtl .card .card-header .card-header-right {
  left: 20px;
  right: unset;
}
body.rtl .card .card-header .card-header-right .card-option {
  text-align: left;
}
body.rtl .card .card-header .card-header-right .card-option.list-unstyled {
  padding-right: 0;
  padding-left: unset;
}
body.rtl code[class*=language-], body.rtl pre[class*=language-] {
  text-align: right;
}
body.rtl .code-box-copy__btn {
  right: unset;
  left: 11px;
}
body.rtl table thead tr th, body.rtl table thead tr td, body.rtl table tbody tr th, body.rtl table tbody tr td {
  text-align: right;
}
body.rtl .pull-right {
  float: left;
}
body.rtl .status-circle {
  right: 40px;
  left: unset;
}
body.rtl .right-sidebar.show {
  left: 0;
  right: unset;
}
body.rtl .right-sidebar .friend-list-search .fa {
  left: 35px;
  right: unset;
}
body.rtl .right-sidebar .chat-box .about {
  float: right;
  padding-right: 10px;
  padding-left: unset;
  text-align: right;
}
body.rtl .right-sidebar .chat-box .friend-list {
  text-align: right;
}
body.rtl .right-sidebar .chat-box .user-image {
  float: right;
  margin-left: 5px;
  margin-right: 0;
}
body.rtl .nav-menus .search-form input {
  padding: 10px 70px 10px 10px;
}
body.rtl ul.notification-dropdown.onhover-show-div li {
  text-align: right;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar .sidebar-user h6, body.rtl .page-wrapper .page-body-wrapper .page-sidebar .sidebar-user p {
  text-align: center;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li {
  text-align: right;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li.active > .sidebar-submenu {
  margin-right: 15px;
  margin-left: unset;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a {
  padding-right: 10px;
  padding-left: unset;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a > .fa-circle {
  right: 0;
  left: unset;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-header svg {
  margin-right: unset;
  margin-left: 14px;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar ~ .page-body {
  margin-right: 255px;
  margin-left: unset;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar.open {
  margin-right: calc(-260px);
  margin-left: unset;
}
body.rtl .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body, body.rtl .page-wrapper .page-body-wrapper .page-sidebar.open ~ footer {
  margin-right: 0;
}
body.rtl .page-wrapper .page-body-wrapper .page-header .row h3 {
  text-align: right;
}
body.rtl .page-wrapper .page-body-wrapper footer {
  margin-right: 255px;
  margin-left: unset;
}
body.rtl .page-wrapper .page-body-wrapper footer p i {
  margin-right: 5px;
  margin-left: unset;
}
body.rtl .page-main-header {
  margin-right: 255px;
  margin-left: unset;
}
body.rtl .page-main-header .main-header-right .nav-right {
  text-align: left;
}
body.rtl .page-main-header .main-header-right .nav-right .profile-dropdown li svg {
  margin-right: unset;
  margin-left: 10px;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li {
  text-align: right;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li:before {
  right: 0;
  left: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li .dot {
  left: 17px;
  right: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li .media .dotted-animation {
  left: -3px;
  right: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li .media .dotted-animation .main-circle {
  left: 6px;
  right: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li .media .dotted-animation .animate-circle {
  left: -4px;
  right: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li.onhover-dropdown:hover .onhover-show-div:before {
  right: 10px;
  left: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li.onhover-dropdown:hover .onhover-show-div:after {
  right: 10px;
  left: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li.onhover-dropdown:last-child {
  padding-left: 0;
  padding-right: 20px;
  border-right: none;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li:first-child {
  border-right: none;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:after {
  right: 22px;
  left: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:before {
  right: 51px;
  left: unset;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li:last-child {
  padding-right: 20px;
  padding-left: 0;
}
body.rtl .page-main-header .main-header-right .nav-right > ul > li i.ml-2 {
  margin-right: 0.5rem !important;
  margin-left: unset !important;
}
body.rtl .page-main-header .main-header-right .nav-right .language-dropdown {
  text-align: right;
}
body.rtl .page-main-header .main-header-right .nav-right .language-dropdown li a i {
  margin-left: 10px;
}
body.rtl .page-main-header .main-header-right .nav-right .profile-dropdown li a svg {
  margin-right: unset;
  margin-left: 10px;
}
body.rtl .page-main-header .main-header-right .mobile-sidebar {
  padding-right: 20px;
}
body.rtl .page-main-header.open {
  margin-right: 0;
}
body.rtl ul.notification-dropdown.onhover-show-div {
  right: initial;
  left: -30px;
  padding: 0;
}
body.rtl ul.notification-dropdown.onhover-show-div:before {
  right: inherit !important;
  left: 35px !important;
}
body.rtl ul.notification-dropdown.onhover-show-div:after {
  right: inherit !important;
  left: 35px !important;
}
body.rtl ul.notification-dropdown.onhover-show-div li {
  margin-right: 0 !important;
}
body.rtl ul.notification-dropdown.onhover-show-div li span.badge {
  margin-left: unset !important;
}
body.rtl ul.notification-dropdown.onhover-show-div li span svg {
  margin-right: unset;
  margin-left: 10px;
}
body.rtl ul.notification-dropdown.onhover-show-div li .notification-icon {
  margin-left: 20px;
  margin-right: unset;
}
body.rtl .owl-carousel {
  direction: ltr;
}
body.rtl .media .media-body {
  text-align: right;
}
body.rtl .order-graph .order-graph-bottom h6 {
  margin-right: 15px !important;
  margin-left: 30px;
}
body.rtl .latest-order-table .btn {
  float: right;
}
body.rtl .product-physical table tr th:nth-child(2), body.rtl .product-physical table tr td:nth-child(2) {
  text-align: right;
}
body.rtl .product-page-details .color-variant, body.rtl .product-page-details .size-box, body.rtl .product-right .color-variant, body.rtl .product-right .size-box {
  text-align: right;
}
body.rtl .add-product-form .qty-box .input-group .input-group-append .btn {
  border-right: none !important;
  border-left: 1px solid #dddddd !important;
}
body.rtl .add-product-form .qty-box .input-group .btn-primary.bootstrap-touchspin-down {
  border-left: none !important;
  border-right: 1px solid #dddddd !important;
}
body.rtl .add-product-form .offset-sm-4 {
  text-align: right;
}
body.rtl .timer {
  float: right;
}
body.rtl .timer ~ .m-t-15 {
  clear: both;
  text-align: right;
  padding-top: 15px;
}
body.rtl .timer span .padding-l {
  right: 35px;
  left: unset;
  padding-right: 0;
}
body.rtl .product-list table tr td, body.rtl .product-list table tr th, body.rtl .report-table table tr td, body.rtl .report-table table tr th {
  text-align: right !important;
}
body.rtl .product-list table tr td:last-child, body.rtl .product-list table tr th:last-child, body.rtl .report-table table tr td:last-child, body.rtl .report-table table tr th:last-child {
  text-align: center !important;
}
body.rtl .dropzone .dz-preview .dz-error-mark, body.rtl .dropzone .dz-preview .dz-success-mark {
  left: unset;
  right: 48%;
  margin-right: -27px;
  margin-left: unset;
}
body.rtl .dropzone.dz-clickable .dz-message * {
  text-align: center;
}
body.rtl .digital-add .col-form-label {
  display: block;
  text-align: right;
}
body.rtl .dataTables_wrapper .dataTables_filter input[type=search] {
  margin-right: 10px;
  margin-left: unset;
}
body.rtl .dataTables_wrapper table.dataTable .vendor-list img {
  margin-left: 20px;
  margin-right: unset;
}
body.rtl .category-table table tr td:nth-child(3), body.rtl .category-table table tr th:nth-child(3) {
  text-align: right;
}
body.rtl .media-table table tr td:last-child, body.rtl .media-table table tr th:last-child {
  text-align: right !important;
}
body.rtl .profile-details h5 {
  text-align: center;
}
body.rtl .account-setting {
  text-align: right;
}
@media only screen and (max-width: 991px) {
  body.rtl .page-wrapper .page-main-header {
    margin-right: 0;
  }
  body.rtl .page-wrapper .page-body-wrapper .page-sidebar ~ .page-body, body.rtl .page-wrapper .page-body-wrapper .page-sidebar ~ footer {
    margin-right: 0;
  }
}
@media only screen and (max-width: 767px) {
  body.rtl .needs-validation .form-group .checkbox {
    padding-right: 15px;
    padding-left: unset;
  }
  body.rtl .page-wrapper .page-body-wrapper footer p {
    text-align: center;
  }
}
@media (min-width: 1200px) {
  body.rtl .offset-xl-3 {
    margin-right: 25%;
    margin-left: unset;
  }
}

@media (min-width: 576px) {
  .rtl .me-sm-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
  }
}
@media (min-width: 768px) {
  .rtl .offset-md-2 {
    margin-left: 0;
    margin-right: 16.66667%;
  }
}
@media (min-width: 992px) {
  .rtl .offset-lg-2 {
    margin-right: 16.66667%;
    margin-left: unset;
  }
  .rtl .offset-lg-4 {
    margin-left: unset;
    margin-right: 33.3333333333%;
  }
  .rtl .offset-lg-3 {
    margin-right: 25%;
    margin-left: 0;
  }
  .rtl .service_slide .service-home .offset-lg-2 {
    margin-right: 16.6666666667%;
    margin-left: 0;
  }
}
@media (min-width: 1200px) {
  .rtl .offset-xl-1 {
    margin-right: 8.33333%;
    margin-left: 0;
  }
  .rtl header .main-navbar .nav-menu > li .nav-submenu li a:before {
    right: 35px;
  }
  .rtl .sm-vertical a {
    text-align: right;
  }
  .rtl .layout3-menu .pixelstrap > li > a, .rtl .layout3-menu .pixelstrap > li > a:hover, .rtl .layout3-menu .pixelstrap > li > a:focus {
    padding-left: 28px;
  }
  .rtl .layout3-menu .pixelstrap .sub-arrow {
    left: -6px;
  }
  .rtl .layout3-menu .pixelstrap > li ul a {
    padding-right: 35px;
    padding-left: 35px;
  }
  .rtl .layout3-menu .pixelstrap .full-mega-menu li a {
    padding-right: 0;
    padding-left: 0;
  }
  .rtl .sm-horizontal > li {
    float: right;
  }
  .rtl .sm-horizontal > li > a,
.rtl .sm-horizontal > li > a:hover,
.rtl .sm-horizontal > li > a:active {
    padding-left: 45px;
    padding-right: 0;
  }
  .rtl .pixelstrap ul a:before {
    right: 35px;
  }
  .rtl .pixelstrap .full-mega-menu .mega-box {
    text-align: right;
  }
  .rtl .pixelstrap .mega ul {
    margin-left: auto !important;
  }
  .rtl .pixelstrap.sm-vertical .mega-menu ul a:before {
    right: 0;
  }
  .rtl .center-margin {
    margin-left: -30px;
    margin-right: 0;
  }
}
@media (min-width: 1367px) {
  .rtl .layout3-menu .pixelstrap > li > a, .rtl .layout3-menu .pixelstrap > li > a:hover, .rtl .layout3-menu .pixelstrap > li > a:focus {
    padding-left: 34px;
  }
  .rtl .layout3-menu .pixelstrap .sub-arrow {
    left: 0px;
  }
}
@media (max-width: 1430px) and (min-width: 1200px) {
  .rtl .layout3-menu .main-menu .menu-left .navbar {
    padding: 40px 0 40px 35px;
  }
}
@media (max-width: 1430px) {
  .rtl .left-sidebar_space {
    padding-right: 0;
  }
  .rtl header.left-header .top-header {
    padding-right: 0;
  }
  .rtl header.left-header .sidenav {
    left: unset;
    right: -300px;
  }
  .rtl header.left-header .sidenav.open-side {
    right: 0;
  }
  .rtl header.left-header.left-header-sm .sidenav {
    right: 0;
  }
  .rtl .collection-product-wrapper .product-top-filter .product-filter-content .collection-view {
    border-right: 1px solid #dddddd;
    border-left: none;
    padding-right: 20px !important;
    padding-left: 0 !important;
    text-align: right;
  }
  .rtl .collection-product-wrapper .product-top-filter .product-filter-content .search-count {
    border-right: 1px solid #dddddd;
  }
  .rtl .collection-product-wrapper .product-top-filter .product-filter-content .product-page-filter {
    border-left: 1px solid #dddddd;
    border-right: none;
  }
  .rtl .collection-product-wrapper .product-top-filter .product-filter-content .collection-grid-view ul,
.rtl .collection-product-wrapper .product-top-filter .popup-filter .collection-grid-view ul {
    justify-content: flex-start;
  }
  .rtl .product-right .product-icon .product-social li {
    padding-left: 10px;
  }
  .rtl .product-form-box .timer {
    padding-right: 17px;
  }
  .rtl .cart-section tfoot tr td {
    padding-left: 41px;
  }
}
@media (max-width: 1367px) {
  .rtl .timer span .padding-l {
    padding-right: 5px;
  }
}
@media (max-width: 1199px) {
  .rtl .pixelstrap .link-section .menu-title .according-menu:before {
    left: -18px;
    right: unset;
  }
  .rtl .pixelstrap .link-section .menu-title.active .according-menu:before {
    left: -18px;
    right: unset;
  }
  .rtl .pixelstrap li a {
    text-align: right;
  }
  .rtl .pixelstrap li .lable-nav {
    right: 15px;
    left: unset;
  }
  .rtl .pixelstrap .full-mega-menu .mega-box {
    text-align: right;
  }
  .rtl .sidenav.marketplace-sidebar {
    right: -300px;
  }
  .rtl .sidenav.marketplace-sidebar.open-side {
    right: 0;
  }
  .rtl .space_sm {
    padding-right: 0;
  }
  .rtl header .main-navbar .nav-menu li {
    text-align: right;
  }
  .rtl header .main-navbar .nav-menu > li a .sub-arrow {
    left: 0;
    right: unset;
  }
  .rtl header .main-navbar .nav-menu > li .nav-submenu li .nav-sub-childmenu {
    left: 0;
  }
  .rtl header.left-header .sidenav .pixelstrap li .nav-submenu {
    margin-right: 0;
  }
  .rtl header.left-header .sidenav .pixelstrap li .nav-submenu li .nav-sub-childmenu {
    margin-right: 0;
  }
  .rtl header.left-header.left-header-relative .sidenav .left-sidebar_center {
    padding: 0 0 25px 25px;
  }
  .rtl header.left-header.left-header-sm .sidenav {
    right: -300px;
  }
  .rtl header.left-header.left-header-sm .sidenav .left-sidebar_center {
    padding: 0;
  }
  .rtl header.left-header.left-header-sm .sidenav .left-sidebar_center .pixelstrap > li > a img {
    margin-left: 12px !important;
    margin-right: 0 !important;
  }
  .rtl header.left-header.left-header-sm.open-side .sidenav {
    right: 0;
  }
  .rtl header.video-header #main-nav {
    right: unset;
    left: 0;
  }
  .rtl header.video-header .main-menu .menu-right .icon-nav {
    padding-left: 40px;
    padding-right: 0;
  }
  .rtl .sidenav .sidebar-menu li {
    direction: rtl;
    text-align: right;
  }
  .rtl .sidenav .sidebar-menu li a .sub-arrow {
    right: auto;
    left: 15px;
  }
  .rtl .service-block + .service-block {
    border: none;
  }
  .rtl .main-nav-center .toggle-nav {
    left: 175px;
    right: unset;
  }
  .rtl .product-right .product-icon .product-social li {
    padding-left: 4px;
  }
  .rtl .product-right .product-icon .wishlist-btn span {
    padding-right: 4px;
  }
  .rtl .cart-section tfoot tr td {
    padding-left: 25px;
  }
  .rtl .full-banner.feature-banner .feature-object li,
.rtl .full-banner.feature-banner .feature-object-right li {
    margin-left: 0;
  }
  .rtl .add_to_cart.top .cart_media .cart_product li, .rtl .add_to_cart.bottom .cart_media .cart_product li {
    margin-left: 0;
    margin-right: 0;
  }
  .rtl .cart-section .cart-buttons > div:last-child,
.rtl .wishlist-section .cart-buttons > div:last-child {
    padding-left: 17px;
  }
  .rtl .tab-border .nav-border {
    border-left: none;
  }
  .rtl .collection-product-wrapper .product-filter-content .collection-view {
    padding-right: 20px !important;
  }
  .rtl .footer-social li {
    padding-right: 0;
    padding-left: 38px;
  }
  .rtl .footer-theme2 .social-white li {
    padding-left: 12px;
    padding-right: 12px;
  }
  .rtl .tab-left .theme-tab .left-side .tab-title {
    text-align: center;
    margin-left: 0;
  }
  .rtl .product-form-box.product-right .timer {
    padding-left: 0;
  }
}
@media (max-width: 991px) {
  .rtl .footer-light .subscribe {
    border: none;
  }
  .rtl .collection-product-wrapper .product-top-filter .product-filter-content .product-page-per-view select {
    border-right: 1px solid #dddddd !important;
  }
  .rtl .filter-main-btn {
    text-align: right;
  }
  .rtl .product-right {
    text-align: center;
  }
  .rtl .product-right .size-text {
    text-align: right;
  }
  .rtl .product-right .timer {
    text-align: right;
  }
  .rtl .product-right .product-icon .product-social li {
    padding-left: 20px;
  }
  .rtl .border-product {
    text-align: center;
  }
  .rtl .faq-tab {
    text-align: right;
  }
  .rtl .faq-tab .nav-tabs .nav-item .nav-link.active, .rtl .faq-tab .nav-tabs .nav-item .nav-link:hover {
    border-left: none;
  }
  .rtl .vendor-profile .profile-left .profile-detail,
.rtl .vendor-profile .profile-left .vendor-contact {
    margin-right: 0;
    padding-right: 0;
    border-right: none;
  }
  .rtl .vendor-profile .profile-left .profile-detail {
    text-align: center;
  }
  .rtl .cart-section tfoot tr td {
    padding-left: 0;
  }
  .rtl .contact-page .contact-right ul li {
    padding-right: 0;
  }
  .rtl .contact-page .contact-right ul li .contact-icon {
    border-left: none;
  }
  .rtl .collection-product-wrapper .product-top-filter .popup-filter .sidebar-popup {
    text-align: center;
    background: none var(--theme-deafult);
    border: none;
  }
  .rtl .testimonial .testimonial-slider .media .media-body {
    padding: 0 30px;
  }
  .rtl .testimonial .testimonial-slider .slick-track .slick-slide:nth-child(even) .media {
    padding-left: 0;
    padding-right: 0;
  }
  .rtl .beauty-about .about-text p {
    text-align: center;
  }
  .rtl .full-banner.feature-banner .feature-object li:nth-child(2),
.rtl .full-banner.feature-banner .feature-object-right li:nth-child(2) {
    padding-right: 0;
    padding-left: 0;
  }
  .rtl .full-banner.feature-banner .feature-object li:nth-child(3),
.rtl .full-banner.feature-banner .feature-object-right li:nth-child(3) {
    padding-right: 0;
    padding-left: 0;
  }
  .rtl .full-banner.feature-banner .feature-object-right {
    margin-right: 0;
  }
  .rtl footer.footer-black.footer-light .subscribe {
    border-left: none;
  }
  .rtl .product-left-title.right-content {
    text-align: right;
  }
}
@media (max-width: 767px) {
  .rtl header.left-header .main-menu .menu-right .icon-nav li {
    padding-left: 0;
    padding-right: 15px;
  }
  .rtl .footer-title h4 {
    text-align: right;
  }
  .rtl .footer-title .according-menu:before {
    left: 2px;
    right: unset;
  }
  .rtl .footer-title.active .according-menu:before {
    left: 2px;
    right: unset;
  }
  .rtl .footer-contant {
    text-align: right;
  }
  .rtl .footer-theme .sub-title li {
    text-align: right;
  }
  .rtl .footer-theme .sub-title .contact-list li {
    padding-right: 0;
  }
  .rtl .footer-theme .sub-title .contact-list i {
    margin-right: 0;
    margin-left: 10px;
  }
  .rtl .service-block .media .media-body {
    text-align: center;
  }
  .rtl .service-block + .service-block {
    border: none;
  }
  .rtl .footer-theme2 .contact-details li {
    text-align: right;
  }
  .rtl .social-white li:first-child {
    padding-right: 0;
  }
  .rtl .product-box .cart-detail,
.rtl .product-wrap .cart-detail {
    left: 10px;
    right: unset;
  }
  .rtl .footer-theme2 .footer-link li,
.rtl .footer-theme2 .footer-link-b li {
    padding-left: 0;
    padding-right: 20px;
  }
  .rtl .layout2-logo {
    padding-left: 70px;
    padding-right: 0;
  }
  .rtl .testimonial .testimonial-slider .media .media-body {
    padding: 30px 30px 30px 30px;
  }
  .rtl .footer-social li {
    padding: 0 15px;
  }
  .rtl .service-block svg {
    margin-left: 0;
    margin-bottom: 20px;
  }
  .rtl .service-style-border .service-block .media-body {
    border-right: none;
    padding-right: 0;
  }
  .rtl .tracking-page .wrapper .arrow-steps .step:after {
    right: unset;
    left: 50%;
    border-right: 17px solid #f8f8f8;
    transform: rotate(-90deg) translateY(-50%);
  }
  .rtl .sticky-bottom-cart .selection-section .form-group:nth-child(2) {
    margin-right: 10px;
  }
}
@media (max-width: 577px) {
  .rtl header.left-header .main-menu .menu-left .mobile-logo {
    left: unset;
    right: 15px;
  }
  .rtl header.left-header .main-menu .menu-left .navbar {
    right: unset;
    left: 15px;
  }
  .rtl header.header-5 .main-menu .brand-logo {
    left: unset;
    right: 0;
  }
  .rtl header.header-5.left-sidebar-header .main-menu .menu-left .navbar {
    left: 0px;
    right: unset;
  }
  .rtl header.header-6 .main-menu .brand-logo {
    margin-right: 0;
  }
  .rtl header.header-style .brand-logo {
    right: 0;
    left: unset;
  }
  .rtl header.header-christmas .main-menu .brand-logo {
    margin-right: 0;
  }
  .rtl header.header-gym .main-menu .brand-logo {
    left: unset;
    right: 0;
    margin: 0;
  }
  .rtl header.left-header .main-menu .menu-right .icon-nav li {
    padding: 0;
  }
  .rtl .top-header .header-dropdown .mobile-account,
.rtl .top-header .header-dropdown .mobile-wishlist {
    padding: 0;
  }
  .rtl .top-header .header-dropdown > li:nth-child(2) {
    padding: 0;
  }
  .rtl .top-header .header-dropdown li {
    padding: 0 !important;
  }
  .rtl .main-menu .menu-left .navbar {
    padding: 30px 0 30px 45px !important;
  }
  .rtl .main-menu .menu-right .icon-nav li {
    padding-right: 0;
  }
  .rtl .main-menu .menu-right .icon-nav .mobile-cart {
    right: unset;
    left: 48%;
  }
  .rtl .main-nav-center .toggle-nav {
    right: unset;
    left: 15px;
  }
  .rtl .layout2-logo {
    padding-left: 0;
  }
  .rtl .layout3-menu .main-menu .menu-left .menu-right .toggle-nav {
    left: 15px;
    right: unset;
  }
  .rtl .layout3-menu .main-menu .menu-left .main-menu-right .toggle-nav {
    top: 3px;
    left: 0;
    right: unset;
  }
  .rtl .collection-product-wrapper .product-top-filter .product-filter-content .product-page-per-view {
    border-right: none !important;
  }
  .rtl .collection-product-wrapper .product-top-filter .product-filter-content .product-page-filter {
    border-right: 1px solid #dddddd !important;
  }
  .rtl .blog-detail-page .blog-detail .post-social li + li {
    padding-right: 0;
    margin-right: 0;
    border-right: none;
  }
  .rtl .full-banner.feature-banner .feature-object {
    text-align: right;
  }
  .rtl .full-banner.feature-banner .feature-object li .media img {
    margin-left: 15px;
    margin-right: 0;
  }
  .rtl .full-banner.feature-banner .feature-object li .media .media-body {
    text-align: right;
  }
  .rtl footer .subscribe-form.rounded-input .form-control {
    border-radius: 20px;
  }
  .rtl footer .subscribe-form.rounded-input .btn-solid {
    border-radius: 20px;
  }
}
@media (max-width: 480px) {
  .rtl .testimonial .testimonial-slider .media .media-body {
    text-align: center;
    padding: 6px;
  }
  .rtl .review-page .comment-section .media {
    text-align: center;
  }
  .rtl .blog-detail-page .comment-section li img {
    margin-left: 0;
  }
  .rtl .blog-page .blog-media .blog-right ul li + li {
    padding-right: 0;
    margin-right: 0;
    border-right: none;
  }
  .rtl .cart_counter .cart_checkout {
    margin-right: 0;
  }
}
@media (max-width: 420px) {
  .rtl .full-box .center-slider .offer-slider .product-box {
    direction: rtl;
  }
  .rtl .full-box .center-slider .offer-slider .product-box .img-wrapper img {
    padding: 0 0 0 15px;
  }
  .rtl .product-right .product-icon .product-social li {
    padding-left: 5px;
  }
  .rtl .timer span .padding-l {
    padding-right: 5px;
  }
  .rtl .order-box .sub-total .shipping .shopping-option:last-child {
    padding-left: 0;
    padding-right: 20px;
  }
  .rtl .center-slider .offer-slider .product-box .product-detail {
    text-align: right !important;
  }
}
@media (max-width: 360px) {
  .rtl .timer span .padding-l {
    padding-right: 10px;
  }
}
/**=====================
    Admin CSS Start
==========================**/
.profile-box .profile-image {
  width: 50px;
  height: 50px;
  -o-object-fit: contain;
     object-fit: contain;
}

.theme-color {
  color: #5f57ea;
}

.remove-icon {
  color: red;
  font-size: 18px;
}

.edit-icon {
  color: #2d8a2d;
  font-size: 18px;
}

label {
  margin-bottom: 0.5rem;
}

.form-footer {
  padding: 30px 30px 0;
  border-top: 1px solid #f8f8f9;
  margin: 30px -30px 0;
}

.page-item.active .page-link {
  background-color: #5f57ea;
  border-color: #5f57ea;
}

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}

.alert-custom {
  background-color: white;
  display: flex;
  align-items: center;
  box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0.05);
  border: none;
  position: relative;
  padding-left: 25px;
}
.alert-custom .btn-close {
  position: absolute;
  top: 50%;
  right: 20px;
  z-index: 2;
  padding: 0;
  transform: translateY(-50%);
}
.alert-custom p {
  font-size: 14px;
}
.alert-custom i {
  border-radius: 100%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.alert-custom .divider {
  content: "";
  position: absolute;
  left: 10px;
  width: 2px;
  height: calc(100% - 20px);
  top: 50%;
  transform: translateY(-50%);
}

.chart-sparkline {
  position: absolute;
  left: 0;
  top: 0;
  visibility: hidden;
  background: rgba(49, 49, 49, 0.7);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000);
  color: #ffffff;
  font: 10px Nunito, sans-serif;
  text-align: left;
  white-space: nowrap;
  padding: 5px;
  border: 1px solid rgba(49, 49, 49, 0.75);
  box-sizing: content-box;
  z-index: 10000;
  border-radius: 5px;
}
.chart-sparkline .jqsfield {
  color: #ffffff;
  font: 10px Nunito, sans-serif;
  text-align: left;
}

.checkbox_animated {
  cursor: pointer;
  position: relative;
  margin: 0 1rem 0 0;
}
.checkbox_animated:before {
  transition: transform 0.4s cubic-bezier(0.45, 1.8, 0.5, 0.75);
  transform: rotate(-45deg) scale(0, 0);
  content: "";
  position: absolute;
  left: 0.25rem;
  top: 0.225rem;
  z-index: 1;
  width: 0.75rem;
  height: 0.375rem;
  border: 2px solid #5f57ea;
  border-top-style: none;
  border-right-style: none;
}
.checkbox_animated:after {
  content: "";
  position: absolute;
  top: -0.125rem;
  left: 0;
  width: 1.3rem;
  height: 1.3rem;
  background: #ffffff;
  border: 2px solid #e8ebf2;
  cursor: pointer;
}
.checkbox_animated:checked:before {
  transform: rotate(-45deg) scale(1, 1);
}

.radio_animated {
  position: relative;
  margin: 0 1rem 0 0;
  cursor: pointer;
}
.radio_animated:before {
  transition: transform 0.4s cubic-bezier(0.45, 1.8, 0.5, 0.75);
  transform: scale(0, 0);
  content: "";
  position: absolute;
  top: 0;
  left: 0.125rem;
  z-index: 1;
  width: 0.75rem;
  height: 0.75rem;
  background: #5f57ea;
  border-radius: 50%;
}
.radio_animated:after {
  content: "";
  position: absolute;
  top: -0.25rem;
  left: -0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background: #ffffff;
  border: 2px solid #e8ebf2;
  border-radius: 50%;
}
.radio_animated:checked:before {
  transform: scale(1, 1);
}

a:hover {
  color: #5f57ea;
}

.btn-popup {
  margin-bottom: 30px;
}

.media-table table tr th:last-child,
.media-table table tr td:last-child {
  text-align: left !important;
}

.permision-section ul {
  -moz-column-count: 2;
       column-count: 2;
}
.permision-section li {
  padding: 15px;
  border-radius: $border-radius;
  background-color: #f8f8f9;
  display: flex;
  align-items: center;
  page-break-inside: avoid;
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}
.permision-section li h5 {
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 40px;
  text-transform: capitalize;
  width: 150px;
}
.permision-section li label {
  text-transform: capitalize;
  cursor: pointer;
  margin-bottom: 0;
}
.permision-section li label + label {
  margin-left: 26px;
}
.permision-section li + li {
  margin-top: 10px;
}

.media-modal .form-control,
.media-modal .form-select {
  line-height: 28px;
  background-color: #f9f9f9;
  font-size: 14px;
}
.media-modal .content-section {
  padding: calc(15px + (35 - 15) * ((100vw - 320px) / (1920 - 320))) 0;
  min-height: 60vh;
}
.media-modal .modal-footer {
  justify-content: space-between;
}
.media-modal .left-part {
  display: flex;
  align-items: center;
}
.media-modal .left-part .btn-sm {
  padding: 5px 16px;
  font-size: 12px;
}
.media-modal .left-part .file-detail {
  margin-left: 16px;
}
.media-modal .left-part .file-detail h6 {
  margin-bottom: 0;
}
.media-modal .nav-tabs {
  margin-bottom: 30px;
}
.media-modal .nav-tabs .nav-link {
  color: #222222;
  cursor: pointer;
}
.media-modal .nav-tabs .nav-link.active {
  background-color: #5f57ea;
  color: #ffffff;
}

.product-physical table .jsgrid-filter-row {
  display: none !important;
}
.product-physical table tr th:nth-child(2),
.product-physical table tr td:nth-child(2) {
  text-align: left;
}

.timer {
  padding-left: 10px;
  padding-right: 10px;
}
.timer span {
  text-align: center;
  position: relative;
}
.timer span .padding-l {
  padding-left: 0;
  position: absolute;
  left: 35px;
  top: 10px;
}

.add-product img {
  width: 500px;
}
.add-product ul li {
  display: flex;
  margin-bottom: 15px;
}
.add-product ul li .box-input-file {
  width: 50px;
  height: 50px;
  background-color: #f1f4fb;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}
.add-product ul li .box-input-file i {
  color: #5f57ea;
}
.add-product ul li .box-input-file .upload {
  position: absolute;
  width: 70px;
  left: 0;
  right: 0;
  opacity: 0;
}

.digital-add .col-form-label {
  font-family: Nunito;
}
.digital-add .form-control {
  font-size: 14px;
}
.digital-add .form-group .radio_animated {
  margin-right: 8px;
}
.digital-add textarea {
  width: 100%;
}

.add-product-form .form-group {
  align-items: center;
}
.add-product-form .form-group label {
  font-size: 16px;
  font-weight: 600;
}
.add-product-form .form-group:last-child {
  align-items: end;
}
.add-product-form .form-group .form-control {
  font-size: 14px;
}
.add-product-form .qty-box .input-group {
  justify-content: flex-start;
}
.add-product-form .qty-box .input-group button {
  padding: 12px;
}
.add-product-form .qty-box .input-group .btn-primary {
  background-color: transparent !important;
  border: 1px solid #ddd !important;
}
.add-product-form .qty-box .input-group .btn-primary.bootstrap-touchspin-down {
  border-right: none !important;
}
.add-product-form .qty-box .input-group .input-group-append {
  margin-left: 0;
}
.add-product-form .qty-box .input-group .input-group-append .btn {
  border-left: none !important;
}
.add-product-form #cke_editor1 .cke_inner .cke_contents.cke_reset {
  height: 100px !important;
}

.zoomContainer {
  top: 225px !important;
}

.chart-block canvas {
  width: 100%;
}

.sell-graph canvas {
  width: 100% !important;
  height: 300px !important;
}

.sales-carousel .value-graph h3 {
  color: #313131;
  font-weight: 600;
}
.sales-carousel .media .small-box {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.small-chartjs canvas {
  width: 100% !important;
  height: 60px !important;
}

.order-graph {
  overflow: hidden;
}
.order-graph h6 {
  color: #313131;
  font-family: Nunito;
  font-weight: 700;
  margin-bottom: 20px;
}
.order-graph .order-graph-bottom {
  margin-top: 30px;
}
.order-graph .order-graph-bottom.sales-location {
  margin-top: 25px;
}
.order-graph .order-graph-bottom h6 {
  color: #999;
  margin-left: 15px;
  margin-right: 30px;
}
.order-graph .order-graph-bottom h6 span {
  color: #313131;
}
.order-graph .order-graph-bottom .media {
  margin-bottom: 20px;
}
.order-graph .order-graph-bottom .media:last-child {
  margin-bottom: 0;
}
.order-graph .order-graph-bottom .media .order-shape-primary {
  background-color: #5f57ea;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .media .order-shape-secondary {
  background-color: #13c9ca;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .media .order-shape-success {
  background-color: #81ba00;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .media .order-shape-danger {
  background-color: #dc3545;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .media .order-shape-info {
  background-color: #00a8ff;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .media .order-shape-light {
  background-color: #f8f8f9;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .media .order-shape-dark {
  background-color: #2a3142;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .media .order-shape-warning {
  background-color: #ffbc58;
  width: 18px;
  height: 8px;
  margin-top: 7px;
  border-radius: 4px;
}
.order-graph .order-graph-bottom .order-color-primary {
  background-color: #5f57ea;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}
.order-graph .order-graph-bottom .order-color-secondary {
  background-color: #13c9ca;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}
.order-graph .order-graph-bottom .order-color-success {
  background-color: #81ba00;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}
.order-graph .order-graph-bottom .order-color-danger {
  background-color: #dc3545;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}
.order-graph .order-graph-bottom .order-color-info {
  background-color: #00a8ff;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}
.order-graph .order-graph-bottom .order-color-light {
  background-color: #f8f8f9;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}
.order-graph .order-graph-bottom .order-color-dark {
  background-color: #2a3142;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}
.order-graph .order-graph-bottom .order-color-warning {
  background-color: #ffbc58;
  width: 8px;
  height: 8px;
  margin-top: 7px;
  border-radius: 2px;
}

.datepickers-container {
  top: -86px;
}

.datepicker--nav {
  color: #5f57ea;
}

.datepicker--nav-action {
  color: #5f57ea;
  background-color: #5f57ea;
}

.datepicker--nav-action:hover {
  color: #5f57ea;
}

.datepicker--nav-title i {
  color: #5f57ea;
}

.table thead th {
  border-bottom: 2px solid #e8ebf2;
}

.switch-sm .switch {
  width: 25px;
  height: 16px;
  margin-top: 10px;
  margin-bottom: 0px;
}

.page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body .activity .media .gradient-round.gradient-line-1:after {
  bottom: -66px;
}
.page-wrapper .page-body-wrapper footer p {
  line-height: 1.7;
}

.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li {
  width: 100%;
}
.page-wrapper .page-body-wrapper .page-header .row h3 {
  color: #000;
}

.widget-cards {
  border-radius: 25px !important;
}

.static-top-widget div.align-self-center svg {
  width: 25px;
  height: 25px;
  vertical-align: middle;
}
.static-top-widget .media-body {
  align-self: center !important;
}
.static-top-widget .media-body h3 {
  font-family: work-Sans, sans-serif;
  color: #ffffff;
}
.static-top-widget .media-body h3 small {
  font-size: 11px;
}
.static-top-widget .icons-widgets .text-center {
  width: 50px;
  height: 50px;
  border-radius: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flot-chart-container {
  height: 400px;
}

.user-status table tbody tr td {
  vertical-align: middle;
}
.user-status table tbody tr td .d-inline-block {
  margin-top: 11px;
}
.user-status table tbody tr td .image-sm-size img {
  width: 41px;
}
.user-status table tbody tr:last-child td {
  padding-bottom: 0;
}
.user-status table thead tr th {
  border-top: 0;
  font-size: 16px;
  color: #2a3142;
  font-weight: 600;
  padding-top: 0;
}

.card-block .table-responsive .table caption {
  padding-left: 10px;
}
.card-block .table-responsive .table-bordered td {
  vertical-align: middle;
}
.card-block .table-border-radius {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.card-block .default-checkbox-align #checkbox1 {
  margin-right: 10px;
}
.card-block .default-checkbox-align #radio {
  margin-right: 5px;
}

.chart-vertical-center {
  display: flex;
  justify-content: center;
}
.chart-vertical-center #myDoughnutGraph,
.chart-vertical-center #myPolarGraph {
  width: auto !important;
}

.products-admin .product-box {
  padding: 20px;
}
.products-admin .product-box .front .product-hover {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  opacity: 0;
  border-radius: 100%;
  transform: scale(0);
  transition: all 0.3s ease;
}
.products-admin .product-box .front .product-hover ul li {
  display: inline-block;
  box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.08);
  padding: 9px 14px;
  background-color: #ffffff;
  font-size: 18px;
  border-radius: 100%;
  line-height: 1.6;
  height: 45px;
  width: 45px;
  margin: 0 3px;
}
.products-admin .product-box .front .product-hover ul li .btn {
  padding: 0;
  background-color: transparent;
}
.products-admin .product-box .front .product-hover ul li .btn i {
  font-size: 16px;
}
.products-admin .product-box .front:hover .product-hover {
  opacity: 1;
  border-radius: 0%;
  transform: scale(1);
  transition: all 0.3s ease;
}

.product-page-main .product-slider .owl-stage-outer {
  margin-top: 0;
}
.product-page-main .product-slider .owl-carousel .owl-stage-outer .owl-stage .owl-item.active.current {
  border: 1px solid #5f57ea;
}
.product-page-main .owl-stage-outer {
  margin-top: 30px;
}
.product-page-main .owl-stage-outer .owl-stage .owl-item.active.current {
  border: 1px solid #5f57ea !important;
}

.br-theme-fontawesome-stars-o .br-widget a {
  color: #ffa800;
}
.br-theme-fontawesome-stars-o .br-widget a .br-selected,
.br-theme-fontawesome-stars-o .br-widget a .br-active:after {
  color: #ffa800;
  font: normal normal normal 14px/1 FontAwesome;
}

.nav-pills .nav-link.active,
.nav-pills .nav-link > .nav-link,
.nav-pills .show.active,
.nav-pills .show > .nav-link {
  background-color: #5f57ea;
}

.deactivate-account {
  margin-top: 30px;
}
.deactivate-account .btn {
  margin-top: 20px;
}

.account-setting h5 {
  margin-bottom: 20px;
}

.tab2-card ul {
  margin-bottom: 30px;
  border-bottom: 1px solid #dee2e6;
}
.tab2-card ul li a svg {
  width: 18px;
  vertical-align: middle;
}
.tab2-card .nav-tabs .nav-link {
  color: #333333;
}
.tab2-card .nav-tabs .nav-link.active, .tab2-card .nav-tabs .nav-link:focus, .tab2-card .nav-tabs .nav-link:hover {
  color: #5f57ea;
  border-color: transparent;
  border-bottom: 2px solid #5f57ea;
}
.tab2-card .nav-tabs .nav-item.show .nav-link {
  border-color: transparent;
  border-bottom: 2px solid #5f57ea;
}

.profile-details img {
  margin-bottom: 20px;
}
.profile-details .social {
  margin-top: 15px;
}
.profile-details .social .btn-showcase {
  margin-bottom: -10px;
}
.profile-details .social .btn-showcase .btn {
  padding: 8px 13px;
  margin: 0 3px 20px;
  border-radius: 100%;
  width: 40px;
  height: 40px;
}
.profile-details .social .btn-showcase .btn-fb {
  background-color: #50598e;
  color: #ffffff;
}
.profile-details .social .btn-showcase .btn-google {
  background-color: #c64e40;
  color: #ffffff;
}
.profile-details .social .btn-showcase .btn-twitter {
  background-color: #6fa2d8;
  color: #ffffff;
}

.project-status {
  margin-top: 20px;
}
.project-status .media {
  margin-top: 20px;
}

.sm-progress-bar {
  height: 6px;
}

.profile-table table tbody tr td:first-child {
  width: 250px;
}
.profile-table table th,
.profile-table table td {
  border-top: none;
}

.report-employee .card-header {
  border-bottom: none !important;
}
.report-employee .flot-chart-container {
  height: 323px;
}

.sales-chart {
  height: 307px;
}
.sales-chart svg .ct-series-b .ct-point,
.sales-chart svg .ct-series-b .ct-line,
.sales-chart svg .ct-series-b .ct-bar,
.sales-chart svg .ct-series-b .ct-slice-donut {
  stroke: #dc3545;
}

.expense-chart #area-chart1 {
  height: 307px;
}

.tab2-card .media {
  align-items: center;
}
.tab2-card .media img {
  margin-right: 20px;
}

footer.blockquote-footer {
  bottom: unset;
}

.bg-black {
  background-color: #000000;
}

.datepicker {
  box-shadow: 0 4px 14px rgba(95, 87, 234, 0.15);
}

.datepicker--day-name {
  color: #000000;
  font-weight: bold;
}

.datepicker--cell.-current- {
  color: #000000;
  border-radius: 5px;
  font-weight: bold;
  border: 2px solid #5f57ea;
}
.datepicker--cell.-focus- {
  background: #5f57ea;
  color: #ffffff;
}
.datepicker--cell.-selected- {
  background: #5f57ea;
}

.dropzone .dz-preview {
  box-shadow: 0px 0px 3px #5f57ea;
}
.dropzone .dz-preview .dz-error-message {
  color: #5f57ea !important;
  background: transparent !important;
  border: 1px solid #5f57ea !important;
}
.dropzone .dz-preview .dz-error-message:after {
  border-bottom: 6px solid #5f57ea !important;
}

.tab-coupon {
  margin-bottom: 30px;
}

.needs-validation .permission-block .attribute-blocks {
  padding-left: 15px;
}
.needs-validation .permission-block .attribute-blocks .row {
  padding-left: 20px;
}
.needs-validation .permission-block .attribute-blocks + .attribute-blocks h5 {
  margin-top: 30px;
}
.needs-validation .permission-block .attribute-blocks h6 {
  border-bottom: 1px solid #eff0f1;
  margin-bottom: 15px;
  padding-bottom: 5px;
}
.needs-validation h4 {
  font-weight: 600;
  margin-bottom: 20px;
}
.needs-validation input {
  font-size: 14px;
}
.needs-validation .form-group {
  align-items: center;
}
.needs-validation .form-group label {
  margin-bottom: 0;
}
.needs-validation .form-group label span {
  color: #5f57ea;
}
.needs-validation .form-group .checkbox {
  padding-left: 0;
}
.needs-validation .editor-label {
  align-items: end;
}
.needs-validation .editor-space {
  padding: 0;
}
.needs-validation .checkbox input {
  opacity: 0;
}
.needs-validation .checkbox label {
  display: inline-block;
  position: relative;
  padding-left: 16px;
  cursor: pointer;
  margin-top: 10px;
  margin-bottom: 10px;
}
.needs-validation .checkbox label::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 19px;
  height: 19px;
  left: 0;
  margin-left: -16px;
  border: 1px solid #5f57ea;
  border-radius: 3px;
  background-color: #fff;
  transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
}
.needs-validation .checkbox input[type=checkbox]:checked + label::before {
  font-family: themify;
  content: "";
  text-align: center;
  line-height: 1.5;
  font-size: 12px;
}
.needs-validation .checkbox-primary input[type=checkbox]:checked + label::before {
  border-color: #5f57ea;
  color: #ffffff;
  background: #5f57ea;
}
.needs-validation .radio-animated label {
  margin-right: 20px;
}
.needs-validation textarea {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.badge-warning {
  color: #ffffff;
}

div.dataTables_wrapper div.dataTables_paginate {
  margin-top: 25px;
}

.dataTables_wrapper .dataTables_length {
  margin-bottom: 30px;
}
.dataTables_wrapper .dataTables_length label select {
  border-color: #eff0f1;
}
.dataTables_wrapper .dataTables_filter input[type=search] {
  border: 1px solid #eff0f1;
  padding: 0 15px;
  margin-left: 10px;
  height: 37px;
  border-radius: 0;
}
.dataTables_wrapper .dataTables_paginate {
  margin-left: 15px !important;
  border: 1px solid #f6f7fb;
  border-radius: 0.25rem;
  padding-top: 0;
}
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button:active {
  background: #5f57ea;
  color: #ffffff !important;
  border: 1px solid #5f57ea;
  box-shadow: none;
}
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  color: #2a3142 !important;
  background: transparent !important;
}
.dataTables_wrapper table.dataTable {
  border: 1px solid #ebf1ff;
  width: 100%;
  overflow-x: auto;
}
.dataTables_wrapper table.dataTable.row-border tbody th,
.dataTables_wrapper table.dataTable.row-border tbody td, .dataTables_wrapper table.dataTable.display tbody th,
.dataTables_wrapper table.dataTable.display tbody td {
  border-top: 1px solid #ebf1ff !important;
}
.dataTables_wrapper table.dataTable tbody tr {
  background-color: rgba(241, 244, 251, 0.5);
}
.dataTables_wrapper table.dataTable tbody tr:hover {
  background-color: rgba(241, 244, 251, 0.7);
}
.dataTables_wrapper table.dataTable tbody tr .sorting_1 {
  background-color: rgba(241, 244, 251, 0.2) !important;
}
.dataTables_wrapper table.dataTable tbody tr.odd {
  background-color: #ffffff;
}
.dataTables_wrapper table.dataTable .vendor-list {
  align-items: center;
}
.dataTables_wrapper table.dataTable .vendor-list img {
  margin-right: 20px;
  height: 40px;
  width: auto !important;
}
.dataTables_wrapper table.dataTable td {
  padding: 0.75rem !important;
}
.dataTables_wrapper table.dataTable th {
  background-color: #f1f4fb !important;
  padding: 24px 0.75rem !important;
}
.dataTables_wrapper table.dataTable thead .sorting:before, .dataTables_wrapper table.dataTable thead .sorting:after,
.dataTables_wrapper table.dataTable thead .sorting_asc:before,
.dataTables_wrapper table.dataTable thead .sorting_asc:after,
.dataTables_wrapper table.dataTable thead .sorting_desc:before,
.dataTables_wrapper table.dataTable thead .sorting_desc:after,
.dataTables_wrapper table.dataTable thead .sorting_asc_disabled:before,
.dataTables_wrapper table.dataTable thead .sorting_asc_disabled:after,
.dataTables_wrapper table.dataTable thead .sorting_desc_disabled:before,
.dataTables_wrapper table.dataTable thead .sorting_desc_disabled:after {
  bottom: 25px;
}

.category-table table tr td:nth-child(3),
.category-table table tr th:nth-child(3) {
  text-align: left;
}

.order-table table tr td:nth-child(3),
.order-table table tr th:nth-child(3) {
  text-align: center !important;
}

.product-list table tr td,
.product-list table tr th,
.report-table table tr td,
.report-table table tr th {
  text-align: left !important;
}
.product-list table tr td:last-child,
.product-list table tr th:last-child,
.report-table table tr td:last-child,
.report-table table tr th:last-child {
  text-align: center !important;
}
.product-list table .jsgrid-filter-row,
.report-table table .jsgrid-filter-row {
  display: none !important;
}
.product-list table .jsgrid-cell,
.report-table table .jsgrid-cell {
  padding: 0.75rem;
}

.jsgrid-pager {
  padding: 2em 0 0;
}

.jsgrid-grid-header {
  border: 1px solid #e8efff;
}

.user-list img {
  border-radius: 100%;
}

.selected-custom-row > div {
  width: 14.28%;
}

.upload-card .img-part {
  background-repeat: no-repeat;
}
.upload-card .select-file.select:after {
  background-color: #5f57ea;
}
.upload-card .select-file:hover::after {
  opacity: 1;
  transition: all 0.5s ease;
}
.upload-card .select-file::after {
  content: "";
  position: absolute;
  left: 12px;
  top: 12px;
  width: 20px;
  height: 20px;
  background-color: #525252;
  border-radius: 0 6px 0 0;
  opacity: 0;
  transition: all 0.5s ease;
  border-radius: 2px;
}
.upload-card .select-file::before {
  content: "";
  font: normal normal normal 14px/1 FontAwesome;
  position: absolute;
  left: 15px;
  top: 15px;
  z-index: 1;
  color: #ffffff;
  opacity: 0;
  transition: all 0.5s ease;
}
.upload-card .select-file.select {
  border: 2px solid #5f57ea;
  overflow: hidden;
}
.upload-card .select-file.select::after, .upload-card .select-file.select::before {
  opacity: 1;
  transition: all 0.5s ease;
}
.upload-card .card {
  border: 2px solid #dddddd;
  box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0.05);
  overflow: hidden;
  border-radius: 5px;
}
.upload-card .card .img-part {
  position: relative;
}
.upload-card .card .img-part .overlay-copy {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme-deafult);
  color: #222;
  opacity: 0;
  transition: all 0.5s ease;
}
.upload-card .card .img-part .overlay-copy a {
  font-size: 16px;
  color: #ffffff;
}
.upload-card .card .img-part:hover .overlay-copy {
  opacity: 0.8;
  transition: all 0.5s ease;
}
.upload-card .card .custom-dropdown {
  position: absolute;
  right: 10px;
  top: 10px;
}
.upload-card .card .custom-dropdown .dropdown-toggle,
.upload-card .card .custom-dropdown a {
  width: 25px;
  height: 25px;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
}
.upload-card .card .custom-dropdown .dropdown-toggle i,
.upload-card .card .custom-dropdown a i {
  color: #ffffff;
  font-size: 13px;
}
.upload-card .card .custom-dropdown .dropdown-toggle:after,
.upload-card .card .custom-dropdown a:after {
  display: none;
}
.upload-card .card .card-body {
  padding: 12px 15px;
  background-color: #ffffff;
}
.upload-card .card .card-body h5 {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: -3px;
}
.upload-card .card .card-body h6 {
  font-size: 12px;
  margin-bottom: -5px;
  margin-top: -3px;
}

.mh-cls {
  min-height: calc(100vh - 320px);
}
.mh-cls form {
  height: calc(100vh - 380px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.snippet-preview.card {
  border: 1px solid #e0e0e0;
  box-shadow: none;
}
.snippet-preview.card .card-header {
  padding: 20px;
}
.snippet-preview.card .card-header h4 {
  font-weight: 400;
  font-size: 16px;
}
.snippet-preview.card .card-body {
  padding: 20px;
}
.snippet-preview.card .card-body a {
  color: #006621;
}
.snippet-preview.card .card-body h4 {
  margin-bottom: 6px;
  color: #1a0dab;
  font-weight: 400;
}
.snippet-preview.card .card-body p {
  font-size: 15px;
  line-height: 1.4;
}

.select2-container--default .select2-selection--single {
  border-color: #ced4da;
  border-radius: 0.25rem;
  padding: 0.48rem 0.75rem;
  height: 38px;
  display: flex;
  align-items: center;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  top: 16px;
  left: unset;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #5f57ea;
}
.select2-container--default .select2-selection--multiple {
  border-color: #ced4da !important;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  margin-right: 8px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e9ecef;
  border: 1px solid transparent;
  padding: 3px 10px;
}

.select2-container {
  width: 100% !important;
}
.select2-container .select2-selection__rendered {
  border-bottom: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 0;
  padding-right: 0;
}

.select2-results__option[aria-selected] {
  display: block;
}

/**=====================
  Admin CSS Ends
==========================**/
/**=====================
      Button CSS start
==========================**/
.btn {
  font-size: 14px;
  padding: 0.5rem 1.5rem;
  border-radius: 5px;
  letter-spacing: 1px;
}

.btn-lg {
  font-size: 18px;
}

.btn-sm {
  font-size: 12px;
}

.btn-xs {
  padding: 0.05rem 0.4rem;
  font-size: 11px;
}

.large-btn {
  margin-bottom: -20px;
}
.large-btn .btn {
  margin-right: 5px;
  margin-bottom: 15px;
}

.btn-air-primary {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-primary:hover, .btn-air-primary:active, .btn-air-primary:not([disabled]):not(.disabled):active {
  background-color: #342ae4;
  border-color: #342ae4;
}

.btn-air-secondary {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-secondary:hover, .btn-air-secondary:active, .btn-air-secondary:not([disabled]):not(.disabled):active {
  background-color: #0f9b9b;
  border-color: #0f9b9b;
}

.btn-air-success {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-success:hover, .btn-air-success:active, .btn-air-success:not([disabled]):not(.disabled):active {
  background-color: #5e8700;
  border-color: #5e8700;
}

.btn-air-danger {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-danger:hover, .btn-air-danger:active, .btn-air-danger:not([disabled]):not(.disabled):active {
  background-color: #bd2130;
  border-color: #bd2130;
}

.btn-air-info {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-info:hover, .btn-air-info:active, .btn-air-info:not([disabled]):not(.disabled):active {
  background-color: #0086cc;
  border-color: #0086cc;
}

.btn-air-light {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-light:hover, .btn-air-light:active, .btn-air-light:not([disabled]):not(.disabled):active {
  background-color: #dddde1;
  border-color: #dddde1;
}

.btn-air-dark {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-dark:hover, .btn-air-dark:active, .btn-air-dark:not([disabled]):not(.disabled):active {
  background-color: #161a23;
  border-color: #161a23;
}

.btn-air-warning {
  box-shadow: 0 5px 10px 2px rgba(88, 103, 221, 0.19) !important;
}
.btn-air-warning:hover, .btn-air-warning:active, .btn-air-warning:not([disabled]):not(.disabled):active {
  background-color: #ffa825;
  border-color: #ffa825;
}

.input-air-primary {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-primary:focus {
  border-color: #5f57ea;
}

.input-air-secondary {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-secondary:focus {
  border-color: #13c9ca;
}

.input-air-success {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-success:focus {
  border-color: #81ba00;
}

.input-air-danger {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-danger:focus {
  border-color: #dc3545;
}

.input-air-info {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-info:focus {
  border-color: #00a8ff;
}

.input-air-light {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-light:focus {
  border-color: #f8f8f9;
}

.input-air-dark {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-dark:focus {
  border-color: #2a3142;
}

.input-air-warning {
  box-shadow: 0 3px 5px 1px rgba(88, 103, 221, 0.15) !important;
}
.input-air-warning:focus {
  border-color: #ffbc58;
}

.form-bg-primary {
  background: #5f57ea;
  border-color: #5f57ea;
  color: #ffffff;
}
.form-bg-primary:focus {
  border-color: #5f57ea;
  background: #5f57ea;
  color: #ffffff;
}
.form-bg-primary:focus:active {
  border-color: #5f57ea;
  background: #5f57ea;
  color: #ffffff;
}

.form-bg-secondary {
  background: #13c9ca;
  border-color: #13c9ca;
  color: #ffffff;
}
.form-bg-secondary:focus {
  border-color: #13c9ca;
  background: #13c9ca;
  color: #ffffff;
}
.form-bg-secondary:focus:active {
  border-color: #13c9ca;
  background: #13c9ca;
  color: #ffffff;
}

.form-bg-success {
  background: #81ba00;
  border-color: #81ba00;
  color: #ffffff;
}
.form-bg-success:focus {
  border-color: #81ba00;
  background: #81ba00;
  color: #ffffff;
}
.form-bg-success:focus:active {
  border-color: #81ba00;
  background: #81ba00;
  color: #ffffff;
}

.form-bg-danger {
  background: #dc3545;
  border-color: #dc3545;
  color: #ffffff;
}
.form-bg-danger:focus {
  border-color: #dc3545;
  background: #dc3545;
  color: #ffffff;
}
.form-bg-danger:focus:active {
  border-color: #dc3545;
  background: #dc3545;
  color: #ffffff;
}

.form-bg-info {
  background: #00a8ff;
  border-color: #00a8ff;
  color: #ffffff;
}
.form-bg-info:focus {
  border-color: #00a8ff;
  background: #00a8ff;
  color: #ffffff;
}
.form-bg-info:focus:active {
  border-color: #00a8ff;
  background: #00a8ff;
  color: #ffffff;
}

.form-bg-light {
  background: #f8f8f9;
  border-color: #f8f8f9;
  color: #2a3142;
}
.form-bg-light:focus {
  border-color: #f8f8f9;
  background: #f8f8f9;
  color: #2a3142;
}
.form-bg-light:focus:active {
  border-color: #f8f8f9;
  background: #f8f8f9;
  color: #2a3142;
}

.form-bg-dark {
  background: #2a3142;
  border-color: #2a3142;
  color: #ffffff;
}
.form-bg-dark:focus {
  border-color: #2a3142;
  background: #2a3142;
  color: #ffffff;
}
.form-bg-dark:focus:active {
  border-color: #2a3142;
  background: #2a3142;
  color: #ffffff;
}

.form-bg-warning {
  background: #ffbc58;
  border-color: #ffbc58;
  color: #ffffff;
}
.form-bg-warning:focus {
  border-color: #ffbc58;
  background: #ffbc58;
  color: #ffffff;
}
.form-bg-warning:focus:active {
  border-color: #ffbc58;
  background: #ffbc58;
  color: #ffffff;
}

.btn-warning {
  color: #ffffff;
}
.btn-warning:hover, .btn-warning.disabled {
  color: #ffffff;
}

.btn-primary-gradien {
  background-image: linear-gradient(to right, #827bef 0%, #3c33e5 51%, #827bef 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-primary-gradien:focus, .btn-primary-gradien:active, .btn-primary-gradien.active {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, #827bef, 0%, #3c33e5, 100%, #ffffff) !important;
}

.btn-secondary-gradien {
  background-image: linear-gradient(to right, #1ce9ea 0%, #0fa4a5 51%, #1ce9ea 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-secondary-gradien:hover, .btn-secondary-gradien:focus, .btn-secondary-gradien:active, .btn-secondary-gradien.active, .btn-secondary-gradien.hover {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, #1ce9ea, 0%, #0fa4a5, 100%, #ffffff) !important;
}

.btn-success-gradien {
  background-image: linear-gradient(to right, #9de300 0%, #659100 51%, #9de300 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-success-gradien:hover, .btn-success-gradien:focus, .btn-success-gradien:active, .btn-success-gradien.active, .btn-success-gradien.hover {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, #9de300, 0%, #659100, 100%, #ffffff) !important;
}

.btn-danger-gradien {
  background-image: linear-gradient(to right, #e25865 0%, #c62232 51%, #e25865 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-danger-gradien:hover, .btn-danger-gradien:focus, .btn-danger-gradien:active, .btn-danger-gradien.active, .btn-danger-gradien.hover {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, #e25865, 0%, #c62232, 100%, #ffffff) !important;
}

.btn-warning-gradien {
  background-image: linear-gradient(to right, #ffcc81 0%, #ffac2f 51%, #ffcc81 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-warning-gradien:hover, .btn-warning-gradien:focus, .btn-warning-gradien:active, .btn-warning-gradien.active, .btn-warning-gradien.hover {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, #ffcc81, 0%, #ffac2f, 100%, #ffffff) !important;
}

.btn-info-gradien {
  background-image: linear-gradient(to right, #29b6ff 0%, #008dd6 51%, #29b6ff 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-info-gradien:hover, .btn-info-gradien:focus, .btn-info-gradien:active, .btn-info-gradien.active, .btn-info-gradien.hover {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, #29b6ff, 0%, #008dd6, 100%, #ffffff) !important;
}

.btn-light-gradien {
  background-image: linear-gradient(to right, white 0%, #e2e2e6 51%, white 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-light-gradien:hover, .btn-light-gradien:focus, .btn-light-gradien:active, .btn-light-gradien.active, .btn-light-gradien.hover {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, white, 0%, #e2e2e6, 100%, #ffffff) !important;
}

.btn-dark-gradien {
  background-image: linear-gradient(to right, #3a445b 0%, #1a1e29 51%, #3a445b 100%);
  border: none;
  color: #ffffff;
  background-size: auto 200%;
  transition: all 0.3s ease;
}
.btn-dark-gradien:hover, .btn-dark-gradien:focus, .btn-dark-gradien:active, .btn-dark-gradien.active, .btn-dark-gradien.hover {
  transition: 1.5s;
  background-position: right center;
  background-image: linear-gradient(to right, #3a445b, 0%, #1a1e29, 100%, #ffffff) !important;
}

[class*=-gradien]:hover {
  background-size: 50% 100%;
  transition: all 0.3s ease;
  color: #ffffff;
}

/**=====================
 Button CSS end
==========================**/
/**=====================
      Card CSS Start
==========================**/
.card {
  margin-bottom: 30px;
  border: 0px;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  border-radius: $border-radius;
  box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0.05);
}
.card.card-load .card-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 8;
  align-items: center;
  justify-content: center;
}
.card.card-load .card-loader i {
  margin: 0 auto;
  color: #5f57ea;
  font-size: 20px;
}
.card.full-card {
  position: fixed;
  top: 80px;
  z-index: 99999;
  box-shadow: none;
  right: 0;
  border-radius: 0;
  border: 1px solid #eff0f1;
  width: calc(100vw - 272px);
  height: calc(100vh - 80px);
}
.card.full-card .card-body {
  overflow: auto;
}
.card .card-header {
  background-color: #ffffff;
  border-bottom: none;
  padding: 30px;
  border-bottom: 1px solid #f8f8f9;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.card .card-header .card-header-right {
  border-radius: 0 0 0 7px;
  right: 20px;
  top: 25px;
  display: inline-block;
  float: right;
  padding: 7px 0;
  position: absolute;
}
.card .card-header .card-header-right .card-option {
  text-align: right;
  width: 35px;
  height: 20px;
  overflow: hidden;
  transition: 0.3s ease-in-out;
}
.card .card-header .card-header-right .card-option li {
  display: inline-block;
}
.card .card-header .card-header-right i {
  margin: 0 5px;
  cursor: pointer;
  color: #2a3142;
  line-height: 20px;
}
.card .card-header .card-header-right i.icofont-refresh {
  font-size: 13px;
}
.card .card-header h5 {
  font-size: 18px;
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 600;
  line-height: 24px;
}
.card .card-header > span {
  font-size: 12px;
  color: #777777;
  margin-top: 5px;
  display: block;
  letter-spacing: 1px;
}
.card .card-body {
  padding: 30px;
  background-color: transparent;
}
.card .card-body p:last-child {
  margin-bottom: 0;
}
.card .sub-title {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 5px;
  margin-bottom: 8px;
  font-size: 18px;
}
.card .card-footer {
  background-color: #ffffff;
  border-top: 1px solid #f8f8f9;
  padding: 30px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.card-header .nav-material {
  margin-bottom: -13px;
}

/**=====================
     Card CSS End
==========================**/
/**=====================
      Footer CSS Start
==========================**/
footer {
  background-color: #ffffff;
  border-top: 1px solid #f8f8f9;
  padding: 15px;
  bottom: 0;
  left: 0;
}
footer a {
  font-weight: 600;
}
footer.footer-starter-kit {
  bottom: -52px;
}

.blockquote-footer {
  margin-left: 0 !important;
  width: 885px !important;
}

.page-wrapper .page-body-wrapper footer {
  margin-left: 255px;
  transition: all 0.3s ease;
  bottom: 0;
  z-index: 8;
}
.page-wrapper .page-body-wrapper footer p {
  color: #a5a5a5;
}
.page-wrapper .page-body-wrapper footer p i {
  color: #dc3545;
  margin-left: 5px;
}

/**=====================
      Footer CSS Ends
==========================**/
/**=====================
      General CSS Start
==========================**/
body {
  background-color: rgba(246, 246, 246, 0.6);
  font-size: 14px;
  overflow-x: hidden;
  font-family: work-Sans, sans-serif;
  color: #313131;
}

ul {
  padding-left: 0px;
  list-style-type: none;
  margin-bottom: 0;
  padding-right: 0px;
}

* a {
  color: #5f57ea;
}

*.btn:focus {
  box-shadow: none !important;
}

p {
  font-size: 13px;
  line-height: 1.7;
  letter-spacing: 0.7px;
}

code {
  color: #5f57ea !important;
  background-color: #f8f8f9;
  padding: 3px;
  margin: 0 3px;
  border-radius: 2px;
}

blockquote {
  border-left: 4px solid #e8ebf2;
  padding: 15px;
}

blockquote.text-center {
  border: none;
  padding: 15px;
}

blockquote.text-end {
  border-left: none;
  border-right: 4px solid #e8ebf2;
  padding: 15px;
}

:focus {
  outline-color: #5f57ea;
}

.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

svg {
  vertical-align: baseline;
}

input:focus {
  outline-color: transparent;
}

.txt-dark {
  color: #1b252a !important;
}

.txt-success {
  color: #81ba00 !important;
}

.txt-danger {
  color: #dc3545 !important;
}

/**=====================
      General CSS Ends
==========================**/
/**=====================
    Header CSS Start
==========================**/
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li.active > a, .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li:hover > a {
  color: #313131;
  border-left-color: #2a3142;
  transition: 0.3s;
}

.onhover-show-div {
  box-shadow: 0 0 2px 2px #f8f8f9;
  transform: translateY(30px);
  opacity: 0;
  visibility: hidden;
  left: 0;
}

.nav-menus .onhover-dropdown:hover .onhover-show-div {
  opacity: 1;
  transform: translateY(0px);
  visibility: visible;
}
.nav-menus .onhover-dropdown:hover .onhover-show-div:before {
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ffffff;
  content: "";
  top: -7px;
  position: absolute;
  left: 10px;
  z-index: 2;
}
.nav-menus .onhover-dropdown:hover .onhover-show-div:after {
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #898989;
  content: "";
  top: -7px;
  position: absolute;
  left: 10px;
  z-index: 1;
}

/*======= Page Header css Start ======= */
.page-wrapper .page-main-header {
  background-color: #ffffff;
  height: 80px;
  position: fixed;
  top: 0;
  z-index: 10;
  box-shadow: 0 0 10px 1px rgba(68, 102, 242, 0.05);
  width: calc(100% - 255px);
  display: flex;
  align-items: center;
  margin-left: 255px;
  transition: 0.3s;
}
.page-wrapper .page-main-header.open {
  margin-left: 0;
  width: 100%;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-body {
  min-height: calc(100vh - 80px);
  margin-top: 80px;
  padding: 0 15px;
  position: relative;
  background-color: #f8f8f9;
}
.page-wrapper .page-body-wrapper .page-header {
  padding-top: 30px;
  padding-bottom: 30px;
}
.page-wrapper .page-body-wrapper .page-header .row {
  align-items: center;
}
.page-wrapper .page-body-wrapper .page-header .row .page-header-left {
  display: flex;
  align-items: center;
}
.page-wrapper .page-body-wrapper .page-header .row h3 {
  font-size: 24px;
  margin-bottom: 0;
  font-weight: 800;
  text-transform: uppercase;
  font-family: Nunito;
}
.page-wrapper .page-body-wrapper .page-header .row h3 small {
  display: block;
  font-size: 12px;
  margin-top: 7px;
  letter-spacing: 1px;
  text-transform: capitalize;
  color: #777777;
}
.page-wrapper .page-body-wrapper .page-header .breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: 0;
}
.page-wrapper .page-body-wrapper .page-header .breadcrumb .breadcrumb-item {
  font-family: Nunito;
}
.page-wrapper .page-body-wrapper .page-header .breadcrumb .breadcrumb-item + .breadcrumb-item:before {
  content: "/";
}
.page-wrapper .page-body-wrapper .page-header .breadcrumb .breadcrumb-item a svg {
  width: 14px;
  height: 14px;
  vertical-align: text-top;
}

/*======= Page Header css ends  ======= */
/**======Main Header css Start ======**/
.offcanvas .page-wrapper .page-body-wrapper .page-body {
  position: relative;
}
.offcanvas .page-wrapper .page-body-wrapper .page-body:before {
  position: fixed;
  content: "";
  background-color: rgba(0, 0, 0, 0.5);
  height: 100%;
  width: 100%;
  z-index: 10;
  right: 0;
  left: 255px;
}

.page-main-header {
  max-width: 100vw;
}
.page-main-header .main-header-right {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 40px;
  margin: 0;
}
.page-main-header .main-header-right svg {
  color: #5f57ea;
}
.page-main-header .main-header-right svg line,
.page-main-header .main-header-right svg polyline {
  color: #5f57ea;
}
.page-main-header .main-header-right .nav-left i {
  margin-right: 20px;
}
.page-main-header .main-header-right .nav-left input:focus {
  outline: 0 !important;
}
.page-main-header .main-header-right .nav-right {
  text-align: right;
  padding-left: 0px;
}
.page-main-header .main-header-right .nav-right ul li .media .dotted-animation {
  position: relative;
  right: -3px;
  top: -7px;
}
.page-main-header .main-header-right .nav-right ul li .media .dotted-animation .animate-circle {
  position: absolute;
  top: -20px;
  right: -4px;
  height: 25px;
  width: 25px;
  z-index: 10;
  border: 5px solid #5f57ea;
  border-radius: 70px;
  -webkit-animation: heartbit 1s ease-out;
          animation: heartbit 1s ease-out;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
}
.page-main-header .main-header-right .nav-right ul li .media .dotted-animation .main-circle {
  width: 6px;
  height: 6px;
  border-radius: 30px;
  background-color: #5f57ea;
  position: absolute;
  right: 6px;
  top: -10px;
}
.page-main-header .main-header-right .nav-right ul li svg {
  margin-top: 10px;
  width: 18px;
  height: 18px;
}
.page-main-header .main-header-right .nav-right ul li svg path {
  color: #5f57ea;
}
.page-main-header .main-header-right .nav-right ul li .dot {
  width: 3px;
  height: 3px;
  border-radius: 30px;
  background-color: #5f57ea;
  position: absolute;
  right: 17px;
  bottom: 6px;
  -webkit-animation: blink 1.5s infinite;
          animation: blink 1.5s infinite;
}
.page-main-header .main-header-right .nav-right ul li .dot-chat {
  right: 4px;
  bottom: -11px;
}
.page-main-header .main-header-right .nav-right .notification {
  position: absolute;
  top: 21px;
  right: -1px;
  font-size: 9px;
  -webkit-animation: blink 1.5s infinite;
          animation: blink 1.5s infinite;
}
@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }
  70% {
    opacity: 1;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
@keyframes blink {
  0% {
    opacity: 1;
  }
  70% {
    opacity: 1;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
.page-main-header .main-header-right .nav-right .icon-user {
  font-size: 16px;
}
@-webkit-keyframes heartbit {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  25% {
    transform: scale(0.1);
    opacity: 0.1;
  }
  50% {
    transform: scale(0.5);
    opacity: 0.3;
  }
  75% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
@keyframes heartbit {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  25% {
    transform: scale(0.1);
    opacity: 0.1;
  }
  50% {
    transform: scale(0.5);
    opacity: 0.3;
  }
  75% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
.page-main-header .main-header-right .nav-right > ul {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.page-main-header .main-header-right .nav-right > ul > li {
  position: relative;
  border-left: 1px solid #eff0f1;
  padding: 0 20px;
}
.page-main-header .main-header-right .nav-right > ul > li:first-child {
  width: 25%;
  border-left: none;
}
.page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group {
  width: 100%;
  position: relative;
}
.page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:focus {
  outline-color: transparent;
}
.page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group input:focus {
  outline-color: transparent;
}
.page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 25px;
  background: #e8ebf2;
  left: 51px;
  top: 9px;
}
.page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:after {
  position: absolute;
  content: "";
  font-family: FontAwesome;
  top: 11px;
  left: 22px;
  color: #8e8e8e;
}
.page-main-header .main-header-right .nav-right > ul > li:last-child {
  border-left: none;
  padding-right: 0;
}
.page-main-header .main-header-right .nav-right > ul > li:nth-child(5) {
  border-left: none;
  border-right: 1px solid #eff0f1;
  padding-left: 0;
}
.page-main-header .main-header-right .nav-right > ul > li h6 {
  margin-top: 4px;
  margin-bottom: 4px;
  color: #5f57ea;
}
.page-main-header .main-header-right .nav-right > ul > li h6 ul {
  left: inherit;
  right: -10px;
  width: 130px;
}
.page-main-header .main-header-right .nav-right > ul > li h6 ul:before, .page-main-header .main-header-right .nav-right > ul > li h6 ul:after {
  left: inherit;
  right: 10px;
}
.page-main-header .main-header-right .nav-right > ul > li h6 ul li {
  display: block;
}
.page-main-header .main-header-right .nav-right > ul > li h6 ul li a {
  font-size: 14px;
  color: #2a3142;
}
.page-main-header .main-header-right .nav-right > ul > li h6 ul li a i {
  margin-left: 10px;
  font-size: 13px;
  color: #2a3142;
}
.page-main-header .main-header-right .nav-right > ul .flag-icon {
  font-size: 16px;
}
.page-main-header .main-header-right .nav-right .notification-dropdown {
  top: 57px;
}
.page-main-header .main-header-right .nav-right .language-dropdown {
  width: 160px;
  text-align: left;
  top: 57px;
}
.page-main-header .main-header-right .nav-right .language-dropdown li {
  padding-top: 10px;
}
.page-main-header .main-header-right .nav-right .language-dropdown li a {
  color: #2a3142;
}
.page-main-header .main-header-right .nav-right .language-dropdown li a i {
  margin-right: 10px;
}
.page-main-header .main-header-right .nav-right .language-dropdown li:first-child {
  padding-top: 0;
}
.page-main-header .main-header-right .nav-right .profile-dropdown {
  right: -10px;
  left: inherit;
  width: 150px;
  top: 63px;
}
.page-main-header .main-header-right .nav-right .profile-dropdown:before, .page-main-header .main-header-right .nav-right .profile-dropdown:after {
  left: inherit;
  right: 10px;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li {
  display: block;
  text-align: left;
  padding-top: 10px;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li:nth-child(3) {
  padding-bottom: 10px;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li:nth-child(4) {
  border-top: 1px solid #eff0f1;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li:first-child {
  padding-top: 0;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li:hover a {
  color: #5f57ea;
  transition: 0.3s;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li:hover a svg {
  color: #5f57ea !important;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li:hover a svg path,
.page-main-header .main-header-right .nav-right .profile-dropdown li:hover a svg line,
.page-main-header .main-header-right .nav-right .profile-dropdown li:hover a svg polyline {
  color: #5f57ea !important;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li a {
  color: #313131;
  transition: 0.3s;
  display: flex;
  align-items: center;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li a svg {
  margin-right: 10px;
  color: #313131;
}
.page-main-header .main-header-right .nav-right .profile-dropdown li a svg polyline {
  color: #313131;
}
.page-main-header .main-header-right li {
  display: inline-block;
  position: relative;
}

.nav-menus .notification-badge {
  position: absolute;
  right: 10px;
  top: 1px;
  padding: 4px 7px;
}
.nav-menus .onhover-dropdown {
  cursor: pointer;
  position: relative;
}
.nav-menus .onhover-dropdown:before {
  display: none;
}
ul.notification-dropdown.onhover-show-div {
  width: 330px;
  right: -18px;
  left: initial;
}
ul.notification-dropdown.onhover-show-div:before, ul.notification-dropdown.onhover-show-div:after {
  left: inherit !important;
  right: 35px !important;
}
ul.notification-dropdown.onhover-show-div li {
  display: block;
  padding: 12px 20px;
  border-bottom: 1px solid #f8f8f9;
  text-align: left;
}
ul.notification-dropdown.onhover-show-div li h6 small {
  padding-top: 5px;
  color: #898989;
  font-size: 12px;
}
ul.notification-dropdown.onhover-show-div li span svg {
  margin-top: 0 !important;
  margin-right: 10px;
  vertical-align: text-top;
}
ul.notification-dropdown.onhover-show-div li span .shopping-color path,
ul.notification-dropdown.onhover-show-div li span .shopping-color line {
  color: #5f57ea;
}
ul.notification-dropdown.onhover-show-div li span .download-color path,
ul.notification-dropdown.onhover-show-div li span .download-color line {
  color: #81ba00;
}
ul.notification-dropdown.onhover-show-div li span .alert-color path,
ul.notification-dropdown.onhover-show-div li span .alert-color line {
  color: #dc3545;
}
ul.notification-dropdown.onhover-show-div li p {
  margin-left: 30px;
}
ul.notification-dropdown.onhover-show-div li + li:hover {
  background-color: #f8f8f9;
}

.onhover-show-div {
  top: 80px;
  position: absolute;
  z-index: 8;
  background-color: #ffffff;
  transition: all linear 0.3s;
}
.onhover-show-div li a svg {
  margin-top: 0 !important;
}
.onhover-show-div li a svg path,
.onhover-show-div li a svg line {
  color: #313131 !important;
}

/**======Main Header css Ends ======**/
.nav-menus .search-form input {
  border: 1px solid #eff0f1;
  padding: 10px 10px 10px 70px;
  border-radius: 50px;
  background-color: #f8f8f9;
}

/**=====================
    Sidebar CSS Start
==========================**/
.page-wrapper .page-body-wrapper .sidebar {
  height: calc(100vh - 80px);
  overflow: auto;
  box-shadow: 0 0 11px rgba(143, 164, 232, 0.08);
}
.page-wrapper .page-body-wrapper .page-sidebar {
  width: 255px;
  position: fixed;
  background: #ffffff;
  top: 0;
  height: calc(100vh);
  z-index: 9;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .main-header-left {
  display: inline-flex;
  width: 100%;
  height: 80px;
  padding: 12px;
  align-items: center;
  background-color: transparent;
  z-index: 10;
  box-shadow: -3px 1px 3px 1px rgba(68, 102, 242, 0.1);
}
.page-wrapper .page-body-wrapper .page-sidebar .main-header-left .logo-wrapper {
  padding-left: 10px;
  height: 100%;
  display: flex;
  align-items: center;
}
.page-wrapper .page-body-wrapper .page-sidebar .main-header-left .logo-wrapper a {
  height: 53px;
  width: 230px;
}
.page-wrapper .page-body-wrapper .page-sidebar .main-header-left .logo-wrapper a img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-user img {
  width: 50px;
  height: 50px;
  -o-object-fit: contain;
     object-fit: contain;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-user {
  padding: 25px 10px;
  box-shadow: 3px 2px 7px -1px rgba(127, 151, 249, 0.13);
  position: relative;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-user h6 {
  color: #5f57ea;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 1.5px;
  margin-bottom: 3px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-user p {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 10px;
  margin-bottom: 0px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-user img {
  box-shadow: 0 0 15px rgba(68, 102, 242, 0.3);
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 20px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-header {
  font-size: 14px;
  letter-spacing: 0.5px;
  padding-bottom: 12px;
  padding-top: 12px;
  text-transform: capitalize;
  font-weight: 600;
  color: #313131;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-header svg {
  width: 14px;
  height: 14px;
  margin-right: 14px;
  stroke-width: 3px;
  vertical-align: text-bottom;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu.menu-open {
  display: block;
  transform: rotateX(0deg);
  transform-origin: 10% 10%;
  transition: transform 0.3s, opacity 0.3s;
  transform-style: preserve-3d;
  margin-left: 15px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu.menu-open li.active .fa-angle-down:before {
  content: "";
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li > a {
  display: block;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li > a.active {
  color: #5f57ea;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li > a:hover {
  color: #5f57ea;
  padding-left: 3px;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li > a i {
  margin-right: 7px;
  text-align: right;
  margin-top: 3px;
  font-size: 15px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li > a i:before {
  content: "";
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li > a i ~ i {
  margin-right: 0;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li .label {
  margin-top: 3px;
  margin-right: 5px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu > li .badge {
  margin-left: 50px;
  text-transform: capitalize;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li.sidebar-header {
  margin-bottom: 0;
  padding: 15px;
  color: #313131;
  font-weight: 600;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li > a > .fa-angle-down {
  width: auto;
  height: auto;
  padding: 0;
  margin-right: 10px;
  margin-top: 10px;
  transform: rotate(-90deg);
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li.active > a > .fa-angle-right:before {
  content: "";
  font-family: FontAwesome;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li.active > .sidebar-submenu {
  display: block;
  transform: rotateX(0deg);
  transform-origin: 10% 10%;
  transition: transform 0.3s, opacity 0.3s;
  transform-style: preserve-3d;
  margin-left: 15px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu li.active > .sidebar-submenu a i.pull-right {
  margin-top: 10px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu a {
  text-decoration: none;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu {
  display: none;
  transform-style: preserve-3d;
  transform: rotateX(-90deg);
  list-style: none;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu .sidebar-submenu {
  padding-top: 0 !important;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a {
  padding-bottom: 7px;
  padding-top: 7px;
  font-size: 14px;
  color: #898989;
  transition: 0.3s;
  text-transform: capitalize;
  position: relative;
  padding-left: 10px;
  line-height: 2.5;
  letter-spacing: 0.7px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a > .fa-circle {
  width: 12px;
  font-size: 4px;
  position: absolute;
  left: 0;
  top: 13px;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a > i {
  width: auto;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a > .fa-angle-down {
  width: auto;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a:hover {
  color: #5f57ea;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li > a.active {
  color: #5f57ea;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li.active > a {
  color: #313131;
}
.page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li.active > a.active {
  color: #5f57ea;
}
.page-wrapper .page-body-wrapper .page-sidebar ~ .page-body {
  margin-left: 255px;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar.open {
  display: block;
  margin-left: calc(-260px);
}
.page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body {
  margin-left: 0;
  transition: 0.3s;
}
.page-wrapper .page-body-wrapper .page-sidebar.open ~ footer {
  margin-left: 0;
  padding-right: 15px;
}
.page-wrapper .page-body-wrapper .page-sidebar.open ~ .footer-fix {
  width: calc(100% - 0px);
}
.page-wrapper .page-body-wrapper .sidebar-close .page-sidebar {
  transition: 0.3s;
  transform: translate(-255px);
}
.page-wrapper .page-body-wrapper .sidebar-close .page-body {
  transition: 0.3s;
  margin-left: 0 !important;
}

.right-sidebar {
  top: 81px;
  right: -285px;
  height: 100%;
  position: fixed;
  width: 285px;
  z-index: 9;
  background-color: #fff;
  transition: 0.5s;
  box-shadow: 0 0 9px rgba(191, 191, 191, 0.36);
}
.right-sidebar.show {
  right: 0;
  transition: 0.3s;
}
.right-sidebar .modal-header .modal-title {
  padding-top: 2px;
}
.right-sidebar .friend-list-search {
  position: relative;
  background-color: #f8f8f9;
  padding: 20px;
}
.right-sidebar .friend-list-search input {
  color: #898989;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #f8f8f9;
  padding: 10px 15px;
  border-radius: 25px;
  letter-spacing: 1px;
}
.right-sidebar .friend-list-search .fa {
  position: absolute;
  right: 35px;
  top: 34px;
  font-size: 14px;
  color: #e8ebf2;
}
.right-sidebar .chat-box .people-list ul {
  padding-top: 20px;
}
.right-sidebar .chat-box .people-list ul li {
  position: relative;
}
.right-sidebar svg {
  width: 16px;
  height: 16px;
  margin-top: 5px;
}

/**=====================
    Sidebar CSS Ends
==========================**/
/**=====================
      Generic CSS Start
==========================**/
/*====== Padding css starts ======*/
.p-0 {
  padding: 0px;
}

.p-5 {
  padding: 5px;
}

.p-10 {
  padding: 10px;
}

.p-15 {
  padding: 15px;
}

.p-20 {
  padding: 20px;
}

.p-25 {
  padding: 25px;
}

.p-30 {
  padding: 30px;
}

.p-35 {
  padding: 35px;
}

.p-40 {
  padding: 40px;
}

.p-45 {
  padding: 45px;
}

.p-50 {
  padding: 50px;
}

/*====== Padding css ends ======*/
/*====== Padding-left css starts ======*/
.p-l-0 {
  padding-left: 0px;
}

.p-l-5 {
  padding-left: 5px;
}

.p-l-10 {
  padding-left: 10px;
}

.p-l-15 {
  padding-left: 15px;
}

.p-l-20 {
  padding-left: 20px;
}

.p-l-25 {
  padding-left: 25px;
}

.p-l-30 {
  padding-left: 30px;
}

.p-l-35 {
  padding-left: 35px;
}

.p-l-40 {
  padding-left: 40px;
}

.p-l-45 {
  padding-left: 45px;
}

.p-l-50 {
  padding-left: 50px;
}

/*====== Padding-left css ends ======*/
/*====== Padding-top css starts ======*/
.p-t-0 {
  padding-top: 0px !important;
}

.p-t-5 {
  padding-top: 5px !important;
}

.p-t-10 {
  padding-top: 10px !important;
}

.p-t-15 {
  padding-top: 15px !important;
}

.p-t-20 {
  padding-top: 20px !important;
}

.p-t-25 {
  padding-top: 25px !important;
}

.p-t-30 {
  padding-top: 30px !important;
}

.p-t-35 {
  padding-top: 35px !important;
}

.p-t-40 {
  padding-top: 40px !important;
}

.p-t-45 {
  padding-top: 45px !important;
}

.p-t-50 {
  padding-top: 50px !important;
}

/*====== Padding-top css ends ======*/
/*====== Padding-bottom css starts ======*/
.p-b-0 {
  padding-bottom: 0px !important;
}

.p-b-5 {
  padding-bottom: 5px !important;
}

.p-b-10 {
  padding-bottom: 10px !important;
}

.p-b-15 {
  padding-bottom: 15px !important;
}

.p-b-20 {
  padding-bottom: 20px !important;
}

.p-b-25 {
  padding-bottom: 25px !important;
}

.p-b-30 {
  padding-bottom: 30px !important;
}

.p-b-35 {
  padding-bottom: 35px !important;
}

.p-b-40 {
  padding-bottom: 40px !important;
}

.p-b-45 {
  padding-bottom: 45px !important;
}

.p-b-50 {
  padding-bottom: 50px !important;
}

/*====== Padding-bottom css ends ======*/
/*====== Padding-right css starts ======*/
.p-r-0 {
  padding-right: 0px;
}

.p-r-5 {
  padding-right: 5px;
}

.p-r-10 {
  padding-right: 10px;
}

.p-r-15 {
  padding-right: 15px;
}

.p-r-20 {
  padding-right: 20px;
}

.p-r-25 {
  padding-right: 25px;
}

.p-r-30 {
  padding-right: 30px;
}

.p-r-35 {
  padding-right: 35px;
}

.p-r-40 {
  padding-right: 40px;
}

.p-r-45 {
  padding-right: 45px;
}

.p-r-50 {
  padding-right: 50px;
}

/*====== Padding-right css ends ======*/
/*====== Margin css starts ======*/
.m-0 {
  margin: 0px !important;
}

.m-5 {
  margin: 5px !important;
}

.m-10 {
  margin: 10px !important;
}

.m-15 {
  margin: 15px !important;
}

.m-20 {
  margin: 20px !important;
}

.m-25 {
  margin: 25px !important;
}

.m-30 {
  margin: 30px !important;
}

.m-35 {
  margin: 35px !important;
}

.m-40 {
  margin: 40px !important;
}

.m-45 {
  margin: 45px !important;
}

.m-50 {
  margin: 50px !important;
}

/*====== Margin css ends ======*/
/*====== Margin-top css starts ======*/
.m-t-0 {
  margin-top: 0px !important;
}

.m-t-5 {
  margin-top: 5px !important;
}

.m-t-10 {
  margin-top: 10px !important;
}

.m-t-15 {
  margin-top: 15px !important;
}

.m-t-20 {
  margin-top: 20px !important;
}

.m-t-25 {
  margin-top: 25px !important;
}

.m-t-30 {
  margin-top: 30px !important;
}

.m-t-35 {
  margin-top: 35px !important;
}

.m-t-40 {
  margin-top: 40px !important;
}

.m-t-45 {
  margin-top: 45px !important;
}

.m-t-50 {
  margin-top: 50px !important;
}

/*====== Margin-top css ends ======*/
/*====== Margin-Bottom css starts ======*/
.m-b-0 {
  margin-bottom: 0px !important;
}

.m-b-5 {
  margin-bottom: 5px !important;
}

.m-b-10 {
  margin-bottom: 10px !important;
}

.m-b-15 {
  margin-bottom: 15px !important;
}

.m-b-20 {
  margin-bottom: 20px !important;
}

.m-b-25 {
  margin-bottom: 25px !important;
}

.m-b-30 {
  margin-bottom: 30px !important;
}

.m-b-35 {
  margin-bottom: 35px !important;
}

.m-b-40 {
  margin-bottom: 40px !important;
}

.m-b-45 {
  margin-bottom: 45px !important;
}

.m-b-50 {
  margin-bottom: 50px !important;
}

/*====== Margin-Bottom css ends ======*/
/*====== Margin-left css starts ======*/
.m-l-0 {
  margin-left: 0px !important;
}

.m-l-5 {
  margin-left: 5px !important;
}

.m-l-10 {
  margin-left: 10px !important;
}

.m-l-15 {
  margin-left: 15px !important;
}

.m-l-20 {
  margin-left: 20px !important;
}

.m-l-25 {
  margin-left: 25px !important;
}

.m-l-30 {
  margin-left: 30px !important;
}

.m-l-35 {
  margin-left: 35px !important;
}

.m-l-40 {
  margin-left: 40px !important;
}

.m-l-45 {
  margin-left: 45px !important;
}

.m-l-50 {
  margin-left: 50px !important;
}

/*====== Margin-left css ends ======*/
/*====== Margin-right css starts ======*/
.m-r-0 {
  margin-right: 0px;
}

.m-r-5 {
  margin-right: 5px;
}

.m-r-10 {
  margin-right: 10px;
}

.m-r-15 {
  margin-right: 15px;
}

.m-r-20 {
  margin-right: 20px;
}

.m-r-25 {
  margin-right: 25px;
}

.m-r-30 {
  margin-right: 30px;
}

.m-r-35 {
  margin-right: 35px;
}

.m-r-40 {
  margin-right: 40px;
}

.m-r-45 {
  margin-right: 45px;
}

.m-r-50 {
  margin-right: 50px;
}

/*====== Margin-right css ends ======*/
/*====== Border-radius css starts ======*/
.b-r-0 {
  border-radius: 0px !important;
}

.b-r-1 {
  border-radius: 1px !important;
}

.b-r-2 {
  border-radius: 2px !important;
}

.b-r-3 {
  border-radius: 3px !important;
}

.b-r-4 {
  border-radius: 4px !important;
}

.b-r-5 {
  border-radius: 5px !important;
}

.b-r-6 {
  border-radius: 6px !important;
}

.b-r-7 {
  border-radius: 7px !important;
}

.b-r-8 {
  border-radius: 8px !important;
}

.b-r-9 {
  border-radius: 9px !important;
}

.b-r-10 {
  border-radius: 10px !important;
}

/*====== Border-radius css ends ======*/
/*====== Font-size css starts ======*/
.f-12 {
  font-size: 12px;
}

.f-14 {
  font-size: 14px;
}

.f-16 {
  font-size: 16px;
}

.f-18 {
  font-size: 18px;
}

.f-20 {
  font-size: 20px;
}

.f-22 {
  font-size: 22px;
}

.f-24 {
  font-size: 24px;
}

.f-26 {
  font-size: 26px;
}

.f-28 {
  font-size: 28px;
}

.f-30 {
  font-size: 30px;
}

.f-32 {
  font-size: 32px;
}

.f-34 {
  font-size: 34px;
}

.f-36 {
  font-size: 36px;
}

.f-38 {
  font-size: 38px;
}

.f-40 {
  font-size: 40px;
}

.f-42 {
  font-size: 42px;
}

.f-44 {
  font-size: 44px;
}

.f-46 {
  font-size: 46px;
}

.f-48 {
  font-size: 48px;
}

.f-50 {
  font-size: 50px;
}

.f-52 {
  font-size: 52px;
}

.f-54 {
  font-size: 54px;
}

.f-56 {
  font-size: 56px;
}

.f-58 {
  font-size: 58px;
}

.f-60 {
  font-size: 60px;
}

.f-62 {
  font-size: 62px;
}

.f-64 {
  font-size: 64px;
}

.f-66 {
  font-size: 66px;
}

.f-68 {
  font-size: 68px;
}

.f-70 {
  font-size: 70px;
}

.f-72 {
  font-size: 72px;
}

.f-74 {
  font-size: 74px;
}

.f-76 {
  font-size: 76px;
}

.f-78 {
  font-size: 78px;
}

.f-80 {
  font-size: 80px;
}

.f-82 {
  font-size: 82px;
}

.f-84 {
  font-size: 84px;
}

.f-86 {
  font-size: 86px;
}

.f-88 {
  font-size: 88px;
}

.f-90 {
  font-size: 90px;
}

.f-92 {
  font-size: 92px;
}

.f-94 {
  font-size: 94px;
}

.f-96 {
  font-size: 96px;
}

.f-98 {
  font-size: 98px;
}

.f-100 {
  font-size: 100px;
}

/*====== Font-size css ends ======*/
/*====== Font-weight css starts ======*/
.f-w-100 {
  font-weight: 100;
}

.f-w-300 {
  font-weight: 300;
}

.f-w-400 {
  font-weight: 400;
}

.f-w-600 {
  font-weight: 600;
}

.f-w-700 {
  font-weight: 700;
}

.f-w-900 {
  font-weight: 900;
}

/*====== Font-weight css ends ======*/
/*====== Font-style css starts ======*/
.f-s-normal {
  font-style: normal;
}

.f-s-italic {
  font-style: italic;
}

.f-s-oblique {
  font-style: oblique;
}

.f-s-initial {
  font-style: initial;
}

.f-s-inherit {
  font-style: inherit;
}

/*====== Font-style css ends ======*/
/*====== Text-Decoration css starts ======*/
.text-overline {
  text-decoration: overline;
}

.text-line-through {
  text-decoration: line-through;
}

.text-underline {
  text-decoration: underline;
}

.text-dashed {
  -webkit-text-decoration: dashed;
          text-decoration: dashed;
}

.text-blink {
  text-decoration: blink;
}

.text-dotted {
  -webkit-text-decoration: dotted;
          text-decoration: dotted;
}

.text-initial {
  text-decoration: initial;
}

.text-none {
  text-decoration: none;
}

.text-solid {
  -webkit-text-decoration: solid;
          text-decoration: solid;
}

.text-wavy {
  -webkit-text-decoration: wavy;
          text-decoration: wavy;
}

.text-inherit {
  text-decoration: inherit;
}

.text-double {
  -webkit-text-decoration: double;
          text-decoration: double;
}

/*====== Text-Decoration css ends ======*/
/*====== Vertical-Align css starts ======*/
.baseline {
  vertical-align: baseline;
}

.sub {
  vertical-align: sub;
}

.super {
  vertical-align: super;
}

.top {
  vertical-align: top;
}

.text-top {
  vertical-align: text-top;
}

.middle {
  vertical-align: middle;
}

.bottom {
  vertical-align: bottom;
}

.text-bottom {
  vertical-align: text-bottom;
}

.initial {
  vertical-align: initial;
}

.inherit {
  vertical-align: inherit;
}

/*====== Vertical-Align css ends ======*/
/*====== Position css starts ======*/
.p-static {
  position: static;
}

.p-absolute {
  position: absolute;
}

.p-fixed {
  position: fixed;
}

.p-relative {
  position: relative;
}

.p-initial {
  position: initial;
}

.p-inherit {
  position: inherit;
}

/*====== Position css ends ======*/
/*====== Float css starts ======*/
.f-left {
  float: left;
}

.f-right {
  float: right;
}

.f-none {
  float: none;
}

/*====== Float css ends ======*/
/*====== Overflow css starts ======*/
.o-hidden {
  overflow: hidden;
}

.o-visible {
  overflow: visible;
}

.o-auto {
  overflow: auto;
}

/*====== Overflow css ends ======*/
/*====== Image-sizes css starts ======*/
.img-10 {
  width: 10px !important;
}

.img-20 {
  width: 20px !important;
}

.img-30 {
  width: 30px !important;
}

.img-40 {
  width: 40px !important;
}

.img-50 {
  width: 50px !important;
}

.img-60 {
  width: 60px !important;
}

.img-70 {
  width: 70px !important;
}

.img-80 {
  width: 80px !important;
}

.img-90 {
  width: 90px !important;
}

.img-100 {
  width: 100px !important;
}

/*====== Image-sizes css ends ======*/
/*======= Text css starts ===========*/
.font-primary {
  color: #5f57ea !important;
}

.font-secondary {
  color: #13c9ca !important;
}

.font-success {
  color: #81ba00 !important;
}

.font-danger {
  color: #dc3545 !important;
}

.font-info {
  color: #00a8ff !important;
}

.font-light {
  color: #f8f8f9 !important;
}

.font-dark {
  color: #2a3142 !important;
}

.font-warning {
  color: #ffbc58 !important;
}

/*======= Text css ends ===========*/
/*======= Label-color css starts  ======= */
.label {
  border-radius: 2px;
  color: #ffffff;
  font-size: 12px;
  line-height: 1;
  margin-bottom: 0;
  text-transform: capitalize;
}

.label-theme {
  background-color: #5f57ea;
}

.label-primary {
  background-color: #5f57ea;
}

.label-secondary {
  background-color: #13c9ca;
}

.label-success {
  background-color: #81ba00;
}

.label-danger {
  background-color: #dc3545;
}

.label-info {
  background-color: #00a8ff;
}

.label-light {
  background-color: #f8f8f9;
}

.label-dark {
  background-color: #2a3142;
}

.label-warning {
  background-color: #ffbc58;
}

/*======= Label-color css ends  ======= */
/*======= Badge-color css starts  ======= */
.badge-primary {
  background-color: #5f57ea;
}

.badge-secondary {
  background-color: #13c9ca;
}

.badge-success {
  background-color: #81ba00;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-info {
  background-color: #00a8ff;
}

.badge-light {
  background-color: #f8f8f9;
}

.badge-dark {
  background-color: #2a3142;
}

.badge-warning {
  background-color: #ffbc58;
}

/*======= Badge-color css end  ======= */
/*======= Background-color css starts  ======= */
.bg-primary {
  background-color: #5f57ea !important;
  color: #ffffff;
}

.bg-secondary {
  background-color: #13c9ca !important;
  color: #ffffff;
}

.bg-success {
  background-color: #81ba00 !important;
  color: #ffffff;
}

.bg-danger {
  background-color: #dc3545 !important;
  color: #ffffff;
}

.bg-info {
  background-color: #00a8ff !important;
  color: #ffffff;
}

.bg-light {
  background-color: #f8f8f9 !important;
  color: #ffffff;
}

.bg-dark {
  background-color: #2a3142 !important;
  color: #ffffff;
}

.bg-warning {
  background-color: #ffbc58 !important;
  color: #ffffff;
}

/*======= Background-color css end  ======= */
/*======= Font-color css starts  ======= */
/*======= Font-color css end  ======= */
/*======= Button-color css starts  ======= */
.btn-primary {
  background-color: #5f57ea !important;
  border-color: #5f57ea !important;
}
.btn-primary.disabled {
  background-color: #5f57ea !important;
  border-color: #5f57ea !important;
}
.btn-primary:disabled {
  background-color: #5f57ea !important;
  border-color: #5f57ea !important;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active {
  background-color: #342ae4 !important;
  border-color: #342ae4 !important;
}
.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem #e1dffb;
}

.btn-secondary {
  background-color: #13c9ca !important;
  border-color: #13c9ca !important;
}
.btn-secondary.disabled {
  background-color: #13c9ca !important;
  border-color: #13c9ca !important;
}
.btn-secondary:disabled {
  background-color: #13c9ca !important;
  border-color: #13c9ca !important;
}
.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active, .btn-secondary.active {
  background-color: #0f9b9b !important;
  border-color: #0f9b9b !important;
}
.btn-secondary:focus {
  box-shadow: 0 0 0 0.2rem #83f3f3;
}

.btn-success {
  background-color: #81ba00 !important;
  border-color: #81ba00 !important;
}
.btn-success.disabled {
  background-color: #81ba00 !important;
  border-color: #81ba00 !important;
}
.btn-success:disabled {
  background-color: #81ba00 !important;
  border-color: #81ba00 !important;
}
.btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active {
  background-color: #5e8700 !important;
  border-color: #5e8700 !important;
}
.btn-success:focus {
  box-shadow: 0 0 0 0.2rem #cbff54;
}

.btn-danger {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}
.btn-danger.disabled {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}
.btn-danger:disabled {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}
.btn-danger:hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active {
  background-color: #bd2130 !important;
  border-color: #bd2130 !important;
}
.btn-danger:focus {
  box-shadow: 0 0 0 0.2rem #f3b7bd;
}

.btn-info {
  background-color: #00a8ff !important;
  border-color: #00a8ff !important;
}
.btn-info.disabled {
  background-color: #00a8ff !important;
  border-color: #00a8ff !important;
}
.btn-info:disabled {
  background-color: #00a8ff !important;
  border-color: #00a8ff !important;
}
.btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active {
  background-color: #0086cc !important;
  border-color: #0086cc !important;
}
.btn-info:focus {
  box-shadow: 0 0 0 0.2rem #99dcff;
}

.btn-light {
  background-color: #f8f8f9 !important;
  border-color: #f8f8f9 !important;
}
.btn-light.disabled {
  background-color: #f8f8f9 !important;
  border-color: #f8f8f9 !important;
}
.btn-light:disabled {
  background-color: #f8f8f9 !important;
  border-color: #f8f8f9 !important;
}
.btn-light:hover, .btn-light:focus, .btn-light:active, .btn-light.active {
  background-color: #dddde1 !important;
  border-color: #dddde1 !important;
}
.btn-light:focus {
  box-shadow: 0 0 0 0.2rem white;
}

.btn-dark {
  background-color: #2a3142 !important;
  border-color: #2a3142 !important;
}
.btn-dark.disabled {
  background-color: #2a3142 !important;
  border-color: #2a3142 !important;
}
.btn-dark:disabled {
  background-color: #2a3142 !important;
  border-color: #2a3142 !important;
}
.btn-dark:hover, .btn-dark:focus, .btn-dark:active, .btn-dark.active {
  background-color: #161a23 !important;
  border-color: #161a23 !important;
}
.btn-dark:focus {
  box-shadow: 0 0 0 0.2rem #67779e;
}

.btn-warning {
  background-color: #ffbc58 !important;
  border-color: #ffbc58 !important;
}
.btn-warning.disabled {
  background-color: #ffbc58 !important;
  border-color: #ffbc58 !important;
}
.btn-warning:disabled {
  background-color: #ffbc58 !important;
  border-color: #ffbc58 !important;
}
.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active {
  background-color: #ffa825 !important;
  border-color: #ffa825 !important;
}
.btn-warning:focus {
  box-shadow: 0 0 0 0.2rem #fff9f1;
}

/*======= Button-color css ends  ======= */
.btn-outline-primary-2x {
  border-width: 2px;
  border-color: #5f57ea;
  color: #5f57ea;
  background-color: transparent;
}
.btn-outline-primary-2x:hover, .btn-outline-primary-2x:focus, .btn-outline-primary-2x:active, .btn-outline-primary-2x.active {
  color: white;
  background-color: #342ae4 !important;
  border-color: #342ae4 !important;
  box-shadow: none;
}

.btn-outline-secondary-2x {
  border-width: 2px;
  border-color: #13c9ca;
  color: #13c9ca;
  background-color: transparent;
}
.btn-outline-secondary-2x:hover, .btn-outline-secondary-2x:focus, .btn-outline-secondary-2x:active, .btn-outline-secondary-2x.active {
  color: white;
  background-color: #0f9b9b !important;
  border-color: #0f9b9b !important;
  box-shadow: none;
}

.btn-outline-success-2x {
  border-width: 2px;
  border-color: #81ba00;
  color: #81ba00;
  background-color: transparent;
}
.btn-outline-success-2x:hover, .btn-outline-success-2x:focus, .btn-outline-success-2x:active, .btn-outline-success-2x.active {
  color: white;
  background-color: #5e8700 !important;
  border-color: #5e8700 !important;
  box-shadow: none;
}

.btn-outline-danger-2x {
  border-width: 2px;
  border-color: #dc3545;
  color: #dc3545;
  background-color: transparent;
}
.btn-outline-danger-2x:hover, .btn-outline-danger-2x:focus, .btn-outline-danger-2x:active, .btn-outline-danger-2x.active {
  color: white;
  background-color: #bd2130 !important;
  border-color: #bd2130 !important;
  box-shadow: none;
}

.btn-outline-info-2x {
  border-width: 2px;
  border-color: #00a8ff;
  color: #00a8ff;
  background-color: transparent;
}
.btn-outline-info-2x:hover, .btn-outline-info-2x:focus, .btn-outline-info-2x:active, .btn-outline-info-2x.active {
  color: white;
  background-color: #0086cc !important;
  border-color: #0086cc !important;
  box-shadow: none;
}

.btn-outline-light-2x {
  border-width: 2px;
  border-color: #f8f8f9;
  color: #f8f8f9;
  background-color: transparent;
}
.btn-outline-light-2x:hover, .btn-outline-light-2x:focus, .btn-outline-light-2x:active, .btn-outline-light-2x.active {
  color: white;
  background-color: #dddde1 !important;
  border-color: #dddde1 !important;
  box-shadow: none;
}

.btn-outline-dark-2x {
  border-width: 2px;
  border-color: #2a3142;
  color: #2a3142;
  background-color: transparent;
}
.btn-outline-dark-2x:hover, .btn-outline-dark-2x:focus, .btn-outline-dark-2x:active, .btn-outline-dark-2x.active {
  color: white;
  background-color: #161a23 !important;
  border-color: #161a23 !important;
  box-shadow: none;
}

.btn-outline-warning-2x {
  border-width: 2px;
  border-color: #ffbc58;
  color: #ffbc58;
  background-color: transparent;
}
.btn-outline-warning-2x:hover, .btn-outline-warning-2x:focus, .btn-outline-warning-2x:active, .btn-outline-warning-2x.active {
  color: white;
  background-color: #ffa825 !important;
  border-color: #ffa825 !important;
  box-shadow: none;
}

.btn-outline-primary {
  border-color: #5f57ea;
  color: #5f57ea;
  background-color: transparent;
}
.btn-outline-primary.disabled {
  color: #5f57ea;
}
.btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active, .btn-outline-primary.active {
  color: white;
  background-color: #342ae4 !important;
  border-color: #342ae4 !important;
}

.btn-outline-secondary {
  border-color: #13c9ca;
  color: #13c9ca;
  background-color: transparent;
}
.btn-outline-secondary.disabled {
  color: #13c9ca;
}
.btn-outline-secondary:hover, .btn-outline-secondary:focus, .btn-outline-secondary:active, .btn-outline-secondary.active {
  color: white;
  background-color: #0f9b9b !important;
  border-color: #0f9b9b !important;
}

.btn-outline-success {
  border-color: #81ba00;
  color: #81ba00;
  background-color: transparent;
}
.btn-outline-success.disabled {
  color: #81ba00;
}
.btn-outline-success:hover, .btn-outline-success:focus, .btn-outline-success:active, .btn-outline-success.active {
  color: white;
  background-color: #5e8700 !important;
  border-color: #5e8700 !important;
}

.btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;
  background-color: transparent;
}
.btn-outline-danger.disabled {
  color: #dc3545;
}
.btn-outline-danger:hover, .btn-outline-danger:focus, .btn-outline-danger:active, .btn-outline-danger.active {
  color: white;
  background-color: #bd2130 !important;
  border-color: #bd2130 !important;
}

.btn-outline-info {
  border-color: #00a8ff;
  color: #00a8ff;
  background-color: transparent;
}
.btn-outline-info.disabled {
  color: #00a8ff;
}
.btn-outline-info:hover, .btn-outline-info:focus, .btn-outline-info:active, .btn-outline-info.active {
  color: white;
  background-color: #0086cc !important;
  border-color: #0086cc !important;
}

.btn-outline-light {
  border-color: #f8f8f9;
  color: #f8f8f9;
  background-color: transparent;
  color: #2a3142;
}
.btn-outline-light.disabled {
  color: #f8f8f9;
}
.btn-outline-light:hover, .btn-outline-light:focus, .btn-outline-light:active, .btn-outline-light.active {
  color: white;
  background-color: #dddde1 !important;
  border-color: #dddde1 !important;
}

.btn-outline-dark {
  border-color: #2a3142;
  color: #2a3142;
  background-color: transparent;
}
.btn-outline-dark.disabled {
  color: #2a3142;
}
.btn-outline-dark:hover, .btn-outline-dark:focus, .btn-outline-dark:active, .btn-outline-dark.active {
  color: white;
  background-color: #161a23 !important;
  border-color: #161a23 !important;
}

.btn-outline-warning {
  border-color: #ffbc58;
  color: #ffbc58;
  background-color: transparent;
}
.btn-outline-warning.disabled {
  color: #ffbc58;
}
.btn-outline-warning:hover, .btn-outline-warning:focus, .btn-outline-warning:active, .btn-outline-warning.active {
  color: white;
  background-color: #ffa825 !important;
  border-color: #ffa825 !important;
}

.btn-primary:not([disabled]):not(.disabled).active {
  background-color: #342ae4;
  border-color: #342ae4;
  box-shadow: none !important;
}
.btn-primary:not([disabled]):not(.disabled).active:hover, .btn-primary:not([disabled]):not(.disabled).active:focus, .btn-primary:not([disabled]):not(.disabled).active:active, .btn-primary:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #342ae4;
  border-color: #342ae4;
}

.btn-secondary:not([disabled]):not(.disabled).active {
  background-color: #0f9b9b;
  border-color: #0f9b9b;
  box-shadow: none !important;
}
.btn-secondary:not([disabled]):not(.disabled).active:hover, .btn-secondary:not([disabled]):not(.disabled).active:focus, .btn-secondary:not([disabled]):not(.disabled).active:active, .btn-secondary:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #0f9b9b;
  border-color: #0f9b9b;
}

.btn-success:not([disabled]):not(.disabled).active {
  background-color: #5e8700;
  border-color: #5e8700;
  box-shadow: none !important;
}
.btn-success:not([disabled]):not(.disabled).active:hover, .btn-success:not([disabled]):not(.disabled).active:focus, .btn-success:not([disabled]):not(.disabled).active:active, .btn-success:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #5e8700;
  border-color: #5e8700;
}

.btn-danger:not([disabled]):not(.disabled).active {
  background-color: #bd2130;
  border-color: #bd2130;
  box-shadow: none !important;
}
.btn-danger:not([disabled]):not(.disabled).active:hover, .btn-danger:not([disabled]):not(.disabled).active:focus, .btn-danger:not([disabled]):not(.disabled).active:active, .btn-danger:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #bd2130;
  border-color: #bd2130;
}

.btn-info:not([disabled]):not(.disabled).active {
  background-color: #0086cc;
  border-color: #0086cc;
  box-shadow: none !important;
}
.btn-info:not([disabled]):not(.disabled).active:hover, .btn-info:not([disabled]):not(.disabled).active:focus, .btn-info:not([disabled]):not(.disabled).active:active, .btn-info:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #0086cc;
  border-color: #0086cc;
}

.btn-light:not([disabled]):not(.disabled).active {
  background-color: #dddde1;
  border-color: #dddde1;
  box-shadow: none !important;
}
.btn-light:not([disabled]):not(.disabled).active:hover, .btn-light:not([disabled]):not(.disabled).active:focus, .btn-light:not([disabled]):not(.disabled).active:active, .btn-light:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #dddde1;
  border-color: #dddde1;
}

.btn-dark:not([disabled]):not(.disabled).active {
  background-color: #161a23;
  border-color: #161a23;
  box-shadow: none !important;
}
.btn-dark:not([disabled]):not(.disabled).active:hover, .btn-dark:not([disabled]):not(.disabled).active:focus, .btn-dark:not([disabled]):not(.disabled).active:active, .btn-dark:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #161a23;
  border-color: #161a23;
}

.btn-warning:not([disabled]):not(.disabled).active {
  background-color: #ffa825;
  border-color: #ffa825;
  box-shadow: none !important;
}
.btn-warning:not([disabled]):not(.disabled).active:hover, .btn-warning:not([disabled]):not(.disabled).active:focus, .btn-warning:not([disabled]):not(.disabled).active:active, .btn-warning:not([disabled]):not(.disabled).active.active {
  color: white;
  background-color: #ffa825;
  border-color: #ffa825;
}

.btn-outline-primary-2x:not([disabled]):not(.disabled).active {
  background-color: #5f57ea;
  border-color: #5f57ea;
  box-shadow: none !important;
  color: white;
}
.btn-outline-primary-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-primary-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-primary-2x:not([disabled]):not(.disabled).active:active, .btn-outline-primary-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #342ae4 !important;
  border-color: #342ae4 !important;
}

.btn-outline-secondary-2x:not([disabled]):not(.disabled).active {
  background-color: #13c9ca;
  border-color: #13c9ca;
  box-shadow: none !important;
  color: white;
}
.btn-outline-secondary-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-secondary-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-secondary-2x:not([disabled]):not(.disabled).active:active, .btn-outline-secondary-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #0f9b9b !important;
  border-color: #0f9b9b !important;
}

.btn-outline-success-2x:not([disabled]):not(.disabled).active {
  background-color: #81ba00;
  border-color: #81ba00;
  box-shadow: none !important;
  color: white;
}
.btn-outline-success-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-success-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-success-2x:not([disabled]):not(.disabled).active:active, .btn-outline-success-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #5e8700 !important;
  border-color: #5e8700 !important;
}

.btn-outline-danger-2x:not([disabled]):not(.disabled).active {
  background-color: #dc3545;
  border-color: #dc3545;
  box-shadow: none !important;
  color: white;
}
.btn-outline-danger-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-danger-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-danger-2x:not([disabled]):not(.disabled).active:active, .btn-outline-danger-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #bd2130 !important;
  border-color: #bd2130 !important;
}

.btn-outline-info-2x:not([disabled]):not(.disabled).active {
  background-color: #00a8ff;
  border-color: #00a8ff;
  box-shadow: none !important;
  color: white;
}
.btn-outline-info-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-info-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-info-2x:not([disabled]):not(.disabled).active:active, .btn-outline-info-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #0086cc !important;
  border-color: #0086cc !important;
}

.btn-outline-light-2x:not([disabled]):not(.disabled).active {
  background-color: #f8f8f9;
  border-color: #f8f8f9;
  box-shadow: none !important;
  color: white;
}
.btn-outline-light-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-light-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-light-2x:not([disabled]):not(.disabled).active:active, .btn-outline-light-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #dddde1 !important;
  border-color: #dddde1 !important;
}

.btn-outline-dark-2x:not([disabled]):not(.disabled).active {
  background-color: #2a3142;
  border-color: #2a3142;
  box-shadow: none !important;
  color: white;
}
.btn-outline-dark-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-dark-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-dark-2x:not([disabled]):not(.disabled).active:active, .btn-outline-dark-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #161a23 !important;
  border-color: #161a23 !important;
}

.btn-outline-warning-2x:not([disabled]):not(.disabled).active {
  background-color: #ffbc58;
  border-color: #ffbc58;
  box-shadow: none !important;
  color: white;
}
.btn-outline-warning-2x:not([disabled]):not(.disabled).active:hover, .btn-outline-warning-2x:not([disabled]):not(.disabled).active:focus, .btn-outline-warning-2x:not([disabled]):not(.disabled).active:active, .btn-outline-warning-2x:not([disabled]):not(.disabled).active.active {
  color: white;
  box-shadow: none !important;
  background-color: #ffa825 !important;
  border-color: #ffa825 !important;
}

/*======= Table-Border-Bottom-color css starts  ======= */
table thead .border-bottom-primary th,
table tbody .border-bottom-primary th,
table tbody .border-bottom-primary td {
  border-bottom: 1px solid #5f57ea;
}

table thead .border-bottom-secondary th,
table tbody .border-bottom-secondary th,
table tbody .border-bottom-secondary td {
  border-bottom: 1px solid #13c9ca;
}

table thead .border-bottom-success th,
table tbody .border-bottom-success th,
table tbody .border-bottom-success td {
  border-bottom: 1px solid #81ba00;
}

table thead .border-bottom-danger th,
table tbody .border-bottom-danger th,
table tbody .border-bottom-danger td {
  border-bottom: 1px solid #dc3545;
}

table thead .border-bottom-info th,
table tbody .border-bottom-info th,
table tbody .border-bottom-info td {
  border-bottom: 1px solid #00a8ff;
}

table thead .border-bottom-light th,
table tbody .border-bottom-light th,
table tbody .border-bottom-light td {
  border-bottom: 1px solid #f8f8f9;
}

table thead .border-bottom-dark th,
table tbody .border-bottom-dark th,
table tbody .border-bottom-dark td {
  border-bottom: 1px solid #2a3142;
}

table thead .border-bottom-warning th,
table tbody .border-bottom-warning th,
table tbody .border-bottom-warning td {
  border-bottom: 1px solid #ffbc58;
}

/*======= Table-Border-Bottom-color css ends  ======= */
/*======= Table styling css starts  ======= */
.table-styling .table-primary,
.table-styling.table-primary {
  background-color: #5f57ea;
  color: #ffffff;
  border: 3px solid #5f57ea;
}
.table-styling .table-primary thead,
.table-styling.table-primary thead {
  background-color: #342ae4;
  border: 3px solid #342ae4;
}

.table-styling .table-secondary,
.table-styling.table-secondary {
  background-color: #13c9ca;
  color: #ffffff;
  border: 3px solid #13c9ca;
}
.table-styling .table-secondary thead,
.table-styling.table-secondary thead {
  background-color: #0f9b9b;
  border: 3px solid #0f9b9b;
}

.table-styling .table-success,
.table-styling.table-success {
  background-color: #81ba00;
  color: #ffffff;
  border: 3px solid #81ba00;
}
.table-styling .table-success thead,
.table-styling.table-success thead {
  background-color: #5e8700;
  border: 3px solid #5e8700;
}

.table-styling .table-danger,
.table-styling.table-danger {
  background-color: #dc3545;
  color: #ffffff;
  border: 3px solid #dc3545;
}
.table-styling .table-danger thead,
.table-styling.table-danger thead {
  background-color: #bd2130;
  border: 3px solid #bd2130;
}

.table-styling .table-info,
.table-styling.table-info {
  background-color: #00a8ff;
  color: #ffffff;
  border: 3px solid #00a8ff;
}
.table-styling .table-info thead,
.table-styling.table-info thead {
  background-color: #0086cc;
  border: 3px solid #0086cc;
}

.table-styling .table-light,
.table-styling.table-light {
  background-color: #f8f8f9;
  color: #ffffff;
  border: 3px solid #f8f8f9;
}
.table-styling .table-light thead,
.table-styling.table-light thead {
  background-color: #dddde1;
  border: 3px solid #dddde1;
}

.table-styling .table-dark,
.table-styling.table-dark {
  background-color: #2a3142;
  color: #ffffff;
  border: 3px solid #2a3142;
}
.table-styling .table-dark thead,
.table-styling.table-dark thead {
  background-color: #161a23;
  border: 3px solid #161a23;
}

.table-styling .table-warning,
.table-styling.table-warning {
  background-color: #ffbc58;
  color: #ffffff;
  border: 3px solid #ffbc58;
}
.table-styling .table-warning thead,
.table-styling.table-warning thead {
  background-color: #ffa825;
  border: 3px solid #ffa825;
}

/*======= Table styling css ends  ======= */
/*======= All-Borders-color css starts  ======= */
.b-primary {
  border: 1px solid #5f57ea !important;
}

.b-t-primary {
  border-top: 1px solid #5f57ea !important;
}

.b-b-primary {
  border-bottom: 1px solid #5f57ea !important;
}

.b-l-primary {
  border-left: 1px solid #5f57ea !important;
}

.b-r-primary {
  border-right: 1px solid #5f57ea !important;
}

.b-secondary {
  border: 1px solid #13c9ca !important;
}

.b-t-secondary {
  border-top: 1px solid #13c9ca !important;
}

.b-b-secondary {
  border-bottom: 1px solid #13c9ca !important;
}

.b-l-secondary {
  border-left: 1px solid #13c9ca !important;
}

.b-r-secondary {
  border-right: 1px solid #13c9ca !important;
}

.b-success {
  border: 1px solid #81ba00 !important;
}

.b-t-success {
  border-top: 1px solid #81ba00 !important;
}

.b-b-success {
  border-bottom: 1px solid #81ba00 !important;
}

.b-l-success {
  border-left: 1px solid #81ba00 !important;
}

.b-r-success {
  border-right: 1px solid #81ba00 !important;
}

.b-danger {
  border: 1px solid #dc3545 !important;
}

.b-t-danger {
  border-top: 1px solid #dc3545 !important;
}

.b-b-danger {
  border-bottom: 1px solid #dc3545 !important;
}

.b-l-danger {
  border-left: 1px solid #dc3545 !important;
}

.b-r-danger {
  border-right: 1px solid #dc3545 !important;
}

.b-info {
  border: 1px solid #00a8ff !important;
}

.b-t-info {
  border-top: 1px solid #00a8ff !important;
}

.b-b-info {
  border-bottom: 1px solid #00a8ff !important;
}

.b-l-info {
  border-left: 1px solid #00a8ff !important;
}

.b-r-info {
  border-right: 1px solid #00a8ff !important;
}

.b-light {
  border: 1px solid #f8f8f9 !important;
}

.b-t-light {
  border-top: 1px solid #f8f8f9 !important;
}

.b-b-light {
  border-bottom: 1px solid #f8f8f9 !important;
}

.b-l-light {
  border-left: 1px solid #f8f8f9 !important;
}

.b-r-light {
  border-right: 1px solid #f8f8f9 !important;
}

.b-dark {
  border: 1px solid #2a3142 !important;
}

.b-t-dark {
  border-top: 1px solid #2a3142 !important;
}

.b-b-dark {
  border-bottom: 1px solid #2a3142 !important;
}

.b-l-dark {
  border-left: 1px solid #2a3142 !important;
}

.b-r-dark {
  border-right: 1px solid #2a3142 !important;
}

.b-warning {
  border: 1px solid #ffbc58 !important;
}

.b-t-warning {
  border-top: 1px solid #ffbc58 !important;
}

.b-b-warning {
  border-bottom: 1px solid #ffbc58 !important;
}

.b-l-warning {
  border-left: 1px solid #ffbc58 !important;
}

.b-r-warning {
  border-right: 1px solid #ffbc58 !important;
}

/*======= All-Borders-color css ends  ======= */
/*====== Border width css starts ======*/
.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.border-6 {
  border-width: 6px !important;
}

.border-7 {
  border-width: 7px !important;
}

.border-8 {
  border-width: 8px !important;
}

.border-9 {
  border-width: 9px !important;
}

.border-10 {
  border-width: 10px !important;
}

/*====== Border width css ends ======*/
.opacity-0 {
  opacity: 0;
}

.shadow-0 {
  box-shadow: none;
}

/**====== custom scrollbar css start ======**/
.digits {
  font-family: work-Sans, sans-serif;
}

.custom-scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px #e8ebf2;
}
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(95, 87, 234, 0.1);
}

/**====== Custom scrollbar css end ======**/
/**====== Animation css Start ======**/
.line pre {
  font-size: 100%;
}

/**====== Animation css end ======**/
/**====== list style css Start ======**/
.list-circle {
  list-style: circle;
}

/**====== list style css end ======**/
/**=====================
      Generic CSS Ends
==========================**/
/**=====================
     Chat CSS Start
==========================**/
.chat-box .toogle-bar {
  display: none;
}
.chat-box .people-list .search {
  position: relative;
}
.chat-box .people-list .search .form-control {
  background-color: #f1f4fb;
  border: 1px solid #f8f8f9;
}
.chat-box .people-list .search .form-control::-moz-placeholder {
  color: #aaaaaa;
}
.chat-box .people-list .search .form-control:-ms-input-placeholder {
  color: #aaaaaa;
}
.chat-box .people-list .search .form-control::placeholder {
  color: #aaaaaa;
}
.chat-box .people-list .search i {
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 14px;
  color: #e8ebf2;
}
.chat-box .people-list ul {
  padding: 0;
}
.chat-box .people-list ul li {
  padding-bottom: 20px;
}
.chat-box .people-list ul li:last-child {
  padding-bottom: 0;
}
.chat-box .user-image {
  float: left;
  width: 52px;
  height: 52px;
  margin-right: 5px;
}
.chat-box .about {
  float: left;
  margin-top: 5px;
  padding-left: 10px;
}
.chat-box .about .name {
  color: #2a3142;
  letter-spacing: 1px;
  font-weight: 600;
}
.chat-box .status {
  color: #aaaaaa;
  letter-spacing: 1px;
  font-size: 12px;
  margin-top: 5px;
}
.chat-box .status .chat-status {
  font-weight: 600;
  color: #313131;
}
.chat-box .status p {
  font-size: 14px;
}
.chat-box .chat-right-aside .chat .chat-header {
  padding: 15px;
  border-bottom: 1px solid #f8f8f9;
}
.chat-box .chat-right-aside .chat .chat-header img {
  float: left;
  width: 50px;
  height: 50px;
  box-shadow: 1px 1px 4px 1px #e8ebf2;
}
.chat-box .chat-right-aside .chat .chat-header .chat-menu-icons {
  margin-top: 15px;
}
.chat-box .chat-right-aside .chat .chat-header .chat-menu-icons li {
  margin-right: 24px;
}
.chat-box .chat-right-aside .chat .chat-header .chat-menu-icons li a i {
  color: #777777;
  font-size: 25px;
  cursor: pointer;
}
.chat-box .chat-right-aside .chat .chat-msg-box {
  padding: 20px;
  overflow-y: auto;
  height: 560px;
  margin-bottom: 90px;
}
.chat-box .chat-right-aside .chat .chat-msg-box .chat-user-img {
  margin-top: -35px;
}
.chat-box .chat-right-aside .chat .chat-msg-box .message-data {
  margin-bottom: 10px;
}
.chat-box .chat-right-aside .chat .chat-msg-box .message-data-time {
  letter-spacing: 1px;
  font-size: 12px;
  color: #aaaaaa;
  font-family: work-Sans, sans-serif;
}
.chat-box .chat-right-aside .chat .chat-msg-box .message {
  color: #2a3142;
  padding: 20px;
  line-height: 1.9;
  letter-spacing: 1px;
  font-size: 14px;
  margin-bottom: 30px;
  width: 50%;
  position: relative;
}
.chat-box .chat-right-aside .chat .chat-msg-box .my-message {
  border: 1px solid #f8f8f9;
  border-radius: 10px;
  border-top-left-radius: 0;
}
.chat-box .chat-right-aside .chat .chat-msg-box .other-message {
  background-color: #f6f6f6;
  border-radius: 10px;
  border-top-right-radius: 0;
}
.chat-box .chat-right-aside .chat .chat-message {
  padding: 20px;
  border-top: 1px solid #f1f4fb;
  position: absolute;
  width: calc(100% - 15px);
  background-color: #ffffff;
  bottom: 0;
}
.chat-box .chat-right-aside .chat .chat-message .smiley-box {
  background: #eff0f1;
  padding: 10px;
  display: block;
  border-radius: 4px;
  margin-right: 0.5rem;
}
.chat-box .chat-right-aside .chat .chat-message .text-box {
  position: relative;
}
.chat-box .chat-right-aside .chat .chat-message .text-box .input-txt-bx {
  height: 50px;
  border: 2px solid #5f57ea;
  padding-left: 18px;
  font-size: 12px;
  letter-spacing: 1px;
}
.chat-box .chat-right-aside .chat .chat-message .text-box i {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 20px;
  color: #e8ebf2;
  cursor: pointer;
}
.chat-box .chat-right-aside .chat .chat-message .text-box .btn {
  font-size: 16px;
  font-weight: 500;
}
.chat-box .chat-menu {
  border-left: 1px solid #f8f8f9;
}
.chat-box .chat-menu .tab-pane {
  padding: 0 15px;
}
.chat-box .chat-menu ul li .about .status i {
  font-size: 10px;
}
.chat-box .chat-menu .user-profile {
  margin-top: 30px;
}
.chat-box .chat-menu .user-profile .user-content h5 {
  margin: 25px 0;
}
.chat-box .chat-menu .user-profile .user-content hr {
  margin: 25px 0;
}
.chat-box .chat-menu .user-profile .user-content p {
  font-size: 16px;
}
.chat-box .chat-menu .user-profile .image {
  position: relative;
}
.chat-box .chat-menu .user-profile .image .icon-wrapper {
  position: absolute;
  bottom: 0;
  left: 55%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 35px;
  width: 35px;
  border-radius: 50%;
  background-color: #ffffff;
  cursor: pointer;
  overflow: hidden;
  margin: 0 auto;
  font-size: 14px;
  box-shadow: 1px 1px 3px 1px #f8f8f9;
}
.chat-box .chat-menu .user-profile .image .avatar img {
  border-radius: 50%;
  border: 5px solid #f8f8f9;
}
.chat-box .chat-menu .user-profile .border-right {
  border-right: 1px solid #f8f8f9;
}
.chat-box .chat-menu .user-profile .follow {
  margin-top: 0;
}
.chat-box .chat-menu .user-profile .follow .follow-num {
  font-size: 22px;
  color: #000000;
}
.chat-box .chat-menu .user-profile .follow span {
  color: #1b252a;
  font-size: 14px;
  letter-spacing: 1px;
}
.chat-box .chat-menu .user-profile .social-media a {
  color: #aaaaaa;
  font-size: 15px;
  padding: 0 7px;
}
.chat-box .chat-menu .user-profile .chat-profile-contact p {
  font-size: 14px;
  color: #aaaaaa;
}
.chat-box .chat-menu .nav {
  margin-bottom: 20px;
}
.chat-box .chat-menu .nav-tabs .nav-item {
  width: 33.33%;
}
.chat-box .chat-menu .nav-tabs .nav-item a {
  padding: 15px !important;
  color: #aaaaaa !important;
  letter-spacing: 1px;
  font-size: 14px;
  font-weight: 600;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chat-box .chat-menu .nav-tabs .nav-item .material-border {
  border-width: 1px;
  border-color: #5f57ea;
}
.chat-box .chat-menu .nav-tabs .nav-item .nav-link.active {
  color: #000000 !important;
}
.chat-box .chat-history .call-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 300px;
}
.chat-box .chat-history .total-time h2 {
  font-size: 50px;
  color: #eff0f1;
  font-weight: 600;
  margin-bottom: 30px;
}
.chat-box .chat-history .receiver-img {
  margin-top: 55px;
}
.chat-box .chat-history .receiver-img img {
  border-radius: 5px;
}
.chat-box .chat-history .call-icons {
  margin-bottom: 35px;
}
.chat-box .chat-history .call-icons ul li {
  width: 60px;
  height: 60px;
  border: 1px solid #f8f8f9;
  border-radius: 50%;
  padding: 12px;
}
.chat-box .chat-history .call-icons ul li + li {
  margin-left: 10px;
}
.chat-box .chat-history .call-icons ul li a {
  color: #999;
  font-size: 25px;
}

.status-circle {
  width: 10px;
  height: 10px;
  position: absolute;
  top: 40px;
  left: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.away {
  background-color: #ffbc58;
}

.online {
  background-color: #81ba00;
}

.offline {
  background-color: #dc3545;
}

.chat-container .aside-chat-left {
  width: 320px;
}
.chat-container .chat-right-aside {
  width: 320px;
}

.call-chat-sidebar {
  max-width: 320px;
}

.call-chat-sidebar .card .card-body,
.chat-body .card .card-body {
  padding: 15px;
}

/**=====================
      Chat CSS Ends
==========================**/
/**=====================
      market-chart CSS Start
==========================**/
.market-chart {
  height: 303px;
}

/*=====================
    radial-bar CSS start
==========================*/
.radial-bar {
  position: relative;
  display: inline-block;
  border-radius: 50%;
  background-color: transparent;
  box-sizing: content-box;
  width: 70px;
  height: 70px;
  font-size: 18px;
  font-family: work-Sans, sans-serif;
  background-clip: content-box;
}
.radial-bar:after {
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  text-align: center;
  font-weight: 500;
  color: #455a64;
  content: attr(data-label);
  background-color: #fff;
  z-index: 1;
  width: 60px;
  height: 60px;
  margin-left: 5px;
  margin-top: 5px;
  line-height: 56px;
}
.radial-bar > img {
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  text-align: center;
  font-weight: 500;
  color: #455a64;
  z-index: 3;
  width: 60px;
  height: 60px;
  margin-left: 5px;
  margin-top: 5px;
  line-height: 56px;
}
.radial-bar.radial-bar-0 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(90deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-5 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(108deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-10 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(126deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-15 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(144deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-20 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(162deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-25 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(180deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-30 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(198deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-35 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(216deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-40 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(234deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-45 {
  background-image: linear-gradient(90deg, #eff0f1 50%, transparent 50%, transparent), linear-gradient(252deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-50 {
  background-image: linear-gradient(270deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-55 {
  background-image: linear-gradient(288deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-60 {
  background-image: linear-gradient(306deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-65 {
  background-image: linear-gradient(324deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-70 {
  background-image: linear-gradient(342deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-75 {
  background-image: linear-gradient(360deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-80 {
  background-image: linear-gradient(378deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-85 {
  background-image: linear-gradient(396deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-90 {
  background-image: linear-gradient(414deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-95 {
  background-image: linear-gradient(432deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar.radial-bar-100 {
  background-image: linear-gradient(450deg, #448aff 50%, transparent 50%, transparent), linear-gradient(270deg, #448aff 50%, #eff0f1 50%, #eff0f1);
}

.radial-bar-primary.radial-bar-0 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(90deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-5 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(108deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-10 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(126deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-15 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(144deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-20 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(162deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-25 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(180deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-30 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(198deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-35 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(216deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-40 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(234deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-45 {
  background-image: linear-gradient(90deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(252deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-50 {
  background-image: linear-gradient(270deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-55 {
  background-image: linear-gradient(288deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-60 {
  background-image: linear-gradient(306deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-65 {
  background-image: linear-gradient(324deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-70 {
  background-image: linear-gradient(342deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-75 {
  background-image: linear-gradient(360deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-80 {
  background-image: linear-gradient(378deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-85 {
  background-image: linear-gradient(396deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-90 {
  background-image: linear-gradient(414deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-95 {
  background-image: linear-gradient(432deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}
.radial-bar-primary.radial-bar-100 {
  background-image: linear-gradient(450deg, #5f57ea 50%, transparent 50%, transparent), linear-gradient(270deg, #5f57ea 50%, #eff0f1 50%, #eff0f1);
}

.market-chart .ct-series-a .ct-point {
  stroke: #5f57ea;
}
.market-chart .ct-series-a .ct-line {
  stroke: #5f57ea;
}
.market-chart .ct-series-a .ct-bar {
  stroke: #5f57ea;
}
.market-chart .ct-series-a .ct-slice-donut {
  stroke: #5f57ea;
}
.market-chart .ct-series-b .ct-point {
  stroke: #dc3545;
}
.market-chart .ct-series-b .ct-line {
  stroke: #dc3545;
}
.market-chart .ct-series-b .ct-bar {
  stroke: #dc3545;
}
.market-chart .ct-series-b .ct-slice-donut {
  stroke: #dc3545;
}

.needs-validation .custom-select {
  background: #ffffff;
}
.needs-validation .form-control {
  border-radius: 0.25rem;
}

.license-key[disabled] {
  cursor: not-allowed;
}

.jsgrid-cell,
.jsgrid-grid-body {
  border: 1px solid #ebf1ff;
}

.jsgrid .jsgrid-pager-container {
  text-align: right;
}
.jsgrid .jsgrid-pager [class*=jsgrid-pager] {
  display: inline-block;
  min-width: 1.5em;
  padding: 0.5em 1em;
  border: 1px solid #f8f8f9;
}
.jsgrid .jsgrid-pager .jsgrid-pager-nav-inactive-button {
  margin-right: -5px;
}

.jsgrid-alt-row .jsgrid-cell {
  background-color: rgba(241, 244, 251, 0.5);
  text-align: center;
}

.jsgrid-row > .jsgrid-cell {
  text-align: center;
}

.jsgrid-filter-row .jsgrid-cell {
  background-color: #f8f8f9;
  text-align: center;
}

.jsgrid-header-row .jsgrid-header-cell {
  background-color: #f1f4fb;
  text-align: center;
  padding: 0.75rem;
}

.jsgrid-selected-row > .jsgrid-cell {
  background-color: rgba(241, 244, 251, 0.5);
  border-color: #f1f4fb;
}

.jstree-loader {
  position: absolute;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.checkbox label {
  display: inline-block;
  position: relative;
  cursor: pointer;
  margin-top: 10px;
  margin-bottom: 10px;
}
.checkbox .form-check-input {
  margin-top: 13px;
  margin-bottom: 10px;
}

.form-row .form-control {
  font-size: 14px;
  letter-spacing: 1px;
}
.form-row .custom-select {
  font-size: 14px;
  letter-spacing: 1px;
}

/*=====================
   bulk-pruduct CSS start
==========================*/
.bulk-pruduct .card .card-body p {
  font-weight: bold;
}

/**=====================
   Datepicker CSS Start
==========================**/
.datepicker {
  box-shadow: 0 4px 14px rgba(95, 87, 234, 0.15);
}

.datepicker--day-name {
  color: #000000;
  font-weight: bold;
}

.datepicker--cell.-current- {
  color: #000000;
  border-radius: 5px;
  font-weight: bold;
  border: 2px solid #5f57ea;
}
.datepicker--cell.-focus- {
  background: #5f57ea;
  color: #ffffff;
}
.datepicker--cell.-selected- {
  background: #5f57ea;
}

.daterangepicker.ltr {
  color: #898989;
  z-index: 8;
}
.daterangepicker.ltr tr {
  font-size: 13px;
}
.daterangepicker.ltr tr td {
  height: 34px;
  width: 34px;
}
.daterangepicker.ltr tr td.in-range {
  background-color: #fafafa;
}
.daterangepicker.ltr tr td.active {
  background-color: #5f57ea;
}
.daterangepicker.ltr .ranges {
  float: none;
}
.daterangepicker.ltr .ranges li {
  color: #5f57ea;
}
.daterangepicker.ltr .ranges li:hover {
  background-color: #5f57ea;
  border: 1px solid #5f57ea;
  color: #ffffff;
}
.daterangepicker.ltr .ranges li.active {
  background-color: #5f57ea;
  border: 1px solid #5f57ea;
  color: #ffffff;
}
.daterangepicker.ltr .calendar-time select {
  color: #898989;
}

.datetime-picker .bootstrap-datetimepicker-widget.dropdown-menu {
  width: auto;
}

.date-range-picker .theme-form {
  margin-bottom: 30px;
}
.date-range-picker > div:last-child .theme-form {
  margin-bottom: 0px;
}

.authentication-box {
  min-width: 100vw;
  min-height: 100vh;
  width: auto;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.authentication-box .custom-checkbox .form-check-input:checked ~ .form-check-label::before {
  background-color: #5f57ea;
}
.authentication-box .back-btn {
  float: right;
  margin: 0;
  font-size: 14px;
  position: relative;
  padding-left: 60px !important;
}
.authentication-box .back-btn svg {
  position: absolute;
  left: 30px;
  height: 18px;
}
.authentication-box .row {
  align-items: center;
}
.authentication-box .tab2-card .nav-tabs .nav-link {
  font-size: 18px;
}
.authentication-box .btn-primary {
  border-radius: 25px;
  margin-top: 20px;
  font-weight: 400;
  padding: 11px 45px;
}
.authentication-box .form-footer {
  position: relative;
  padding-top: 25px;
  border-top: 1px solid #f3f3f3;
  margin-top: 25px;
}
.authentication-box .form-footer > span {
  position: absolute;
  top: -11px;
  padding-right: 20px;
  background-color: #fff;
  color: #909090;
}
.authentication-box .form-footer .social {
  margin-bottom: 0;
  border-bottom: 0;
}
.authentication-box .form-footer .social li {
  padding: 10px;
  border-radius: 100%;
  border: 1px solid #5f57ea;
  height: 40px;
  width: 40px;
  margin-right: 10px;
  text-align: center;
  display: inline-block;
}
.authentication-box .slick-dots li.slick-active button:before {
  color: #ffffff;
}
.authentication-box .slick-dots li button:before {
  font-size: 12px;
  color: #ffffff;
}
.authentication-box .container {
  max-width: 900px;
}
.authentication-box .container .bg-primary {
  padding: 50px;
  background-position: center;
  box-shadow: 1px 5px 24px 0 rgba(255, 128, 132, 0.8);
}
.authentication-box .container .form-group {
  margin-bottom: 1.5rem;
}
.authentication-box .container .svg-icon {
  padding: 24px;
  margin: 0 auto;
  border: 2px dashed #ffffff;
  border-radius: 100%;
  height: 130px;
  width: 130px;
  margin-bottom: 40px;
}
.authentication-box .container .svg-icon svg {
  height: 80px;
}
.authentication-box .container p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  line-height: 2;
  text-align: center;
}
.authentication-box .container h3 {
  color: #ffffff;
  font-weight: 600;
  text-align: center;
}

.auth-form .form-control {
  border-radius: 25px;
  padding: 9px 25px;
  border: 1px solid #eaeaea;
}

.card-left {
  z-index: 1;
}

.card-right {
  margin-left: -15px;
}
.card-right .card {
  padding-left: 15px;
}

.forgot-pass {
  padding: 0;
  margin-left: auto;
  color: #5f57ea;
}

.custom-theme {
  position: fixed;
  right: 0;
  cursor: pointer;
  top: 50%;
  transform: translateY(-50%);
  display: block;
}
.custom-theme li {
  width: 40px;
  height: 38px;
  display: flex;
  z-index: 1;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  text-decoration: none;
  border-radius: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  background-color: #f7f7f7;
  box-shadow: 0px 0px 5px 0px rgba(154, 154, 154, 0.54);
}
.custom-theme li:first-child {
  margin-bottom: 15px;
}

/**=====================
    Admin CSS End
==========================**/
.dark .page-main-header .main-header-right .nav-right .profile-dropdown li:nth-child(4) {
  border-color: #404040;
}
.dark .page-main-header .main-header-right .nav-right .profile-dropdown li a {
  color: #cbcbcb;
}
.dark .page-main-header .main-header-right .nav-right .profile-dropdown li a svg {
  stroke: #cbcbcb;
}
.dark .page-main-header .main-header-right .nav-right > ul > li {
  border-color: #404040;
}
.dark .page-main-header .main-header-right .nav-right > ul > li:nth-child(5) {
  border-color: #404040;
}
.dark .page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:before {
  background: #404040;
}
.dark .page-wrapper .page-main-header {
  background-color: #2b2b2b;
}
.dark .page-wrapper .page-body-wrapper .page-body {
  background-color: #232323;
}
.dark .page-wrapper .page-body-wrapper .page-header .row h3 {
  color: #eaedef;
}
.dark .page-wrapper .page-body-wrapper .page-sidebar {
  background: #2b2b2b;
}
.dark .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-header {
  color: #cbcbcb;
}
.dark .page-wrapper .page-body-wrapper .page-sidebar .sidebar-menu .sidebar-submenu > li.active > a {
  color: #cbcbcb;
}
.dark .card {
  background-color: #2b2b2b;
}
.dark .card .card-header {
  border-color: #404040;
  background-color: #2b2b2b;
}
.dark .nav-menus .search-form input {
  background-color: #232323;
  border-color: #404040;
}
.dark .nav-menus .onhover-dropdown:hover .onhover-show-div:after {
  border-bottom-color: #404040;
}
.dark .nav-menus .onhover-dropdown:hover .onhover-show-div:before {
  border-bottom-color: #404040;
}
.dark .ct-label {
  fill: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.4);
}
.dark .table {
  color: #dee2e6;
}
.dark footer {
  background-color: #2b2b2b;
  border-color: #404040;
}
.dark .sales-carousel .value-graph h3 {
  color: #eaedef;
}
.dark .user-status table thead tr th {
  color: #cfd4da;
}
.dark ul.notification-dropdown.onhover-show-div li {
  border-color: #404040;
}
.dark ul.notification-dropdown.onhover-show-div li + li:hover {
  background-color: #2b2b2b;
}
.dark .page-main-header .main-header-right .nav-right .language-dropdown li a {
  color: #cbcbcb;
}
.dark .order-graph h6 {
  color: #cfd4da;
}
.dark .order-graph .order-graph-bottom h6 span {
  color: #cfd4da;
}
.dark .custom-theme li {
  background-color: #232323;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.54);
}
.dark .jsgrid-grid-header {
  border-color: #404040;
}
.dark .jsgrid-cell,
.dark .jsgrid-grid-body {
  background: #404040;
  border-color: #404040;
}
.dark .jsgrid-alt-row .jsgrid-cell {
  background: #232323;
}
.dark .jsgrid .jsgrid-pager [class*=jsgrid-pager] {
  border-color: #404040;
}
.dark .jsgrid-header-scrollbar::-webkit-scrollbar-track {
  background: #2b2b2b;
}
.dark .jsgrid-header-row > .jsgrid-header-cell {
  border-color: #404040;
}
.dark .jsgrid-header-row > .jsgrid-cell {
  background-color: #2b2b2b;
}
.dark .jsgrid-header-row .jsgrid-header-cell {
  background-color: #232323;
}
.dark .add-product ul li .box-input-file {
  background-color: #232323;
}
.dark .needs-validation .custom-select,
.dark .needs-validation textarea {
  background: #2b2b2b;
  color: #cfd4da;
  border-color: #404040;
}
.dark .add-product-form .qty-box .input-group .btn-primary {
  border-color: #404040 !important;
}
.dark .dataTables_wrapper .dataTables_length,
.dark .dataTables_wrapper .dataTables_filter,
.dark .dataTables_wrapper .dataTables_info,
.dark .dataTables_wrapper .dataTables_processing,
.dark .dataTables_wrapper .dataTables_paginate {
  color: #cbcbcb;
}
.dark .dataTables_wrapper table.dataTable.row-border tbody th,
.dark .dataTables_wrapper table.dataTable.row-border tbody td {
  border-color: #404040 !important;
}
.dark .dataTables_wrapper table.dataTable.display tbody th,
.dark .dataTables_wrapper table.dataTable.display tbody td {
  border-color: #404040 !important;
}
.dark .tab2-card .nav-tabs .nav-link {
  color: #cbcbcb;
}
.dark .dataTables_wrapper .dataTables_paginate {
  border-color: #404040;
}
.dark .dataTables_wrapper .dataTables_paginate .paginate_button {
  color: #cbcbcb !important;
}
.dark .dataTables_wrapper .dataTables_paginate .paginate_button.disabled, .dark .dataTables_wrapper .dataTables_paginate .paginate_button:hover, .dark .dataTables_wrapper .dataTables_paginate .paginate_button:active {
  color: #cbcbcb !important;
}
.dark .dataTables_wrapper table.dataTable {
  border-color: #404040;
}
.dark .dataTables_wrapper table.dataTable tbody tr {
  background-color: #2b2b2b;
}
.dark .dataTables_wrapper table.dataTable tbody tr .sorting_1 {
  background-color: #2b2b2b !important;
}
.dark .dataTables_wrapper table.dataTable tbody tr.odd {
  background-color: #2b2b2b;
}
.dark .dataTables_wrapper .dataTables_filter input[type=search] {
  background-color: #2b2b2b;
  border-color: #404040;
}
.dark .dataTables_wrapper table.dataTable th {
  background-color: #232323 !important;
  border-color: #404040;
}
.dark .dataTables_wrapper .dataTables_length label select {
  background-color: #2b2b2b;
  border-color: #404040;
}

/**=====================
    Datepicker CSS End
==========================**/
.jvector-map-height svg {
  height: 350px;
}

/*=====================
    Responsive CSS start
==========================*/
@media only screen and (max-width: 1660px) {
  .peity-chart-dashboard .peity {
    height: 148px;
  }

  .products-table table tr td:first-child {
    min-width: 268px;
  }
}
@media screen and (max-width: 1440px) and (min-width: 1366px) {
  .peity-chart-dashboard .peity {
    height: 120px;
  }

  .flot-chart-container {
    height: 350px;
  }

  .product-adding .col-xl-5 {
    flex: 0 0 36%;
    max-width: 36%;
  }
  .product-adding .col-xl-7 {
    flex: 0 0 64%;
    max-width: 64%;
  }
}
@media only screen and (max-width: 1366px) {
  .page-main-header .main-header-right .nav-right > ul > li:first-child {
    width: 32%;
  }

  .peity-chart-dashboard .peity {
    height: 180px;
  }

  .xl-space {
    margin-top: 30px;
  }

  .chart-vertical-center #myDoughnutGraph,
.chart-vertical-center #myPolarGraph {
    height: 180px !important;
  }

  .product-adding .add-product-form {
    margin-top: 30px;
  }

  .user-list table tr td:nth-child(5),
.user-list table tr th:nth-child(5) {
    width: 195px !important;
  }

  .translation-list table tr td,
.translation-list table tr th {
    width: 162px !important;
  }
  .translation-list table tr td:first-child, .translation-list table tr td:nth-child(2),
.translation-list table tr th:first-child,
.translation-list table tr th:nth-child(2) {
    width: 274px !important;
  }

  .order-datatable table {
    display: block;
  }
  .order-datatable table tr th,
.order-datatable table tr td {
    min-width: 121px;
  }
  .order-datatable table tr th:nth-child(2),
.order-datatable table tr td:nth-child(2) {
    min-width: 150px;
  }

  .vendor-table table {
    display: block;
  }
  .vendor-table table tr th,
.vendor-table table tr td {
    min-width: 112px;
  }
  .vendor-table table tr th:first-child,
.vendor-table table tr td:first-child {
    min-width: 150px;
  }

  .media-table table tr th:nth-child(3),
.media-table table tr td:nth-child(3) {
    width: 146px !important;
  }
  .media-table table tr th:last-child,
.media-table table tr td:last-child {
    width: 248px !important;
  }
}
@media only screen and (max-width: 1199px) {
  .product-page-details {
    margin-top: 30px;
  }

  .product-physical table tr th,
.product-physical table tr td {
    width: 70px !important;
  }
  .product-physical table tr th:nth-child(2),
.product-physical table tr td:nth-child(2) {
    width: 173px !important;
  }
  .product-physical table tr th:nth-child(5),
.product-physical table tr td:nth-child(5) {
    width: 100px !important;
  }

  .category-table table tr td,
.category-table table tr th {
    width: 120px !important;
  }
  .category-table table tr td:first-child,
.category-table table tr th:first-child {
    width: 130px !important;
  }
  .category-table table tr td:nth-child(3),
.category-table table tr th:nth-child(3) {
    width: 225px !important;
  }
  .category-table table tr td:last-child,
.category-table table tr th:last-child {
    width: 150px !important;
  }

  .media-table table tr td:last-child,
.media-table table tr th:last-child {
    width: 370px !important;
  }

  .order-table table tr td:nth-child(4),
.order-table table tr th:nth-child(4) {
    width: 230px !important;
  }

  .user-list table tr th,
.user-list table tr td {
    width: 200px !important;
  }

  .transactions table tr td:nth-child(4),
.transactions table tr th:nth-child(4) {
    width: 100px !important;
  }
  .transactions table tr td:first-child,
.transactions table tr th:first-child {
    width: 100px !important;
  }
  .transactions table tr td:nth-child(2),
.transactions table tr th:nth-child(2) {
    width: 150px !important;
  }
}
@media screen and (max-device-width: 1366px) and (min-device-width: 1200px) {
  .xl-100 {
    max-width: 100%;
    flex: 0 0 100%;
  }

  .xl-50 {
    max-width: 50%;
    flex: 0 0 50%;
  }

  .product-adding .col-xl-5,
.product-adding .col-xl-7 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .product-adding .add-product-form {
    margin-top: 30px;
  }

  .category-table table tr td:first-child,
.category-table table tr th:first-child {
    width: 90px !important;
  }

  .order-table table tr td:nth-child(2),
.order-table table tr th:nth-child(2) {
    width: 70px !important;
  }

  .user-list table tr td:first-child,
.user-list table tr th:first-child {
    width: 130px !important;
  }

  .profile-table table tr th,
.profile-table table tr td {
    width: 200px !important;
  }
}
@media only screen and (max-width: 991px) {
  .authentication-box {
    padding: 30px;
  }
  .authentication-box .container .bg-primary {
    padding: 40px;
  }

  .btn-popup {
    margin-bottom: 20px;
  }

  .order-graph .order-graph-bottom {
    margin-top: 20px;
  }
  .order-graph .order-graph-bottom.sales-location {
    margin-top: 15px;
  }

  .xl-space {
    margin-top: 20px;
  }

  .card .card-body,
.card .card-header {
    padding: 20px;
  }
  .card .card-body .card-header-right,
.card .card-header .card-header-right {
    right: 11px;
    top: 15px;
  }

  .page-wrapper .page-main-header.open {
    z-index: 11;
  }

  .right-sidebar {
    top: 133px;
  }

  .offcanvas .page-wrapper .page-body-wrapper .page-body:before {
    left: 0;
  }

  .page-wrapper .page-body-wrapper .page-header .row h3 {
    font-size: 22px;
  }
  .page-wrapper .page-body-wrapper .page-header .row .pull-right {
    float: none;
    margin-top: 5px;
  }
  .page-wrapper .page-body-wrapper .page-sidebar {
    top: 60px !important;
    height: calc(100vh - 60px) !important;
  }
  .page-wrapper .page-body-wrapper .page-sidebar ~ .page-body {
    margin-left: 0;
    margin-top: 60px;
  }
  .page-wrapper .page-body-wrapper .page-sidebar ~ .page-body .page-header .row .page-header-left {
    display: inherit;
  }
  .page-wrapper .page-body-wrapper .sidebar {
    height: calc(100vh - 60px);
  }
  .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body {
    margin-top: 60px;
  }
  .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body .activity .media .gradient-round.gradient-line-1:after {
    bottom: -45px;
  }
  .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body .activity .media .gradient-round.small-line:after {
    bottom: -28px;
  }
  .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body .activity .media .gradient-round.medium-line:after {
    bottom: -41px;
  }
  .page-wrapper .page-body-wrapper footer {
    margin-left: 0;
  }
  .page-wrapper .page-main-header {
    height: 60px;
    margin-left: 0;
    width: 100%;
    z-index: 11;
  }
  .page-wrapper .page-main-header .main-header-left .logo-wrapper img {
    height: 23px;
    margin-top: 0;
    margin-right: 15px;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right {
    position: unset;
    padding: 0;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul {
    top: 60px;
    position: absolute;
    z-index: -1;
    background-color: #fff;
    transition: all linear 0.3s;
    box-shadow: 0 2px 2px 2px #efefef;
    width: 100%;
    left: 0;
    padding: 0 40px;
    transform: translateY(-35px) scaleY(0);
    opacity: 0;
    visibility: hidden;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul > li {
    margin: 10px 0;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > .mobile-toggle {
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > .mobile-toggle svg circle {
    color: #5f57ea;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul.open {
    z-index: 1;
    opacity: 1;
    transform: translateY(0px) scaleY(1);
    visibility: visible;
  }

  .page-header {
    padding-top: 25px !important;
    padding-bottom: 25px !important;
  }
  .page-header .row .page-header-left .breadcrumb {
    margin-top: 5px;
    margin-left: 0;
  }

  .product-page-details {
    text-align: left !important;
  }

  .product-page-main .owl-stage-outer {
    margin-top: 20px;
  }

  .dataTables_wrapper .dataTables_length {
    margin-bottom: 20px;
  }

  .add-product img {
    width: 460px;
  }

  .zoomContainer {
    top: 209px !important;
  }

  .product-adding .add-product-form {
    margin-top: 20px;
  }

  .tab-coupon {
    margin-bottom: 20px;
  }

  .deactivate-account {
    margin-top: 20px;
  }

  .sell-graph canvas {
    height: 312px !important;
  }

  .dark .page-wrapper .page-main-header .main-header-right .nav-right > ul {
    background-color: #2b2b2b;
    box-shadow: none;
  }
}
@media only screen and (max-width: 767px) {
  .card-right {
    margin-left: 0;
  }

  .authentication-box .form-footer,
.authentication-box .form-button {
    text-align: center;
  }
  .authentication-box .form-footer > span,
.authentication-box .form-button > span {
    padding: 0;
    left: 0;
    right: 0;
  }

  .page-main-header .main-header-right .nav-right > ul > li:first-child {
    width: 40%;
  }

  .products-table table tr td:first-child,
.products-table table tr th:first-child {
    min-width: 280px;
  }
  .products-table table tr td:nth-child(3),
.products-table table tr th:nth-child(3) {
    min-width: 110px;
  }

  .page-wrapper .page-body-wrapper .page-header .row .col {
    flex-basis: unset;
  }
  .page-wrapper .page-body-wrapper .page-header .row .col .pull-right {
    float: none;
    margin-top: 20px;
  }
  .page-wrapper .page-body-wrapper footer .row div {
    text-align: center;
  }
  .page-wrapper .page-body-wrapper footer .row div .pull-right {
    float: none;
  }

  .footer-fix .pull-right {
    float: none !important;
    text-align: center !important;
  }

  .product-physical table tr th,
.product-physical table tr td {
    width: 90px !important;
  }
  .product-physical table tr th:nth-child(2),
.product-physical table tr td:nth-child(2) {
    width: 196px !important;
  }

  .add-product-form .form-group .form-control,
.add-product-form .form-group select {
    width: 100% !important;
    margin: 0 !important;
  }
  .add-product-form .input-group .touchspin {
    width: 30% !important;
  }
  .add-product-form .radio-animated label {
    margin-bottom: 0;
  }
  .add-product-form .editor-space {
    padding: 0 15px;
  }
  .add-product-form textarea {
    margin: 0 15px;
  }

  .add-product img {
    width: 400px;
  }

  .digital-add textarea {
    margin: 0 !important;
  }
  .digital-add .form-group select,
.digital-add .form-group .form-control {
    width: 100% !important;
  }
  .digital-add .form-group label {
    margin-bottom: 0 !important;
  }

  .digital-product table tr th,
.digital-product table tr td {
    width: 120px !important;
  }

  .report-table table tr th,
.report-table table tr td {
    width: 150px !important;
  }

  .needs-validation .form-group.row {
    margin-left: 0;
    margin-right: 0;
  }
  .needs-validation .form-group .form-control,
.needs-validation .form-group select {
    width: 95%;
    margin: 0 auto;
  }
  .needs-validation .form-group .checkbox {
    padding-left: 15px;
  }
  .needs-validation .form-group label {
    margin-bottom: 4px;
  }
  .needs-validation .radio-animated label {
    margin-bottom: 0;
  }
  .needs-validation .editor-space {
    padding: 0 15px;
  }
  .needs-validation textarea {
    margin: 0 15px;
  }

  .dataTables_wrapper .dataTables_paginate {
    float: none !important;
    margin-top: 20px !important;
  }
  .dataTables_wrapper .dataTables_length label,
.dataTables_wrapper .dataTables_filter label {
    float: none !important;
  }
  .dataTables_wrapper table.dataTable {
    margin-top: 20px !important;
  }
}
@media only screen and (max-width: 577px) {
  footer {
    margin-bottom: 0 !important;
  }
}
@media only screen and (max-width: 575px) {
  .authentication-box .btn-primary {
    margin-top: 10px;
  }

  .btn-popup {
    margin-bottom: 15px;
  }

  .static-top-widget div.align-self-center svg {
    width: 20px;
    height: 20px;
  }

  .latest-order-table table tr td {
    min-width: 100px;
  }
  .latest-order-table table tr td + td {
    min-width: 150px;
  }
  .latest-order-table table tr td:nth-child(3) {
    min-width: 200px;
  }

  .card {
    margin-bottom: 15px;
  }
  .card .card-header {
    padding: 15px;
  }
  .card .card-header h5 {
    font-size: 17px;
  }
  .card .card-header .card-header-right {
    right: 6px;
    top: 10px;
  }
  .card .card-body {
    padding: 15px;
  }

  .sm-order-space,
.xl-space {
    margin-top: 15px;
  }

  .order-graph .order-graph-bottom {
    margin-top: 15px;
  }
  .order-graph .order-graph-bottom h6 {
    margin-right: 0;
  }
  .order-graph .order-graph-bottom .media {
    margin-bottom: 15px;
  }

  .offcanvas .page-wrapper .page-body-wrapper .page-header .row h3 {
    font-size: 22px;
  }
  .offcanvas .page-wrapper .page-body-wrapper .page-body:before {
    left: 0;
  }

  .form-inline .form-control-plaintext {
    display: inline-block;
  }

  .d-sm-none:after {
    display: none;
  }

  ul.notification-dropdown.onhover-show-div {
    width: 284px;
    right: -112px;
  }
  ul.notification-dropdown.onhover-show-div:before, ul.notification-dropdown.onhover-show-div:after {
    right: 135px !important;
  }

  .page-wrapper .page-main-header .main-header-right {
    padding: 0 15px;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul {
    padding: 0 0;
    justify-content: flex-start;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul > li:first-child {
    width: auto;
    margin: 0;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:before {
    display: none;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul > li:first-child .search-form .form-group:after {
    display: none;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul .search-form .form-group {
    margin-bottom: 0;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul .search-form .form-control-plaintext.open {
    transform: translateY(0px) scaleY(1);
    opacity: 1;
    visibility: visible;
    transition: all linear 0.3s;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul .search-form .form-control-plaintext {
    top: 60px;
    position: absolute;
    transition: all linear 0.3s;
    left: 0;
    background-color: #fff;
    transform: translateY(-35px) scaleY(0);
    opacity: 0;
    visibility: hidden;
    width: 180px;
    padding: 10px 10px 10px 15px;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul .search-form .mobile-search svg {
    color: #5f57ea;
  }
  .page-wrapper .search-form .form-group {
    margin-right: 0;
  }
  .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body .activity .media .gradient-round.gradient-line-1:after {
    bottom: -41px;
  }
  .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body .activity .media .gradient-round.small-line:after {
    bottom: -19px;
    height: 12px;
  }
  .page-wrapper .page-body-wrapper .page-sidebar.open ~ .page-body .activity .media .gradient-round.medium-line:after {
    bottom: -34px;
  }
  .page-wrapper .page-body-wrapper .page-body {
    padding: 0;
  }
  .page-wrapper .page-body-wrapper .page-header {
    padding-top: 20px !important;
    padding-bottom: 20px !important;
  }

  .product-page-main .owl-stage-outer {
    margin-top: 15px;
  }

  .profile-details img {
    margin-bottom: 15px;
  }

  .project-status {
    margin-top: 15px;
  }
  .project-status .media {
    margin-top: 15px;
  }

  .tab2-card ul {
    display: inherit;
    margin-bottom: 15px;
  }
  .tab2-card ul li {
    display: block;
  }
  .tab2-card ul li a {
    text-align: center;
  }

  .profile-table table tbody tr td:first-child {
    min-width: 150px;
  }

  .account-setting h5 {
    margin-bottom: 15px;
  }

  .deactivate-account {
    margin-top: 15px;
  }
  .deactivate-account .btn {
    margin-top: 15px;
  }

  .sm-label-radio {
    margin-bottom: 4px !important;
  }

  .permission-block .attribute-blocks .form-group {
    padding-bottom: 15px;
  }

  .needs-validation textarea {
    margin: 0 10px;
  }
  .needs-validation h4 {
    margin-bottom: 15px;
  }
  .needs-validation .permission-block .attribute-blocks .row {
    padding-left: 15px;
  }
  .needs-validation .permission-block .attribute-blocks + .attribute-blocks h5 {
    margin-top: 20px;
  }
  .needs-validation .radio_animated {
    margin: 0 6px 0 0;
  }
  .needs-validation .radio-animated label {
    margin-right: 15px;
  }

  .tab-coupon {
    display: grid;
  }

  .translation-list table tr td:nth-child(3), .translation-list table tr td:nth-child(4),
.translation-list table tr th:nth-child(3),
.translation-list table tr th:nth-child(4) {
    width: 180px !important;
  }

  .product-list table tr td,
.product-list table tr th {
    width: 100px !important;
  }
  .product-list table tr td:nth-child(2),
.product-list table tr th:nth-child(2) {
    width: 250px !important;
  }
  .product-list table tr td:nth-child(3),
.product-list table tr th:nth-child(3) {
    width: 120px !important;
  }

  .digital-product table tr td:nth-child(2),
.digital-product table tr th:nth-child(2) {
    width: 120px !important;
  }

  .add-product-form .form-group .form-control {
    width: 93%;
    margin: 0 auto;
  }
  .add-product-form .form-group label {
    padding: 0;
  }
  .add-product-form .qty-box {
    width: 162px;
  }
  .add-product-form .qty-box .input-group .form-control {
    width: 80px;
  }
  .add-product-form .description-sm {
    padding: 0 !important;
  }

  .zoomContainer {
    top: 184px !important;
  }

  .add-product ul li .box-input-file {
    width: 30px;
    height: 30px;
  }

  .dataTables_wrapper .dataTables_paginate {
    margin-top: 15px !important;
    margin-left: 0 !important;
  }
  .dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 1px 4px !important;
  }
  .dataTables_wrapper .dataTables_length {
    margin-bottom: 15px;
  }
  .dataTables_wrapper table.dataTable {
    margin-top: 15px !important;
  }
}
@media only screen and (max-width: 360px) {
  .authentication-box {
    padding: 30px 15px;
  }
  .authentication-box .forgot-pass {
    float: none;
    padding-top: 10px;
  }
  .authentication-box .container .form-group {
    margin-bottom: 1rem;
  }
  .authentication-box .container h3 {
    font-size: 20px;
  }
  .authentication-box .container p {
    line-height: 1.6;
  }
  .authentication-box .container .bg-primary {
    padding: 30px 15px;
  }
  .authentication-box .container .svg-icon {
    padding: 17px;
    height: 90px;
    width: 90px;
    margin-bottom: 25px;
  }
  .authentication-box .container .svg-icon svg {
    height: 50px;
  }

  .nav-menus .notification-badge {
    right: 0;
  }

  .page-wrapper .page-main-header .main-header-right .nav-right > ul > li {
    padding: 0 13px;
  }
  .page-wrapper .page-main-header .main-header-right .nav-right > ul > li .dot {
    right: 13px;
  }

  .footer {
    padding-left: 0;
    padding-right: 0 !important;
  }
  .footer p {
    font-size: 12px;
  }

  .add-product img {
    width: 300px;
  }

  .product-page-main .btn {
    padding: 5px 12px;
  }
}
@media only screen and (max-height: 800px) {
  .right-sidebar .chat-box .friend-list {
    max-height: calc(100vh - 150px);
    overflow: scroll;
  }
}
@media (min-width: 576px) {
  .form-inline .form-group {
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }
}
@media (min-width: 1200px) {
  .media-modal .modal-xl {
    max-width: 1400px;
  }
}
/*=====================
  Responsive CSS End
==========================*/
