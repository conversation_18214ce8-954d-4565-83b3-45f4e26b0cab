/**=====================
      Dropdown CSS Start
==========================**/

.dropdown {
    .dropdown-menu {
        top: 9px !important;
        margin: 0;
        padding: 10px;
        width: max-content !important;
        min-width: 13rem;
        left: 0;

        .form-check-label {
            margin-bottom: 0;
            font-size: 14px;
        }

        .form-check {
            margin-bottom: 4px;
        }
    }
}

.btn-group {
    .dropdown-menu {
        top: 55px;
        margin: 0;
        padding: 10px;
        width: max-content !important;
        min-width: 13rem;

        #table-filter-status-wrapper,
        #coupons-filter-status-wrapper {
            padding: 0 !important;

            label {
                font-size: 14px;
                font-weight: 500;
            }

            .form-select {
                width: 100%;

                option:hover {
                    background-color: yellow;
                }
            }
        }

        button {
            &.dropdown-item {
                font-size: 14px;
                font-weight: 500;
                background-color: $primary-color;
                border-radius: 8px;
                padding: 12px;
                color: $white;
                margin-top: 10px;

                &:hover {
                    color: $primary-color !important;
                    background-color: rgba($primary-color, 0.10);
                }
            }
        }

        .dropdown-divider {
            display: none;
        }
    }
}

.dropdown-item {
    &:hover {
        color: $primary-color !important;
        background-color: rgba($primary-color, 0.10);
    }
}

.shortcode-dropdown-box {
    .dropdown-btn {
        background-color: $input-bg;
        border: 1px solid $input-bg;
        border-radius: 6px;
        padding: 11px 14px;
        font-size: 15px;
        gap: 7px;
        line-height: 1.3;
        color: #2263eb;

        &:active {
            background-color: rgba(237, 239, 241, 0.4);
            border: 1px solid $input-bg;
        }

        &::after {
            content: "\ea4e";
            @include icon;
            margin-left: calc(13px + (17 - 13) * ((100vw - 320px) / (1920 - 320)));
            font-size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
            line-height: 1;
        }
    }

    .dropdown-menu {
        padding-inline: 0;
        border: 1px solid $input-bg;
        width: 100% !important;

        li {
            display: block;

            .dropdown-item {
                font-size: 15px;
                cursor: pointer;
                line-height: 1.3;
                padding: 9px 15px;
                color: #777777;

                &:hover {
                    color: #2263eb !important;
                    background-color: #f8f9f9;
                    // font-weight: 500;
                }
            }
        }
    }
}

/**=====================
      Dropdown CSS End
==========================**/
