/**=====================
      Tables CSS Start
==========================**/
.user-info {
    display: flex;
    align-items: center;
    text-align: left;
    gap: calc(8px + (10 - 8) * ((100vw - 320px) / (1920 - 320)));
    color: rgba(0, 22, 46, 0.7);
    &:hover{
        .user-details{
            .user-email{
                .feather{
                    opacity: 1;
                }
            }
        }
    }

    .initial-letter {
        width: calc(40px + (44 - 40) * ((100vw - 320px) / (1920 - 320)));
        min-width: auto;
        height: calc(38px + (44 - 38) * ((100vw - 320px) / (1920 - 320)));
        font-size: calc(20px + (22 - 20) * ((100vw - 320px) / (1920 - 320)));
        font-weight: 600;
    }

    .user-name {
        font-size: 14px;
        font-weight: 500;
        line-height: 1.2;
        letter-spacing: unset;
        color: #00162e;

        &:has(.rate) {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .rate {
            gap: 4px;

            img {
                &.star {
                    width: 14px;
                    height: auto;
                    margin-top: -3px;
                }
            }

            small {
                font-size: 14px;
                font-weight: 400;
                line-height: 1;
            }
        }
    }

    .user-email {
        font-size: 12px;
        line-height: 1.3;
        font-weight: 400;
        margin-top: calc(1px + (3 - 1) * ((100vw - 320px) / (1920 - 320)));
        display: flex;
        align-items: flex-end;

        a {
            line-height: 1;
            color: #777;
        }

        .feather {
            width: 14px;
            height: auto;
            margin-left: 10px;
            color: #00162e;
            cursor: pointer;
        }
    }

    .user-details{
        .user-email{
            .feather{
                opacity: 0;
                transition: all 0.1s ease-in-out;
            }
        }
    }
}

.service-list-item {
    display: flex;
    align-items: center;
    text-align: left;
    gap: calc(8px + (10 - 8) * ((100vw - 320px) / (1920 - 320)));
    color: rgba(0, 22, 46, 0.7);

    .img-thumbnail {
        margin: 0;
    }

    .initial-letter {
        width: calc(40px + (44 - 40) * ((100vw - 320px) / (1920 - 320)));
        min-width: auto;
        height: calc(38px + (44 - 38) * ((100vw - 320px) / (1920 - 320)));
        font-size: calc(20px + (22 - 20) * ((100vw - 320px) / (1920 - 320)));
        font-weight: 600;
    }

    .details {
        h5 {
            font-size: 14px;
            font-weight: 500;
            line-height: 1.2;
            letter-spacing: unset;

            a {
                color: #222;
            }
        }
    }

    .user-email {
        font-size: 13px;
        line-height: 1.3;
        font-weight: 400;
        margin-top: calc(1px + (3 - 1) * ((100vw - 320px) / (1920 - 320)));
    }
}

.action-div {
    >.d-inline-block {
        >a {
            color: rgba(0, 22, 46, 0.7);
        }
    }
}

.table {
    &.dataTable {
        &:has(tbody tr td:nth-child(2) .action-div .user-info) {
            thead {
                tr {
                    th {
                        &:nth-child(2) {
                            text-align: left;
                        }
                    }
                }
            }
        }

        .action-div {
            >.d-inline-block {
                >a {
                    color: rgba(0, 22, 46, 0.7);
                }
            }

            // .user-info {
            //     display: flex;
            //     align-items: center;
            //     text-align: left;
            //     gap: calc(8px + (15 - 8) * ((100vw - 320px) / (1920 - 320)));
            //     color: rgba(0, 22, 46, 0.7);

            //     .initial-letter {
            //         width: calc(40px + (44 - 40) * ((100vw - 320px) / (1920 - 320)));
            //         min-width: auto;
            //         height: calc(38px + (44 - 38) * ((100vw - 320px) / (1920 - 320)));
            //         font-size: calc(20px + (22 - 20) * ((100vw - 320px) / (1920 - 320)));
            //         font-weight: 600;
            //     }

            //     .user-name {
            //         font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));
            //         font-weight: 500;
            //         line-height: 1.4;
            //         letter-spacing: unset;
            //     }

            //     .user-email {
            //         font-size: 13px;
            //         line-height: 1.3;
            //         font-weight: 400;
            //         margin-top: calc(1px + (3 - 1) * ((100vw - 320px) / (1920 - 320)));
            //     }
            // }

            &:has(.switch) {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .switch {
                width: 46px;
                height: 24px;

                input {
                    &:checked {
                        +.switch-state {
                            &:before {
                                transform: translateX(24px) translateY(-50%);
                                background-color: var(--theme-color);
                            }
                        }
                    }
                }

                .switch-state {
                    top: 50%;
                    transform: translateY(-50%);
                    height: 100%;

                    &::before {
                        bottom: unset;
                        top: 50%;
                        transform: translateX(3px) translateY(-50%);
                    }
                }
            }
        }
    }
}

.common-table {
    .d-flex {
        &.flex-column {
            .mb-3 {
                small {
                    font-size: 14px;
                }

                .badge {
                    &.bg-info {
                        background-color: $primary-color !important;
                        line-height: 1.5;
                        padding: 0.25em 0.65em;
                    }

                    &.rounded-pill {
                        font-weight: 500;
                        font-size: 12px;
                        padding: 3px 8px;

                        a {
                            .visually-hidden {
                                display: none;
                            }

                            svg {

                                width: 8px !important;
                                height: 8px !important;
                            }
                        }
                    }
                }
            }

            .d-md-flex {
                &.justify-content-between {
                    &.mb-3 {
                        flex-direction: row-reverse !important;

                        .d-md-flex {
                            flex-direction: row-reverse;
                            gap: 12px;
                            position: relative;

                            .ms-md-2 {
                                margin-left: 0 !important;
                            }

                            .btn {
                                &.dropdown-toggle {
                                    background-color: rgba($input-bg, 0.4);
                                    border: 1px solid rgba($dark-color, 0.10);
                                    padding: 12px 16px;
                                    font-size: calc(12px + (14 - 12) * ((100vw - 320px) / (1920 - 320)));
                                    font-weight: 400;
                                    border-radius: $border-radius;

                                    @media (max-width:768px) {
                                        padding: 10px 12px;
                                    }
                                }
                            }

                            .form-select {
                                background-color: rgba($input-bg, 0.4);
                                border: 1px solid rgba($dark-color, 0.10);
                                padding: 12px 16px;
                                font-size: calc(12px + (14 - 12) * ((100vw - 320px) / (1920 - 320)));
                                font-weight: 400;
                                border-radius: $border-radius;
                                width: 80px;

                                &:focus {
                                    box-shadow: none;
                                }

                                @media (max-width:768px) {
                                    padding: 10px 12px;
                                }
                            }

                            .dropdown-menu {
                                .form-select {
                                    width: 100%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    &:has(.common-table-select) {
        .table-responsive {
            .dataTables_wrapper {
                .dataTables_filter {
                    right: 185px;
                }
            }
        }
    }
}

.table-responsive {
    border-radius: $table-radius;
    position: relative;

    .dataTables_wrapper {
        font-family: $font-Poppins;
        position: static;
        overflow: auto;

        .dataTables_length {
            position: absolute;
            left: 0;
            top: 0;

            label {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            select {
                background-color: rgba($input-bg, 0.4);
                border: 1px solid $input-bg;
                padding: 12px 16px;
                font-size: calc(12px + (14 - 12) * ((100vw - 320px) / (1920 - 320)));
                font-weight: 400;
                border-radius: 5px;
                width: 80px;
                background-image: url('../../admin/images/svg/down-arrow.svg');
                background-repeat: no-repeat;
                background-position: right 0.75rem center;
                background-size: 18px 18px;
                appearance: none;

                &:focus {
                    box-shadow: none;
                }

                @media (max-width:768px) {
                    padding: 10px 12px;
                }
            }
        }

        .dataTables_filter {
            position: absolute;
            right: 0;
            top: 0;

            input {
                background-color: rgba($input-bg, 0.4);
                border: 1px solid $input-bg;
                // border-radius: $border-radius;
                padding: 12px 16px;
                font-size: 14px;
                font-weight: 400;
                color: $dark-color;
                width: 220px !important;
                margin-top: 0;
                margin-left: 8px;
                border-radius: 5px;

                &::placeholder {
                    color: $tab-border;
                }

                &:focus,
                &:active {
                    background-color: rgba($input-bg, 0.4);
                    border: 1px solid rgba($dark-color, 0.10);
                }

                &[type="color"] {
                    width: 50px;
                    height: 40px;
                    padding: 8px !important;
                }
            }
        }

        .dataTables_info {
            position: absolute;
            bottom: 10px;
            left: 0;
        }

        .dataTables_paginate {
            display: flex;
            align-items: center;
            gap: 8px;
            position: absolute;
            bottom: 10px;
            right: 0;

            .paginate_button {
                border-radius: 100%;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 30px;
                height: 30px;
                min-width: 30px;
                line-height: 1;
                color: rgba($dark-color, 0.8) !important;

                &:hover {
                    color: $primary-color !important;
                    border-color: rgba($primary-color, 0.01);
                    background: rgba($primary-color, 0.1);
                    box-shadow: unset;
                }

                &.current {
                    color: $white !important;
                    border-color: $primary-color;
                    background: $primary-color;
                }

                &.previous,
                &.next {
                    padding: 0;
                    background: unset;
                    box-shadow: unset;
                    border: unset;
                    outline: unset;

                    &:hover,
                    &:active {
                        background: unset;
                        box-shadow: unset;
                        border: unset;
                        outline: unset;
                        color: rgba($dark-color, 0.8) !important;
                    }
                }

                &.previous {
                    width: 60px;
                }

                &.next {
                    width: 35px;
                }
            }

            span {
                display: inline-flex;
                align-items: center;
                gap: 5px;
            }
        }
    }

    table {
        margin-top: 15px;
        display: inline-table;
        width: 100% !important;
        height: 100%;

        .form-check {
            padding: 0;
            width: fit-content;

            input {
                margin: 0;
            }
        }

        thead {
            tr {
                th {
                    background-color: $primary-color;
                    color: $white;
                    font-size: $table-font;
                    font-weight: $table-font-wt;
                    padding: $th-padding;
                    vertical-align: middle;
                    white-space: nowrap;
                    border: none !important;
                    text-align: center;

                    .form-check {
                        .form-check-input {

                            &:after {
                                background-color: $primary-color;
                                border: 1px solid $white;

                            }

                            &:checked {
                                &:before {
                                    border: 2px solid $white;
                                    border-top-style: none !important;
                                    border-right-style: none !important;
                                }
                            }
                        }
                    }

                    div {
                        gap: 20px;
                    }

                    &:first-child {
                        // width: 25px;
                        border-radius: 10px 0 0 0;
                    }

                    &:last-child {
                        border-radius: 0 10px 0 0;
                    }
                }
            }
        }

        tbody {
            tr {
                border: none;

                td {
                    padding: $td-padding;
                    padding-inline: 18px;
                    font-size: 14px;
                    font-weight: $table-font-wt;
                    border: none;
                    color: $dark-color;
                    vertical-align: middle;
                    text-align: center;

                    &.title {
                        padding-right: 9px;
                    }

                    .badge {

                        &.badge-success {
                            background-color: $success-color;
                            color: $white;
                            font-weight: 500;
                            line-height: 1.3;
                            border: 1px solid $success-color;
                            padding: 0.25em 0.65em;
                        }
                    }

                    .edit-icon,
                    .show-icon,
                    .lock-icon,
                    .delete-svg {
                        background-color: rgba($dark-color, 0.10);
                        border-radius: 6px;
                        padding: 8px;
                        width: calc(32px + (35 - 32) * ((100vw - 320px) / (1920 - 320)));
                        height: calc(32px + (35 - 32) * ((100vw - 320px) / (1920 - 320)));
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        img {
                            width: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
                            height: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
                        }

                        svg {
                            width: calc(17px + (18 - 17) * ((100vw - 320px) / (1920 - 320)));
                            height: calc(17px + (18 - 17) * ((100vw - 320px) / (1920 - 320)));
                            color: rgba($dark-color, 0.7);
                        }
                    }

                    .show-icon {
                        background-color: rgba($dark-color, 0.04);
                    }

                    .delete-svg {
                        background-color: rgba($danger-color, 0.10);

                        svg {
                            color: rgba($danger-color, 0.8);
                        }
                    }

                    .wallet-icon {
                        background-color: rgba($warning-color, 0.20);
                        border-radius: 6px;
                        padding: 8px;
                        width: calc(32px + (35 - 32) * ((100vw - 320px) / (1920 - 320)));
                        height: calc(32px + (35 - 32) * ((100vw - 320px) / (1920 - 320)));
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: $warning-color;

                        svg {
                            width: calc(17px + (18 - 17) * ((100vw - 320px) / (1920 - 320)));
                            height: calc(17px + (18 - 17) * ((100vw - 320px) / (1920 - 320)));
                        }
                    }

                    &:last-child {
                        .action-div {
                            text-align: center;
                            display: flex;
                            flex-wrap: nowrap;
                            align-items: center;
                            justify-content: center;
                            gap: 8px;
                        }
                    }

                    .switch {
                        margin-bottom: -6px;
                    }

                    // &:first-child {
                    //     width: 25px;
                    // }

                    .input-group-text {
                        border: none;
                    }
                }

                &:nth-child(even) {
                    background-color: rgba($dark-color, 0.04);

                    td {
                        background: $transparent-color;
                    }
                }

                &:nth-child(odd) {
                    background-color: $white;
                    background-image: unset;

                    td {
                        background: $transparent-color;
                    }
                }

                &:last-child {
                    td {
                        &:first-child {
                            border-bottom-left-radius: 8px;
                        }

                        &:last-child {
                            border-bottom-right-radius: 8px;
                        }
                    }
                }
            }
        }

        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    position: relative;

                    &:before,
                    &:after {
                        top: 50% !important;
                        transform: translateY(-50%);
                        bottom: unset;
                        font-size: 20px !important;
                    }

                    &:before {
                        right: 14px;
                    }
                }
            }

            &.no-footer {
                margin-block: 62px 56px !important;
                padding-bottom: 16px;
                // margin-top: 62px !important;
                // margin-bottom: 56px !important;
                border-bottom: 1px dashed rgba($dark-color, 0.1);
            }
        }
    }

    &.modal-table {
        overflow-x: unset;

        table {
            margin: 0 !important;
            box-shadow: 0 0 0 1px rgba($dark-color, 0.1);
            border-radius: 8px;
            overflow: hidden;

            tbody {
                tr {
                    td {
                        padding: 12px;
                        text-align: left;
                        font-size: 14px;

                        &:first-child {
                            min-width: 170px;
                            font-weight: 500;
                        }

                        textarea {
                            &.form-control {
                                min-height: 80px;
                            }
                        }

                        .form-control {
                            background: unset !important;
                            border: none !important;
                            padding: 0;
                            opacity: 0.8;
                        }
                    }
                }
            }
        }
    }

    &.language-table {
        table {
            margin-top: 12px !important;

            thead {
                tr {
                    th {
                        min-width: 250px;
                    }
                }
            }

            tbody {
                tr {
                    td {
                        input {
                            background-color: transparent;
                        }
                    }
                }
            }
        }
    }

    &.service-detail {
        table {
            margin: 0 !important;

            thead {
                tr {
                    th {
                        &:first-child {
                            width: 250px;
                        }
                    }
                }
            }

            tbody {
                tr {
                    td {
                        .booking-icon {
                            display: inline-block;
                            width: max-content;
                        }
                    }
                }
            }
        }
    }

    &.review-box {
        border: 1px solid $input-bg;

        .table {
            margin: 0 !important;

            tbody {
                tr {
                    background-color: $white !important;
                    border-bottom: 1px solid $input-bg;

                    &:last-child {
                        border-bottom: none;
                    }

                    td {
                        padding: 12px 13px;
                        vertical-align: middle;

                        .review-content {
                            display: flex;
                            align-items: center;
                            gap: 10px;

                            .img-box {
                                width: 40px;
                                min-width: 40px;
                                height: 40px;
                                border-radius: 6px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                overflow: hidden;
                            }

                            span {
                                text-align: left;
                                width: 100%;
                                overflow: hidden;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;
                                overflow: hidden;

                            }
                        }

                        &.review-rate {
                            white-space: nowrap;
                        }

                        &:nth-child(1) {
                            width: auto;
                            min-width: 218px;
                        }

                        &:nth-child(2) {
                            width: auto;
                            min-width: 150px;
                            text-align: center;
                        }
                    }
                }
            }
        }
    }

    &.provider-box {

        .table {
            margin: 0 !important;
        }

        tbody {
            tr {
                td {
                    padding: 12px 14px;

                    .provider-detail {
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        .provider-img {
                            width: 45px;
                            height: 45px;
                            border-radius: 100%;
                        }

                        .location {
                            svg {
                                width: 18px;
                                height: 18px;
                                color: $dark-gray;
                            }
                        }

                        h5 {
                            text-transform: capitalize;
                            font-weight: 500;
                            margin-bottom: 0;
                        }
                    }

                    &:first-child {
                        width: auto;
                        min-width: 320px;
                    }

                    &:last-child {
                        width: auto;
                        min-width: 70px;
                    }
                }

                &:last-child {
                    td {
                        border: none;
                    }
                }
            }

            .rate {
                small {
                    line-height: 1;
                    padding-top: 2px;
                }
            }
        }
    }

    &.service-box {

        .table {
            margin: 0 !important;
        }

        thead {
            tr {
                th {
                    &:last-child {
                        text-align: end;
                    }

                    &:first-child {
                        text-align: start;
                    }
                }
            }
        }

        tbody {
            tr {
                td {
                    padding: 12px 14px;

                    .service-detail {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        text-align: left;

                        .service-img {
                            width: 45px;
                            height: 45px;
                            border-radius: 6px;
                        }

                        h5 {
                            text-transform: capitalize;
                            font-weight: 500;
                            margin-bottom: 0;
                        }
                    }

                    &:nth-child(1) {
                        width: 100%;
                        min-width: 310px;
                    }

                    &:nth-child(2) {
                        width: auto;
                        min-width: 95px;
                    }

                    &:nth-child(3) {
                        width: auto;
                        min-width: 80px;
                    }
                }

                &:last-child {
                    td {
                        border: none;
                    }
                }
            }
        }
    }

    &.booking-box {
        .table {
            margin: 0 !important;
        }

        thead {
            tr {
                th {
                    &:last-child {
                        text-align: end;
                    }

                    &:first-child {
                        text-align: start;
                    }
                }
            }
        }

        tbody {
            tr {
                td {
                    padding: 14px 16px;

                    .booking-data {
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        .booking-img {
                            width: 50px;
                            height: 50px;
                            border-radius: 6px;
                        }

                        h5 {
                            text-transform: capitalize;
                            font-weight: 400;
                            margin-bottom: 0;
                            color: $dark-color;

                            &:hover {
                                color: $primary-color;
                            }
                        }

                        h6 {
                            color: $dark-text;
                            text-align: left;
                        }
                    }

                    &:first-child {
                        width: 100%;
                        min-width: 270px;
                    }

                    &:nth-last-child(2) {
                        width: auto;
                    }
                }

                &:last-child {
                    td {
                        border: none;
                    }
                }
            }
        }
    }
}

.latest-blogs {
    .col-12 {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .no-data-detail {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
}

.table-no-data {
    text-align: center;
    padding: 60px 0;

    h4 {
        font-size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
        font-weight: 500;
        color: $dark-gray;
    }
}

.initial-letter {
    width: calc(38px + (40 - 38) * ((100vw - 320px) / (1920 - 320)));
    min-width: 38px;
    height: calc(38px + (40 - 38) * ((100vw - 320px) / (1920 - 320)));
    border-radius: 100%;
    display: inline-flex;
    color: rgba($dark-color, 0.7);
    align-items: center;
    justify-content: center;
    background-color: rgba($dark-color, 0.08);
    font-size: 20px;
    text-transform: uppercase;
}

.data-not-found {
    text-align: center;
    padding: 8px 0;

    span {
        display: block;
        font-size: 16px;
        font-weight: 500;
        color: rgba($dark-color, 0.7);
    }
}

.wallet-main {
    label {
        &.col-md-2 {
            display: none;
        }
    }

    .col-md-10 {
        width: 100%;
    }
}

.full-width {
    width: 100%;
}

.user-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(3) {
                        min-width: 150px;
                    }

                    &:nth-child(4) {
                        min-width: 150px;
                    }

                    &:nth-child(5) {
                        min-width: 140px;
                    }

                    &:nth-child(6) {
                        min-width: 240px;
                    }

                    &:nth-child(7) {
                        min-width: 80px;
                    }

                    &:last-child {
                        min-width: 150px;
                    }
                }
            }
        }

        tbody {
            tr {
                td {
                    &:last-child {
                        .action-div {
                            justify-content: center;
                        }
                    }
                }
            }
        }
    }
}

.customer-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(3) {
                        min-width: 245px;
                    }

                    &:nth-child(4) {
                        min-width: 150px;
                    }

                    &:nth-child(6) {
                        min-width: 230px;
                    }

                    &:nth-child(7) {
                        min-width: 80px;
                    }
                }
            }
        }

        tbody {
            tr {
                td {
                    .action-div {
                        text-align: left;

                        .user-info {
                            display: flex;
                            align-items: center;
                            gap: calc(0px + (7 - 0) * ((100vw - 320px) / (1920 - 320)));
                        }

                        .user-details {
                            color: #222;
                        }
                    }
                }
            }
        }
    }
}

.role-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 220px;
                    }

                    &:nth-child(3) {
                        min-width: 200px;
                    }

                    &:nth-child(4) {
                        min-width: 200px;
                    }

                }
            }
        }
    }
}

.provider-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 50px;
                    }

                    // &:nth-child(3) {
                    //     min-width: 310px;
                    // }

                    // &:nth-child(4) {
                    //     min-width: 140px;
                    // }

                    // &:nth-child(5) {
                    //     min-width: 120px;
                    // }

                    // &:nth-child(6) {
                    //     min-width: 240px;
                    // }

                    // &:nth-child(7) {
                    //     min-width: 70px;
                    // }
                }
            }
        }
    }
}

.provider-transaction-table {
    .table {
        thead {
            tr {
                th {
                    &:nth-child(1) {
                        min-width: 170px;
                    }

                    &:nth-child(2) {
                        min-width: 120px;
                    }

                    &:nth-child(3) {
                        min-width: 300px;
                    }

                    &:nth-child(4) {
                        min-width: 240px;
                    }
                }
            }
        }

        tbody {
            tr {
                td {
                    .badge {
                        padding: 7px 12px;
                        border-radius: 6px;
                    }
                }
            }
        }
    }
}

.provider-wallet-table {
    .table {
        margin-top: 0 !important;

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 160px;
                    }

                    &:nth-child(3) {
                        min-width: 170px;
                    }

                    &:nth-child(4) {
                        min-width: 350px;
                    }

                    &:nth-child(5) {
                        min-width: 250px;
                    }
                }
            }
        }
    }
}

.provider-document-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 100px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 170px;
                    }
                }
            }
        }
    }
}

.provider-time-slot-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }


        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 250px;
                    }

                    &:nth-child(3) {
                        min-width: 80px;
                    }

                    &:nth-child(4) {
                        min-width: 200px;
                    }
                }
            }
        }
    }
}

.service-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }

                tr {
                    th {

                        &:nth-child(2),
                        &:nth-child(3) {
                            text-align: left;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 350px;
                    }

                    // &:nth-child(3) {
                    //     min-width: 360px;
                    // }

                    // &:nth-child(4) {
                    //     min-width: 240px;
                    // }

                    // &:nth-child(5) {
                    //     min-width: 110px;
                    // }

                    // &:nth-child(6) {
                    //     min-width: 230px;
                    // }

                    // &:nth-child(7) {
                    //     min-width: 80px;
                    // }

                }
            }
        }
    }
}

.service-package-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 100px;
                    }

                    &:nth-child(3) {
                        min-width: 300px;
                    }

                    &:nth-child(4) {
                        min-width: 140px;
                    }

                    &:nth-child(5) {
                        min-width: 240px;
                    }

                    &:nth-child(6) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.zone-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 300px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.blog-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 320px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 160px;
                    }

                    &:nth-child(5) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.tag-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 230px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 80px;
                    }

                }
            }
        }
    }
}

.page-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 100px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 240px;
                    }

                    &:nth-child(5) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.tax-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 220px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.zone-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 220px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.currency-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 100px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.transaction-table {
    .table {
        thead {
            tr {
                th {
                    &:nth-child(1) {
                        min-width: 100px;
                    }

                    &:nth-child(2) {
                        min-width: 150px;
                    }

                    &:nth-child(3) {
                        min-width: 150px;
                    }

                    &:nth-child(4) {
                        min-width: 300px;
                    }

                    &:nth-child(5) {
                        min-width: 230px;
                    }

                }
            }
        }
    }
}

.serviceman-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(5) {
                        min-width: 240px;
                    }

                    &:nth-child(6) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.document-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 250px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.coupon-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 150px;
                    }

                    &:nth-child(3) {
                        min-width: 240px;
                    }

                    &:nth-child(4) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.plan-table {
    .table {
        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 160px;
                    }

                    &:nth-child(3) {
                        min-width: 150px;
                    }

                    &:nth-child(6) {
                        min-width: 230px;
                    }

                }
            }
        }
    }
}

.banner-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 100px;
                    }

                    &:nth-child(3) {
                        min-width: 230px;
                    }

                    &:nth-child(4) {
                        min-width: 180px;
                    }

                    &:nth-child(5) {
                        min-width: 240px;
                    }

                    &:nth-child(6) {
                        min-width: 80px;
                    }
                }
            }
        }
    }
}

.common-table-select {
    position: absolute;
    top: 87px;
    right: 20px;
    z-index: 1;
    width: 170px;

    .select2 {
        &.select2-container {
            .select2-selection {
                &.select2-selection--single {
                    .select2-selection__placeholder {
                        line-height: 1.2;
                    }
                }
            }
        }
    }
}

.booking-table {
    // .booking-select {
    //     width: 220px !important;
    //     margin-bottom: 16px;
    //     margin-left: auto;
    //     display: block;
    // }

    // .common-table-select{
    //     position: absolute;
    //     top: 87px;
    //     right: 32px;
    //     z-index: 1;
    //     width: 170px !important;
    //     .select2{
    //         &.select2-container {
    //             .select2-selection{
    //                 &.select2-selection--single {
    //                     .select2-selection__placeholder{
    //                         line-height: 1.2;
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    .table {
        thead {
            tr {
                th {
                    // &:nth-child(1) {
                    //     min-width: 160px;
                    // }

                    &:nth-child(2) {
                        min-width: 170px;
                    }

                    &:nth-child(3) {
                        min-width: 160px;
                    }

                    &:nth-child(4) {
                        min-width: 120px;
                    }

                    &:nth-child(5) {
                        min-width: 140px;
                    }

                    &:nth-child(6) {
                        min-width: 90px;
                    }

                    &:nth-child(7) {
                        min-width: 150px;
                    }

                    &:nth-child(8) {
                        min-width: 126px;
                    }
                }
            }
        }
    }

    tbody {
        tr {
            td {
                .form-controll {
                    color: $dark-color;

                    &:hover {
                        color: $primary-color;
                    }
                }

                .badge {
                    padding-block: 6px;

                    &.COMPLETED {
                        color: $success-color;
                        background-color: rgba($success-color, 0.1);
                    }

                    &.PENDING {
                        color: $warning-color;
                        background-color: rgba($warning-color, 0.1);
                    }

                    &.FAILED {
                        color: #FF4B4B;
                        background-color: rgba(255, 75, 75, 0.1);
                    }
                }

                span {
                    color: var(--theme-color);
                }
            }
        }
    }
}

.sub-booking-table {
    .table {
        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 260px;
                    }

                    &:nth-child(3) {
                        min-width: 250px;
                    }

                    &:nth-child(4) {
                        min-width: 160px;
                    }
                }
            }
        }
    }
}

.commission-history-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 180px;
                    }

                    &:nth-child(3) {
                        min-width: 250px;
                    }

                    &:nth-child(4) {
                        min-width: 170px;
                    }

                    &:nth-child(5) {
                        min-width: 170px;
                    }
                }
            }
        }
    }
}

.serviceman-info-table {
    .table {
        thead {
            tr {
                th {
                    &:nth-child(1) {
                        min-width: 210px;
                    }

                    &:nth-child(2) {
                        min-width: 270px;
                    }

                    &:nth-child(3) {
                        min-width: 180px;
                    }
                }
            }
        }
    }
}

.withdraw-request-table {
    .table {
        thead {
            tr {
                th {

                    &:nth-child(1) {
                        min-width: 250px;
                    }

                    &:nth-child(2) {
                        min-width: 170px;
                    }

                    &:nth-child(3) {
                        min-width: 170px;
                    }

                    &:nth-child(4) {
                        min-width: 250px;
                    }
                }
            }
        }
    }
}

.notification-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 230px;
                    }

                    &:nth-child(3) {
                        min-width: 300px;
                    }

                    &:nth-child(4) {
                        min-width: 200px;
                    }

                    &:nth-child(5) {
                        min-width: 200px;
                    }
                }
            }
        }
    }
}

.consumer-transaction-table {
    .table {
        thead {
            tr {
                th {
                    &:nth-child(1) {
                        min-width: 180px;
                    }

                    &:nth-child(2) {
                        min-width: 200px;
                    }

                    &:nth-child(3) {
                        min-width: 300px;
                    }

                    &:nth-child(4) {
                        min-width: 240px;
                    }
                }
            }
        }
    }
}

.userreview-table {
    .table {
        &.dataTable {
            thead {

                .sorting,
                .sorting_asc,
                .sorting_desc,
                .sorting_asc_disabled,
                .sorting_desc_disabled {
                    &:first-child {

                        &:before,
                        &:after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 230px;
                    }

                    &:nth-child(3) {
                        min-width: 230px;
                    }

                    &:nth-child(4) {
                        min-width: 120px;
                    }

                    &:nth-child(5) {
                        min-width: 300px;
                    }
                }
            }
        }
    }
}

.language-table {
    table {
        thead {
            tr {
                th {
                    &:nth-child(2) {
                        min-width: 250px;
                    }

                    &:nth-child(3) {
                        min-width: 140px;
                    }

                    &:nth-child(4) {
                        min-width: 140px;
                    }

                    &:nth-child(5) {
                        min-width: 80px;
                    }

                    &:nth-child(6) {
                        min-width: 80px;
                    }

                    &:nth-child(7) {
                        min-width: 240px;
                    }
                }
            }
        }
    }
}

.service-list-item {
    display: flex;
    align-items: center;

    .img-thumbnail {
        width: 50px;
        height: 50px;
    }

    .details {
        // padding-left: 0.5rem;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0;

        h5 {
            margin-bottom: 0;
        }

        .info {
            font-size: small;
            color: #6c757d;
            display: flex;
            // gap: 10px;
            margin-top: 3px;

            span {
                position: relative;

                +span {
                    margin-left: 6px;
                    padding-left: 6px;
                    // border-left: 1px solid #6c757d38;

                    &::before {
                        content: "";
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: 0;
                        width: 1px;
                        height: calc(100% - 4px);
                        border-left: 1px solid #6c757d38;
                        // border-radius: 100%;
                        // background-color: #6c757d85;
                    }
                }
            }
        }
    }
}

.service-table {

    thead,
    tbody {
        tr {

            th,
            td {
                &:first-child {
                    width: auto;
                }
            }
        }
    }
}

/**=====================
      Tables CSS End
==========================**/
