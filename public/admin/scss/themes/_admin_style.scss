/**=====================
Admin CSS Start
==========================**/

.jstree-loader {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;

    img {
        width: 70px;
    }
}

.no-icons {
    li {

        i,
        svg,
        img {
            display: none;
        }
    }
}

.row {
    >div {
        &:nth-child(1) {
            .widget-card {
                background-color: rgba($dark-gray, 0.2);

                .widget-icon {
                    background-color: $dark-gray;
                }
            }
        }

        &:nth-child(2) {
            .widget-card {
                background-color: rgba($warning-color, 0.2);

                .widget-icon {
                    background-color: $warning-color;
                }
            }
        }

        &:nth-child(3) {
            .widget-card {
                background-color: rgba($success-color, 0.2);

                .widget-icon {
                    background-color: $success-color;
                }
            }
        }

        &:nth-child(4) {
            .widget-card {
                background-color: rgba($tertairy-color, 0.2);

                .widget-icon {
                    background-color: $tertairy-color;
                }
            }
        }

        &:nth-child(5) {
            .widget-card {
                background-color: rgba($danger-color, 0.2);

                .widget-icon {
                    background-color: $danger-color;
                }
            }
        }
    }
}

.widget-card,
.booking-widget-card {
    padding: 16px;
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    gap: 4px;

    h3 {
        color: $dark-color;
        font-size: calc(20px + (28 - 20) * ((100vw - 320px) / (1920 - 320)));
        line-height: 28px;
        font-weight: 500;
        margin-bottom: 0;
        display: flex;
        align-items: end;
        gap: 4px;
    }

    h5 {
        margin-bottom: 0;
    }

    svg {
        color: $white;
        width: 22px;
        height: 22px;
    }

    .widget-icon,
    .booking-widget-icon {
        padding: 10px;
        border-radius: 4px;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.row {
    >div {
        &:nth-child(1) {
            .widget-card-2 {
                background-color: rgba($dark-gray, 0.2);

                .widget-icon {
                    background-color: $dark-gray;
                }
            }
        }

        &:nth-child(2) {
            .widget-card-2 {
                background-color: rgba($warning-color, 0.2);

                .widget-icon {
                    background-color: $warning-color;
                }
            }
        }

        &:nth-child(3) {
            .widget-card-2 {
                background-color: rgba($success-color, 0.2);

                .widget-icon {
                    background-color: $success-color;
                }
            }
        }

        &:nth-child(4) {
            .widget-card-2 {
                background-color: rgba($tertairy-color, 0.2);

                .widget-icon {
                    background-color: $tertairy-color;
                }
            }
        }

        &:nth-child(5) {
            .widget-card-2 {
                background-color: rgba($danger-color, 0.2);

                .widget-icon {
                    background-color: $danger-color;
                }
            }
        }
    }
}

.widget-card-2 {
    padding: calc(12px + (16 - 12) * ((100vw - 320px) / (1920 - 320)));
    display: flex;
    flex-direction: row;
    justify-content: unset;
    gap: calc(9px + (13 - 9) * ((100vw - 320px) / (1920 - 320)));
    align-items: flex-start;
    border-radius: calc(6px + (8 - 6) * ((100vw - 320px) / (1920 - 320)));

    h3 {
        display: block;
        font-size: calc(20px + (26 - 20) * ((100vw - 320px) / (1920 - 320)));
        line-height: 1;
        margin-top: calc(4px + (6 - 4) * ((100vw - 320px) / (1920 - 320)));
        font-weight: 500;
        color: #2263eb;
        margin-bottom: 0;
    }

    h5 {
        line-height: 1.4;
        font-weight: 400;
        letter-spacing: 0.8px;
        color: #777;
        font-size: calc(14px + (15 - 14) * ((100vw - 320px) / (1920 - 320)));
    }

    // svg {
    //     color: $white;
    //     width: 22px;
    //     height: 22px;
    // }

    .widget-icon {
        padding: 0;
        width: calc(38px + (46 - 38) * ((100vw - 320px) / (1920 - 320)));
        height: calc(38px + (46 - 38) * ((100vw - 320px) / (1920 - 320)));
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
            width: calc(20px + (24 - 20) * ((100vw - 320px) / (1920 - 320)));
            height: auto;
            color: $white;
        }
    }
}

.booking-status-main {
    .booking-status-card {
        .booking-widget-card {
            border: 1px solid $input-bg;
            background-color: $transparent-color;

            .booking-widget-icon {
                svg {
                    width: 22px;
                    height: 22px;
                }
            }
        }

        &:nth-child(1) {
            .booking-widget-card {
                border-bottom: 2px solid $primary-color;

                .booking-widget-icon {
                    background-color: rgba($primary-color, 0.2);

                    svg {
                        color: $primary-color;
                    }
                }
            }
        }

        &:nth-child(2) {
            .booking-widget-card {
                border-bottom: 2px solid $warning-color;

                .booking-widget-icon {
                    background-color: rgba($warning-color, 0.2);

                    svg {
                        color: $warning-color;
                    }
                }
            }
        }

        &:nth-child(3) {
            .booking-widget-card {
                border-bottom: 2px solid $tertairy-color;

                .booking-widget-icon {
                    background-color: rgba($tertairy-color, 0.2);

                    svg {
                        color: $tertairy-color;
                    }
                }
            }
        }

        &:nth-child(4) {
            .booking-widget-card {
                border-bottom: 2px solid $dark-color;

                .booking-widget-icon {
                    background-color: rgba($dark-color, 0.2);

                    svg {
                        color: $dark-color;
                    }
                }
            }
        }

        &:nth-child(5) {
            .booking-widget-card {
                border-bottom: 2px solid $danger-color;

                .booking-widget-icon {
                    background-color: rgba($danger-color, 0.2);

                    svg {
                        color: $danger-color;
                    }
                }
            }
        }

        &:nth-child(6) {
            .booking-widget-card {
                border-bottom: 2px solid $info-color;

                .booking-widget-icon {
                    background-color: rgba($info-color, 0.2);

                    svg {
                        color: $info-color;
                    }
                }
            }
        }
    }
}

.blog-box {
    .blog-img {
        margin-bottom: 12px;
        display: block;
        overflow: hidden;

        img {
            object-fit: cover;
            width: 100%;
            height: 180px;
            border-radius: 8px;

        }
    }

    .blog-content {
        a {
            color: rgba($dark-color, 0.80);
            font-size: 16px;
            margin-bottom: 4px;
            display: block;
            font-weight: 500;
            letter-spacing: .01em;
            width: 100%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        h6 {
            color: rgba($dark-color, 0.60);
            margin-bottom: 0;
            line-height: 1.3;
        }
    }
}

.rate {
    display: flex;
    align-items: center;
    gap: 6px;

    small {
        font-size: 14px;
        font-weight: 500;
        line-height: 1.2;
    }

    img {
        &.star {
            width: 15px;
            height: 15px;
        }
    }
}

.location {
    display: flex;
    align-items: start;
    justify-content: center;
    gap: 4px;

    i {
        --Iconsax-Size: 18px;
        --Iconsax-Color: #808B97;
        margin-top: 3px;
    }

    h6 {
        line-height: 20px;
        color: $dark-gray;
        margin: 0;
        width: 100%;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
}

.static-top-widget {
    gap: 15px;

    div.align-self-center {
        svg {
            width: 25px;
            height: 25px;
            vertical-align: middle;
        }
    }

    .media-body {
        align-self: center !important;

        h3 {
            font-family: $font-Poppins, $font-serif;
            color: $white;

            small {
                font-size: 11px;
            }
        }
    }

    .icons-widgets {
        .text-center {
            width: 50px;
            height: 50px;
            border-radius: 100%;
            background-color: $white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.account-setting {
    h5 {
        margin-bottom: 20px;
    }
}

// .notify-img {
//     height: auto;
//     width: 300px;
// }

/*======= text-color css end  ======= */

/**=====================
  Admin CSS Ends
==========================**/