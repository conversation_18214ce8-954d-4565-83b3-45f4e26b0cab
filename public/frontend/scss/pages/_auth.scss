/**=====================
    Auth scss
==========================**/

.log-in-section {
    width: 100%;
    margin: 0 auto;
    overflow: auto;
    position: relative;
    height: 100vh;

    .login-content {
        height: 100%;
    }
}

.image-contain {
    background-color: var(--theme-color);
    padding: 50px;
    text-align: center;
    flex-direction: column;
    @include flex_common;
    height: 100%;

    .logo {
        height: 50px;
        margin: 0 auto;
        display: flex;
        margin-bottom: 30px;
    }

    .auth-image {
        width: 40%;
    }

    .auth-content {
        color: $white;
        margin-top: 50px;

        h2 {
            text-transform: uppercase;
            line-height: 41px;
            font-weight: 700;
        }

        p {
            font-size: 16px;
            line-height: 20px;
        }

        .app-install {
            margin-top: 30px;
            @include flex_common;
            gap: 16px;

            img {
                height: 50px;
            }
        }
    }
}

.login-main {
    background-color: $section-bg;
    height: 100%;
    position: relative;
    @include flex_common;

    .language-dropdown {
        position: absolute;
        top: 40px;
        right: 40px;
        @include flex_common ($dis: flex, $align: center, $justify: end);
        gap: 6px;
        padding: 5px 8px;
        border-radius: 30px;
        border: 1px solid rgba($title-color, 0.10);
        background-color: $white;
        margin-bottom: 30px;
        width: max-content;
        margin-left: auto;

        [dir="rtl"] & {
            right: unset;
            left: 40px;
        }

        img {
            width: 24px !important;
            height: 24px !important;
            border-radius: 100%;
        }

        i {
            --Iconsax-Size: 18px;
            --Iconsax-Color: #2263eb;
        }

        .language-btn {
            background: transparent;
            color: $title-color;
            border: none;
            line-height: 1;
            padding: 0;
            @include flex_common_1 ($dis: flex, $align: center);
            gap: 12px;

            span {
                @include flex_common_1 ($dis: inline-flex, $align: center);
                gap: 6px;
                line-height: 1;
                font-size: 16px;


                a {
                    font-size: 18px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    color: #2263eb;

                }
            }
        }

        .onhover-show-div {
            min-width: 120px;
            transition: all 0.3s linear;
            position: absolute;
            z-index: 3;
            border: 1px solid rgba($title-color, 0.10);
            background-color: $white;
            opacity: 0;
            visibility: hidden;
            transform: translate3d(0, -5%, 0);
            top: 37px;
            right: 0;
            padding: 15px;
            border-radius: 4px;


            [dir="rtl"] & {
                left: 0;
                right: unset;
            }

            .lang {
                cursor: pointer;
                transition: all 0.3s;
                @include flex_common_1 ($dis: flex, $align: center);
                border: none;
                color: $title-color;
                background-color: transparent;
                gap: 8px;
                font-size: 13px;
                padding: 0;

                // img {
                //     border-radius: 0 !important;
                //     width: auto;
                //     height: auto;
                // }


                &:hover {
                    color: var(--theme-color);
                    transform: translateX(5px);

                    [dir="rtl"] & {
                        transform: translateX(-5px);
                    }
                }

                +li {
                    margin-top: 8px;
                }
            }

            a {
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 8px;
                color: #2263eb;

            }
        }

        &:hover {
            .onhover-show-div {
                opacity: 1;
                visibility: visible;
                transform: none;
            }
        }

        @media (max-width:425px) {
            right: 20px;

            [dir="rtl"] & {
                left: 20px;
                right: unset;
            }
        }
    }

    .login-card {
        width: 100%;
        @include flex_common;
        flex-direction: column;
        background-color: white;
        width: $login-card;
        border-radius: $login-br;
        margin: 20px auto;
        padding-bottom: 40px;

        .login-title {
            width: 100%;
            border-bottom: 1px dashed $gray-color;
            padding: calc(16px + (25 - 16) * ((100vw - 320px) / (1920 - 320)));
            text-align: center;

            h2 {
                font-weight: 700;
                line-height: 33px;
            }

            p {
                color: $light-color;
                padding-top: 6px;
                font-size: 16px;
                margin: 0;
            }
        }

        .login-detail {
            background-color: $section-bg;
            border-radius: 10px;
            width: calc(100% - 80px);
            margin: 30px 0;
            padding: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320)));

            .form-group {
                position: relative;
                margin-bottom: calc(12px + (24 - 12) * ((100vw - 320px) / (1920 - 320)));

                i {
                    --Iconsax-Size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
                    --Iconsax-Color: #808B97;
                    position: absolute;
                    top: calc(42px + (45 - 42) * ((100vw - 320px) / (1920 - 320)));
                    left: 15px;

                    [dir="rtl"] & {
                        left: unset;
                        right: 15px;
                    }

                }

                .toggle-password {
                    position: absolute;
                    top: 0;
                    right: 45px;
                    left: unset;
                    cursor: pointer;

                    [dir="rtl"] & {
                        left: 45px;
                        right: unset;
                    }

                    i {
                        position: absolute;
                    }

                    .eye-slash {
                        display: none;
                    }

                    &.eye {
                        i.eye {
                            display: block;
                        }

                        i.eye-slash {
                            display: none;
                        }
                    }

                    &.eye-slash {
                        i.eye-slash {
                            display: block;
                        }

                        i.eye {
                            display: none;
                        }
                    }
                }

                label {
                    font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));
                    font-weight: 500;
                    line-height: 23px;
                    margin-bottom: 8px;
                    position: relative;

                    [dir="rtl"] & {
                        text-align: right;
                    }

                    &.error {
                        margin-bottom: 0;
                        margin-top: 10px;
                        font-weight: 400;
                        font-size: 13px;
                        opacity: 0.8;
                        color: $decline-color !important;

                        &:before {
                            display: none;
                        }
                    }

                    &:before {
                        @include pos;
                        background-color: var(--theme-color);
                        height: 30px;
                        width: 4px;
                        top: -3px;
                        left: -30px;
                        border-radius: 4px;

                        [dir="rtl"] & {
                            left: unset;
                            right: -30px;
                        }

                        @media (max-width:1199px) {
                            left: -20px;

                            [dir="rtl"] & {
                                left: unset;
                                right: -20px;
                            }
                        }
                    }
                }

                .form-control {
                    border-radius: 8px;
                    padding: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) 45px;
                    background-color: $white;
                    color: rgba($title-color, 0.8) !important;
                    border: none;

                    [dir="rtl"] & {
                        padding: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) 45px calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320)));
                    }





                    &:focus {
                        box-shadow: none;
                    }
                }
            }

            .forgot-pass {
                margin-top: 12px;
                text-align: end;

                [dir="rtl"] & {
                    text-align: start;
                }

                a {
                    font-size: 16px;
                    line-height: 20px;
                }
            }

            .btn {
                margin-top: 40px;
                border-radius: 8px;
                width: 100%;
                justify-content: center;
                font-size: 18px;

                &:focus {
                    box-shadow: none;
                }
            }

            .not-member {
                text-align: center;
                font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));
                font-weight: 400;
                color: $light-color;
                line-height: 20px;
                padding-top: 10px;

                small {
                    color: var(--theme-color);
                    font-weight: 500;
                }
            }

            .phone-details {
                .form-group {
                    position: relative;
                    label {
                        &.error {
                            position: absolute !important;
                            bottom: -30px;
                            left: 0;
                        }
                    }
                }
            }
        }

        .other-options {
            width: 100%;
            padding: calc(20px + (30 - 20) * ((100vw - 320px) / (1920 - 320))) 0 0;
        }

        .options {
            position: relative;
            text-align: center;
            width: max-content;
            margin: 0 auto;
            color: $light-color;
            font-size: 18px;
            display: flex;

            &:before,
            &:after {
                @include pos;
                background-color: $light-color;
                width: 22px;
                height: 1px;
                @include center(vertical);
            }

            &:before {
                left: -30px;

                [dir="rtl"] & {
                    left: unset;
                    right: -30px;
                }
            }

            &:after {
                right: -30px;

                [dir="rtl"] & {
                    right: unset;
                    left: -30px;
                }
            }
        }

        .social-media {
            @include flex_common;
            gap: 20px;
            margin-top: calc(15px + (24 - 15) * ((100vw - 320px) / (1920 - 320)));

            .social-icon {
                background-color: $section-bg;
                border-radius: 100%;
                border: 1px solid rgba($title-color, 0.1);
                height: calc(50px + (70 - 50) * ((100vw - 320px) / (1920 - 320)));
                width: calc(50px + (70 - 50) * ((100vw - 320px) / (1920 - 320)));
                @include flex_common;

                img {
                    width: calc(25px + (30 - 25) * ((100vw - 320px) / (1920 - 320)));
                    height: calc(25px + (30 - 25) * ((100vw - 320px) / (1920 - 320)));
                }
            }
        }

        @include mq-max(sm) {
            width: 100%;
        }
    }

}

.terms {
    p {
        font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));
        ;
        margin: 0;
        color: $light-color;
    }
}

.terms {
    position: relative;
    padding-bottom: 25px;

    label {
        position: absolute;
        bottom: 0;

        &.error {
            color: #FF4B4B !important;
        }
    }
}

.login-img {
    width: 250px;
    padding-top: 30px;
}

.otp-field {
    @include flex_common_1 ($dis: flex, $align: center);
    gap: 12px;
    position: relative;

    input {
        position: relative;
        width: 50px;
        height: 50px;
        border: none;
        background-color: $white;
        border-radius: 8px;
        text-align: center;
    }

    label {
        &.error {
            position: absolute !important;
            bottom: -30px !important;
            left: 0;
        }
    }
}

.service-list-section {
    .row {
        [class*="col-"] {
            &:has(.no-cart-found) {
                &:first-child {
                    width: 100%;
                }
            }
        }
    }
}