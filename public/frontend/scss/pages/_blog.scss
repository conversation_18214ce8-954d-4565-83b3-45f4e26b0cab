/**=====================
    Blog scss
==========================**/

.blog-section {
    .detail-content {
        margin-top: calc(14px + (24 - 14) * ((100vw - 320px) / (1920 - 320)));

        .title {
            padding: 0;
            margin-bottom: 5px;

            h4 {
                font-size: calc(22px + (30 - 22) * ((100vw - 320px) / (1920 - 320)));
                font-weight: 700;
                line-height: 1.3;
                color: #2263eb;
                white-space: wrap;
            }
        }

        .detail-sec {
            margin-top: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));
        }
    }

    .blog-content {
        // background-color: $section-bg;
        // padding: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320)));
        // border-radius: 6px;
        // margin-bottom: 30px;

        &:last-child {
            margin-bottom: 0;
        }

        .blog-main {
            background-color: #F5F6F7;
            border-radius: 15px;
            // padding-inline: 0;

            +.blog-main {
                // border-left: 1px solid $gray-color;

                &:nth-child(3n+4) {
                    border-left: none;

                    [dir="rtl"] & {
                        border-right: none;
                        border-left: unset;

                    }
                }

                @include mq-max(xl) {
                    border: none;
                }
            }

            @include mq-max(xl) {
                border: none;

                &:nth-child(even) {
                    border-left: 1px solid $gray-color !important;

                    [dir="rtl"] & {
                        border-right: 1px solid $gray-color !important;
                        border-left: unset !important;
                    }
                }
            }

            @include mq-max(md) {
                border: none !important;

                &:nth-child(even) {
                    border: none !important;
                }
            }
        }

        .card {
            background-color: transparent;
            border: none;
            border-radius: 0;
            margin-bottom: 0;

            .card-img {
                transition: 0.5s ease;
            }

            .card-body {
                padding: calc(10px + (18 - 10) * ((100vw - 320px) / (1920 - 320))) calc(15px + (24 - 15) * ((100vw - 320px) / (1920 - 320)));
                color: $light-color;

                h4 {
                    font-size: calc(18px + (22 - 18) * ((100vw - 320px) / (1920 - 320)));
                    font-weight: 500;
                    line-height: 1.2;
                    color: $title-color;
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;

                    a {
                        color: $title-color;
                    }

                    &:hover {
                        a {
                            color: var(--theme-color);
                        }
                    }
                }

                i {
                    --Iconsax-Size: 20px;
                    --Iconsax-Color: #808B97;
                }

                .blog-footer {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 4px;
                    margin-top: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));
                    font-size: 16px;
                    padding-top: calc(10px + (12 - 10) * ((100vw - 320px) / (1920 - 320)));
                    border-top: 1px solid #E5E8EA;

                }
            }
        }

        &:has(.no-data-found) {
            padding: 0;
            background-color: transparent;
        }


    }

    .blog-detail {
        line-height: 1;
        color: #808B97;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        font-weight: 400;
        row-gap: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));
        margin-top: 5px;
        padding: 0;

        li {
            font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));
            position: relative;
            font-weight: 400;
            line-height: 1.2;


            +li {
                // border-left: 1px solid #E5E8EA;
                padding-left: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));
                margin-left: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));

                [dir="rtl"] & {
                    padding-left: unset;
                    padding-right: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));
                    margin-left: unset;
                    margin-right: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));
                }

                &::before {
                    @include pos;
                    @include center(vertical);
                    left: 0;
                    width: 1px;
                    height: calc(100% + calc(1px + (4 - 1) * ((100vw - 320px) / (1920 - 320))));
                    border-left: 1px solid #E5E8EA;

                    [dir="rtl"] & {
                        left: unset;
                        right: 0;
                        border-right: 1px solid #E5E8EA;
                        border-left: none;
                    }
                }
            }
        }
    }
}

h4 {
    font-size: calc(19px + (22 - 19) * ((100vw - 320px) / (1920 - 320)));
    font-weight: 500;
    line-height: 28px;
    color: $title-color;
    white-space: wrap;
}

.detail-sec {
    // padding: calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320)));
    border-radius: 10px;
    background-color: transparent !important;

    .details-title {
        margin-bottom: calc(8px + (15 - 8) * ((100vw - 320px) / (1920 - 320)));
        padding-bottom: calc(8px + (15 - 8) * ((100vw - 320px) / (1920 - 320)));
        border-bottom: 1px dashed #E5E8EA;

        h3 {
            font-size: calc(20px + (22 - 20) * ((100vw - 320px) / (1920 - 320)));
            font-weight: 600;
        }

        p {
            font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));
            margin: 5px 0 0;
            line-height: 1.4;
        }
    }

    .overview-list {
        padding: 0;
        font-size: 16px;
        gap: 0;

        +.overview-list {
            margin-top: 10px;
        }

        h3 {
            font-size: 18px;
            font-weight: 600;
            color: $title-color;
            // margin-top: 13px;

            // ~p {
            //     ~h3 {
            //         margin-top: 20px;
            //     }
            // }

            ~p {
                margin-top: 5px;
            }
        }

        p {
            line-height: 1.5;
            margin: 0 0 23px;
            position: relative;
            padding-left: 21px;

            ~p {
                margin-bottom: 10px;
            }

            &:last-child {
                margin: 0;
            }

            &::before {
                @include pos;
                top: 10px;
                left: 4px;
                width: 5px;
                height: 5px;
                border-radius: 100%;
                background-color: #808B97;

                [dir="rtl"] & {
                    left: unset;
                    right: 4px;
                }
            }
        }
    }
}

.custom-row-col {
    &:has(.no-data-found) {
        width: 100%;
        margin-inline: 0;

        >.col-12 {
            width: 100%;
            padding-inline: 0;
        }
    }
}

.privacy-content,
.terms-content {
    &:has(.no-data-found) {
        width: 100%;
        margin-inline: 0;

        >.col-xxl-8.col-xl-9.col-lg-10 {
            width: 100%;
            margin-inline: 0;
        }
    }
}