/**=====================
     Profile Setting scss
==========================**/

.profile-body-wrapper {
    .profile-wrapper {
        background-color: var(--theme-color);
        border-radius: 15px;
        padding: 20px;
        position: sticky;
        top: 100px;

        @include mq-max(xl) {
            position: fixed;
            top: 0;
            overflow: auto;
            left: -350px;
            width: calc(300px + (350 - 300) * ((100vw - 320px) / (1920 - 320)));
            height: 100vh;
            z-index: 4;
            border-radius: 0;
            background-color: $white;
            transform: unset;
            opacity: 1;
            padding: 0;
            border: none;
            box-shadow: 4px 4px 8px rgba(34, 34, 34, 0.078);
            visibility: visible;

            [dir="rtl"] & {
                left: unset;
                right: -350px;
            }

            &.open {
                left: 0;

                [dir="rtl"] & {
                    left: unset;
                    right: 0;
                }
            }



            .close {
                margin-left: auto;
                transform: rotate(45deg);
                --Iconsax-Color: #2263eb;
                --Iconsax-Size: 28px;
                position: absolute;
                right: 10px;
                top: 10px;
                cursor: pointer;
                
                [dir="rtl"] & {
                    right: unset;
                    left: 10px;
                   margin-left: unset;
                   margin-right: auto; 
                }
            }


            i {
                --Iconsax-Color: #fff
            }
        }

        .profile {
            padding: 20px 0 40px;

            @include mq-max(xl) {
                border-bottom: 1px solid $gray-color;
                background-color: $section-bg;
                padding-top: 40px;
                padding-bottom: 20px;
            }

            .profile-img {
                position: relative;
                text-align: center;
                margin-bottom: -40px;

                @include mq-max(xl) {
                    margin: 0;
                }

                img {
                    border-radius: 100%;
                    border: 2px solid $white;
                    height: 80px;
                    width: 80px;
                }

                .profile-name {
                    position: relative;
                    border-radius: 100%;
                    border: 2px solid $white;
                    height: calc(62px + (80 - 62) * ((100vw - 320px) / (1920 - 320)));
                    width: calc(62px + (80 - 62) * ((100vw - 320px) / (1920 - 320)));
                    @include flex_common;
                    background-color: $section-bg;
                    color: var(--theme-color);
                    position: relative;
                    z-index: 1;
                    font-size: calc(33px + (40 - 33) * ((100vw - 320px) / (1920 - 320)));
                    font-weight: 600;
                    margin: auto;

                    @include mq-max(xl) {
                        border: 2px solid $gray-color;
                        background-color: $white;
                    }

                }

                .edit-modal {
                    position: absolute;
                    bottom: 0px;
                    left: 60%;
                    transform: translateX(-50%);
                    z-index: 3;
                    background-color: var(--theme-color);
                    border-radius: 100%;
                    width: 35px;
                    height: 35px;
                    padding: 5px;

                    [dir="rtl"] & {
                        left: unset;
                    }

                    i {
                        --Iconsax-Size: 18px;
                        --Iconsax-Color: #fff;
                    }
                }

            }

            .profile-detail {
                padding: 20px;
                border-radius: 12px;
                background-image: url(@/Base.png);
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
                height: 120px;
                color: $white;
                @include flex_common ($dis: flex, $align: center, $justify: end);
                flex-direction: column;

                @include mq-max(xl) {
                    padding-bottom: .0;
                    background: none;
                    height: unset;
                    padding-top: calc(14px + (20 - 14) * ((100vw - 320px) / (1200 - 320)));
                }

                h5 {
                    line-height: 1.3;
                    margin-bottom: 4px;
                    font-size: 19px;
                    font-weight: 600;

                    @include mq-max(xl) {
                        color: #222;
                        font-weight: 500;
                    }
                }

                p {
                    @include flex_common;
                    margin: 0;
                    gap: 4px;
                    line-height: 1.3;
                    color: #ddd;
                    font-size: 15px;

                    @include mq-max(xl) {
                        color: #808B97;
                    }

                    i {
                        --Iconsax-Size: 16px;
                        --Iconsax-Color: white;

                        @include mq-max(xl) {
                            --Iconsax-Color: #808B97;
                        }
                    }
                }
            }
        }

        .profile-settings {
            overflow: auto;
            height: 527px;

            .navbar {
                .mainnav-close {
                    display: none !important;
                }

                .navbar-collapse {
                    @include mq-max(xl) {
                        display: block;
                    }

                    .menu-panel {
                        width: 100%;

                        .menu-wrapper {
                            display: flex;
                            flex-direction: column;
                            gap: 16px;
                            margin: 0 8px;
                            border-bottom: unset;

                            @include mq-max(xl) {
                                margin: 17px 8px 0;
                            }

                            li {
                                display: block;

                                .nav-link {
                                    @include flex_common_1 ($dis: flex, $align: center);
                                    border-radius: 8px;
                                    gap: 16px;
                                    border: none;
                                    transition: all 0.5s ease;
                                    outline: none;
                                    width: 100%;
                                    padding: 14px 16px;
                                    white-space: nowrap;

                                    &.active {
                                        i {
                                            display: none;
                                        }
                                    }

                                    i {
                                        display: block;
                                        --Iconsax-Size: 24px;
                                        --Iconsax-Color: white;

                                        @include mq-max(xl) {
                                            --Iconsax-Color: #222;
                                        }
                                    }

                                    span {
                                        color: $white;
                                        font-size: 16px;

                                        @include mq-max(xl) {
                                            color: #222;
                                        }
                                    }

                                    .active-icon {
                                        display: none;
                                    }

                                    .deactive-icon {
                                        display: block;
                                    }

                                    &.active {
                                        background-color: $white;

                                        @include mq-max(xl) {
                                            background-color: rgba(84, 101, 255, 0.1);
                                        }

                                        span {
                                            color: var(--theme-color);
                                            font-weight: 500;
                                        }

                                        .active-icon {
                                            display: block;
                                        }

                                        .deactive-icon {
                                            display: none;
                                        }

                                        &:hover {
                                            background-color: $white;
                                        }
                                    }

                                    &:hover {
                                        background-color: rgba($white, 0.10);

                                    }
                                }

                            }
                        }
                    }
                }
            }
        }

        .profile-logout {
            margin: 0 8px;

            .nav-link {
                @include flex_common_1 ($dis: flex, $align: center);
                border-radius: 8px;
                gap: 16px;
                border: none;
                transition: all 0.5s ease;
                outline: none;
                width: 100%;
                padding: 14px 16px;

                i {
                    --Iconsax-Size: 24px;
                    --Iconsax-Color: white;
                }

                span {
                    color: $white;
                    font-size: 16px;
                }

                &:hover {
                    background-color: rgba($white, 0.10);

                }
            }
        }
    }

    .profile-main {
        .card {
            background-color: $section-bg;
            border: none;
            border-radius: 10px;

            .card-header {
                @include flex_common ($dis: flex, $align: center, $justify: space-between);
                gap: 4px;
                padding: calc(16px + (25 - 16) * ((100vw - 320px) / (1920 - 320)));
                background-color: transparent;
                border-radius: 0;
                border-bottom: 1px solid $gray-color;
                flex-wrap: wrap;

                .title-3 {
                    position: relative;

                    &:before {
                        @include pos;
                        left: calc(-15px + (-25 - -15) * ((100vw - 320px) / (1920 - 320)));
                        @include center(vertical);
                        background-color: var(--theme-color);
                        height: calc(28px + (35 - 28) * ((100vw - 320px) / (1920 - 320)));
                        width: calc(3px + (4 - 3) * ((100vw - 320px) / (1920 - 320)));
                    }

                    h3 {
                        line-height: 1.3;
                        font-size: calc(19px + (22 - 19) * ((100vw - 320px) / (1920 - 320)));
                        font-weight: 600;
                        letter-spacing: 0.7px;
                    }
                }

                a,
                button {
                    font-weight: 500;
                    @include flex_common_1 ($dis: flex, $align: center);
                    gap: 4px;
                    font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));

                    i {
                        --Iconsax-Size: 20px;
                        --Iconsax-Color: #5565FE;
                    }
                }
            }

            .card-body {
                padding: calc(16px + (25 - 16) * ((100vw - 320px) / (1920 - 320)));

                &.service-booking {

                    // min-height: 760px;
                    .delivery-location {
                        .location-header {
                            display: flex;
                            align-items: center;
                            gap: calc(9px + (16 - 9) * ((100vw - 320px) / (1920 - 320)));
                            justify-content: unset;
                            flex-direction: unset;
                            overflow: unset;

                            .badge {
                                margin-left: auto;

                                [dir="rtl"] & {
                                    margin-left: unset;
                                    margin-right: auto;
                                }
                            }

                            .delivery-name {
                                width: calc(100% - calc(42px + (50 - 42) * ((100vw - 320px) / (1920 - 320))) - calc(9px + (16 - 9) * ((100vw - 320px) / (1920 - 320))));
                                display: flex;
                                align-items: flex-start;
                                justify-content: space-between;
                            }
                        }
                    }


                }

                .widgets {
                    .card {
                        border: 1px solid $gray-color;
                        padding: calc(13px + (20 - 13) * ((100vw - 320px) / (1920 - 320))) calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320)));
                        border-radius: 10px;
                        background-color: $white;

                        .widget-data {
                            @include flex_common ($dis: flex, $align: center, $justify: space-between);
                            gap: 4px;

                            .data {
                                h5 {
                                    line-height: 20px;
                                    color: $light-color;
                                    margin-bottom: 4px;
                                }

                                h3 {
                                    font-weight: 700;
                                    line-height: 1.4;
                                }
                            }

                            .data-icon {
                                position: relative;
                                padding-right: 10px;

                                [dir="rtl"] & {
                                    padding-right: unset;
                                    padding-left: 10px;
                                }

                                .dot {
                                    background-color: rgba($primary-color, 0.10);
                                    border-radius: 100%;
                                    height: calc(22px + (30 - 22) * ((100vw - 320px) / (1920 - 320)));
                                    width: calc(22px + (30 - 22) * ((100vw - 320px) / (1920 - 320)));
                                    position: absolute;
                                    top: 0;
                                    right: 0;

                                    [dir="rtl"] & {
                                        right: unset;
                                        left: 0;
                                    }
                                }

                                i {
                                    --Iconsax-Size: calc(31px + (38 - 31) * ((100vw - 320px) / (1920 - 320)));
                                    --Iconsax-Color: #5565FE;
                                    padding-top: calc(3px + (10 - 3) * ((100vw - 320px) / (1920 - 320)));
                                }
                            }
                        }
                    }
                }

                .profile-data {
                    h3 {
                        line-height: 26px;
                    }

                    .card {
                        border: 1px solid $gray-color;
                        padding: calc(13px + (20 - 13) * ((100vw - 320px) / (1920 - 320))) calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320)));
                        border-radius: 10px;
                        background-color: $white;
                        margin: 0;

                        .profile-setting-img {
                            background-color: $section-bg;
                            border-radius: 8px;
                            height: 100%;
                            @include flex_common;
                            background-image: url(@/setting.png);
                            background-position: top;
                            background-repeat: no-repeat;
                            background-size: auto;
                            z-index: 0;
                            position: relative;
                            padding: calc(15px + (24 - 15) * ((100vw - 320px) / (1920 - 320)));

                            .girl-on-chair {
                                height: auto;
                                width: calc(148px + (250 - 148) * ((100vw - 320px) / (1920 - 320)));
                                object-fit: contain;
                            }
                        }

                        .form-group {
                            .value {
                                font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));
                                font-weight: 500;
                                line-height: 1.4;
                                margin: 0;
                            }

                            &:last-child {
                                margin: 0;
                            }
                        }

                        .login-detail {
                            h4 {
                                color: var(--theme-color);
                                font-weight: 500;
                                line-height: 23px;
                            }
                        }
                    }

                    .personal-detail {
                        position: relative;

                        .edit-modal {
                            position: absolute;
                            top: 0;
                            right: 0;

                            [dir="rtl"] & {
                                right: unset;
                                left: 0;
                            }
                        }

                        .form-group {
                            margin-bottom: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));

                            label {
                                font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));
                                line-height: 1.3;
                                font-weight: 400;
                                margin-bottom: 4px;

                            }

                            .edit-btn {
                                padding-left: 5px;
                                --Iconsax-Color: #5565FE;
                                --Iconsax-Size: 18px;

                                [dir="rtl"] & {
                                    padding-left: unset;
                                    padding-right: 5px;
                                }
                            }
                        }
                    }
                }
            }


            .card-footer {
                padding: calc(16px + (25 - 16) * ((100vw - 320px) / (1920 - 320)));
                border-top: 1px solid $gray-color;
                gap: 16px;
                justify-content: start;

                .btn {
                    width: max-content;
                    padding: 10px 30px;
                }
            }
        }

        .review-main {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .review-list {
                position: relative;
                width: 100%;
                border-radius: 8px;
                border: 1px solid $gray-color;
                padding: 20px;
                background-color: $white;

                .review {
                    @include flex_common_1 ($dis: flex, $align: start);
                    gap: calc(8px + (16 - 8)*((100vw - 320px) /(1920 - 320)));

                    @media (max-width:576px) {
                        display: block;
                    }

                    .review-img {
                        height: calc(35px + (45 - 35)*((100vw - 320px) /(1920 - 320)));
                        width: calc(35px + (45 - 35)*((100vw - 320px) /(1920 - 320)));
                        border-radius: 100%;

                        img {
                            width: 100%;
                            height: 100%;
                            border-radius: 100%;
                        }

                        .initial-letter {
                            width: 100%;
                            height: 100%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 24px;
                            font-weight: 400;
                            line-height: 1;
                            border-radius: 100%;
                            background-color: $section-bg;
                        }
                    }

                    .review-note {
                        width: calc(100% - calc(8px + (16 - 8)*((100vw - 320px) /(1920 - 320))) - calc(35px + (45 - 35)*((100vw - 320px) /(1920 - 320))));

                        @media (max-width:576px) {
                            width: 100%;
                            margin-top: 8px;
                        }

                        .name-date {
                            display: flex;
                            align-items: start;
                            justify-content: space-between;
                            flex-wrap: wrap;
                            gap: 5px;

                            @media (max-width:576px) {
                                align-items: center;
                            }

                            span {
                                font-weight: 500;
                            }

                            small {
                                color: $light-color;
                            }

                            h3 {
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }

                            h6 {
                                margin-top: 4px;
                                color: $light-color;
                            }
                        }

                        h5 {
                            color: $title-color;
                            line-height: 1.4;
                            font-size: 15px;
                            margin-top: 4px;
                        }

                    }
                }

                p {
                    padding-left: 60px;
                    font-size: calc(14px + (16 - 14)*((100vw - 320px) /(1920 - 320)));
                    margin: 0;
                    color: #808B97;
                    line-height: 1.5;
                    margin-top: 10px;

                    @media (max-width:576px) {
                        padding: 0;
                    }
                }

                .notify-time {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    @media (max-width:576px) {
                        position: absolute;
                        right: 20px;
                        top: 25px;
                    }

                    .rate {
                        justify-content: end;
                        margin-top: 0;

                        img {
                            width: 13px;
                            height: 13px;
                        }

                        span {
                            font-weight: 500;
                        }
                    }

                    button {
                        background: unset;
                        border: none;
                        padding: 0;
                    }

                    .edit {
                        --Iconsax-Size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));
                        --Iconsax-Color: #2263eb;
                        background-color: $section-bg;
                        border-radius: 100%;
                        height: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));
                        width: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));
                        @include flex_common;
                    }

                    .delete {
                        --Iconsax-Size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));
                        --Iconsax-Color: #FF4B4B;
                        background-color: rgba($danger-color, 0.10);
                        border-radius: 100%;
                        height: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));
                        width: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));
                        @include flex_common;
                    }
                }
            }
        }
    }
}

.edit-modal {
    height: 40px;
    width: 40px;
    border: none;
    border-radius: 6px;
    background-color: rgba($primary-color, 0.10);

    i {
        --Iconsax-Size: 20px;
        --Iconsax-Color: #5565FE;
    }
}

.col-img {
    @include flex_common;

    img {
        height: 594px;
    }
}

.notifications {
    display: flex;
    flex-direction: column;

    .notification-list {
        border-bottom: 1px solid $gray-color;
        padding: calc(12px + (20 - 12) * ((100vw - 320px) / (1920 - 320)));
        display: block;
        position: relative;

        &:last-child {
            border-bottom: none;
        }

        .notify {
            @include flex_common_1 ($dis: flex, $align: start);
            gap: 14px;

            @include mq-max(sm) {
                display: grid;
            }

            .notify-icon {
                height: 40px;
                width: 40px;
                min-width: 40px;
                border-radius: 100%;
                background-color: $section-bg;
                @include flex_common;

                i {
                    --Iconsax-Size: 20px;
                    --Iconsax-Color: #a3aab1;
                }
            }

            .notify-note {
                h5 {
                    font-weight: 500;
                    color: $light-color;
                    line-height: 20px;
                    margin-bottom: 8px;
                    font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));
                }

                p {
                    margin: 0;
                    color: $light-color;
                    line-height: 19px;
                    margin-bottom: 0;
                    font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));
                }

                .notify-img {
                    height: 50px;
                    width: 50px;
                    border-radius: 4px;
                    overflow: hidden;

                    img {
                        height: 100%;
                        object-fit: cover;
                        width: 100%;
                    }
                }
            }
        }

        .notify-time {
            min-width: 70px;
            text-align: end;
            position: absolute;
            right: calc(12px + (20 - 12) * ((100vw - 320px) / (1920 - 320)));

            [dir="rtl"] & {
                left: calc(12px + (20 - 12) * ((100vw - 320px) / (1920 - 320)));
                right: unset;
            }

            @include mq-max(sm) {
                position: absolute;
                top: 10px;
                right: 20px;

                [dir="rtl"] & {
                    right: unset;
                    left: 20px;
                }
            }

            span {
                color: $light-color;
            }
        }

        &.unread {
            .notify {
                .notify-note {

                    h5,
                    p {
                        color: $title-color;
                    }
                }

                .notify-icon {
                    i {
                        --Iconsax-Color: #2263eb;
                    }
                }
            }
        }
    }
}

.notifications {
    display: flex;
    flex-direction: column;
    border: 1px solid $gray-color;
    background-color: $white;
    border-radius: 10px;
    height: 100%;

    @media (max-width:1199px) {
        height: auto;
    }

    &.no-data-notifications {
        border: none;
    }

    .no-data-detail {
        display: flex;
        align-items: center;
        justify-content: center;

        .no-data-notification-img {
            height: 400px;
        }
    }

    .notification-list {
        border-bottom: 1px solid $gray-color;
        padding: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));
        @include flex_common ($dis: flex, $align: start, $justify: space-between);
        gap: 12px;

        &:last-child {
            border-bottom: none;
        }

        .notify {
            @include flex_common_1 ($dis: flex, $align: start);
            gap: 14px;

            .notify-icon {
                height: 40px;
                width: 40px;
                min-width: 40px;
                border-radius: 100%;
                background-color: $section-bg;
                @include flex_common ($dis: flex, $align: center, $justify: center);

                i {
                    --Iconsax-Size: 20px;
                    --Iconsax-Color: #808B97;
                }
            }

            .notify-note {
                h5 {
                    font-weight: 500;
                    color: $light-color;
                    line-height: 20px;
                    margin-bottom: 8px;
                }

                p {
                    margin: 0;
                    color: $light-color;
                    line-height: 19px;
                    // margin-bottom: 16px;
                }

                .notify-img {
                    height: 50px;
                    width: 50px;
                    border-radius: 4px;
                    overflow: hidden;

                    img {
                        height: 100%;
                        width: 100%;
                    }
                }
            }
        }

        .notify-time {
            min-width: 70px;
            text-align: end;

            span {
                color: $light-color;
            }
        }
    }
}

.card {
    &.delivery-location {
        .location-header {
            padding: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));
            display: flex;
            align-items: center;
            gap: calc(8px + (16 - 8) * ((100vw - 320px) / (1920 - 320)));
            justify-content: unset;
            flex-direction: unset;

            .badge {
                margin-left: auto;

                [dir="rtl"] & {
                    margin-left: unset;
                    margin-right: auto;
                }
            }

            .delivery-name {
                width: calc(100% - calc(42px + (50 - 42) * ((100vw - 320px) / (1920 - 320))) - calc(9px + (16 - 9) * ((100vw - 320px) / (1920 - 320))));
                display: flex;
                align-items: flex-start;
                justify-content: space-between;

                [dir="rtl"] & {
                    align-items: flex-end;
                }
            }

            .location-icon {
                border-radius: 100%;
                height: calc(42px + (50 - 42) * ((100vw - 320px) / (1920 - 320)));
                width: calc(42px + (50 - 42) * ((100vw - 320px) / (1920 - 320)));
                // background-color: $gray-color;
                background-color: rgba(84, 101, 255, 0.1);
                border: calc(2px + (4 - 2) * ((100vw - 320px) / (1920 - 320))) solid $white;
                outline: 1px solid $gray-color;
                @include flex_common;

                img {
                    width: 24px;
                    height: 24px;

                    @include mq-max(sm) {
                        height: 16px;
                    }
                }
            }

            .active-icon {
                border: 1px solid var(--theme-color);
                border-radius: 100%;
                height: calc(42px + (50 - 42) * ((100vw - 320px) / (1920 - 320)));
                width: calc(42px + (50 - 42) * ((100vw - 320px) / (1920 - 320)));
                @include flex_common;

                img {
                    background-color: var(--theme-color);
                    border: 1px solid $white;
                    padding: calc(8px + (10 - 8) * ((100vw - 320px) / (1920 - 320)));
                    border-radius: 100%;
                    height: calc(38px + (42 - 38) * ((100vw - 320px) / (1920 - 320)));
                    width: calc(38px + (42 - 38) * ((100vw - 320px) / (1920 - 320)));
                    @include flex_common;
                }
            }
        }

        .address {
            padding: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));

            p {
                font-size: 16px;
                line-height: 1.5;
                height: 48px;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
            }

            .btn-outline {
                border-width: 1px;
                width: max-content;
                padding: 9px 18px;
            }
        }

        .address-bottom-box {
            padding: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320))) calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));
            border-top: 1px solid #ddd;
            position: relative;

            .action {
                position: relative;
                display: flex;
                align-items: center;
                gap: 13px;
            }

            .btn {
                width: 100%;
                padding-block: 10px;
                font-weight: 500;
                border-radius: 8px;

                &-fill {
                    border: transparent;
                    color: $white;
                    background-color: var(--theme-color);

                    &:hover {
                        background-color: transparent;
                        color: var(--theme-color);
                        border: 1px solid var(--theme-color);
                    }
                }

                &-outline {
                    border: 1px solid var(--theme-color);
                    color: var(--theme-color);
                    background-color: transparent;

                    &:hover {
                        background-color: var(--theme-color);
                        color: $white;
                        border-color: transparent;

                        .icon {
                            --Iconsax-Color: #fff
                        }
                    }
                }
            }

            .radio {
                position: absolute;
                top: 0;
                left: 0;
                @include pseudowh;
                opacity: 0;
                cursor: pointer;

                [dir="rtl"] & {
                    left: unset;
                    right: 0;
                }
            }
        }

        .radio:checked {
            ~button {
                background-color: var(--theme-color);
                color: $white;
            }
        }
    }
}

.add-location {
    color: var(--theme-color);
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
    text-decoration: underline;

    &:hover {
        text-decoration: underline;
    }
}

.menu-wrapper {
    [dir="rtl"] & {
        padding-right: 0;
    }
}