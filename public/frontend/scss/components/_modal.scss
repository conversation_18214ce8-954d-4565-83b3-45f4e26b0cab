/**=====================
    Modal scss
==========================**/
.modal-backdrop {
    z-index: 5;
}

.modal {
    z-index: 6;
    margin: 0;

    .modal-dialog {
        .modal-content {
            border-radius: 12px;
            border: none;

            .modal-header {
                padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320))) calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320))) calc(19px + (25 - 19) * ((100vw - 320px) / (1920 - 320)));
                border: none;

                h3 {
                    font-size: calc(21px + (26 - 21) * ((100vw - 320px) / (1920 - 320)));
                    font-weight: 500;
                    line-height: 1.3;
                    text-transform: capitalize;
                }

                .btn-close {
                    opacity: 1;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    margin: 0 0 0 auto;

                    [dir="rtl"] & {
                        margin: 0 auto 0 0;
                    }

                    .iconsax {
                        --Iconsax-Color: #222;
                        transform: rotate(45deg);
                        --Iconsax-Size: calc(27px + (31 - 27) * ((100vw - 320px) / (1920 - 320)));
                    }

                    &:focus {
                        border: none;
                        box-shadow: none;
                    }
                }
            }

            .modal-body {
                padding: 0 calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320))) calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));

                &.custom-scroll {
                    overflow: auto;
                    height: 630px;

                }

                .card {
                    margin: 0;
                }

                .modal-body-content {
                    background-color: $section-bg;
                    border-radius: 10px;
                    width: 100%;
                    height: calc(200px + (250 - 200) * ((100vw - 320px) / (1920 - 320)));
                    @include flex_common;
                    margin-bottom: 16px;
                    padding: 20px 0;

                    img {
                        height: 100%;
                    }
                }

                .cancel-content {
                    background-color: $section-bg;
                    border-radius: 10px;
                    width: 100%;

                    label {
                        font-size: 18px;
                        font-weight: 500;
                        list-style: 23px;
                        color: $title-color;
                        margin-bottom: 6px;
                    }
                }

                p {
                    font-size: 16px;
                    line-height: 1.5;
                    color: $light-color;
                }

                .btn {
                    &.btn-solid {
                        width: 100%;
                        justify-content: center;
                        border-radius: 12px;
                    }
                }
            }

            .modal-footer {
                border: none;
                padding: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));
                flex-wrap: nowrap;
                gap: 12px;

                .status-note {
                    width: 100%;
                    border-radius: 0;
                    margin: 0;
                }
            }
        }
    }

    &.location-detected-modal {
        .modal-dialog {
            max-width: 650px;
        }

        .modal-header {
            border: none;
        }

        .modal-body {
            .location-search {
                background-color: #F5F6F7;
                color: #2263eb;

            }

            .spinner-border {
                display: block;
                margin: 20px auto 10px;
                width: 26px !important;
                height: 26px !important;

            }

            .location-list {
                margin-top: 20px;

                .location {
                    cursor: pointer;
                    transition: all 0.3s;
                    display: flex;
                    align-items: start;
                    justify-content: start;
                    border-bottom: 1px solid #E5E8EA;
                    color: rgba($white, 0.6);
                    background-color: transparent;
                    gap: 8px;
                    font-size: 16px;
                    padding-bottom: 16px;

                    &:last-child {
                        border-bottom: none;
                    }
                    span {
                        &.text-danger {
                            width: 100%;
                            text-align: center;
                            display: flex;
                            justify-content: center;
                            padding-block: 50px;
                            background-color: #f5f6f7;
                            font-size: 18px;
                            font-weight: 500;
                        }
                    }
                    // &:has(.span.text-danger) {
                    //     .location-list {
                            // width: 100%;
                            // text-align: center;
                            // display: flex;
                            // justify-content: center;
                            // padding-block: 50px;
                            // background-color: #f5f6f7;
                            // font-size: 18px;
                            // font-weight: 500;
                    //     }

                    // }
                    

                    +li {
                        margin-top: 0;
                    }

                    >div {
                        h5 {
                            color: #2263eb;
                            font-size: 18px;

                        }

                        h6 {
                            color: rgba($title-color, 0.5);
                            font-size: 16px;
                            margin-top: 4px;
                        }

                        img {
                            border-radius: 0 !important;
                            width: auto !important;
                            height: auto !important;
                        }
                    }

                    i {
                        margin-top: 3px;
                        --Iconsax-Color: #2263eb !important;
                    }

                    &:hover {
                        color: $white;

                        i {
                            --Iconsax-Color: #2263eb;
                        }
                    }

                    +li {

                        margin-top: 16px;
                    }
                }
            }
        }
    }

    &.reset-modal {
        .modal-dialog {
            .modal-content {
                .modal-body {
                    text-align: center;
                }
            }
        }
    }

    &.book-now {
        .modal-dialog {
            .modal-content {
                .modal-body {

                    .service-item {
                        padding: 20px;
                        border-radius: 8px;
                        align-items: start;
                        margin-bottom: 20px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .serviceman {
                            @include flex_common_1 ($dis: flex, $align: center);
                            gap: 16px;

                            .serviceman-detail {
                                width: max-content;
                                @include flex_common_1 ($dis: flex, $align: center);
                                gap: 12px;
                                background-color: $section-bg;
                                border-radius: 12px;
                                padding: 12px 16px;

                                h6 {
                                    color: $light-color;
                                }

                                p {
                                    margin: 0;
                                    font-size: 14px;
                                    font-weight: 500;
                                }

                                .rate {
                                    .star {
                                        width: 15px !important;
                                        height: 15px !important;
                                    }
                                }
                            }
                        }

                        .btn-outline {
                            width: max-content;
                            border-radius: 8px;
                            margin-top: 4px;
                            font-size: 16px;
                            padding: 9px 18px;
                        }
                    }
                }
            }
        }
    }

    &.book-service {
        .modal-dialog {
            .modal-content {
                .modal-body {
                    .service {
                        padding: calc(14px + (20 - 14) * ((100vw - 320px) / (1920 - 320)));
                        border-radius: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320)));
                        background-color: $section-bg;
                        margin-bottom: 25px;
                        display: flex;
                        align-items: center;
                        gap: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));

                        @include mq-max(sm) {
                            flex-direction: column;
                            align-items: unset;
                        }

                        img {
                            object-fit: cover;
                            border-radius: 8px;
                            object-position: top;
                            width: 110px;
                            height: 110px;

                            @include mq-max(sm) {
                                width: 100%;
                                height: calc(183px + (280 - 183) * ((100vw - 320px) / (576 - 320)));
                            }
                        }
                    }

                    .services {
                        label {
                            color: $title-color;
                            margin-bottom: 8px;
                            font-weight: 500;
                        }

                        .service {
                            @include flex_common_1 ($dis: flex, $align: center);
                            gap: 10px;
                            background-color: $section-bg;
                            padding: 20px;
                            border-radius: 10px;

                            img {
                                width: 80px;
                                height: 80px;
                            }

                            span {
                                font-size: 22px;
                                font-weight: 700;
                                color: var(--theme-color);
                            }
                        }
                    }

                    .service-title {
                        color: $title-color;
                        font-size: 16px;
                        margin-top: 16px;
                        line-height: 1.4;
                        margin-bottom: 10px;
                        font-weight: 500;
                    }

                    .book-service-title {
                        display: block;
                        margin: 0;
                        width: calc(100% - 16px - 110px);

                        @include mq-max(sm) {
                            width: 100%;
                        }

                        h3 {
                            font-size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
                            font-weight: 400;
                            line-height: 1.4;
                            color: $title-color;
                            width: 100%;
                            overflow: hidden;
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            word-break: break-all;
                            text-overflow: ellipsis;
                        }

                        span {
                            font-size: 20px;
                            font-weight: 700;
                            margin-top: calc(2px + (4 - 2) * ((100vw - 320px) / (1920 - 320)));
                            line-height: 1.4;
                            color: var(--theme-color);
                        }
                    }
                }
            }
        }
    }

    &.location-modal {
        .modal-content {
            .modal-body {
                padding-bottom: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));
            }
        }

        .location-map {
            border-radius: 12px;
            background-color: $section-bg;
            padding: 0;
            margin: 0;
            gap: 0;
            overflow: hidden;

            .map {
                height: 100%;
                overflow: hidden;

                iframe {
                    height: 100%;
                }
            }

            label {
                color: $title-color;
                font-size: 18px;
            }

            .location-detail {
                padding: 20px;
            }

            .category {
                @include flex_common_1 ($dis: flex, $align: center);
                flex-wrap: wrap;
                gap: calc(11px + (40 - 11) * ((100vw - 320px) / (1920 - 320)));

                .form-check {
                    @include mq-max(sm) {
                        gap: 8px;
                    }

                    label {
                        color: $light-color;
                        font-size: 16px;
                    }

                    .form-radio-input {
                        &:checked {
                            ~label {
                                color: $title-color;
                            }
                        }
                    }
                }
            }

            i {
                --Iconsax-Size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
                --Iconsax-Color: #808B97;
            }

            .input-icon {
                position: absolute;
                top: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320)));
                left: 16px;

                [dir="rtl"] & {
                    left: unset;
                    right: 16px;
                }
            }
        }
    }

    &.servicemen-list-modal {

        .search {
            margin-bottom: 16px;

            input {
                border-radius: 30px !important;
            }
        }

        .select-dropdown {
            border: none;
            margin: 0;
            padding-bottom: 16px;

            @include mq-max(lg) {
                align-items: flex-start;
                gap: 12px;

                .form-group {
                    width: 100%;
                }
            }
        }
    }

    &.servicemen-detail-modal {
        .modal-dialog {
            .modal-content {

                .modal-header {
                    position: relative;

                    i {
                        position: absolute;
                        left: 20px;
                        top: 50%;
                        transform: translateY(-50%);
                        --Iconsax-Size: 22px;
                        --Iconsax-Color: #2263eb;

                        [dir="rtl"] & {
                            left: unset;
                            right: 20px;
                        }
                    }

                    .modal-title {
                        padding-left: 20px;
                        line-height: 1;
                        text-transform: capitalize;


                        [dir="rtl"] & {
                            padding-left: 0;
                            padding-right: 20px;
                        }
                    }

                    .btn-close {
                        padding: 0;
                        position: absolute;
                        @include center(vertical);
                        right: 30px;

                        [dir="rtl"] & {
                            right: unset;
                            left: 30px;
                        }
                    }
                }

                .modal-body {
                    .provider-content {
                        padding: 0;
                        border: none;

                        p {
                            text-align: start;
                        }
                    }

                    i {
                        --Iconsax-Size: 18px;
                        --Iconsax-Color: #808B97;
                    }

                    .profile-bg {
                        position: relative;
                        background-image: url(@/profile-bg.png);
                        background-position: center;
                        background-size: cover;
                        background-repeat: no-repeat;
                        z-index: 0;
                        height: 100px;
                    }

                    .profile {
                        z-index: 1;
                        position: relative;
                        margin-top: -60px;

                        .img {
                            border: 4px solid $white;
                            width: 100px;
                            height: 100px;
                        }

                        .rate {
                            position: relative;
                            margin: 0;
                            padding-left: 8px;

                            [dir="rtl"] & {
                                padding-left: 0;
                                padding-right: 8px;
                            }

                            &:before {
                                @include pos;
                                top: 7px;
                                left: 0;
                                background-color: $gray-color;
                                width: 1px;
                                height: 12px;

                                [dir="rtl"] & {
                                    left: unset;
                                    right: 0;
                                }
                            }

                            .star {
                                width: 13px;
                                height: 13px;
                            }
                        }

                    }

                    .profile-info {
                        background-color: $section-bg;
                        border-radius: 10px;
                        column-count: 2;
                        padding: 16px 20px;

                        label {
                            @include flex_common_1 ($dis: flex, $align: center);
                            gap: 6px;
                            line-height: 1;
                        }
                    }

                    .information {
                        p {
                            font-size: 16px;
                            font-weight: 500;
                        }

                        .btn {
                            font-size: 16px;
                            width: max-content;
                            border-radius: 6px;
                            padding: 6px 12px;
                        }
                    }

                    .note {
                        font-weight: 400;
                        color: $light-color;
                        margin-top: 8px;
                    }

                    .expert {
                        @include flex_common_1 ($dis: flex, $align: center);
                        gap: 30px;
                        margin-top: 0.5rem;
                        list-style: disc;
                        padding-left: 30px;

                        [dir="rtl"] & {
                            padding-left: 0;
                            padding-right: 30px;
                        }

                        li {
                            display: list-item;
                        }
                    }
                }
            }
        }
    }

    &.service-charge-modal {
        .modal-dialog {
            .modal-content {
                .modal-body {
                    padding-bottom: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320))) !important;

                    .bill-summary {

                        .charge,
                        .total {
                            background-color: $section-bg;
                        }

                        .total {

                            &:before,
                            &:after {
                                background-color: $white;
                            }
                        }
                    }
                }
            }
        }
    }

    &.coupon-modal {
        .modal-content {
            .modal-body {
                padding-bottom: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320))) !important;
            }
        }
    }

    &.confirm-modal,
    &.fail-modal {
        .modal-body {
            p {
                color: $light-color !important;
                line-height: 25px;
                text-align: center;
                margin: 0 calc(5px + (25 - 5) * ((100vw - 320px) / (1920 - 320)));
            }
        }
    }

    &.wallet-modal {
        .modal-dialog {
            .modal-content {
                .modal-body {
                    .phone-detail {
                        display: grid;

                        .error {
                            order: 1;
                        }

                        .select2-container {
                            width: 100% !important;
                        }

                        .select2-container--default {
                            .selection {
                                min-width: unset;
                                width: 100%;
                            }

                            .select2-selection--single {
                                background-color: $white;
                                border: none;
                                padding: 10px 16px 10px 46px;

                                [dir="rtl"] & {
                                    padding: 10px 46px 10px 16px;
                                }
                            }

                            .select2-selection__rendered {
                                padding: 0;
                                font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));
                            }

                            .select2-selection__placeholder {
                                color: #2263eb;
                            }
                        }
                    }

                    .add-money {
                        background-color: $section-bg;
                        padding: 20px;
                        border-radius: 10px;

                        label {
                            color: $title-color;
                        }

                        .input-group {
                            input {
                                border-radius: 8px;
                                border: none;
                            }
                        }
                    }
                }
            }
        }
    }

    &.pending-modal,
    &.accepted-modal,
    &.ongoing-modal,
    &.completed-modal {
        .modal-body {
            &.booking-sec {
                .card {
                    border: none;
                }

                .primary-badge {
                    position: absolute;
                    top: 10px;
                    left: 10px;

                    [dir="rtl"] & {
                        left: unset;
                        right: 20px;
                    }

                    @include mq-max(lg) {
                        top: 10px;
                        left: 10px;

                        [dir="rtl"] & {
                            left: unset;
                            right: 10px;
                        }
                    }
                }

                .status {
                    margin-bottom: 4px;

                    @include mq-max(lg) {
                        flex-direction: column;
                        align-items: start;
                        justify-content: start;

                        [dir="rtl"] & {
                            align-items: end;
                        }

                    }

                    h5 {
                        font-size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));
                        color: $title-color;

                        &:hover {
                            text-decoration: unset;
                        }
                    }

                    .status-btn {
                        font-size: 20px;

                        &:hover {
                            text-decoration: unset;
                        }
                    }
                }

                .view-status {
                    align-items: start;
                    margin-bottom: 10px;

                    button {
                        @include flex_common_1 ($dis: flex, $align: center);
                        gap: 4px;
                        border-radius: 4px;
                        border: none;

                        i {
                            --Iconsax-Size: 18px;
                            --Iconsax-Color: #5565FE;
                        }
                    }
                }

                .status-note {
                    color: $danger-color;
                    padding: calc(8px + (16 - 8) * ((100vw - 320px) / (1920 - 320)));
                    border-radius: 8px;
                    background-color: rgba($danger-color, 0.10);
                    font-size: 14px;
                    text-align: center;

                    span {
                        font-weight: 500;
                    }
                }

                .bill-summary {
                    label {
                        color: $title-color;
                        font-weight: 500;
                        margin-bottom: 8px;
                    }

                    .charge,
                    .total {
                        background-color: $section-bg;
                        border-color: $gray-color;
                    }

                    .charge {
                        border-bottom: none;

                        p {
                            color: $light-color;
                            flex-wrap: wrap;
                        }
                    }

                    .total {

                        &:before,
                        &:after {
                            background-color: $white;
                        }
                    }

                    .total-amount {
                        background-color: $section-bg;
                        padding: 20px;
                        border: 1px solid $gray-color;
                        border-bottom: none;

                        li {
                            @include flex_common ($dis: flex, $align: center, $justify: space-between);
                            gap: 4px;
                            font-size: 16px;

                            &.total-amount-data {
                                font-weight: 700;
                            }
                        }
                    }

                    .circle {
                        height: 16px;
                        margin: 0;
                        margin-top: -8px;
                        margin-bottom: -8px;
                    }
                }

                .payment-summary {
                    label {
                        color: $title-color;
                        font-weight: 500;
                        margin-bottom: 8px;
                    }

                    .charge {
                        background-color: $section-bg;
                        border-color: $gray-color;
                        border-radius: 8px;
                        padding: 20px;
                        display: flex;
                        flex-direction: column;
                        gap: 16px;
                        position: relative;

                        &:after,
                        &:before {
                            @include pos;
                            pointer-events: none;
                            border: solid transparent;
                            display: block;
                            border-width: 1px;
                            width: 10px;
                            height: 15px;
                            bottom: 48px;
                            border-radius: 100%;
                            background-color: $white;
                        }

                        &:after {
                            border-left-color: $gray-color;
                            border-top-color: $gray-color;
                            border-bottom-color: $gray-color;
                            right: -2px;

                            [dir="rtl"] & {
                                right: unset;
                                left: -2px;
                            }
                        }

                        &:before {
                            border-right-color: $gray-color;
                            border-top-color: $gray-color;
                            border-bottom-color: $gray-color;
                            left: -2px;

                            [dir="rtl"] & {
                                left: unset;
                                right: -2px;
                            }
                        }
                    }

                    .circle {
                        height: 18px;
                        margin: -11px 0;
                    }
                }

                .extra-service {
                    label {
                        color: $title-color;
                        font-weight: 500;
                        margin-bottom: 8px;
                    }

                    .total-amount {
                        @include flex_common ($dis: flex, $align: center, $justify: space-between);
                        gap: 4px;
                        border: 1px solid $gray-color;
                        border-radius: 12px;
                        padding: 16px;

                        h4 {
                            font-weight: 500;
                            line-height: 23px;
                            margin-bottom: 6px;
                        }

                        p {
                            margin: 0;
                            color: var(--theme-color);
                            line-height: 18px;
                        }

                        .receipt {
                            background-color: rgba($primary-color, 0.10);
                            border-radius: 4px;
                            height: 50px;
                            width: 50px;
                            @include flex_common;

                            .receipt-img {
                                width: 25px;
                                height: 25px;
                            }
                        }
                    }
                }

                .profile-view {
                    background-color: rgba($primary-color, 0.10);
                    border-radius: 5px;
                    @include flex_common;
                    width: 30px;
                    height: 30px;
                    border: none;

                    i {
                        --Iconsax-Size: 18px;
                        --Iconsax-Color: #5565FE;
                    }
                }

                .chat {
                    background-color: $section-bg;
                    border-radius: 5px;
                    @include flex_common;
                    width: 30px;
                    height: 30px;
                    border: none;

                    i {
                        --Iconsax-Size: 18px;
                        --Iconsax-Color: #808B97;
                    }
                }
            }
        }
    }

    &.status-modal {
        .modal-content {
            .modal-header {
                position: relative;
                padding-bottom: 0;

                .modal-back {
                    position: absolute;
                    left: 24px;
                    background-color: unset;
                    padding: 0;
                    border: none;

                    [dir="rtl"] & {
                        left: unset;
                        right: 24px;
                    }
                }

                i {
                    --Iconsax-Size: 22px;
                    --Iconsax-Color: #2263eb;
                }

                .modal-title {
                    padding-left: 25px;
                    text-transform: capitalize;

                    [dir="rtl"] & {
                        padding-left: 0;
                        padding-right: 25px;
                    }
                }
            }

            .modal-body {
                padding: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));

                .pattern-btn-1 {
                    padding: 20px;

                    &:before,
                    &:after {
                        background-color: $white;
                    }
                }

                .form-control.pattern-input {
                    position: relative;
                    padding: 20px;
                    vertical-align: middle;

                    &:after,
                    &:before {
                        @include pos;
                        pointer-events: none;
                        border: dashed transparent;
                        display: block;
                        border-width: 1px;
                        width: 12px;
                        height: 12px;
                        border-radius: 100%;
                        background-color: $white;
                        z-index: 1;
                    }

                    &:after {
                        border-top-color: var(--theme-color);
                        border-left-color: var(--theme-color);
                        border-bottom-color: var(--theme-color);
                        right: -132px;
                        @include center(vertical);

                        [dir="rtl"] & {
                            right: unset;
                            left: -127px;
                        }
                    }

                    &:before {
                        border-top-color: var(--theme-color);
                        border-right-color: var(--theme-color);
                        border-bottom-color: var(--theme-color);
                        left: -3px;
                        @include center(vertical);

                        [dir="rtl"] & {
                            left: unset;
                            right: -3px;
                        }
                    }
                }

                .status-history {
                    margin-top: 15px;

                    ul {
                        display: flex;
                        flex-direction: column;
                        gap: 30px;

                        [dir="rtl"] & {
                            padding-left: 0;
                            padding-right: 10px;
                        }

                        li {
                            position: relative;
                            @include flex_common_1 ($dis: flex, $align: center);
                            gap: 16px;
                            padding-bottom: 15px;

                            @include mq-max(xs) {
                                flex-wrap: wrap;
                                gap: 8px;
                            }

                            i {
                                --Iconsax-Size: 24px;
                                --Iconsax-Color: #E5E8EA;
                            }

                            p {
                                margin: 0;
                            }

                            .status-time {
                                line-height: 20px;
                                color: $light-color;
                                white-space: nowrap;
                            }

                            .status-title {
                                font-size: 15px;
                                line-height: 20px;
                            }

                            .status-des {
                                font-size: 15px;
                                line-height: 20px;
                                color: $light-color;
                            }

                            .status-main {
                                padding-left: 16px;
                                position: relative;
                                width: 100%;

                                [dir="rtl"] & {
                                    padding-left: 0;
                                    padding-right: 16px;
                                }

                                @include mq-max(xs) {
                                    padding-left: 42px;

                                    [dir="rtl"] & {
                                        padding-left: 0;
                                        padding-right: 42px;
                                    }

                                }

                                &:before {
                                    @include pos;
                                    left: 0;
                                    @include center(vertical);
                                    background-color: $gray-color;
                                    height: 26px;
                                    width: 1px;

                                    [dir="rtl"] & {
                                        left: unset;
                                        right: 0;
                                    }

                                    @include mq-max(xs) {
                                        display: none;
                                    }
                                }

                            }

                            .dashed-border {
                                @include pos;
                                height: 1px;
                                width: 100%;
                                left: 0;
                                bottom: 0;
                                margin: 0;

                                [dir="rtl"] & {
                                    left: unset;
                                    right: 0;
                                }
                            }

                            &.recent {
                                .activity-dot {
                                    background-color: var(--theme-color);
                                    outline-color: var(--theme-color);

                                    &:after {
                                        background-color: $white;
                                        border: 1px dashed var(--theme-color);
                                    }
                                }

                                i {
                                    --Iconsax-Size: 24px;
                                    --Iconsax-Color: #5565FE;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    &.terms-modal {
        --bs-modal-width: 650px !important;
        height: 800px !important;

        .modal-content {
            .modal-body {
                .accordion {
                    width: 100%;

                    .accordion-item {
                        background-color: $white;
                        border-radius: 12px;

                        +.accordion-item {
                            margin-top: 20px;
                        }

                        .accordion-header {
                            .accordion-button {
                                padding: 15px;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                width: 100%;
                                line-height: 1.2;
                                font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));
                                font-weight: 500;
                                gap: 4px;
                                color: $title-color;
                                cursor: pointer;

                                i {
                                    --Iconsax-Size: calc(20px + (24 - 20) * ((100vw - 320px) / (1920 - 320)));
                                    --Iconsax-Color: #2263eb;
                                }

                                &:after {
                                    display: none;
                                }

                                .add {
                                    display: none;
                                }

                                .minus {
                                    display: flex;
                                }

                                &.collapsed {

                                    color: rgba($title-color, 0.7);

                                    .add {
                                        display: flex;
                                    }

                                    .minus {
                                        display: none;
                                    }
                                }
                            }
                        }

                        .accordion-body {
                            padding: 20px;

                            p {
                                font-size: 18px;
                                line-height: 1.5;
                            }
                        }
                    }
                }

                ul,
                ol {
                    list-style-type: disc;
                    padding-left: 1.25rem;

                    [dir="rtl"] & {
                        padding-right: 1.25rem;
                        padding-left: unset;
                    }


                    li {
                        display: list-item;
                        font-size: 17px;
                        color: rgba($title-color, 0.7);
                        line-height: 1.3 !important;
                        margin-bottom: 12px;
                    }
                }
            }
        }
    }

    &.delete-modal {
        .modal-content {
            .btn-close {
                position: absolute;
                top: 20px;
                right: 30px;
                z-index: 1;

                [dir="rtl"] & {
                    right: unset;
                    left: 30px;
                }

                &:focus {
                    box-shadow: none;
                }
            }

            .modal-body {
                position: relative;
                padding: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320))) calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));
                padding-bottom: 0;

                .modal-icon {
                    --Iconsax-Color: #5565FE;
                    --Iconsax-Size: 50px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 80px;
                    height: 80px;
                    border-radius: 100%;
                    margin: 0 auto 10px;
                    background-color: #F5F6F7;
                }

                h3 {
                    font-weight: 600;
                    margin-bottom: 8px;
                }

                p {
                    width: 80%;
                }
            }
        }
    }

    &.start-service-modal,
    &.restart-service-modal {
        .modal-body {
            text-align: center;

            .modal-body-content {
                flex-direction: column;

                .ellipse {
                    width: 220px;
                    padding-bottom: 20px;
                }
            }
        }
    }

    &.pause-service-modal {
        .modal-body {
            text-align: center;

            .modal-body-content {
                position: relative;
                flex-direction: column;

                .ellipse {
                    width: 100px;
                    height: auto !important;
                }

                .hold {
                    position: absolute;
                    top: 40px;
                    right: 70px;
                    height: auto !important;

                    [dir="rtl"] & {
                        right: unset;
                        left: 70px;
                    }

                    @include mq-max(sm) {
                        height: 40px !important;
                    }

                    @include mq-max(xs) {
                        right: 10px;

                        [dir="rtl"] & {
                            right: unset;
                            left: 10px;
                        }
                    }
                }
            }
        }
    }

    &.completed-service-modal {
        .modal-content {
            .modal-body {
                text-align: center;

                .modal-body-content {
                    position: relative;

                    .success-tick {
                        top: 50px;
                        left: 35%;
                        transform: translateX(-35%);
                        position: absolute;
                        height: 40px;

                        [dir="rtl"] & {
                            left: unset;
                            right: 35%;
                            transform: translateX(35%);
                        }
                    }

                    .girl-on-chair {
                        position: absolute;
                        top: 20px;
                        @include center(horizontal);
                        height: 210px;
                    }
                }
            }
        }
    }

    &.review-modal {
        .modal-content {
            .modal-body {
                .rate-content {
                    background-color: $section-bg;
                    padding: calc(10px + (20 - 10)*((100vw - 320px) /(1920 - 320)));
                    border-radius: 12px;

                    p {
                        text-align: center;
                        margin: 0;
                        color: $light-color;
                    }

                    .form-group {
                        label {
                            font-size: 18px;
                            font-weight: 500;
                            color: $title-color;
                            margin-bottom: 8px;
                        }
                    }
                }
            }
        }
    }

    &.profile-update-modal {
        .update-img {
            display: flex;
            margin: 0 auto;
            width: max-content;
            position: relative;
            margin-bottom: -50px;

            img {
                width: 120px;
                height: 120px;
                border-radius: 100%;
                border: 2px solid $white;
            }

            .update-profile {
                --Iconsax-Size: 20px;
                --Iconsax-Color: #5565FE;
                background-color: $section-bg;
                border: 2px solid $white;
                height: 40px;
                width: 40px;
                border-radius: 100%;
                position: absolute;
                bottom: -10px;
                right: -10px;
                cursor: pointer;
                @include flex_common;

                [dir="rtl"] & {
                    right: unset;
                    left: -10px;
                }
            }


            .initial-letter {
                width: 80px;
                height: 80px;
                background-color: $white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 40px;
                font-weight: 500;
                border-radius: 100%;
                border: 2px solid rgba($primary-color, 0.1);

            }
        }

        .update-detail {
            background-color: $section-bg;
            border-radius: 10px;
            padding: 20px;
            padding-top: 70px;
        }

        input[type="file"] {
            display: none;
        }
    }

    &.change-password-modal {
        .modal-body {
            padding-bottom: 0 !important;

            .form-group {
                margin-bottom: 15px;

                .form-control {
                    background-color: #F5F6F7;
                }
            }
        }
    }



    &.address-modal {
        .modal-body {
            .category-list-box {
                +.category-list-box {
                    margin-top: calc(12px + (20 - 12) * ((100vw - 320px) / (1920 - 320)));
                }

                .error-div {
                    .select2-container--default {
                        .selection {
                            min-width: unset;
                            display: block;
                        }
                    }

                }

                .form-control {
                    padding: 10px 16px;
                    background-color: $section-bg;
                    color: $title-color;
                }

                .label-title {
                    color: $title-color;
                    font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));
                    margin-bottom: calc(4px + (9 - 4) * ((100vw - 320px) / (1920 - 320)));
                    font-weight: 500;
                }

                .category-list {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 12px;

                    @include mq-max(sm) {
                        grid-template-columns: 1fr 1fr;
                    }

                    @include mq-max(2xs) {
                        grid-template-columns: 1fr;
                    }

                    .form-check {
                        border: 1px solid $gray-color;
                        border-radius: 8px;
                        padding: 11px 15px;
                        justify-content: space-between;
                        cursor: pointer;
                        margin: 0;

                        &:has(.form-check-input:checked) {
                            border-color: var(--theme-color);
                            background-color: rgba(84, 101, 255, 0.12);

                            label {
                                color: var(--theme-color);
                                font-weight: 500;
                            }
                        }

                        label {
                            cursor: pointer;
                            font-size: 17px;
                            font-weight: 400;
                            color: #2263eb;
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
}

.update-detail {
    label {
        font-size: 14px;
        font-weight: 500;
        color: $title-color;
        margin-top: 4px;
        margin-bottom: 4px;
    }

    .form-group {
        position: relative;

        .form-control {
            border: none;
            padding-left: 46px;

            [dir="rtl"] & {
                padding-left: unset;
                padding-right: 46px;
            }
        }

        i {
            --Iconsax-Size: 20px;
            --Iconsax-Color: #808B97;
            position: absolute;
            left: 15px;
            top: 12px;

            [dir="rtl"] & {
                left: unset;
                right: 15px;
            }
        }

        .toggle-password {
            position: absolute;
            @include center(vertical);
            right: 15px;
            left: unset;
            cursor: pointer;

            [dir="rtl"] & {
                left: 15px;
                right: unset;
            }

            i {
                position: static;
                transform: unset;
            }

            .eye-slash {
                display: none;
            }

            &.eye {
                i.eye {
                    display: block;
                }

                i.eye-slash {
                    display: none;
                }
            }

            &.eye-slash {
                i.eye-slash {
                    display: block;
                }

                i.eye {
                    display: none;
                }
            }
        }
    }

    .phone-detail {
        .form-control {
            &-white {
                padding-left: 16px;

                [dir="rtl"] & {
                    padding-left: unset;
                    padding-right: 16px;
                }
            }
        }

    }
}

.select-option {
    border: 1px solid $gray-color;
    padding: calc(13px + (16 - 13) * ((100vw - 320px) / (1920 - 320)));
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    color: $light-color;

    .form-check {
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        border-bottom: 1px solid $gray-color;
        padding-bottom: calc(8px + (12 - 8) * ((100vw - 320px) / (1920 - 320)));
        margin-bottom: calc(8px + (12 - 8) * ((100vw - 320px) / (1920 - 320)));

        &:last-child {
            border: none;
            padding-bottom: 0;
            margin-bottom: 0;
        }

        Label {
            font-size: 16px;
            color: rgba($title-color, 0.9) !important;
            cursor: pointer;
        }
    }

    .form-radio-input {
        &:checked {
            ~label {
                font-weight: 500;
                color: $title-color !important;
            }
        }
    }
}

.select-servicemen {
    @include flex_common ($dis: flex, $align: center, $justify: space-between);
    border: 1px solid $gray-color;
    padding: calc(13px + (16 - 13) * ((100vw - 320px) / (1920 - 320)));
    border-radius: 10px;
    background-color: $white;
    gap: 12px;

    p {
        margin: 0;
        font-size: 16px;
    }

    @include mq-max(sm) {
        flex-direction: column;
        align-items: start;

        [dir="rtl"] & {
            align-items: end;
        }
    }
}

.servicemen-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: auto;
    height: 450px;
}

.servicemen-list-item {
    border: 1px solid $gray-color;
    padding: 10px 12px;
    border-radius: 10px;
    gap: 12px;
    box-shadow: 0px 4px 4px 0px rgba($title-color, 0.06);
    @include flex_common ($dis: flex, $align: center, $justify: space-between);

    .list {
        @include flex_common_1 ($dis: flex, $align: center);
        gap: 12px;

        @include mq-max(sm) {
            gap: 6px;
        }

        p {
            margin: 0;
            color: $light-color !important;
            font-size: 14px !important;
        }

        ul {
            @include flex_common_1 ($dis: flex, $align: center);
            gap: 12px;
        }

        h5 {
            font-weight: 500;

            @include mq-max(2xs) {
                font-size: 14px;
                width: 70px;
                overflow: hidden;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                white-space: nowrap;
                text-align: left;
                text-overflow: ellipsis;

                [dir="rtl"] & {
                    text-align: right;
                }
            }
        }

        .rate {
            margin: 0;
            position: relative;
            padding-left: 12px;

            [dir="rtl"] & {
                padding-left: unset;
                padding-right: 12px;
            }

            .star {
                width: 13px;
                height: 13px;
            }

            &:before {
                @include pos;
                top: 2px;
                left: 0;
                background-color: $gray-color;
                width: 1px;
                height: 12px;

                [dir="rtl"] & {
                    left: unset;
                    right: 0;
                }
            }
        }

        .detail {
            font-size: 16px;
            font-weight: 500;
            border: none;
            background: none;
            padding: 0;
            line-height: 1;

            @include mq-max(2xs) {
                font-size: 14px;
                width: 100px;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: left;

                [dir="rtl"] & {
                    text-align: right;
                }
            }

            &:hover {
                color: var(--theme-color);
            }
        }

        .img-45 {
            @include mq-max(sm) {
                width: 35px !important;
                height: 35px !important;
            }
        }
    }
}

.status-note {
    color: $danger-color;
    padding: 16px;
    border-radius: 8px;
    background-color: rgba($danger-color, 0.10);
    font-size: 14px;
    text-align: center;

    span {
        font-weight: 500;
    }
}

.emoji-tab {
    @include flex_common;
    gap: 10px;

    .emoji-icon {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;

        h4 {
            font-size: 16px;
            margin-top: 8px;
            color: $light-color;

            @include mq-max(xs) {
                display: none;
            }
        }

        .emojis {
            padding: 10px;
            background-color: $white;
            border-radius: 12px;
            border: 1px solid $gray-color;
            @include flex_common;
            cursor: pointer;

            @include mq-max(xs) {
                padding: 8px;
            }

            @include mq-max(2xs) {
                padding: 6px;
            }

            .emoji {
                width: 40px;
                height: 40px;
                filter: invert(65%) sepia(8%) saturate(540%) hue-rotate(171deg) brightness(83%) contrast(87%);

                @include mq-max(xs) {
                    width: 30px;
                    height: 30px;
                }

                @include mq-max(2xs) {
                    width: 25px;
                    height: 25px;
                }
            }

            .deactive {
                display: block;
            }

            .active {
                display: none;
            }
        }

        &.active {
            .emojis {
                outline: 2px solid var(--theme-color);
            }

            .emoji {
                filter: invert(54%) sepia(78%) saturate(5765%) hue-rotate(223deg) brightness(98%) contrast(107%);
            }

            .deactive {
                display: none;
            }

            .active {
                display: block;
            }

            h4 {
                color: var(--theme-color);
            }
        }
    }
}