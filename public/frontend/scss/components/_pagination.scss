/**=====================
     Pagination scss
==========================**/
.pagination-main {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;

    @include mq-max(sm) {
        justify-content: center;
    }

    label {
        color: $light-color;

        @include mq-max(sm) {
            display: none;
        }
    }

    i {
        --Iconsax-Size: 18px;
        --Iconsax-Color: #2263eb;
    }

    .pagination {
        @include flex_common_1 ($dis: flex, $align: center);
        gap: calc(8px + (12 - 8) * ((100vw - 320px) / (1200 - 320)));

        .page-item {
            .page-link {
                @include flex_common;
                color: $light-color;
                width: calc(30px + (35 - 30) * ((100vw - 320px) / (1200 - 320)));
                height: calc(30px + (35 - 30) * ((100vw - 320px) / (1200 - 320)));
                border: none;
                border-radius: 100%;
                padding: 0;

                &:hover {
                    background-color: rgba(84, 101, 255, 0.2);
                }


                &:focus {
                    border: 1px solid $gray-color;
                    outline: none;
                    box-shadow: none;
                }



                span {
                    line-height: 1;
                }
            }

            &.active {
                .page-link {
                    background-color: var(--theme-color);
                    color: $white;
                    font-weight: 600;
                    border-radius: 100%;
                }
            }

            &.disabled {
                .page-link {
                    background-color: transparent;
                }
            }

            &:hover {
                border-radius: 100%;

            }
        }
    }
}

.pagination {
    justify-content: center;
    gap: 10px;
    // margin-top: 20px;

    .page-item {
        &:not(:first-child) {
            .page-link {
                margin: 0;
            }
        }

        &:first-child {
            .page-link {
                color: transparent;
                background-image: url(../images/svg/left.svg);
                background-repeat: no-repeat;
                background-size: 20px;
                background-position: center;
            }
        }

        &:last-child {
            .page-link {
                color: transparent;
                background-image: url(../images/svg/right.svg);
                background-repeat: no-repeat;
                background-size: 20px;
                background-position: center;
            }
        }

        &.active {
            .page-link {
                background-color: var(--theme-color);
                color: $white;
            }
        }


        .page-link {
            width: 38px;
            height: 38px;
            cursor: pointer;
            padding: 0;
            @include flex_common;
            border-radius: 4px;
            color: #808B97;
        }
    }
}