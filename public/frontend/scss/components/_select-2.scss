.phone-detail {
    background-color: $section-bg;
    color: $title-color;
    border-radius: 8px;
    overflow: hidden;

    &:has(.form-control.form-control-white) {
        .select2-container--default {
            .selection {
                border-right: 1px solid #E5E8EA;
            }

            .select2-selection--single {
                background-color: #ffffff;
            }
        }
    }

    .select2-dropdown {
        border-color: $gray-color;
    }

    .select2-container--default {
        .selection {
            height: 100%;
            min-width: calc(95px + (115 - 95) * ((100vw - 320px) / (1920 - 320)));
        }

        .select2-selection--single {
            height: 100%;
            border: none;
            border-right: 1px solid $gray-color;
            background-color: #f5f6f7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-inline: 9px;
            border-radius: 0;
        }

        .select2-selection__rendered {
            display: flex;
            align-items: center;
            color: #2263eb;
            font-weight: 400;
            line-height: 1.5;
            font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));
        }

        .select2-selection__arrow {
            position: relative;
            inset: unset;
            @include center(vertical);
            height: 100%;
        }
    }
}

.select2-container--open {

    .select2-dropdown--above,
    .select2-dropdown--below {
        border-color: $gray-color;
    }
}

.select2-container--default {
    .select2-search--dropdown {
        .select2-search__field {
            border-color: $gray-color;
            border-radius: 4px;
        }
    }

    .select2-results__option[aria-selected] {
        width: 100%;
        padding-inline: 15px;

        span {
            width: 100%;
            font-size: 15px;
            color: #808B97;
        }
    }

    .select2-results__option[aria-selected=true] {
        background-color: #f5f6f7;
    }

    .select2-results__option--highlighted[aria-selected] {
        background-color: var(--theme-color);

        span {
            font-weight: 600;
            color: $white;
        }
    }

    .select2-results {
        >.select2-results__options {
            &::-webkit-scrollbar-track {
                background-color: $section-bg;
            }

            &::-webkit-scrollbar {
                width: 6px;
                background-color: #F5F5F5;
            }

            &::-webkit-scrollbar-thumb {
                background-color: #ddd;
            }
        }
    }
}

.error-div {
    display: flex;
    flex-direction: column;
    justify-content: unset;
    align-items: unset;

    .dropdown-wrapper {
        display: none;
    }

    .error {
        display: block;
        order: 1;
    }

    .select2-dropdown {
        border-color: $gray-color;
    }

    .select2-container--default {
        &.select2-container--open {
            &.select2-container--below {

                .select2-selection--multiple,
                .select2-selection--single {
                    background-color: transparent;
                    border: 1px solid $gray-color;
                    border-bottom: 0;
                }
            }
        }
    }

    .select2-container--default {
        .selection {
            height: 100%;
            min-width: 115px;
        }

        .select2-selection--single {
            height: 46px;
            border: none;
            background-color: $section-bg;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-inline: 9px;
            border-radius: 4px;
        }

        .select2-selection__placeholder {
            font-size: 16px;
            font-weight: 500;
            color: $title-color;
        }

        .select2-selection__rendered {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            color: $title-color;
        }

        .select2-selection__arrow {
            position: relative;
            inset: unset;
            @include center(vertical);
            height: 100%;
        }
    }
}

.select2-container--open {

    .select2-dropdown--above,
    .select2-dropdown--below {
        border-color: $gray-color;
    }
}

.select2-container--default {
    .select2-search--dropdown {
        .select2-search__field {
            border-color: $gray-color;
            border-radius: 4px;
        }
    }

    .select2-results__option[aria-selected] {
        width: 100%;
        letter-spacing: 1px;

        span {
            width: 100%;
            font-size: 15px;
            color: #808B97;
        }
    }

    .select2-results__option--highlighted[aria-selected] {
        background-color: var(--theme-color);

        span {
            font-weight: 600;
            color: $white;
        }
    }

    .select2-results {
        >.select2-results__options {
            &::-webkit-scrollbar-track {
                background-color: $section-bg;
            }

            &::-webkit-scrollbar {
                width: 6px;
                background-color: #F5F5F5;
            }

            &::-webkit-scrollbar-thumb {
                background-color: #ddd;
            }
        }
    }
}

.select2-container--default {
    .selection {
        width: 100%;

        .select2-selection--single {
            background-color: #F5F6F7;
            border: 1px solid #E5E8EA;
            border-radius: 4px;
            height: 46px;
            width: 100%;


            .select2-selection__rendered {
                border-radius: 6px;
                padding: 12px 24px 12px 12px;
                line-height: 1.5;

                [dir="rtl"] & {
                    padding: 12px 12px 12px 24px;
                }

                .select2-selection__placeholder {
                    color: #999;
                    font-size: 16px;
                    font-weight: 400;
                    line-height: 1.3;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 100%;
                }
            }
        }

        .select2-selection__arrow {
            position: absolute;
            top: 45%;
            right: 1px;
            width: 20px;
            transform: translateY(-50%);
            
            [dir="rtl"] & {
                right: unset;
                left: 1px;
            }

            b {
                border: none;
                background-image: url(../images/svg/arrow.svg);
                background-position: center;
                background-repeat: no-repeat;
                width: 16px;
                height: 16px;
                position: absolute;
                top: 55%;
                right: 10px;
                transform: translate(-50%, -50%);

                [dir="rtl"] & {
                    right: unset;
                    left: 15px;
                }
            }
        }
    }
}