/**=====================
     Accordion scss
==========================**/

.accordion {
     .accordion-item {
          border: none;
          background-color: transparent;

          .accordion-button {
               background-color: transparent;
               width: max-content;
               padding: 0;
               border: none;
               box-shadow: none;

               &:not(.collapsed) {
                    &:after {
                         background-image: url(@/svg/arrow.svg);
                    }
               }
          }

          .accordion-body {
               padding: 0;
          }
     }
}

.filter {
     #no-results-message {
          @include pseudowh;
          @include flex_common;
          font-size: 18px;
          font-weight: 500;
          border-radius: 7px;
          letter-spacing: 0.4px;
          line-height: 1.6;
     }

     .accordion {
          margin-bottom: 20px;

          .accordion-item {
               .accordion-header {
                    .accordion-button {
                         font-size: 17px;
                         width: 100%;
                         @include flex_common($dis: flex, $align: center, $justify: space-between);
                         font-weight: 500;
                         color: $title-color;
                         cursor: pointer;

                         &:after {
                              background-size: 16px;

                              [dir="rtl"] & {
                                   margin-left: unset;
                                   margin-right: auto;
                              }
                         }
                    }
               }

               .accordion-body {
                    padding: 16px 0;

                    .category-body {
                         .category-list {

                              .category-item {
                                   &.no-category {
                                        margin: 30px auto;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-size: 18px;
                                        font-weight: 500;
                                        border-radius: 7px;
                                        letter-spacing: 0.4px;
                                        line-height: 1.6;
                                   }

                                   .form-check-label {
                                        width: 100%;
                                        font-size: 14px;
                                        display: flex;
                                        align-items: center;
                                        gap: 10px;

                                        img {
                                             width: 30px;
                                        }
                                   }

                                   .form-check-input {
                                        &:checked {
                                             ~.form-check-label {
                                                  font-weight: 500;
                                                  color: #2263eb;
                                             }
                                        }
                                   }
                              }
                         }
                    }

                    .filter-body {
                         .service {
                              border-radius: 8px;
                              padding: 16px;
                              background-color: $white;
                              display: flex;
                              flex-direction: column;
                              gap: 10px;

                              .form-check {
                                   label {
                                        width: 100%;
                                        font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));
                                        cursor: pointer;
                                   }
                              }
                         }

                         .form-group {
                              margin: 0;
                         }

                         .search-provider {
                              margin-top: 10px;
                         }
                    }

                    .accordion {
                         .accordion-item {
                              .search-div {
                                   @include flex_common($dis: flex, $align: center, $justify: space-between);
                                   background-color: $white;
                                   border-radius: 8px;
                                   padding: 10px 16px;

                                   i {
                                        --Iconsax-Size: 18px;
                                        --Iconsax-Color: #808b97;
                                   }

                                   .form-group {
                                        width: 100%;
                                        display: flex;
                                        align-items: center;
                                        gap: 4px;

                                        input {
                                             border: none;
                                             width: 100%;
                                        }
                                   }

                                   .accordion-button {
                                        &:after {
                                             background-size: 16px;
                                        }
                                   }
                              }

                              .accordion-body {

                                   .search-body {
                                        display: flex;
                                        flex-direction: column;
                                        gap: 12px;
                                        height: 210px;
                                        overflow: auto;

                                        .form-check {
                                             gap: 8px;
                                             margin-bottom: 0;
                                        }

                                        ul {
                                             width: 100%;
                                             overflow: hidden;
                                             text-overflow: ellipsis;
                                             display: block;
                                             white-space: nowrap;
                                             cursor: pointer;

                                             li {
                                                  position: relative;
                                                  color: $light-color;
                                                  font-size: 14px;
                                                  line-height: 18px;
                                                  padding: 0 10px;

                                                  &:before {
                                                       @include pos;
                                                       top: 4px;
                                                       right: 0px;
                                                       background-color: $light-color;
                                                       width: 1px;
                                                       height: 12px;

                                                            [dir="rtl"] & {
                                                                 left: 0px;
                                                                 right: unset;
                                                            }
                                                  }

                                                  &:first-child {
                                                       color: $title-color;
                                                       padding-left: 0;
                                                       
                                                       [dir="rtl"] & {
                                                            padding-left: unset;
                                                            padding-right: 0;
                                                       }

                                                  }

                                                  &:last-child {
                                                       padding-right: 0;

                                                       [dir="rtl"] & {
                                                            padding-right: unset;
                                                            padding-left: 0;
                                                       }

                                                       &:before {
                                                            display: none;
                                                       }
                                                  }
                                             }
                                        }
                                   }
                              }
                         }
                    }
               }

               .price-range-box {
                    background-color: $white;
                    padding: 16px;
                    border-radius: 8px;
                    height: 80px;
                    margin-block: 16px;

                    .irs--round {
                         background-color: transparent;
                         padding: 0;
                         border-radius: 0;
                         height: unset;

                         .irs-bar {
                              top: 10px;
                         }

                         .irs-handle {
                              top: 5px;
                         }

                         .irs {
                              width: 93%;
                              margin: auto;
                         }
                    }
               }
          }
     }

     .card-footer {
          padding-top: 0 !important;

          button {
               width: 100%;
               justify-content: center;
               border-radius: 8px;
          }

          &:before,
          &:after {
               display: none;
          }
     }
}

.faq-section,
.terms-section,
.privacy-section {
     .accordion {
          .accordion-item {
               font-family: "DM Sans", serif !important;
               background-color: $white;
               border-radius: 12px;

               +.accordion-item {
                    margin-top: 20px;
               }

               .accordion-header {
                    .accordion-button {
                         padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));
                         display: flex;
                         align-items: center;
                         justify-content: space-between;
                         width: 100%;
                         line-height: 1.2;
                         font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));
                         font-weight: 500;
                         gap: 4px;
                         color: $title-color;
                         cursor: pointer;

                         i {
                              --Iconsax-Size: calc(20px + (24 - 20) * ((100vw - 320px) / (1920 - 320)));
                              --Iconsax-Color: #2263eb;
                         }

                         &:after {
                              display: none;
                         }

                         .add {
                              display: none;
                         }

                         .minus {
                              display: flex;
                         }

                         &.collapsed {

                              color: rgba($title-color, 0.7);

                              .add {
                                   display: flex;
                              }

                              .minus {
                                   display: none;
                              }
                         }
                    }
               }

               .accordion-body {
                    padding: 20px;

                    p {
                         font-size: 18px;
                         line-height: 1.5;
                    }
               }
          }
     }

     ul,
     ol {
          list-style-type: disc;
          padding-left: 1.25rem;

          [dir="rtl"] & {
               padding-right: 1.25rem;
               padding-left: unset;
          } 


          li {
               display: list-item;
               font-size: 17px;
               color: rgba($title-color, 0.7);
               line-height: 1.3 !important;
               margin-bottom: 12px;
          }
     }
}

.up-down-image {
     display: flex;
     gap: 20px;

     img {
          border-radius: 12px;
          background-color: $gray-color;
     }
}