{"version": 3, "sources": ["utils/_variables.scss", "base/_typography.scss", "utils/mixin/_breakpoints.scss", "base/_reset.scss", "utils/mixin/_common.scss", "components/_accordion.scss", "components/_animation.scss", "components/_booking.scss", "components/_breadcrumb.scss", "components/_button.scss", "components/_calendar.scss", "components/_card.scss", "components/_cart.scss", "components/_comment.scss", "components/_dropdown.scss", "components/_form-wizard.scss", "components/_forms.scss", "components/_home-section.scss", "components/_invoice.scss", "components/_loader.scss", "components/_modal.scss", "components/_offer.scss", "components/_pagination.scss", "components/_profile-setting.scss", "components/_range-slider.scss", "components/_rating.scss", "components/_ratio.scss", "components/_swiper.scss", "components/_table.scss", "components/_tabs.scss", "layout/_header.scss", "layout/_footer.scss", "pages/_about-us.scss", "pages/_auth.scss", "pages/_blog.scss", "pages/_category.scss", "pages/_contact.scss", "pages/_policy.scss", "pages/_provider.scss", "pages/_service.scss"], "names": [], "mappings": "AAAA,MACI,sBAAc,CACd,8BAAkB,CACrB,KCEI,qBDiBc,CChBd,iBAAkB,CAClB,cAAe,CACf,QAAS,CACT,qBDGU,CCFV,aDIgB,CCFpB,GAGI,cAAe,CACf,eAAgB,CACpB,GAGI,oBAAqB,CACrB,cAAe,CACnB,EAGI,cAAe,CACf,gBAAiB,CACjB,QAAS,CACb,EAGI,wBAAyB,CACzB,4BAAqB,CAArB,oBAAqB,CACrB,oBAAqB,CAH1B,QAMU,wBAAyB,CACzB,oBAAqB,CACrB,4BAAY,CAAZ,oBAAqB,CAR/B,QAYU,YAAa,CACjB,aAKI,YAAa,CACjB,OAID,YAAa,CACjB,MAGI,cAAe,CACf,aD3CgB,CC4CpB,GAGI,mEAAoE,CACpE,eAAgB,CAChB,eAAgB,CAChB,yBAA0B,CAC1B,QAAS,CACb,GAGI,mEAAoE,CACpE,eAAgB,CAChB,yBAA0B,CAC1B,QAAS,CACb,GAGI,mEAAoE,CACpE,eAAgB,CAChB,eAAgB,CAChB,QAAS,CACb,GAGI,mEAAoE,CACpE,eAAgB,CAChB,QAAS,CACT,eAAgB,CACpB,GAGI,mEAAoE,CACpE,eAAgB,CAChB,QAAS,CACT,eAAgB,CACpB,GAGI,mEAAoE,CACpE,eAAgB,CAChB,QAAS,CACT,eAAgB,CACpB,KAGI,oBAAqB,CACzB,aAGI,mCAAoC,CACxC,gBAGI,wCAAyC,CAC7C,cAGI,SAAU,CC9FP,2BD6FR,cAGU,SAAU,CAMnB,CCtGO,2BD6FR,cAMU,WAAY,CACZ,iBAAkB,CAE3B,CAED,cACK,SAAU,CCzGP,2BDwGR,cAGU,SAAU,CAKnB,CChHO,2BDwGR,cAMU,UAAW,CAEpB,CAED,OACK,oCAAO,CAAP,iCAAO,CAAP,4BAA6B,CACjC,QAGI,qBAAsB,CAC1B,yBEvIG,qEAAsE,CACtE,eAAgB,CACnB,iBAGG,wEAAyE,CAC5E,mBAGG,sEAAuE,CAC1E,iBAGG,qEAAsE,CACzE,kBAGG,qEAAsE,CACzE,iBAGG,wEAAyE,CAC5E,kBAIG,wEAAyE,CAC5E,oBAGG,4HAA6H,CAChI,oBAGG,oEAAqE,CACxE,oBAGG,oEAAqE,CACxE,oBAGG,oEAAqE,CACxE,oBAGG,oEAAqE,CACxE,YAGG,wBHzCgB,CG0CnB,kEAUG,uBAAwB,CACxB,QAAS,CACZ,mBAIG,yBAA0B,CAC7B,MChEG,mBDqEkC,CCrElC,mBDqEkC,CCrElC,YDqEkC,CCpElC,wBDoEkD,CCpElD,qBDoEkD,CCpElD,kBDoEkD,CAClD,OAAQ,CACR,cAAe,CAHnB,YAMQ,cAAe,CACf,eAAgB,CAChB,aAAc,CARtB,eAaY,UAAW,CACX,WAAY,CACf,OCzFL,mBDgGgC,CChGhC,mBDgGgC,CChGhC,YDgGgC,CC/FhC,wBD+FgD,CC/FhD,qBD+FgD,CC/FhD,kBD+FgD,CC9FhD,wBD8FyE,CC9FzE,qBD8FyE,CC9FzE,6BD8FyE,CACzE,sEAAuE,CACvE,+BHxFgB,CGyFhB,iBAAkB,CAClB,uEAAwE,CACxE,OAAQ,CANZ,kBASQ,UHlGO,CGmGP,6CHnGO,CGyFf,cAeQ,UAAW,CACX,iBAAkB,CAClB,mCAAoC,CACpC,UAAW,CACX,UAAW,CACX,aAAc,CACd,MAAO,CArBf,UAyBQ,eAAgB,CAChB,qEAAsE,CDxGtE,0BC8ER,UA6BY,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,sBAAuB,CAE9B,CApCL,iBCzFI,mBDgIsC,CChItC,mBDgIsC,CChItC,YDgIsC,CC/HtC,wBD+HsD,CC/HtD,qBD+HsD,CC/HtD,kBD+HsD,CAClD,OAAQ,CACR,mEAAoE,CACpE,eAAgB,CAChB,iBAAkB,CAClB,kBAAmB,CA5C3B,mBA+CY,yEAAe,CACf,wBAAgB,CAChB,iCAA0B,CAA1B,yBAA0B,CAC1B,gCAAY,CAAZ,wBAAyB,CAlDrC,uBAsDY,yBAA0B,CAtDtC,yBAyDgB,iCAA0B,CAA1B,yBAA0B,CAC1B,gCAAY,CAAZ,wBAAyB,CAC5B,SC1JT,mBDgKgC,CChKhC,mBDgKgC,CChKhC,YDgKgC,CC/JhC,wBD+JgD,CC/JhD,qBD+JgD,CC/JhD,kBD+JgD,CC9JhD,uBD8JkE,CC9JlE,oBD8JkE,CC9JlE,sBD8JkE,CAClE,iBAAkB,CAClB,aAAc,CACd,kEAAmE,CACnE,iBAAkB,CALtB,YAQQ,eAAgB,CAChB,qEAAsE,CAT9E,gBAaQ,UAAW,CACX,iBAAkB,CAClB,mCAAoC,CACpC,UAAW,CACX,UAAW,CACX,YAAa,CACb,QAAS,CACT,kCAAW,CAAX,0BAA2B,CAC9B,cAMD,iBAAkB,CAClB,QAAS,CACT,UAAW,CACX,wBHzLkB,CG0LlB,UHxLW,CGyLX,kBAAmB,CACnB,gBAAiB,CACjB,cAAe,CACf,eAAgB,CACnB,wCAMO,iBAAkB,CAClB,wDAAyD,CACzD,2BAA4B,CAC5B,yBAA0B,CAC1B,UAAW,CACX,eAAgB,CD7LhB,0BCsLR,wCAUY,qBAAsB,CAyH7B,CAnIL,2DAcY,UH/MG,CGgNH,oBAAqB,CACrB,8HAA+H,CAC/H,wEAAyE,CDvM7E,0BCsLR,2DAoBgB,iEAAkE,CAElE,0DAA2D,CAC3D,0BAA2B,CAC3B,qBAAsB,CACtB,qBAAsB,CACtB,kBAAmB,CACnB,2BAA4B,CAiEnC,CA5FT,8DA+BgB,wBAAyB,CACzB,eAAgB,CAChB,qEAAsE,CAjCtF,6DAqCgB,mEAAoE,CACpE,gBAAiB,CACjB,2BHxOD,CGyOC,QAAS,CACT,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,sBAAuB,CA9CvC,uECjMI,mBDmP8C,CCnP9C,mBDmP8C,CCnP9C,YDmP8C,CClP9C,wBDkP8D,CClP9D,qBDkP8D,CClP9D,kBDkP8D,CAClD,OAAQ,CACR,gBAAiB,CACjB,QAAS,CArDzB,qFAwDoB,iBAAkB,CAClB,qBH1PL,CG2PK,aHxPC,CEQb,0BCsLR,qFA6DwB,gBAAiB,CAExB,CA/DjB,4EAkEoB,iBAAkB,CAClB,yBAAO,CAAP,sBAAO,CAAP,iBAAkB,CAnEtC,iFAsEwB,SAAU,CD5P1B,0BCsLR,4EA0EwB,gBAAiB,CACjB,cAAe,CA3EvC,iFA8E4B,YAAa,CAChB,CA/EzB,gEAqFgB,qEAAsE,CACtE,cAAe,CD5QvB,0BCsLR,gEAyFoB,gBAAiB,CAExB,CDjRL,0BCsLR,0DAgGgB,YAAa,CAkCpB,CAlIT,8DAoGgB,iBAAkB,CAClB,6CAAsC,CAAtC,qCAAsC,CACtC,0BAAiB,CAAjB,kBAAmB,CAtGnC,wEA0GgB,SAAU,CACV,SAAU,CACV,WAAY,CA5G5B,wEAgHgB,QAAS,CACT,UAAW,CACX,WAAY,CDxSpB,0BCsLR,wEAqHoB,YAAa,CAEpB,CAvHb,wEA0HgB,YAAa,CACb,WAAY,CACZ,WAAY,CDlTpB,2BCsLR,wEA+HoB,YAAa,CAEpB,CAOb,kCAGQ,SAAU,CAHlB,wCAOQ,kBH5UY,CGqUpB,wCAWQ,+BH3Ve,CG4Vf,iBAAkB,CAZ1B,8CAgBQ,kBHhWe,CGiWlB,gBAcD,yBAA0B,CAC7B,WAGG,aH1WiB,CG2WjB,eAAgB,CACnB,YAGG,UHjXW,CGkXd,QAKG,wBHnXgB,CGoXnB,OAGG,4BHvXgB,CGwXnB,UAGG,+BH3XgB,CG4XnB,QAGG,6BH/XgB,CGgYnB,SAGG,8BHnYgB,CGoYnB,eAGG,yBHvYgB,CGwYnB,cAGG,wBH3YgB,CG4YhB,aAAc,CACjB,cAIG,6BHjZgB,CGkZnB,iBAGG,gCHrZgB,CGsZnB,eAGG,8BHzZgB,CG0ZnB,gBAGG,+BH7ZgB,CG8ZnB,OAGG,kBAAmB,CACtB,OAGG,kBAAmB,CACtB,OAGG,kBAAmB,CACtB,MAGG,iBAAkB,CACrB,MAGG,iBAAkB,CACrB,SAIG,wBAAyB,CAC5B,SAGG,yBAA0B,CAC7B,SAGG,2BAA4B,CAC/B,SAGG,4BAA6B,CAChC,MAKG,YAAa,CAChB,MAGG,iEAAkE,CACrE,OAGG,6BAA8B,CACjC,QAIG,qBAAsB,CACtB,sBAAuB,CACvB,kBAAmB,CACtB,uBAIG,wBH3dgB,CG4dhB,iBAAkB,CCjelB,mBDkekC,CClelC,mBDkekC,CClelC,YDkekC,CCjelC,wBDiekD,CCjelD,qBDiekD,CCjelD,kBDiekD,CAClD,KAAM,CALV,2BAQQ,oBAAe,CACf,wBAAgB,CAChB,cAAe,CC9enB,mBD+eoC,CC/epC,mBD+eoC,CC/epC,YD+eoC,CC9epC,wBD8eoD,CC9epD,qBD8eoD,CC9epD,kBD8eoD,CC7epD,uBD6esE,CC7etE,oBD6esE,CC7etE,sBD6esE,CAClE,UAAW,CACX,WAAY,CAbpB,mCAiBQ,aH7ea,CG8eb,WAAY,CACZ,wBH5eY,CG6eZ,iBAAkB,CAClB,UAAW,CACX,WAAY,CAtBpB,iCA0BQ,wBH/fe,CGggBf,qBAAgB,CAChB,iBAAkB,CACrB,aAID,oCHjgBkB,CGkgBlB,aHlgBkB,CGmgBlB,YAAa,CACb,iBAAkB,CAJtB,eAOQ,cAAe,CAPvB,gBAWQ,wBAA+B,CAC/B,iBAAkB,CACrB,eAID,6BH3gBgB,CG4gBhB,kEAAmE,CACtE,MAGG,eAAgB,CADpB,YAIQ,eAAgB,CAChB,aHthBa,CGihBrB,QASQ,aHzhBa,CG0hBhB,8FAQe,4BAA6B,CALjD,uEAYgB,4BAA6B,CAChC,YA6BT,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,QAAS,CAHb,cAMQ,iBAAkB,CAClB,gBAAiB,CDrkBjB,0BC8jBR,YAWQ,UAAW,CAElB,CD3kBO,2BC6kBR,QAEQ,iBAAkB,CAClB,QAAS,CAET,SAAU,CACV,gCAAyB,CAAzB,wBAAyB,CACzB,SAAU,CACV,WAAY,CACZ,mCAA4B,CAA5B,2BAA4B,CAC5B,SAAU,CACV,iBAAkB,CAX1B,aAcY,SAAU,CACV,gCAAyB,CAAzB,wBAAyB,CACzB,iCAA0B,CAA1B,yBAA0B,CAC1B,SAAU,CACV,kBAAmB,CACtB,CAIT,WACI,WAAY,CACZ,eAAgB,CAChB,SAAU,CACV,cAAe,CACf,wBAAyB,CAC5B,eAGG,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,gBAAiB,CACjB,iBAAkB,CACrB,iBAGG,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,OAAQ,CACR,YAAa,CANjB,qBASQ,kBAAmB,CAT3B,mBAaQ,uBH1oBa,CG2oBb,mEAAoE,CAd5E,sBAkBQ,eAAgB,CAChB,yBAAO,CAAP,sBAAO,CAAP,iBAAkB,CAnB1B,wBAsBY,yEAAe,CACf,qBAAgB,CAChB,iCAA0B,CAA1B,yBAA0B,CAC1B,gCAAY,CAAZ,wBAAyB,CAzBrC,8BA8BgB,kCAA2B,CAA3B,0BAA2B,CAC3B,gCAAY,CAAZ,wBAAyB,CAC5B,2BErqBH,WAAY,CACZ,4BAA6B,CAHvC,6CAMe,4BAA6B,CAC7B,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,SAAU,CACV,WAAY,CACZ,uBAAY,CAAZ,eAAgB,CAV/B,mEAcyB,uDAAwD,CAdjF,2CAoBe,SAAU,CACd,mBAMD,kBAAmB,CAF7B,qDAMoB,+BLnBA,CKoBA,kBAAmB,CAPvC,uEAUyB,UAAW,CDjChC,mBCkCoD,CDlCpD,mBCkCoD,CDlCpD,YCkCoD,CDjCpD,wBCiCoE,CDjCpE,qBCiCoE,CDjCpE,kBCiCoE,CDhCpE,wBCgC6F,CDhC7F,qBCgC6F,CDhC7F,6BCgC6F,CACxE,eAAgB,CAChB,aL5BJ,CKerB,6EAgB8B,oBAAqB,CAhBnD,mDAsBoB,cAAe,CAtBnC,yEA0B8B,iBAAkB,CAClB,YAAa,CACb,qBL7Cf,CK8Ce,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CA/BvC,4EAmC8B,QAAS,CAnCvC,iFAuC8B,eAAgB,CAvC9C,0FDvBI,mBCqE8D,CDrE9D,mBCqE8D,CDrE9D,YCqE8D,CDpE9D,wBCoE8E,CDpE9E,qBCoE8E,CDpE9E,kBCoE8E,CDnE9E,wBCmEuG,CDnEvG,qBCmEuG,CDnEvG,6BCmEuG,CACxE,qBLhEpB,CKiEoB,iBAAkB,CAClB,iBAAkB,CAjDrD,4FAoDwC,oBAAe,CACf,wBAAgB,CArDxD,sGAyDwC,UAAW,CAzDnD,4GA4D6C,WAAY,CACZ,UAAW,CA7DxD,kHAmE6C,oBAAqB,CAnElE,8FAyEmC,YAAa,CAzEhD,2GA4EwC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,YAAa,CACb,aAAc,CAhFtD,uHAmF6C,OAAQ,CACR,gBAAiB,CACjB,eAAgB,CArF7D,8GAyF6C,UAAW,CACX,eAAgB,CAChB,sBAAuB,CACvB,aAAc,CACd,kBAAmB,CA7FhE,iHAgGkD,iBAAkB,CAClB,aL/G7B,CKgH6B,cAAe,CACf,gBAAiB,CACjB,cAAe,CApGjE,wHAuGuD,iBAAkB,CAClB,UAAW,CACX,OAAQ,CACR,SAAU,CACV,wBLzHlC,CK0HkC,SAAU,CACV,WAAY,CA7GnE,6HAiHuD,aLhIlC,CKiIkC,cAAe,CAlHtE,4HAsHuD,eAAgB,CAtHvE,mIAyH4D,YAAa,CAzHzE,qBAuIU,wBAAyB,CAvInC,4BA0Ie,UAAW,CACX,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,iBAAkB,CA5IjC,uDAiJe,YAAa,CACjB,8HASI,qBL5KA,CK6KA,kBAAmB,CANlC,8KASoB,eAAgB,CATpC,0OAcyB,iEAAkE,CAClE,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,UAAW,CACX,eAAgB,CAChB,mEAAoE,CACpE,eAAgB,CAChB,OAAQ,CACR,aL5LJ,CKqKrB,gPA0B8B,yEAAe,CACf,wBAAgB,CA3B9C,4PA+B8B,YAAa,CA/B3C,yPAmC8B,YAAa,CAnC3C,+PAuC8B,mBAAS,CAAT,mBAAS,CAAT,YAAa,CAvC3C,wQA4C8B,uBLjNT,CKqKrB,uRA+CmC,mBAAS,CAAT,mBAAS,CAAT,YAAa,CA/ChD,6RAmDmC,YAAa,CAnDhD,8KA0DoB,YAAa,CA1DjC,oLA6DyB,cAAe,CACf,eAAgB,CA9DzC,4GAsEU,sBAAuB,CACvB,oBAAqB,CAvE/B,8HA2Ee,iBAAkB,CAClB,cAAe,CACf,uBLlPM,CKmPN,0BAA2B,CAC3B,kBAAmB,CACvB,eAKN,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,QAAS,CAFd,mBAKU,kBAAmB,CACnB,wBL7PU,CK8Pd,yBCzQF,GACI,+BAAW,CAAX,uBAAwB,CAG5B,KACI,mCAAW,CAAX,2BAA4B,CAAA,CDoQ9B,iBCzQF,GACI,+BAAW,CAAX,uBAAwB,CAG5B,KACI,mCAAW,CAAX,2BAA4B,CAAA,CAIpC,6BACI,GACI,0BAA2B,CAG/B,KACI,4BAA6B,CAAA,CANrC,qBACI,GACI,0BAA2B,CAG/B,KACI,4BAA6B,CAAA,CAIrC,8BACI,GACI,cAAe,CAEnB,IACI,kBAAmB,CAEvB,IACI,oBAAqB,CAEzB,IACI,eAAgB,CAAA,CAXxB,sBACI,GACI,cAAe,CAEnB,IACI,kBAAmB,CAEvB,IACI,oBAAqB,CAEzB,IACI,eAAgB,CAAA,CAIxB,wBACI,GACI,2BAAW,CAAX,mBAAoB,CAExB,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,KACI,2BAAW,CAAX,mBAAoB,CAAA,CAhC5B,gBACI,GACI,2BAAW,CAAX,mBAAoB,CAExB,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,qDAAkC,CAAlC,6CAA8C,CAElD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,IACI,sDAAkC,CAAlC,8CAA+C,CAEnD,KACI,2BAAW,CAAX,mBAAoB,CAAA,CCnE5B,gCAEQ,sBAAuB,CAF/B,iCAKQ,eAAgB,CAChB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CARjB,6CAUY,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,OAAQ,CAXpB,gDHQI,mBGK8C,CHL9C,mBGK8C,CHL9C,YGK8C,CHJ9C,wBGI8D,CHJ9D,qBGI8D,CHJ9D,kBGI8D,CAClD,QAAS,CAdzB,mDAgBoB,iBAAkB,CAClB,iBAAkB,CAClB,cAAe,CAlBnC,uDAoBwB,UAAW,CACX,WAAY,CArBpC,0DAwBwB,UAAW,CACX,iBAAkB,CAClB,OAAQ,CACR,MAAO,CACP,wBPhBJ,COiBI,WAAY,CACZ,SAAU,CA9BlC,sEAkC4B,YAAa,CAlCzC,yCA0CQ,uBAAwB,CACxB,yBAA0B,CLxB1B,0BK4BR,yBAGY,UAAW,CAKlB,CLpCG,0BK4BR,yBAMY,UAAW,CAElB,CARL,iCAWY,eAAgB,CLvCpB,0BK4BR,8BAcY,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,QAAS,CAhBrB,0CAmBgB,UAAW,CACd,CApBb,4BAwBQ,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,OAAQ,CA3BhB,iCAiCY,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,aPzES,COqCrB,kCAuCY,cAAe,CACf,eAAgB,CAChB,gBAAiB,CAzC7B,+BA4CY,mEAAoE,CA5ChF,+CH7CI,mBG6FoC,CH7FpC,mBG6FoC,CH7FpC,YG6FoC,CH5FpC,wBG4FoD,CH5FpD,qBG4FoD,CH5FpD,kBG4FoD,CH3FpD,wBG2F6E,CH3F7E,qBG2F6E,CH3F7E,6BG2F6E,CACzE,kBAAmB,CAjD3B,4HAmDY,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,aPpGW,COqGX,WAAY,CACZ,sBAAuB,CACvB,SAAU,CAzDtB,oJA2DgB,yBAA0B,CA3D1C,6DA+DY,gBAAiB,CA/D7B,mBAmEQ,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,OAAQ,CArEhB,sBH7CI,mBGoHwC,CHpHxC,mBGoHwC,CHpHxC,YGoHwC,CHnHxC,wBGmHwD,CHnHxD,qBGmHwD,CHnHxD,kBGmHwD,CHlHxD,wBGkHiF,CHlHjF,qBGkHiF,CHlHjF,6BGkHiF,CACzE,mEAAoE,CAxEhF,0BA2EY,aP/GS,CIHjB,mBGmH0C,CHnH1C,mBGmH0C,CHnH1C,YGmH0C,CHlH1C,wBGkH0D,CHlH1D,qBGkH0D,CHlH1D,kBGkH0D,CAClD,OAAQ,CA7EpB,0BAgFY,eAAgB,CAhF5B,mCAkFgB,0BAA2B,CAlF3C,qBAsFY,oBAAe,CACf,wBAAgB,CAvF5B,2BA2FQ,SAAU,CA3FlB,iDA6FY,wBP/HQ,COgIR,WAAY,CACZ,uBAAgB,CAAhB,eAAgB,CAChB,eAAgB,CL5HpB,0BK4BR,iDAkGgB,eAAgB,CAkBvB,CApHT,uDAqGgB,UAAW,CArG3B,2DAuGoB,UAAW,CAvG/B,8DAyGwB,wBAAiB,CAAjB,qBAAiB,CAAjB,6BAA8B,CAzGtD,8EA6GoC,YAAa,CA7GjD,gCAwHY,gBAAiB,CACjB,eAAgB,CAChB,UAAW,CACd,wBAKL,sBAAuB,CACvB,SAAU,CACV,WAAY,CACf,YHhLG,mBIDgC,CJChC,mBIDgC,CJChC,YIDgC,CJEhC,wBIFgD,CJEhD,qBIFgD,CJEhD,kBIFgD,CJGhD,uBIHkE,CJGlE,oBIHkE,CJGlE,sBIHkE,CAClE,QAAS,CACT,eAAgB,CAHpB,6BAMQ,mEAAoE,CACpE,URCO,CQRf,qDAUgB,URFD,CQGF,iBAMZ,4BAAwB,CADzB,oBAIE,oBAAqB,CACrB,KCpBE,mEAAoE,CACpE,yHAA0H,CAC1H,kBAAmB,CACnB,gBAAiB,CACjB,OAAQ,CACR,kBAAmB,CACnB,UAAW,CACX,kBAAmB,CLNnB,mBKOgC,CLPhC,mBKOgC,CLPhC,YKOgC,CLNhC,wBKMgD,CLNhD,qBKMgD,CLNhD,kBKMgD,CLLhD,uBKK4D,CLL5D,oBKK4D,CLL5D,sBKKkE,CATtE,qCAaQ,mCAAoC,CACpC,UTNO,CSOP,+BAAgC,CAfxC,yCAkBY,oBAAe,CACf,sBAAgB,CAnB5B,iBAwBQ,wBTvBe,CSwBf,aTxBe,CSDvB,mBA4BY,oBAAe,CACf,wBAAgB,CA7B5B,oBAkCQ,wBTrBY,CSsBZ,aTzBa,CSVrB,oBAwCQ,wBT9Ba,CS+Bb,UTjCO,CSRf,WA6CQ,uBAAY,CAAZ,eAAgB,CA7CxB,cAiDQ,wBTrCY,CSsCZ,aTvCa,CSwCb,eAAgB,CAnDxB,sBAuDQ,wBTjDc,CSkDd,UThDO,CSRf,wBA2DY,oBAAe,CACf,sBAAgB,CA5D5B,uBAiEQ,wBT9De,CS+Df,UT1DO,CSRf,yBAqEY,oBAAe,CACf,sBAAgB,CACnB,OAKL,cAAe,CACf,eAAgB,CAChB,kBAAmB,CACtB,cAaO,wBAA6B,CADjC,gBACI,wBAA6B,CADjC,cACI,wBAA6B,CADjC,aACI,wBAA6B,CADjC,WACI,wBAA6B,CADjC,YACI,wBAA6B,CADjC,YACI,wBAA6B,CADjC,cACI,wBAA6B,CAChC,eAeG,qBAAwB,CACxB,mCAAyC,CAF7C,iBACI,qBAAwB,CACxB,mCAAyC,CAF7C,eACI,qBAAwB,CACxB,mCAAyC,CAF7C,cACI,qBAAwB,CACxB,mCAAyC,CAF7C,YACI,qBAAwB,CACxB,mCAAyC,CAF7C,aACI,qBAAwB,CACxB,mCAAyC,CAF7C,aACI,qBAAwB,CACxB,mCAAyC,CAF7C,eACI,qBAAwB,CACxB,mCAAyC,CAC5C,qBAcG,wBAA8B,CAC9B,gDAAqD,CAFzD,uBACI,wBAA8B,CAC9B,gDAAqD,CAFzD,qBACI,wBAA8B,CAC9B,+CAAqD,CAFzD,oBACI,wBAA8B,CAC9B,+CAAqD,CAFzD,kBACI,wBAA8B,CAC9B,gDAAqD,CAFzD,mBACI,wBAA8B,CAC9B,iDAAqD,CAFzD,mBACI,wBAA8B,CAC9B,6CAAqD,CAFzD,qBACI,wBAA8B,CAC9B,gDAAqD,CACxD,oBC7HD,SAAU,CACV,eAAgB,CAChB,YAAa,CAHjB,yBAMQ,SAAU,CANlB,qDAWQ,YAAa,CAXrB,8CAeQ,cAAe,CACf,wBVHY,CUIZ,iBAAkB,CNftB,mBMgBoC,CNhBpC,mBMgBoC,CNhBpC,YMgBoC,CNfpC,wBMeoD,CNfpD,qBMeoD,CNfpD,kBMeoD,CNdpD,uBMcgE,CNdhE,oBMcgE,CNdhE,sBMcsE,CAlB1E,wCAsBQ,gBAAiB,CRHjB,0BQnBR,wCAwBY,mBAAoB,CAY3B,CApCL,uFA6BgB,aV5BO,CU6BP,cAAe,CRXvB,0BQnBR,uFAgCoB,cAAe,CAEtB,CAlCb,mCAuCQ,cAAe,CACf,WAAY,CACZ,gBAAiB,CACjB,aVhCa,CUiCb,cAAe,CRxBf,0BQnBR,mCA8CW,cAAe,CACf,cAAe,CACf,WAAY,CACZ,gBAAiB,CAsCvB,CAvFL,yCAqDY,qCVpDW,CUqDX,aVrDW,CUsDX,eAAgB,CAvD5B,yCA2DY,wBV1DW,CU2DX,UVpDG,CUqDH,WAAY,CA7DxB,4CAiEY,UVzDG,CU0DH,wBVjEW,CUDvB,sXA4EY,aVjES,CUXrB,2CAgFY,qCV/EW,CUgFX,uBAAY,CAAZ,eAAgB,CAjF5B,iDAoFgB,wBVnFO,CUDvB,kCA0FQ,YAAa,CRvEb,0BQnBR,kCA4FW,SAAU,CAEhB,CA9FL,sCAiGQ,kBAAmB,CAjG3B,uDAoGY,aV1FS,CESb,0BQnBR,uDAsGgB,WAAY,CAEnB,CAxGT,+DA2GY,SAAU,CACV,QAAS,CN1GjB,mBM2GwC,CN3GxC,mBM2GwC,CN3GxC,YM2GwC,CN1GxC,wBM0GwD,CN1GxD,qBM0GwD,CN1GxD,kBM0GwD,CNzGxD,uBMyGoE,CNzGpE,oBMyGoE,CNzGpE,sBMyG0E,CR1FtE,0BQnBR,+DA+GgB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,WAAY,CAWnB,CA3HT,8EAqHoB,cAAe,CRlG3B,0BQnBR,8EAuHwB,cAAe,CAEtB,CAzHjB,wHA+HY,wBVlHQ,CUmHR,kBAAmB,CACnB,WAAY,CACZ,WAAY,CACZ,UAAW,CACX,QAAS,CNlIjB,mBMmIwC,CNnIxC,mBMmIwC,CNnIxC,YMmIwC,CNlIxC,wBMkIwD,CNlIxD,qBMkIwD,CNlIxD,kBMkIwD,CNjIxD,uBMiIoE,CNjIpE,oBMiIoE,CNjIpE,sBMiI0E,CArI9E,4IAyIoB,YV/HC,CUVrB,gIA8IgB,UAAW,CACX,WAAY,CA/I5B,4DAoJY,oBAAqB,CApJjC,4DAwJY,qBAAsB,CAxJlC,sEA4JY,wBV/IQ,CUgJR,iBAAkB,CAClB,cAAe,CACf,gBAAiB,CR5IrB,0BQnBR,sEAiKe,cAAe,CAGrB,CApKT,uDAuKY,wBV1JQ,CU2JR,iBAAkB,CAClB,cAAe,CACf,gBAAiB,CACjB,UAAW,CA3KvB,gDAkLgB,WAAY,CAlL5B,gDAwLgB,sBAAuB,CACvB,WAAY,CAzL5B,wDA8LoB,OAAQ,CA9L5B,0DAoMoB,OAAQ,CApM5B,4CA4MY,WAAY,CACZ,iBAAkB,CAClB,sBAAuB,CACvB,0BAA2B,CAC3B,QAAS,CAhNrB,kDAmNgB,YAAa,CAnN7B,6DAuNgB,WAAY,CACZ,iBAAkB,CAClB,4CAA6C,CAC7C,2BAA4B,CAC5B,0BAA2B,CAC3B,uBAAwB,CACxB,SAAU,CACV,SAAU,CA9N1B,kJAoOwB,sBAAuB,CApO/C,kEA0OoB,UAAW,CA1O/B,gFA8O4B,2BV7OL,CU8OK,gBAAiB,CACjB,SAAU,CACV,UAAW,CAjPvC,kFAuP4B,wBVtPL,CUuPK,gBAAiB,CACjB,OAAQ,CAzPpC,kDAgQgB,eAAgB,CAChB,iBAAkB,CAClB,cAAe,CACf,aVlQO,CUmQP,eAAgB,CApQhC,sEAyQgB,QAAS,CACT,aVzQO,CU0QP,cAAe,CNzQ3B,mBM0Q4C,CN1Q5C,mBM0Q4C,CN1Q5C,YM0Q4C,CNzQ5C,wBMyQ4D,CNzQ5D,qBMyQ4D,CNzQ5D,kBMyQ4D,CNxQ5D,uBMwQwE,CNxQxE,oBMwQwE,CNxQxE,sBMwQ8E,CA5QlF,6DAgRgB,WAAY,CACZ,iBAAkB,CAClB,4CAA6C,CAC7C,2BAA4B,CAC5B,0BAA2B,CAC3B,uBAAwB,CACxB,SAAU,CACV,SAAU,CACV,cAAe,CACf,aVxRO,CICnB,mBMwR4C,CNxR5C,mBMwR4C,CNxR5C,YMwR4C,CNvR5C,wBMuR4D,CNvR5D,qBMuR4D,CNvR5D,kBMuR4D,CNtR5D,uBMsRwE,CNtRxE,oBMsRwE,CNtRxE,sBMsR8E,CACrE,kCAQL,2BAAgB,CAAhB,4BAAgB,CAAhB,yBAAgB,CAAhB,qBAAsB,CAF9B,gDAKY,UAAW,CACX,4BAA6B,CAC7B,aVvSW,CUwSX,SAAU,CACV,cAAe,CACf,eAAgB,CAChB,gBAAiB,CAX7B,sDAcgB,WAAY,CAd5B,+GAmBgB,wBAAiB,CAAjB,gBAAiB,CAClB,WAAY,CACZ,sBAAuB,CArBtC,0DA0BY,wBAAiB,CAAjB,gBAAiB,CACjB,WAAY,CACZ,sBAAuB,CA5BnC,6DAiCgB,UAAW,CACX,4BAA6B,CRhTrC,0BQ8QR,6DAoCoB,eAAgB,CAChB,eAAgB,CAChB,QAAS,CACT,kCAAW,CAAX,0BAA2B,CAUlC,CR/TL,0BQ8QR,6DA0CoB,eAAgB,CAChB,6BAAW,CAAX,0BAAW,CAAX,qBAAsB,CAM7B,CR/TL,0BQ8QR,6DA8CoB,eAAgB,CAChB,6BAAW,CAAX,0BAAW,CAAX,qBAAsB,CAE7B,CAjDb,4DAqDY,aAAc,CArD1B,wDAyDY,aAAc,CAzD1B,4EA4DgB,UAAW,CA5D3B,wEAgEgB,UAAW,CAhE3B,sFAmEoB,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,QAAS,CRpVrB,0BQ8QR,sFAyEwB,QAAS,CA2BhB,CRlXT,0BQ8QR,sFA6EwB,OAAO,CACP,WAAY,CAsBnB,CRlXT,0BQ8QR,sFAkFuB,KAAM,CAkBZ,CApGjB,qGAuFwB,kBAAmB,CACnB,UAAW,CACX,aVhXH,CUuRrB,2GA4F4B,aV5XL,CUgSvB,yNAiG4B,UV1Xb,CEWP,0BQuXR,oBAEQ,eAAgB,CAChB,eAAgB,CAChB,kBAAmB,CAW1B,CRtYO,0BQuXR,oBAQQ,eAAgB,CAChB,6BAAW,CAAX,0BAAW,CAAX,qBAAsB,CAM7B,CRtYO,0BQuXR,oBAYQ,eAAgB,CAChB,6BAAW,CAAX,0BAAW,CAAX,qBAAsB,CAE7B,CRtYO,0BQwYR,mBAEQ,eAAgB,CAChB,eAAgB,CAChB,kBAAmB,CAU1B,CRtZO,0BQwYR,mBAOQ,eAAgB,CAChB,6BAAW,CAAX,0BAAW,CAAX,qBAAsB,CAM7B,CRtZO,0BQwYR,mBAWQ,eAAgB,CAChB,6BAAW,CAAX,0BAAW,CAAX,qBAAsB,CAE7B,CCzaD,MACI,qBXOW,CWNX,kBAAmB,CACnB,eAAgB,CAChB,wBXQgB,CWLhB,QAAS,CAPb,mBASQ,YAAa,CACb,4BAA6B,CAC7B,WAAY,CACZ,OAAQ,CPVZ,mBOWoC,CPXpC,mBOWoC,CPXpC,YOWoC,CPVpC,wBOUoD,CPVpD,qBOUoD,CPVpD,kBOUoD,CPTpD,wBOSgE,CPThE,qBOSgE,CPThE,6BOS6E,CAbjF,oBAiBY,UAAW,CACX,WAAY,CAlBxB,gBAsBQ,sCXdO,CWeP,aXZa,CWab,WAAY,CAxBpB,gBA2BQ,wBXdY,CWeZ,WAAY,CACf,MC5BD,wBZYgB,CYbpB,mBAGQ,+BZSY,CYRZ,YAAa,CAJrB,wBAMY,aZKS,CYJT,cAAe,CACf,cAAe,CAR3B,iBAYQ,YAAa,CAZrB,oBAcY,eAAgB,CAd5B,+BAkBgB,YAAa,CAlB7B,4BAsBY,qBZdG,CYeH,wBZXQ,CYYR,iBAAkB,CAClB,kBAAmB,CAzB/B,uCA4BgB,QAAS,CA5BzB,0CA+BgB,YAAa,CACb,+BZpBI,CYqBJ,OAAQ,CR/BpB,mBQgC4C,CRhC5C,mBQgC4C,CRhC5C,YQgC4C,CR/B5C,wBQ+B4D,CR/B5D,qBQ+B4D,CR/B5D,kBQ+B4D,CR9B5D,wBQ8BwE,CR9BxE,qBQ8BwE,CR9BxE,6BQ8BqF,CVfjF,0BUnBR,0CAoCoB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,sBAAuB,CA2C9B,CAhFb,sDRQI,mBQiCkD,CRjClD,mBQiCkD,CRjClD,YQiCkD,CRhClD,wBQgCkE,CRhClE,qBQgCkE,CRhClE,kBQgCkE,CAClD,OAAQ,CA1C5B,wDA4CwB,QAAS,CA5CjC,4DA+CwB,QAAS,CA/CjC,kEAiD4B,UAAW,CACX,WAAY,CV/BhC,0BUnBR,8DAuD4B,qBAAsB,CACtB,sBAAuB,CAE9B,CA1DrB,uDRQI,mBQqDkD,CRrDlD,mBQqDkD,CRrDlD,YQqDkD,CRpDlD,wBQoDkE,CRpDlE,qBQoDkE,CRpDlE,kBQoDkE,CAClD,QAAS,CV3CrB,0BUnBR,uDAgEwB,OAAQ,CACR,gBAAiB,CAcxB,CA/EjB,8DAoEwB,sBAAuB,CACvB,WAAY,CACZ,SAAU,CVnD1B,0BUnBR,0IAyEgC,UAAW,CACX,WAAY,CACZ,oBAAe,CAClB,CA5E7B,yCAkFgB,YAAa,CAlF7B,2DRQI,mBQ4EkD,CR5ElD,mBQ4EkD,CR5ElD,YQ4EkD,CR3ElD,uBQ2EiE,CR3EjE,oBQ2EiE,CR3EjE,iBQ2EiE,CACjD,QAAS,CVlErB,0BUnBR,2DAuFwB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAmDzB,CA3IjB,yEA2FwB,WAAY,CACZ,YAAa,CVzE7B,0BUnBR,yEA8F4B,WAAY,CACZ,YAAa,CAMpB,CVlFb,0BUnBR,yEAkG4B,WAAY,CACZ,YAAa,CAEpB,CArGrB,kEAwGwB,cAAe,CAxGvC,yEA4GwB,UAAW,CA5GnC,2EA8G4B,QAAS,CA9GrC,+ERQI,mBQyG0D,CRzG1D,mBQyG0D,CRzG1D,YQyG0D,CRxG1D,wBQwGoE,CRxGpE,qBQwGoE,CRxGpE,kBQwG0E,CAjH9E,kFAoHgC,8BZxGZ,CYyGY,cAAe,CACf,cAAe,CAtH/C,8FAyHoC,cAAe,CAzHnD,6FA6HoC,WAAY,CACZ,eAAgB,CA9HpD,+FAmI4B,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,eAAgB,CVjHpC,0BUnBR,+FAsIgC,eAAgB,CAGvB,CAzIzB,2CA6IoB,oBAAe,CACf,wBAAgB,CA9IpC,+BAmJY,iBAAkB,CAClB,yBZxIQ,CYyIR,cAAe,CACf,eAAgB,CAtJ5B,gCA2JY,yBZ1JW,CY2JX,aZ3JW,CY4JX,qCZ5JW,CYDvB,4EA+JgB,qCZ9JO,CY+JP,aZ/JO,CYgKV,aAMT,qBZ/JW,CYgKX,yBZ5JgB,CY6JhB,aZxKmB,CYyKnB,cAAe,CACf,eAAgB,CAChB,oEAAqE,CACrE,gBAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,iBAAkB,CAVtB,uCAYQ,UAAW,CACX,iBAAkB,CAClB,mBAAoB,CACpB,wBAAyB,CACzB,aAAc,CACd,gBAAiB,CACjB,UAAW,CACX,WAAY,CACZ,kBAAmB,CACnB,wBZ9KY,CY+KZ,SAAU,CAtBlB,mBA0BQ,0BZpLY,CYqLZ,yBZrLY,CYsLZ,wBZtLY,CYuLZ,SAAU,CACV,WAAY,CA9BpB,oBAiCQ,0BZ3LY,CY4LZ,yBZ5LY,CY6LZ,2BZ7LY,CY8LZ,SAAU,CACV,QAAS,CACZ,4BAID,oCAA4C,CAC5C,aZhNmB,CYiNnB,qCZjNmB,CYkNnB,iBAAkB,CAJtB,oEAMQ,qCZpNe,CYqNf,aZrNe,CYsNlB,eAGD,qBZlNW,CYmNX,yBZ1NmB,CY2NnB,qCZ3NmB,CY4NnB,aZ5NmB,CY6NnB,cAAe,CACf,eAAgB,CAChB,oEAAqE,CACrE,gBAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,iBAAkB,CAXtB,2CAaQ,UAAW,CACX,iBAAkB,CAClB,mBAAoB,CACpB,yBAA0B,CAC1B,aAAc,CACd,gBAAiB,CACjB,UAAW,CACX,WAAY,CACZ,kBAAmB,CACnB,wBZlOY,CYmOZ,SAAU,CAvBlB,qBA2BQ,0BZnPe,CYoPf,yBZpPe,CYqPf,wBZrPe,CYsPf,SAAU,CACV,WAAY,CA/BpB,sBAkCQ,0BZ1Pe,CY2Pf,yBZ3Pe,CY4Pf,2BZ5Pe,CY6Pf,SAAU,CACV,QAAS,CACZ,MAID,cAAe,CACf,qCZpQmB,CYqQnB,6BAA8B,CAC9B,8BAA+B,CAC/B,YAAa,CALjB,WAOQ,aZ/Pa,CYgQb,cAAe,CACf,gBAAiB,CATzB,aAYQ,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,aZjRe,CYkQvB,WAkBQ,yHAA0H,CAC1H,mEAAoE,CAnB5E,iBAuBgB,+DAAgE,CAChE,gEAAiE,CACpE,OAMT,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,aZpSmB,CYqStB,UAEG,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,aZrSkB,CYsSlB,kBAAmB,CACtB,4FAIO,qBZzSO,CY0SP,wBZtSY,CYuSZ,iBAAkB,CAClB,iEAAkE,CAClE,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CVpST,0BU4RR,4FAUY,OAAQ,CAmBf,CA7BL,wHAaY,YAAa,CAbzB,oGAgBY,QAAS,CACT,cAAe,CAjB3B,wGR7SI,mBQiUwC,CRjUxC,mBQiUwC,CRjUxC,YQiUwC,CRhUxC,wBQgUwD,CRhUxD,qBQgUwD,CRhUxD,kBQgUwD,CR/TxD,wBQ+TiF,CR/TjF,qBQ+TiF,CR/TjF,6BQ+TiF,CACzE,OAAQ,CACR,QAAS,CVlTb,0BU4RR,wGAwBgB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,OAAQ,CAEf,CA5BT,+CA6CQ,4BAA6B,CAC7B,2BAA4B,CA9CpC,mDAgCY,aZpUS,CIHjB,mBQwU0C,CRxU1C,mBQwU0C,CRxU1C,YQwU0C,CRvU1C,wBQuU0D,CRvU1D,qBQuU0D,CRvU1D,kBQuU0D,CAClD,OAAQ,CAlCpB,6DAoCgB,aZxUK,CYyUL,eAAgB,CArChC,yDAyCY,aZ9US,CY+UT,eAAgB,CAChB,cAAe,CA3C3B,6CAiDQ,iBAAkB,CAClB,yBAA0B,CAC1B,wBAAyB,CAnDjC,iDAqDY,aZ1VS,CY2VT,eAAgB,CAtD5B,uDAyDY,aZvWW,CYwWX,eAAgB,CAChB,cAAe,CA3D3B,oHA8DY,UAAW,CACX,iBAAkB,CAClB,mBAAoB,CACpB,wBAAyB,CACzB,aAAc,CACd,gBAAiB,CACjB,UAAW,CACX,WAAY,CACZ,QAAS,CACT,kBAAmB,CACnB,wBZ1WQ,CYkSpB,yDA4EY,yBZ/WQ,CYgXR,wBZhXQ,CYiXR,2BZjXQ,CYkXR,UAAW,CA/EvB,2DAkFY,0BZrXQ,CYsXR,wBZtXQ,CYuXR,2BZvXQ,CYwXR,SAAU,CACb,MAKL,oBAAe,CACf,wBAAgB,CAChB,wBZ/XgB,CYgYhB,kBAAmB,CACnB,WAAY,CACZ,UAAW,CR7YX,mBQ8YgC,CR9YhC,mBQ8YgC,CR9YhC,YQ8YgC,CR7YhC,wBQ6YgD,CR7YhD,qBQ6YgD,CR7YhD,kBQ6YgD,CR5YhD,uBQ4Y4D,CR5Y5D,oBQ4Y4D,CR5Y5D,sBQ4YkE,CACrE,QAGG,oBAAe,CACf,wBAAgB,CAChB,oCZhZkB,CYiZlB,kBAAmB,CACnB,WAAY,CACZ,UAAW,CRvZX,mBQwZgC,CRxZhC,mBQwZgC,CRxZhC,YQwZgC,CRvZhC,wBQuZgD,CRvZhD,qBQuZgD,CRvZhD,kBQuZgD,CRtZhD,uBQsZ4D,CRtZ5D,oBQsZ4D,CRtZ5D,sBQsZkE,CACrE,qBAGG,WAAY,CACZ,sBAAuB,CACvB,SAAU,CAHd,uBAKQ,oBAAe,CACf,wBAAgB,CACnB,aAID,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,YAAa,CACb,aAAc,CACjB,aAGG,wBZpagB,CYqahB,kBAAmB,CAFvB,6BAIQ,iBAAkB,CAClB,OAAQ,CACR,YAAa,CACb,eAAgB,CRpbpB,mBQqboC,CRrbpC,mBQqboC,CRrbpC,YQqboC,CRpbpC,wBQoboD,CRpbpD,qBQoboD,CRpbpD,kBQoboD,CRnbpD,wBQmbgE,CRnbhE,qBQmbgE,CRnbhE,6BQmb6E,CARjF,gCAUY,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAZ9B,+BAgBY,wBAA8B,CAC9B,gBAAiB,CACjB,QAAS,CAlBrB,oCAoBgB,eAAgB,CApBhC,sCAwBY,cAAe,CACf,UAAW,CACX,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAClB,wCZlcS,CYqarB,uEAgCY,UAAW,CACX,iBAAkB,CAClB,mBAAoB,CACpB,wBAAyB,CACzB,aAAc,CACd,gBAAiB,CACjB,UAAW,CACX,WAAY,CACZ,kBAAmB,CACnB,qBZhdG,CYidH,0BZ7cQ,CY8cR,yBZ9cQ,CY+cR,2BZ/cQ,CYgdR,QAAS,CA7CrB,mCAiDY,UAAW,CAjDvB,oCAoDY,SAAU,CApDtB,4BAwDQ,qCZtee,CYuef,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,OAAQ,CACR,YAAa,CACb,8BAA+B,CAC/B,+BAAgC,CAChC,gBAAiB,CA/DzB,8BAkEY,QAAS,CACT,wBAA8B,CAnE1C,mCAqEgB,aZ1eK,CYqarB,sCRvaI,mBQif0C,CRjf1C,mBQif0C,CRjf1C,YQif0C,CRhf1C,wBQgf0D,CRhf1D,qBQgf0D,CRhf1D,kBQgf0D,CAClD,OAAQ,CACR,aZ1fW,CY2fX,eAAgB,CAChB,gBAAiB,CA9E7B,wCAgFgB,oBAAe,CACf,wBAAgB,CAChB,QAKZ,oDAAqD,CACrD,4BAA6B,CAC7B,0BAA2B,CAC3B,uBAAwB,CACxB,WAAY,CACZ,UAAW,CACX,cAAe,CACf,iBAAkB,CAClB,SAAU,CACb,SAGG,wBZrgBgB,CYsgBhB,kBAAmB,CAFvB,yBAIQ,+BZzgBY,CY0gBZ,iEAAkE,CAClE,iBAAkB,CRrhBtB,mBQshBoC,CRthBpC,mBQshBoC,CRthBpC,YQshBoC,CRrhBpC,uBQqhBmD,CRrhBnD,oBQqhBmD,CRrhBnD,iBQqhBmD,CRphBnD,wBQohB+D,CRphB/D,qBQohB+D,CRphB/D,6BQohB4E,CAPhF,8BASY,aZ/gBS,CYghBT,cAAe,CACf,cAAe,CAX3B,oCAcY,yEAAe,CACf,wBAAgB,CAChB,iBAAkB,CAClB,6DAA8D,CAC9D,SAAU,CVhhBd,0BU8fR,yBAsBY,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAvB9B,sCAyBgB,iBAAkB,CACrB,CA1Bb,uBA8BQ,iEAAkE,CAClE,aAAc,CACd,YAAa,CAhCrB,wDAoCgB,qBZ7iBD,CY8iBC,wBZ1iBI,CY2iBJ,kBAAmB,CACnB,iEAAkE,CAvClF,uERzgBI,mBQkjBkD,CRljBlD,mBQkjBkD,CRljBlD,YQkjBkD,CRjjBlD,wBQijBkE,CRjjBlE,qBQijBkE,CRjjBlE,kBQijBkE,CAClD,2DAA4D,CA1ChF,yEA4CwB,aZnjBH,CYugBrB,0EA+CwB,gBAAiB,CACjB,mEAAoE,CAhD5F,uFAmDgC,aZnkBT,CYokBS,yBAA0B,CApD1D,yEAyDwB,QAAS,CACT,mEAAoE,CACpE,aZjkBH,CYkkBG,gBAAiB,CA5DzC,8EA8D4B,eAAgB,CAChB,aZ7kBL,CY8gBvB,oFAoEwB,mEAAoE,CACpE,+DAAgE,CAChE,gEAAiE,CACjE,wBZ3kBJ,CY4kBI,iBAAkB,CRvlBtC,mBQwlBoD,CRxlBpD,mBQwlBoD,CRxlBpD,YQwlBoD,CRvlBpD,wBQulBoE,CRvlBpE,qBQulBoE,CRvlBpE,kBQulBoE,CRtlBpE,uBQslBgF,CRtlBhF,oBQslBgF,CRtlBhF,sBQslBsF,CAzE1F,wFA2E4B,+DAAgE,CAChE,gEAAiE,CACjE,qBAAY,CAAZ,kBAAmB,CA7E/C,4FAgF4B,YAAa,CAhFzC,8FAmF4B,aAAc,CAnF1C,oEA2FoB,OAAQ,CR1mBxB,mBQ2mBgD,CR3mBhD,mBQ2mBgD,CR3mBhD,YQ2mBgD,CR1mBhD,wBQ0mBgE,CR1mBhE,qBQ0mBgE,CR1mBhE,kBQ0mBgE,CRzmBhE,wBQymByF,CRzmBzF,qBQymByF,CRzmBzF,6BQymByF,CACzE,6BAAgB,CAAhB,6BAAgB,CAAhB,8BAAgB,CAAhB,0BAA2B,CA7F/C,4HAkGoC,qCZlnBb,CYghBvB,oIAoGwC,aAAc,CApGtD,sIAuGwC,YAAa,CAvGrD,iHA2GoC,aZ3nBb,CYghBvB,yBAsHQ,YAAa,CACb,4BZ5nBY,CYqgBpB,8BAyHY,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,iBAAkB,CAClB,yHAA0H,CAC1H,mEAAoE,CA5HhF,sBAgIQ,YAAa,CAhIrB,qCAkIY,wBZlpBW,CYmpBX,sDAAuD,CACvD,0BAA2B,CAC3B,2BAA4B,CAC5B,qBAAsB,CACtB,iBAAkB,CAClB,SAAU,CACV,kBAAmB,CACnB,iEAAkE,CAClE,kBAAmB,CA3I/B,uCA6IgB,2BZtpBD,CYupBC,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,gBAAiB,CAjJjC,wCAoJgB,eAAgB,CAChB,UZ9pBD,CY+pBC,gBAAiB,CAtJjC,sCA0JY,aAAc,CACd,YAAa,CA3JzB,oDA6JgB,wBZlqBI,CYmqBJ,YAAa,CACb,kBAAmB,CACnB,qBZzqBD,CINX,mBQgrB4C,CRhrB5C,mBQgrB4C,CRhrB5C,YQgrB4C,CR/qB5C,wBQ+qB4D,CR/qB5D,qBQ+qB4D,CR/qB5D,kBQ+qB4D,CR9qB5D,wBQ8qBqF,CR9qBrF,qBQ8qBqF,CR9qBrF,6BQ8qBqF,CACzE,OAAQ,CACR,qDZ1qBF,CY0qBE,6CZ1qBK,CYugBrB,4DAsKoB,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,gBAAiB,CAzKrC,0DA6KoB,cAAe,CACf,aZprBC,CYqrBD,gBAAiB,CA/KrC,6DAmLoB,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,gBAAiB,CAtLrC,uHA0LoB,cAAe,CACf,eAAgB,CAChB,gBAAiB,CA5LrC,4DAgMoB,aZ9sBG,CY8gBvB,2DAoMoB,aZ/sBE,CEad,0BU8fR,oDAwMoB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAGzB,CA5Mb,mCR/gBI,mBQ8tBwC,CR9tBxC,mBQ8tBwC,CR9tBxC,YQ8tBwC,CR7tBxC,wBQ6tBwD,CR7tBxD,qBQ6tBwD,CR7tBxD,kBQ6tBwD,CR5tBxD,wBQ4tBiF,CR5tBjF,qBQ4tBiF,CR5tBjF,6BQ4tBiF,CACzE,QAAS,CACT,mBAAoB,CACpB,QAAS,CAlNrB,sCAoNgB,aZ3tBK,CY4tBL,eAAgB,CArNhC,8CRzgBI,mBQiuB8C,CRjuB9C,mBQiuB8C,CRjuB9C,YQiuB8C,CRhuB9C,wBQguB8D,CRhuB9D,qBQguB8D,CRhuB9D,kBQguB8D,CAClD,QAAS,CAzNzB,oDA2NoB,kBAAmB,CA3NvC,iEA+NwB,iBAAkB,CAClB,cAAe,CACf,eAAgB,CV/tBhC,0BU8fR,mCAsOgB,QAAS,CACT,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,sBAAiB,CAAjB,mBAAiB,CAAjB,qBAAsB,CAE7B,CC5vBT,6CAGY,wBbSQ,CaRR,iEAAkE,CAClE,iBAAkB,CAClB,wBbOQ,CabpB,0DASgB,eAAgB,CAThC,4DAagB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,OAAQ,CACR,iBAAkB,CAjBlC,8EAoBoB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,2DAA4D,CAC5D,SAAU,CAvB9B,iFA0BwB,eAAgB,CAChB,mEAAoE,CA3B5F,mFA+BwB,abpBH,CaqBG,eAAgB,CAhCxC,oKAqCwB,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,sBAAuB,CACvB,oBAAqB,CA3C7C,iDAkDgB,kBAAmB,CACnB,gEAAiE,CACjE,+DAAgE,CApDhF,qDAwDgB,sEAAuE,CAxDvF,uDA2DoB,mEAAoE,CACpE,gBAAiB,CXzC7B,0BWnBR,qDAgEoB,cAAe,CAEtB,CAlEb,wDAqEgB,KAAM,CACN,mEAAoE,CACpE,UAAW,CACX,SAAU,CACV,sBAAuB,CAzEvC,gEA4EoB,yEAAe,CACf,wBAAgB,CA7EpC,qEAgFwB,YAAa,CAhFrC,8DAqFoB,2EAA4E,CAC5E,0EAA2E,CAC3E,YAAa,CAvFjC,mEA0FwB,aAAc,CA1FtC,0DAgGgB,YAAa,CACb,SAAU,CACV,mBAAoB,CACpB,sBAAuB,CACvB,qEAAsE,CACtE,eAAgB,CACnB,sBAOL,wBbjGY,CakGZ,iEAAkE,CAClE,iBAAkB,CAClB,wBbnGY,Ca8FpB,2BAQY,yBAAO,CAAP,sBAAO,CAAP,iBAAkB,CAErB,4BCnHL,kCAA2B,CAA3B,0BAA2B,CAC3B,iBAAkB,CAClB,SAAU,CACV,qBdGW,CcFX,8CdGW,CcHX,sCdGW,CcFX,SAAU,CACV,iBAAkB,CAClB,wCAAiC,CAAjC,gCAAiC,CACjC,QAAS,CACT,UAAW,CACX,YAAa,CACb,iBAAkB,CAbtB,+BAgBM,aAAc,CACd,cAAe,CACf,2BAAY,CAAZ,mBAAoB,CAlB1B,qCAqBQ,iCAAW,CAAX,yBAA0B,CAS3B,iDANG,kCAAW,CAAX,0BAA2B,CAxBrC,uCA4BU,wBAAyB,CA5BnC,kCAiCQ,cAAe,CAjCvB,kCAwCM,SAAU,CACV,kBAAmB,CACnB,sBAAW,CAAX,cAAe,CA1CrB,0CAgDM,edxCS,CcyCT,advCe,CcwCf,WAAY,CACZ,YAAa,CACb,QAAS,CAET,gEAAiE,CACjE,iBAAkB,CAClB,UAAW,CVtDb,mBUuDkC,CVvDlC,mBUuDkC,CVvDlC,YUuDkC,CVtDlC,wBUsDkD,CVtDlD,qBUsDkD,CVtDlD,kBUsDkD,CVrDlD,wBUqD8D,CVrD9D,qBUqD8D,CVrD9D,6BUqD2E,CAzD/E,4CA4DQ,+BAAe,CA5DvB,+CAgEQ,0BAAoB,CAApB,0BAAoB,CAApB,mBAAoB,CACpB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,OAAQ,CACR,aAAc,CACd,cAAe,CApEvB,mDAuEU,UAAW,CAvErB,+CA6EM,sCAA+B,CAA/B,8BAA+B,CAC/B,iBAAkB,CAClB,SAAU,CACV,qBdxES,CcyET,wCAAiC,CAAjC,gCAAiC,CACjC,QAAS,CACT,OAAQ,CACR,YAAa,CACb,iBAAkB,CAClB,SAAU,CACV,iBAAkB,CAClB,UAAW,CAxFjB,sDA2FQ,SAAU,CACV,kBAAmB,CACnB,sCAAY,CAAZ,8BAA+B,CA7FvC,qEAiGQ,UAAW,CACX,cAAe,CAlGvB,0EVQI,mBU4FwC,CV5FxC,mBU4FwC,CV5FxC,YU4FwC,CV3FxC,wBU2FwD,CV3FxD,qBU2FwD,CV3FxD,kBU2FwD,CAClD,OAAQ,CACR,cAAe,CAtGzB,8EAwGY,UAAW,CACZ,iBVvGP,mBUiH8B,CVjH9B,mBUiH8B,CVjH9B,YUiH8B,CVhH9B,wBUgH8C,CVhH9C,qBUgH8C,CVhH9C,kBUgH8C,CV/G9C,wBU+GuE,CV/GvE,qBU+GuE,CV/GvE,6BU+GuE,CACzE,OAAQ,CACR,wEAAyE,CACzE,uEAAwE,CACxE,+Bd3GkB,CcsGpB,mCAQI,eAAgB,CAChB,UAAW,CACX,YAAa,CAyBd,+CAtBG,SAAU,CACV,WAAY,CAdlB,sCAkBM,aAAc,CACd,cAAe,CACf,2BAAY,CAAZ,mBAAoB,CApB1B,4CAuBQ,wBAAyB,CACzB,iCAAW,CAAX,yBAA0B,CAK3B,wDAFG,kCAAW,CAAX,0BAA2B,CA3BrC,yCAgCQ,cAAe,CAhCvB,6BV1GI,mBUgJkC,CVhJlC,mBUgJkC,CVhJlC,YUgJkC,CV/IlC,wBU+IkD,CV/IlD,qBU+IkD,CV/IlD,kBU+IkD,CAClD,QAAS,CAvCb,mCA0CM,adjJe,CckJf,cAAe,CAChB,kCC5JG,YAAa,CACnB,UAAW,CACX,aAAc,CACd,kBAAmB,CACb,+BfMY,CeLlB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACP,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,QAAS,CbUT,0BanBR,kCAYY,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CAuHnB,CApIF,wCAiBG,iBAAkB,CXTjB,mBWU0C,CXV1C,mBWU0C,CXV1C,YWU0C,CXT1C,wBWS0D,CXT1D,qBWS0D,CXT1D,kBWS0D,CAClD,QAAS,CACT,mBAAoB,CApBhC,yDAuBoB,YAAa,CAvBjC,8CA4BgB,UAAW,CACX,gCf5BO,Ce6BP,UAAW,CACX,UAAW,CACX,iBAAkB,CAClB,OAAQ,CACR,WAAY,CbfpB,0BanBR,8CAoCoB,YAAa,CAEpB,CAtCb,oDAyCI,mEAAoE,CACpE,kBAAmB,CACnB,afhCiB,CeXrB,6DAgDQ,yBf/Ce,CegDH,afhDG,CeDvB,4DAoDoB,afnDG,CeoDH,eAAgB,CArDpC,oDA0DI,cAAe,CbvCX,0BanBR,oDA4DoB,eAAgB,CAEhC,CA9DJ,mDAiEI,eAAgB,Cb9CZ,0BanBR,mDAmEoB,cAAe,CAE/B,CArEJ,qIAyEI,YAAa,CAzEjB,qDA6EI,+DAA+D,CACnD,gEAAgE,CAChE,iBAAkB,CAClB,sBAAuB,CX9EnC,mBW+E4C,CX/E5C,mBW+E4C,CX/E5C,YW+E4C,CX9E5C,wBW8E4D,CX9E5D,qBW8E4D,CX9E5D,kBW8E4D,CX7E5D,uBW6E8E,CX7E9E,oBW6E8E,CX7E9E,sBW6E8E,CAC9E,cAAe,CACf,eAAgB,CAnFpB,0DAwFK,wBfvFkB,CewFH,wBfxFG,CeyFlB,UflFU,CemFV,iBAAkB,CA3FvB,iEA8FwB,UAAW,CACX,iBAAkB,CAClB,6DAA8D,CAC9D,8DAA+D,CAC/D,sDAAuD,CACvD,2BAA4B,CAC5B,0BAA2B,CAC3B,uBAAwB,CACxB,SAAU,CACV,UAAW,CACX,WAAY,CAxGpC,4DA4GM,YAAa,CA5GnB,yDAiHK,afhHkB,CeDvB,kEAqHwB,yBfpHD,CeqHC,afrHD,CeDvB,iEAyHwB,afxHD,CeyHC,eAAgB,CA1HxC,2GAgII,iBAAkB,CAClB,iBAOA,iEAAkE,CAClE,UAAW,CAFf,kCAKY,mBAAoB,CACpB,iBAAkB,CAN9B,yCAQgB,iBAAkB,CACtB,UAAW,CACX,yBftIS,CeuIT,WAAY,CACZ,QAAS,CACT,QAAS,CACT,WAAY,CAdxB,6CAiBgB,SAAU,CAjB1B,oDAmBoB,YAAa,CAnBjC,oBAyBQ,mEAAoE,CACpE,eAAgB,CAChB,gBAAiB,CACjB,afzJa,Ce0Jb,kBAAmB,CA7B3B,oCAgCQ,gCAAmC,CACnC,mCAAwC,CACxC,kBAAmB,CACnB,eAAgB,CAnCxB,qDAsCY,iEAAkE,CX3K1E,mBW4KwC,CX5KxC,mBW4KwC,CX5KxC,YW4KwC,CX3KxC,wBW2KwD,CX3KxD,qBW2KwD,CX3KxD,kBW2KwD,CX1KxD,wBW0KiF,CX1KjF,qBW0KiF,CX1KjF,6BW0KiF,CACzE,OAAQ,CACR,+BfpKQ,CEOZ,0BaoHR,qDA4CgB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAuDzB,CApGT,oEAiDgB,kBAAmB,CACnB,WAAY,CACZ,cAAe,CACf,UAAW,CACX,wBfhLI,CIVhB,mBW2L4C,CX3L5C,mBW2L4C,CX3L5C,YW2L4C,CX1L5C,wBW0L4D,CX1L5D,qBW0L4D,CX1L5D,kBW0L4D,CXzL5D,uBWyLwE,CXzLxE,oBWyLwE,CXzLxE,sBWyL8E,Cb1K1E,0BaoHR,oEAwDoB,WAAY,CACZ,cAAe,CACf,UAAW,CAQlB,CAlEb,wEA6DoB,WAAY,CbjLxB,0BaoHR,wEA+DwB,WAAY,CAEnB,CAjEjB,kEAoEgB,wBf1MO,Ce2MP,kBAAmB,CACnB,WAAY,CACZ,cAAe,CACf,UAAW,CX7MvB,mBW8M4C,CX9M5C,mBW8M4C,CX9M5C,YW8M4C,CX7M5C,wBW6M4D,CX7M5D,qBW6M4D,CX7M5D,kBW6M4D,CX5M5D,uBW4MwE,CX5MxE,oBW4MwE,CX5MxE,sBW4M8E,CAzElF,sEA2EoB,WAAY,CACZ,wBflNG,CemNH,qBf5ML,Ce6MK,YAAa,CACb,kBAAmB,CACnB,WAAY,CACZ,cAAe,CACf,UAAW,CXvN3B,mBWwNgD,CXxNhD,mBWwNgD,CXxNhD,YWwNgD,CXvNhD,wBWuNgE,CXvNhE,qBWuNgE,CXvNhE,kBWuNgE,CXtNhE,uBWsN4E,CXtN5E,oBWsN4E,CXtN5E,sBWsNkF,CAnFtF,8DAwFgB,eAAgB,CAChB,mEAAoE,CAzFpF,gEA4FgB,mEAAoE,CbhN5E,0BaoHR,4DAiGoB,gBAAiB,CAExB,CAnGb,6CAsGY,iEAAkE,CAtG9E,+CAwGgB,cAAe,CAxG/B,0DA2GgB,gBAAiB,CACjB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,gBAAkB,CA7GlC,4CX/HI,mBWgP0C,CXhP1C,mBWgP0C,CXhP1C,YWgP0C,CX/O1C,wBW+O0D,CX/O1D,qBW+O0D,CX/O1D,kBW+O0D,CAClD,QAAS,CACT,iEAAkE,CAClE,aAAc,CACd,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,iBAAkB,CAtH9B,iDAwHgB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,mEAAoE,CACpE,iBAAkB,CAClB,mEAAoE,CA3HpF,mDA8HgB,iBAAkB,CAClB,OAAQ,CACR,8DAA+D,CAC/D,kEAAmE,CACnE,gEAAiE,CACjE,SAAU,CACV,cAAe,CApI/B,4DAyIgB,wBf/QO,CegRP,UfzQD,Ce+Hf,+EAkJgB,wBfxRO,CeyRP,UflRD,Ce+Hf,+BAyJQ,af/Re,CegSf,4BAA6B,CAC7B,WAAY,CACZ,cAAe,CACf,eAAgB,CAChB,cAAe,CA9JvB,qCAgKY,yBAA0B,CAhKtC,+BAoKQ,eAAgB,CAChB,UAAW,CACX,qEAAsE,CAtK9E,8CAyKY,6BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CACnB,4BAA6B,CAC7B,WAAY,CACZ,qBf3SG,Ce4SH,kBAAoB,CACpB,2DAA4D,CAC5D,kBAAW,CAAX,cAAe,CA/K3B,0DAiLgB,WAAY,CACZ,SAAU,CACV,6BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CACnB,iBAAkB,CApLlC,6CAwLY,cAAe,CAxL3B,qDA2LY,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,eAAgB,CAChB,mCAAwC,CACxC,sBAAuB,CACvB,kCAAY,CAAZ,0BAA2B,CbnT/B,0BaoHR,qDAiMgB,eAAgB,CAChB,UAAW,CAElB,CApMT,+BAuMQ,qBftUO,CeuUP,kBAAmB,CACnB,YAAa,CACb,afvUa,Ce6HrB,0EA4MY,afzUS,Ce0UT,qBf5UG,Ce6UN,cAKL,aAAc,CACd,SAAU,CACV,UAAW,CACX,wBflViB,CemVjB,kBAAmB,CACnB,yBfpViB,CeqVjB,iBAAkB,CAClB,SAAU,CARd,oBAWQ,UAAW,CACX,iBAAkB,CAClB,WAAY,CACZ,UAAW,CACX,wBf7Va,Ce8Vb,kBAAmB,CACnB,QAAS,CACT,SAAU,CACb,eXpWD,mBWwWkC,CXxWlC,mBWwWkC,CXxWlC,YWwWkC,CXvWlC,wBWuWkD,CXvWlD,qBWuWkD,CXvWlD,kBWuWkD,CAClD,QAAS,CACT,6BftWgB,CeuWhB,2HAA4H,CAJhI,4BAMQ,gBAAiB,CANzB,sBASQ,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,yHAA0H,CAC1H,mEAAoE,CACvE,sBAID,qBfvXW,CewXX,kBAAmB,CACnB,wBfrXgB,CesXhB,iEAAkE,CACrE,kBX3XG,mBW6XkC,CX7XlC,mBW6XkC,CX7XlC,YW6XkC,CX5XlC,wBW4XkD,CX5XlD,qBW4XkD,CX5XlD,kBW4XkD,CAClD,QAAS,CbnXL,0BaiXR,kBAIQ,kBAAW,CAAX,cAAe,CAKtB,CATD,wBAOQ,wBAA8B,CACjC,YAID,iBAAkB,CAClB,2DAA4D,CAC5D,SAAU,CACV,yEAAe,CACf,wBAAgB,CAChB,SAAU,CACb,aAGG,sBAAuB,CACvB,WAAY,CACZ,SAAU,CACV,af3ZmB,Ce4ZnB,yBAA0B,CAC1B,cAAe,CAClB,cAGG,qBf1ZW,Ce2ZX,iEAAkE,CAClE,kBAAmB,CX5ZnB,mBW6ZkC,CX7ZlC,mBW6ZkC,CX7ZlC,YW6ZkC,CX5ZlC,wBW4ZkD,CX5ZlD,qBW4ZkD,CX5ZlD,kBW4ZkD,CAClD,kBAAe,CAAf,cAAe,CACf,QAAS,CACZ,cCvaG,sChBOW,CgBNX,sChBMW,CgBLX,mEAAoE,CACpE,UhBIW,CgBHX,oEAAqE,CACrE,UAAW,CACX,iBAAkB,CAPtB,0CAUQ,ahBEY,CgBZpB,iCAUQ,ahBEY,CgBZpB,qCAUQ,ahBEY,CgBZpB,sCAUQ,ahBEY,CgBZpB,4BAUQ,ahBEY,CgBZpB,0BAcQ,2BAA4B,CAC5B,8BAA+B,CAfvC,yBAmBQ,4BAA6B,CAC7B,+BAAgC,CApBxC,wCAyBQ,sChBjBO,CgBkBP,ahBhBa,CgBiBb,uBAAgB,CAAhB,eAAgB,CAChB,sChBpBO,CgBRf,gCAgCQ,wBhBnBY,CgBoBZ,ahBvBa,CgBVrB,4EAqCY,wBhBxBQ,CgByBR,ahB5BS,CgB6BT,uBAAY,CAAZ,eAAgB,CAvC5B,4DA2CY,ahB/BQ,CgBZpB,mDA2CY,ahB/BQ,CgBZpB,uDA2CY,ahB/BQ,CgBZpB,wDA2CY,ahB/BQ,CgBZpB,8CA2CY,ahB/BQ,CgBZpB,iCAgDQ,qBhBxCO,CgByCP,ahBvCa,CgBwCb,iBAAkB,CAlD1B,8EAuDY,qBhB/CG,CgBgDH,ahB9CS,CgB+CT,uBAAY,CAAZ,eAAgB,CAzD5B,6DA6DY,ahBjDQ,CgBZpB,oDA6DY,ahBjDQ,CgBZpB,wDA6DY,ahBjDQ,CgBZpB,yDA6DY,ahBjDQ,CgBZpB,+CA6DY,ahBjDQ,CgBkDX,sBAMD,oEAAqE,CAExE,uCAMO,gBAAiB,CACpB,YAKL,kBAAmB,CADvB,uBAIQ,eAAgB,CAJxB,kBAQQ,mEAAoE,CACpE,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAClB,iBAAkB,CACrB,YAID,SAAU,CACV,gBAAiB,CZ3FjB,mBY4FkC,CZ5FlC,mBY4FkC,CZ5FlC,YY4FkC,CZ3FlC,wBY2FkD,CZ3FlD,qBY2FkD,CZ3FlD,kBY2FkD,CAClD,QAAS,CAJb,4DAQQ,cAAe,CACf,iBAAkB,CAClB,WAAY,CACZ,UAAW,CACX,WAAY,CACZ,uBAAgB,CAAhB,eAAgB,CAChB,4BAA6B,CZvGjC,mBYwGsC,CZxGtC,mBYwGsC,CZxGtC,YYwGsC,CZvGtC,wBYuGsD,CZvGtD,qBYuGsD,CZvGtD,kBYuGsD,CAClD,YAAa,CAhBrB,wEAmBY,uBAAY,CAAZ,eAAgB,CAnB5B,0EAuBY,4EAA6D,CAA7D,oEAA6D,CAA7D,4DAA6D,CAA7D,uHAA6D,CAC7D,4CAAqC,CAArC,oCAAqC,CACrC,UAAW,CACX,iBAAkB,CAClB,QAAS,CACT,OAAQ,CACR,SAAU,CACV,aAAc,CACd,aAAc,CACd,qBhBzHG,CgB0HH,qBAAsB,CACtB,uBAAwB,CAlCpC,wEAsCY,UAAW,CACX,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,UAAW,CACX,WAAY,CACZ,ehBrIG,CgBsIH,wBhBlIQ,CgBmIR,cAAe,CACf,iBAAkB,CA/C9B,0FAoDgB,4CAA0B,CAA1B,oCAAqC,CApDrD,wFAwDgB,wBhBxJO,CgByJP,oBhBzJO,CgBgGvB,8BA+DQ,QAAS,CACT,SAAU,CACV,WAAY,CAjEpB,oCAoEY,kBAAmB,CACnB,MAAO,CACP,QAAS,CAtErB,qCA0EY,WAAY,CACZ,wBhB3KW,CgB4KX,kBAAmB,CACnB,UAAW,CACX,WAAY,CACZ,OAAQ,CACR,QAAS,CAhFrB,sCAqFY,ahB3KS,CgBsFrB,4CA0FgB,sChB1LO,CgB2LP,qBhBpLD,CgByFf,8CA+FgB,eAAgB,CAChB,ahBvLK,CgBuFrB,kDAsGgB,wBhBtMO,CgBgGvB,cA4GQ,cAAe,CA5GvB,kBAgHQ,cAAe,CAClB,aAID,wBhBzMgB,CgB0MhB,WAAY,CACZ,ahB9MiB,CgB+MjB,cAAe,CACf,yBAA0B,CAC1B,iBAAkB,CANtB,mBASQ,uBAAY,CAAZ,eAAgB,CATxB,+BAaQ,qBhB1NO,CgB2NP,ahBxNa,CgByNb,iBAAkB,CAClB,iBAAkB,CAhB1B,0EAoBY,qBhBjOG,CgBkOH,ahB/NS,CgBgOT,uBAAY,CAAZ,eAAgB,CACnB,2BAMD,iIAAkI,CAF1I,yBAMQ,QAAS,CANjB,sCASY,iBAAkB,CACrB,aAML,eAAgB,CAChB,SAAU,CACV,kBAAmB,CACnB,YAAa,CACb,6BAA8B,CAC9B,sChB7PW,CgBuPf,2BASQ,kBAAmB,CACnB,UhBjQO,CgBuPf,yCAcQ,ahBlQa,CgBoPrB,gCAcQ,ahBlQa,CgBoPrB,oCAcQ,ahBlQa,CgBoPrB,qCAcQ,ahBlQa,CgBoPrB,2BAcQ,ahBlQa,CgBoPrB,eAkBQ,yEAAe,CACf,wBAAgB,CAChB,iBAAkB,CAClB,UAAW,CACX,OAAQ,CACR,kCAAW,CAAX,0BAA2B,CdnQ3B,2Bc4OR,aA2BQ,SAAU,CAYjB,CdnRO,0Bc4OR,aA+BQ,SAAU,CACV,WAAY,CAOnB,CdnRO,0Bc4OR,aAoCQ,SAAU,CACV,eAAgB,CAEvB,CAED,cACI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CAJvB,sBAOQ,UAAW,CACX,WAAY,CARpB,0BAWY,kBAAmB,CACnB,UAAW,CACX,WAAY,CAbxB,kCAiBY,kBAAmB,CAjB/B,mCAqBY,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,SAAU,CAxBtB,iCA4BY,iBAAkB,CACrB,cCpUL,iBAAkB,CAClB,gDAAiD,CACjD,2BAA4B,CAC5B,0BAA2B,CAC3B,qBAAsB,CACtB,SAAU,CACV,qEAAsE,CAP1E,6BAWY,iBAAkB,CAClB,6CAAsC,CAAtC,qCAAsC,CACtC,UAAW,CAbvB,qCAgBgB,SAAU,CACV,WAAY,CACZ,0BAAmB,CAAnB,kBAAmB,CACnB,WAAY,CACZ,eAAgB,CfDxB,0BenBR,qCAsBoB,YAAa,CAEpB,CAxBb,qCA2BgB,gEAAiE,CACjE,SAAU,CACV,0BAAmB,CAAnB,kBAAmB,CACnB,YAAa,CfXrB,0BenBR,qCAgCmB,YAAa,CAEnB,CAlCb,qCAqCgB,0BAAmB,CAAnB,kBAAmB,CACnB,YAAa,CACb,kBAAmB,CACnB,YAAa,CACb,8BAAuB,CAAvB,sBAAuB,CACvB,2CAAoC,CAApC,mCAAoC,CACpC,uBAAgB,CAAhB,eAAgB,CAChB,gEAAiE,CACjE,gEAAiE,Cf1BzE,0BenBR,qCA+CoB,YAAa,CAEpB,CAjDb,2BAsDQ,uEAAwE,CfnCxE,0BenBR,2BAyDY,uEAAwE,CAwE/E,Cf9GG,0BenBR,2BA6DY,uEAAwE,CAoE/E,CAjIL,2DAkEiB,YAAa,CACb,yCAA6B,CAA7B,iCAAmC,CfhD5C,0BenBR,2DAqEoB,YAAa,CAyDpB,Cf3GL,0BenBR,2DAwEoB,YAAa,CAsDpB,CA9Hb,yEA2EoB,iBAAkB,CAClB,gBAAiB,CA5ErC,uFA+EwB,WAAY,CA/EpC,gGAiF4B,WAAY,CAjFxC,yGAmFgC,4BAA6B,CAC7B,2BAA4B,CAC5B,UAAW,CACX,WAAY,CACZ,yCAA0C,CAvF1E,wGA4F4B,sJAA6I,CAA7I,+GAA6I,CAC7I,iBAAkB,CAClB,QAAS,CACT,MAAO,CACP,OAAQ,CACR,aAAc,CACd,iBAAkB,CAClB,2BjB3Fb,CiB4Fa,mBAAoB,CACpB,cAAe,CArG3C,6GAwGgC,iBAAkB,CAClB,iBAAkB,CAzGlD,oHA4GoC,UAAW,CACX,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,sCjBxGrB,CiByGqB,kBAAmB,CACnB,OAAQ,CACR,UAAW,CACX,kCAAW,CAAX,0BAA2B,CApH/D,yFA2HwB,sEAAuE,CA3H/F,4BAoIQ,iBAAkB,CAClB,8DAA+D,CAC/D,iBAAkB,CAClB,QAAS,CACT,OAAQ,CACR,SAAU,CACV,iBAAkB,CAClB,aAAc,CfvId,0BeJR,4BA8IY,UAAW,CAgJlB,Cf3QG,0BenBR,4BAiJY,QAAS,CA6IhB,CA9RL,+BAqJY,UjB7IG,CiB8IH,aAAc,CfnIlB,0BenBR,+BAwJgB,SAAU,CACV,kBAAmB,CAQ1B,Cf9ID,0BenBR,+BA4JgB,SAAU,CAKjB,Cf9ID,0BenBR,+BA+JgB,UAAW,CAElB,CAjKT,8BAoKY,ajBzJS,CiB0JT,cAAe,CACf,gBAAiB,CACjB,SAAU,CACV,gBAAiB,CACjB,aAAc,CftJlB,2BenBR,8BA4KgB,SAAU,CAYjB,CfrKD,2BenBR,8BA+KgB,cAAe,CAStB,CfrKD,2BenBR,8BAkLgB,SAAU,CACV,gBAAiB,CAKxB,CfrKD,0BenBR,8BAsLgB,YAAa,CAEpB,CAxLT,6CA2LY,gBAAiB,CAEjB,WAAY,CACZ,aAAc,Cf1LlB,0BeJR,6CAiMgB,qEAAsE,CAyC7E,CfvND,0BenBR,6CAqMe,YAAa,CAqCnB,CA1OT,0DAyMgB,YAAa,CACb,6BAA8B,CAC9B,sCjBnMD,CiBoMC,eAAgB,CbpM5B,mBaqM8C,CbrM9C,mBaqM8C,CbrM9C,YaqM8C,CbpM9C,wBaoM8D,CbpM9D,qBaoM8D,CbpM9D,kBaoM8D,CAClD,QAAS,CACT,oBAAW,CAAX,gBAAiB,CA/MjC,+EAmNwB,oBAAe,CACf,wBAAgB,CAChB,iBAAkB,CAClB,OAAQ,CACR,kCAA2B,CAA3B,0BAA2B,CAC3B,SAAU,CAxNlC,2FA4NwB,2BAA4B,CAC5B,UjBrNT,CiBRf,yGAiOwB,ajBtNH,CiBXrB,gGAiOwB,ajBtNH,CiBXrB,oGAiOwB,ajBtNH,CiBXrB,qGAiOwB,ajBtNH,CiBXrB,2FAiOwB,ajBtNH,CiBXrB,+DAqOoB,4BAA6B,CAC7B,+BAAgC,CAtOpD,4CA6OY,iBAAkB,CAClB,aAAc,CACd,gBAAiB,CACjB,wBAAyB,CACzB,mEAAoE,CACpE,kBAAmB,CACnB,eAAgB,CAChB,gKACkH,CADlH,wFACkH,CAClH,4BAA6B,CAC7B,oBAAqB,CACrB,mCAAoC,CACpC,2BAA4B,CAC5B,yBAA0B,CAC1B,6DAAW,CAAX,qDAAsD,CA3PlE,mDA8PgB,kBAAmB,CACnB,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,UAAW,CACX,kEAAmE,CACnE,iEAAkE,CfjP1E,0BenBR,mDAiRoB,kEAAmE,CACnE,gEAAiE,CACjE,SAAU,CACV,UAAW,CAGlB,CfpQL,0BenBR,4CA0Re,eAAgB,CAChB,aAAc,CAEpB,CA7RT,qCAkSY,UAAW,CACX,8DAA+D,CAnS3E,wCAsSgB,mEAAoE,CACpE,eAAgB,CAChB,gKACkH,CADlH,wFACkH,CAClH,4BAA6B,CAC7B,oBAAqB,CACrB,mCAAoC,CACpC,2BAA4B,CAC5B,yBAA0B,CAC1B,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,aAAc,CACd,iBAAkB,CAjTlC,6FAmToB,iBAAkB,CAClB,UAAW,CACX,wBjBpTG,CiBsTH,gEAAiE,CACjE,SAAU,CACV,iBAAkB,CAClB,OAAQ,CfvSpB,2BenBR,6FA4TwB,OAAQ,CAEf,CA9TjB,+CAiUoB,qEAAsE,CAjU1F,8CAqUoB,sEAAuE,CArU3F,uGA0UwB,iBAAkB,CAClB,UAAW,CACX,wBjB3UD,CiB6UC,gEAAiE,CACjE,SAAU,CACV,iBAAkB,CAClB,QAAS,Cf9TzB,2BenBR,uGAmV4B,QAAS,CAEhB,CArVrB,oDAwVwB,qEAAsE,CAxV9F,mDA6VwB,sEAAuE,CA7V/F,uCAmWgB,cAAe,CACf,gBAAiB,CACjB,eAAgB,CAChB,SAAU,CACV,qEAAsE,CfpV9E,0BenBR,uCAyWoB,YAAa,CAEpB,CA3Wb,wCA6WgB,UjBrWD,CiBsWC,eAAgB,CAChB,cAAe,Cf5VvB,0BenBR,wCAkXoB,YAAa,CAEpB,CfjWL,2BenBR,uCAyXoB,iFAAkF,CAUzF,CfhXL,0BenBR,uCA6XoB,+EAAgF,CAMvF,Cf/XL,2BeJR,uCAiYoB,iFAAkF,CAEzF,CCnYb,QACI,wBAAyB,CAC5B,OAGG,gBAAiB,CACjB,cAAe,CAClB,aAGG,eAAgB,CAChB,WAAY,CACZ,YAAa,CACb,qBAAsB,CACtB,4CAAuC,CAAvC,oCAAuC,CACvC,cAAe,CACf,gBAAiB,CACjB,uEAAwE,CACxE,UAAW,CACd,mBAGG,UAAW,CACX,mBAAoB,CACpB,eAAgB,CAChB,4BAA6B,CAChC,sBAGG,WAAY,CACZ,qBAAsB,CACzB,sCAGG,gBAAiB,CACpB,mCAGG,mBAAoB,CACvB,yCAGG,cAAe,CACf,gBAAiB,CACjB,UAAW,CACd,2CAGG,mBAAoB,CACvB,iCAGG,eAAgB,CAChB,4BAA6B,CAC7B,gBAAiB,CACjB,cAAe,CAClB,iCAGG,mBAAoB,CACvB,8BAGG,4BAA6B,CAChC,mCAGG,kBAAmB,CACtB,4CAGG,yBAA0B,CAC1B,gBAAiB,CACpB,0BAGG,mCACI,UAAW,CACX,aAAc,CACd,iBAAkB,CACrB,2CAGG,UAAW,CACX,aAAc,CACd,iBAAkB,CACrB,CCtFL,QACI,iBAAkB,CAClB,gBAAiB,CACjB,UAAW,CACX,YAAa,CACb,OAAQ,CACR,QAAS,CACT,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,YAAa,CACd,WAGC,YAAa,CACb,eAAgB,CAChB,cAAe,CACf,UAAW,CACX,UAAW,CACZ,kCAGC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,UAAW,CACX,WAAY,CACZ,QAAS,CACV,KAGG,0CAA2C,CAC3C,0BAA2B,CAC3B,qBAAsB,CACtB,2BAA4B,CAC5B,+DAAgE,CAChE,gEAAiE,CACjE,kDAAW,CAAX,0CAA2C,CAC9C,gBCrCC,SAAU,CACb,OAGG,SAAU,CADd,oCAIY,kBAAmB,CAJ/B,kDAOgB,iEAAkE,CAClE,WAAY,CACZ,kBAAmB,CACnB,QAAS,CAVzB,6DAaoB,WAAY,CACZ,oBAAqB,CAdzC,mEAgBwB,WAAY,CACZ,uBAAY,CAAZ,eAAgB,CAjBxC,gDAuBgB,mEAAoE,CAvBpF,8DAyBoB,aAAc,CACd,YAAa,CA1BjC,sDA+BoB,QAAS,CA/B7B,oEAmCoB,wBpB1BA,CoB2BA,kBAAmB,CACnB,UAAW,CACX,mEAAoE,ChBxCpF,mBgByCgD,ChBzChD,mBgByCgD,ChBzChD,YgByCgD,ChBxChD,wBgBwCgE,ChBxChE,qBgBwCgE,ChBxChE,kBgBwCgE,ChBvChE,uBgBuCkF,ChBvClF,oBgBuCkF,ChBvClF,sBgBuCkF,CAClE,kBAAmB,CACnB,cAAe,CAzCnC,wEA4CwB,WAAY,CA5CpC,gEAiDoB,wBpBxCA,CoByCA,kBAAmB,CACnB,UAAW,CACX,YAAa,CApDjC,sEAsDwB,cAAe,CACf,eAAgB,CAChB,eAAgB,CAChB,apBnDH,CoBoDG,iBAAkB,CA1D1C,kDA+DoB,cAAe,CACf,apB1DC,CoBNrB,+DAqEwB,UAAW,CACX,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CAvE3C,kDA4EgB,WAAY,CACZ,iEAAkE,CAClE,oBAAiB,CAAjB,gBAAiB,CACjB,QAAS,CA/EzB,uDAiFoB,QAAS,CACT,oEAAqE,CAlFzF,+DAqFoB,UAAW,CACX,eAAgB,CAChB,QAAS,CAvF7B,4DAoGoB,iBAAkB,CApGtC,uEAgHwB,YAAa,CACb,iBAAkB,CAClB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,kBAAmB,CAnH3C,kFAsH4B,eAAgB,CAtH5C,mFhBII,mBgBsH0D,ChBtH1D,mBgBsH0D,ChBtH1D,YgBsH0D,ChBrH1D,wBgBqH0E,ChBrH1E,qBgBqH0E,ChBrH1E,kBgBqH0E,CAClD,QAAS,CA3HrC,sGA8HgC,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,ChB1H9C,mBgB2H8D,ChB3H9D,mBgB2H8D,ChB3H9D,YgB2H8D,ChB1H9D,wBgB0H8E,ChB1H9E,qBgB0H8E,ChB1H9E,kBgB0H8E,CAClD,QAAS,CACT,wBpBxHZ,CoByHY,kBAAmB,CACnB,iBAAkB,CAnIlD,yGAsIoC,apB/Hf,CoBPrB,wGA0IoC,QAAS,CACT,cAAe,CACf,eAAgB,CA5IpD,kHAiJwC,qBAAsB,CACtB,sBAAuB,CAlJ/D,oFAyJ4B,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,iBAAkB,CAClB,cAAe,CACf,cAAe,CACf,gBAAiB,CA7J7C,6EA2K4B,apBrKP,CoBsKO,iBAAkB,CAClB,eAAgB,CA7K5C,gFhBII,mBgB6K0D,ChB7K1D,mBgB6K0D,ChB7K1D,YgB6K0D,ChB5K1D,wBgB4K0E,ChB5K1E,qBgB4K0E,ChB5K1E,kBgB4K0E,CAClD,QAAS,CACT,wBpB1KR,CoB2KQ,YAAa,CACb,kBAAmB,CArL/C,oFAwLgC,UAAW,CACX,WAAY,CAzL5C,qFA6LgC,cAAe,CACf,eAAgB,CAChB,apBlMT,CoBGvB,iDA8MgB,wEAAyE,CA9MzF,oCAkNY,kBAAmB,CACnB,wBpB1MQ,CoB2MR,SAAU,CACV,QAAS,CACT,KAAM,CACN,eAAgB,CAvN5B,yCAyNgB,WAAY,CACZ,eAAgB,CA1NhC,gDA4NoB,WAAY,CA5NhC,0CAiOgB,apB3NK,CoB4NL,cAAe,CAlO/B,qDAsOgB,YAAa,CAtO7B,8ChBII,mBgBsO8C,ChBtO9C,mBgBsO8C,ChBtO9C,YgBsO8C,ChBrO9C,wBgBqO8D,ChBrO9D,qBgBqO8D,ChBrO9D,kBgBqO8D,CAClD,kBAAe,CAAf,cAAe,CACf,6DAA8D,ClB7NtE,0BkBfR,0DAgPwB,OAAQ,CAcf,CA9PjB,gEAmPwB,apB5OH,CoB6OG,cAAe,CApPvC,4FA0PgC,apBpPX,CoBNrB,sCAiQgB,yEAAe,CACf,wBAAgB,CAlQhC,gDAqQgB,iBAAkB,CAClB,2DAA4D,CAC5D,SAAU,CAvQ1B,qCA+QY,kBAAmB,CA/Q/B,2CAiRgB,6BAA8B,CAjR9C,8CAqRY,WAAY,CACZ,QAAS,CACT,mBAAoB,ClBxQxB,0BkBfR,8CAyRgB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,QAAS,CA3RzB,0DA6RoB,UAAW,CACd,CA9RjB,0EAwSoB,iBAAkB,CAxStC,uFA0SwB,iBAAkB,CA1S1C,4EA6SwB,oBAAe,CACf,wBAAgB,CA9SxC,iFAiTwB,SAAU,CACV,WAAY,CACZ,gBAAiB,CACjB,iBAAkB,CAClB,OAAQ,CACR,kCAAmC,CACnC,0BAA2B,CAC3B,SAAU,CAxTlC,0FA6TwB,SAAU,CACV,WAAY,CA9TpC,4FAgU4B,gBAAiB,CAhU7C,0EAoUwB,oBAAe,CACf,wBAAgB,CArUxC,oFAwUwB,iBAAkB,CAClB,wDAAyD,CACzD,0BAA2B,CAC3B,qBAAsB,CACtB,2BAA4B,CAC5B,SAAU,CACV,YAAa,CA9UrC,iFAiVwB,SAAU,CACV,iBAAkB,CAClB,gBAAiB,CAnVzC,sFAqV4B,qBpBjVb,CoBkVa,WAAY,CACZ,YAAa,CAvVzC,uFA0V4B,iBAAkB,CAClB,QAAS,CACT,gBAAiB,CA5V7C,8FA8VgC,iBAAkB,CAClB,UAAW,CACX,OAAQ,CACR,MAAO,CACP,wBAAyB,CACzB,SAAU,CACV,WAAY,CApW5C,6FAuWgC,UAAW,CACX,WAAY,CAxW5C,sFA8WwB,wBpBrWJ,CoBsWI,kBAAmB,CACnB,sBAAe,CAAf,mBAAe,CAAf,cAAe,CACf,iBAAkB,CAjX1C,4FhBII,mBgB+W0D,ChB/W1D,mBgB+W0D,ChB/W1D,YgB+W0D,ChB9W1D,wBgB8W0E,ChB9W1E,qBgB8W0E,ChB9W1E,kBgB8W0E,CAClD,OAAQ,CACR,aAAc,CArX1C,uFA0X4B,cAAe,CACf,eAAgB,CA3X5C,0FA8X4B,cAAe,CACf,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,iBAAkB,CAClB,gBAAiB,CAjY7C,8EAqYwB,eAAgB,CAChB,apB/XH,CoBgYG,cAAe,CAvYvC,gFhBII,mBgBsYsD,ChBtYtD,mBgBsYsD,ChBtYtD,YgBsYsD,ChBrYtD,wBgBqYsE,ChBrYtE,qBgBqYsE,ChBrYtE,kBgBqYsE,CAClD,QAAS,CACT,iBAAkB,CAClB,eAAgB,CAChB,iBAAkB,CA9Y1C,mFAgZ4B,iBAAkB,CAhZ9C,qEA4ZoB,mFAAoF,CA5ZxG,qLA+Z4B,wBpBtZR,CoBTpB,iMAmagC,qBpB/ZjB,CoBJf,+CA+agB,mFAAoF,CA/apG,mEAubgB,wBAA8B,CAC9B,gBAAiB,CACjB,iBAAkB,CAClB,gEAAiE,CA1bjF,wEAocwB,wBpB3bJ,CoB4bI,YAAa,CACb,kBAAmB,CAtc3C,8EAwc4B,apBlcP,CoBNrB,2FA4cgC,iBAAkB,CAClB,WAAY,CA7c5C,+MA6doB,WAAY,CA7dhC,mPAgeoB,iBAAkB,CAClB,QAAS,CACT,SAAU,ClBndtB,0BkBfR,mPAoewB,QAAS,CACT,SAAU,CAEjB,CAvejB,uNAyeoB,iBAAkB,ClB1d9B,0BkBfR,uNA2ewB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,sBAAiB,CAAjB,mBAAiB,CAAjB,qBAAsB,CAe7B,CA5fjB,mOAgfwB,mEAAoE,CACpE,apB3eH,CoBNrB,2PAmf4B,qBAAsB,CAnflD,uQAufwB,cAAe,CAvfvC,+RAyf4B,qBAAsB,CAzflD,2OA8foB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,kBAAmB,CA/fvC,uQhBII,mBgB6fsD,ChB7ftD,mBgB6fsD,ChB7ftD,YgB6fsD,ChB5ftD,wBgB4fsE,ChB5ftE,qBgB4fsE,ChB5ftE,kBgB4fsE,CAClD,OAAQ,CACR,iBAAkB,CAClB,WAAY,CApgBpC,+QAsgB4B,oBAAe,CACf,wBAAgB,CAvgB5C,2OA6gBoB,apB3gBE,CoB4gBF,+DAAgE,CAChE,iBAAkB,CAClB,oCpB9gBE,CoB+gBF,cAAe,CACf,iBAAkB,CAlhBtC,+PAohBwB,eAAgB,CAphBxC,uQAyhBwB,apBnhBH,CoBohBG,eAAgB,CAChB,iBAAkB,CA3hB1C,0hBA8hBwB,wBpBrhBJ,CoBshBI,oBpBvhBJ,CoBRpB,+QAkiBwB,kBAAmB,CAliB3C,uRAoiB4B,apB7hBP,CoB8hBO,kBAAW,CAAX,cAAe,CAriB3C,0kBA0iB4B,qBpBtiBb,CoBJf,uSA8iBwB,wBpBriBJ,CoBsiBI,YAAa,CACb,wBpBxiBJ,CoByiBI,kBAAmB,CAjjB3C,mThBFI,mBgBqjBwD,ChBrjBxD,mBgBqjBwD,ChBrjBxD,YgBqjBwD,ChBpjBxD,wBgBojBwE,ChBpjBxE,qBgBojBwE,ChBpjBxE,kBgBojBwE,ChBnjBxE,wBgBmjBiG,ChBnjBjG,qBgBmjBiG,ChBnjBjG,6BgBmjBiG,CACzE,OAAQ,CACR,cAAe,CArjB3C,2XAujBgC,eAAgB,CAvjBhD,+QA4jBwB,WAAY,CACZ,QAAS,CACT,eAAgB,CAChB,kBAAmB,CA/jB3C,mRAqkBwB,apB/jBH,CoBgkBG,eAAgB,CAChB,iBAAkB,CAvkB1C,2RA0kBwB,wBpBjkBJ,CoBkkBI,oBpBnkBJ,CoBokBI,iBAAkB,CAClB,YAAa,CACb,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,iBAAkB,CAjlB1C,0mBAmlB4B,UAAW,CACX,iBAAkB,CAClB,mBAAoB,CACpB,wBAAyB,CACzB,aAAc,CACd,gBAAiB,CACjB,UAAW,CACX,WAAY,CACZ,WAAY,CACZ,kBAAmB,CACnB,qBpBzlBb,CoBJf,mTAimB4B,yBpBzlBR,CoB0lBQ,wBpB1lBR,CoB2lBQ,2BpB3lBR,CoB4lBQ,UAAW,CApmBvC,uTAumB4B,0BpB/lBR,CoBgmBQ,wBpBhmBR,CoBimBQ,2BpBjmBR,CoBkmBQ,SAAU,CA1mBtC,2RA8mBwB,WAAY,CACZ,cAAe,CA/mBvC,2QAqnBwB,apB/mBH,CoBgnBG,eAAgB,CAChB,iBAAkB,CAvnB1C,2ShBFI,mBgB4nBoD,ChB5nBpD,mBgB4nBoD,ChB5nBpD,YgB4nBoD,ChB3nBpD,wBgB2nBoE,ChB3nBpE,qBgB2nBoE,ChB3nBpE,kBgB2nBoE,ChB1nBpE,wBgB0nB6F,ChB1nB7F,qBgB0nB6F,ChB1nB7F,6BgB0nB6F,CACzE,OAAQ,CACR,wBpBpnBJ,CoBqnBI,kBAAmB,CACnB,YAAa,CA9nBrC,uTAgoB4B,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAloB9C,mTAqoB4B,QAAS,CACT,apBzoBL,CoB0oBK,gBAAiB,CAvoB7C,+UA0oB4B,qCpB7oBL,CoB8oBK,iBAAkB,CAClB,WAAY,CACZ,UAAW,ChB/oBnC,mBgBgpBwD,ChBhpBxD,mBgBgpBwD,ChBhpBxD,YgBgpBwD,ChB/oBxD,wBgB+oBwE,ChB/oBxE,qBgB+oBwE,ChB/oBxE,kBgB+oBwE,ChB9oBxE,uBgB8oBoF,ChB9oBpF,oBgB8oBoF,ChB9oBpF,sBgB8oB0F,CA9oB9F,mYAgpBgC,UAAW,CACX,WAAY,CAjpB5C,+OAupBoB,qCpB1pBG,CoB2pBH,iBAAkB,ChB1pBlC,mBgB2pBgD,ChB3pBhD,mBgB2pBgD,ChB3pBhD,YgB2pBgD,ChB1pBhD,wBgB0pBgE,ChB1pBhE,qBgB0pBgE,ChB1pBhE,kBgB0pBgE,ChBzpBhE,uBgBypBkF,ChBzpBlF,oBgBypBkF,ChBzpBlF,sBgBypBkF,CAClE,UAAW,CACX,WAAY,CACZ,WAAY,CA5pBhC,uPA8pBwB,oBAAe,CACf,wBAAgB,CA/pBxC,+MAmqBoB,wBpB1pBA,CoB2pBA,iBAAkB,ChBtqBlC,mBgBuqBgD,ChBvqBhD,mBgBuqBgD,ChBvqBhD,YgBuqBgD,ChBtqBhD,wBgBsqBgE,ChBtqBhE,qBgBsqBgE,ChBtqBhE,kBgBsqBgE,ChBrqBhE,uBgBqqBkF,ChBrqBlF,oBgBqqBkF,ChBrqBlF,sBgBqqBkF,CAClE,UAAW,CACX,WAAY,CACZ,WAAY,CAxqBhC,uNA0qBwB,oBAAe,CACf,wBAAgB,CA3qBxC,6DAsrBoB,iBAAkB,CAClB,6DAA8D,CAC9D,SAAU,CACV,sBAAuB,CACvB,SAAU,CACV,WAAY,CA3rBhC,mDA8rBoB,oBAAe,CACf,wBAAgB,CA/rBpC,8DAksBoB,iBAAkB,CAlsBtC,+CAssBgB,iEAAkE,CAtsBlF,8DAwsBoB,iBAAkB,CAxsBtC,yIA0sBwB,qBpBtsBT,CoBJf,2EA8sBoB,iBAAkB,CAClB,iBAAkB,CAClB,qBAAsB,CAhtB1C,mKAktBwB,UAAW,CACX,iBAAkB,CAClB,mBAAoB,CACpB,yBAA0B,CAC1B,aAAc,CACd,gBAAiB,CACjB,UAAW,CACX,WAAY,CACZ,kBAAmB,CACnB,qBpBvtBT,CoBwtBS,SAAU,CA5tBlC,iFAguBwB,wBpBnuBD,CoBouBC,yBpBpuBD,CoBquBC,2BpBruBD,CoBsuBC,YAAa,CACb,OAAQ,CACR,kCAAW,CAAX,0BAA2B,CAruBnD,kFAwuBwB,wBpB3uBD,CoB4uBC,0BpB5uBD,CoB6uBC,2BpB7uBD,CoB8uBC,SAAU,CACV,OAAQ,CACR,kCAAW,CAAX,0BAA2B,CA7uBnD,+DAmvBoB,eAAgB,CAnvBpC,kEAqvBwB,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CAxvBjC,qEhBII,mBgBsvB0D,ChBtvB1D,mBgBsvB0D,ChBtvB1D,YgBsvB0D,ChBrvB1D,wBgBqvB0E,ChBrvB1E,qBgBqvB0E,ChBrvB1E,kBgBqvB0E,CAClD,QAAS,ClB5uB7B,0BkBfR,qEA6vBgC,kBAAe,CAAf,cAAe,CACf,OAAQ,CAiEf,CA/zBzB,uEAiwBgC,oBAAe,CACf,wBAAgB,CAlwBhD,uEAqwBgC,QAAS,CArwBzC,kFAwwBgC,gBAAiB,CACjB,apBlwBX,CoBmwBW,kBAAmB,CA1wBnD,mFA6wBgC,cAAe,CACf,gBAAiB,CA9wBjD,iFAixBgC,cAAe,CACf,gBAAiB,CACjB,apB5wBX,CoBPrB,kFAsxBgC,iBAAkB,CAClB,iBAAkB,CAClB,UAAW,ClBzwBnC,0BkBfR,kFA0xBoC,iBAAkB,CAsBzB,CAhzB7B,yFA6xBoC,UAAW,CACX,iBAAkB,CAClB,OAAQ,CACR,MAAO,CACP,kCAA2B,CAA3B,0BAA2B,CAC3B,wBpB1xBhB,CoB2xBgB,WAAY,CACZ,SAAU,ClBrxBtC,0BkBfR,yFAsyBwC,YAAa,CAEpB,CAxyBjC,iGA0yBoC,iBAAkB,CAClB,UAAW,CACX,UAAW,CACX,UAAW,CACX,MAAO,CA9yB3C,0FAozBoC,wBpBvzBb,CoBwzBa,qBpBxzBb,CoBGvB,gGAuzBwC,yBpB1zBjB,CoBGvB,8EA2zBoC,oBAAe,CACf,wBAAgB,CA5zBpD,gFAw0BY,iBAAkB,CAx0B9B,wHA00BgB,2BAAgB,CAAhB,4BAAgB,CAAhB,yBAAgB,CAAhB,qBAAsB,CA10BtC,0IA40BoB,WAAY,CACZ,mBAAoB,CA70BxC,uCAq1BY,iBAAkB,CAr1B9B,2DAu1BgB,iBAAkB,CAClB,2BAAgB,CAAhB,4BAAgB,CAAhB,yBAAgB,CAAhB,qBAAsB,CAx1BtC,oEA01BoB,WAAY,CACZ,sBAAuB,CA31B3C,iEA81BoB,iBAAkB,CAClB,QAAS,CACT,UAAW,CACX,sBAAuB,ClBl1BnC,0BkBfR,iEAo2BwB,sBAAuB,CAK9B,ClB11BT,0BkBfR,iEAu2BwB,UAAW,CAElB,CAz2BjB,0DAi3BgB,iBAAkB,CAj3BlC,8EAm3BoB,iBAAkB,CAn3BtC,4FA83BwB,QAAS,CACT,QAAS,CACT,kCAAmC,CACnC,0BAA2B,CAC3B,iBAAkB,CAClB,WAAY,CAn4BpC,6FAs4BwB,iBAAkB,CAClB,QAAS,CACT,QAAS,CACT,kCAA2B,CAA3B,0BAA2B,CAC3B,YAAa,CA14BrC,6DAq5BoB,wBpB54BA,CoB64BA,YAAa,CACb,kBAAmB,CAv5BvC,+DAy5BwB,iBAAkB,CAClB,QAAS,CACT,apBp5BH,CoBPrB,+EA+5B4B,cAAe,CACf,eAAgB,CAChB,apB35BP,CoB45BO,iBAAkB,CAl6B9C,wCA46BY,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,aAAc,CACd,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,iBAAkB,CAClB,mBAAoB,CAh7BhC,4CAk7BgB,WAAY,CACZ,YAAa,CACb,kBAAmB,CACnB,qBpBj7BD,CoBJf,wDAw7BgB,oBAAe,CACf,wBAAgB,CAChB,wBpBj7BI,CoBk7BJ,qBpBv7BD,CoBw7BC,WAAY,CACZ,UAAW,CACX,kBAAmB,CACnB,iBAAkB,CAClB,UAAW,CACX,SAAU,CACV,cAAe,ChBp8B3B,mBgBq8B4C,ChBr8B5C,mBgBq8B4C,ChBr8B5C,YgBq8B4C,ChBp8B5C,wBgBo8B4D,ChBp8B5D,qBgBo8B4D,ChBp8B5D,kBgBo8B4D,ChBn8B5D,uBgBm8BwE,ChBn8BxE,oBgBm8BwE,ChBn8BxE,sBgBm8B8E,CAn8BlF,2CAu8BY,wBpB97BQ,CoB+7BR,kBAAmB,CACnB,YAAa,CACb,gBAAiB,CA18B7B,+CA88BY,YAAa,CAChB,qBAOD,cAAe,CACf,eAAgB,CAChB,apBl9Ba,CoBm9Bb,iBAAkB,CAN1B,2BASQ,iBAAkB,CAT1B,yCAWY,WAAY,CACZ,iBAAkB,CAZ9B,6BAeY,oBAAe,CACf,wBAAgB,CAChB,iBAAkB,CAClB,QAAS,CACT,SAAU,CAnBtB,4CAuBY,iBAAkB,CAClB,QAAS,CACT,UAAW,CACX,UAAW,CACX,cAAe,CA3B3B,8CA6BgB,eAAgB,CA7BhC,uDAgCgB,YAAa,CAhC7B,sDAoCoB,aAAc,CApClC,4DAuCoB,YAAa,CAvCjC,kEA4CoB,aAAc,CA5ClC,4DA+CoB,YAAa,CAChB,eAOb,wBpBlgCgB,CoBmgChB,YAAa,CACb,kBAAmB,CACnB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,apBzgCiB,CoBkgCrB,2BAUQ,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,6BAA2B,CAA3B,6BAA2B,CAA3B,8BAA2B,CAA3B,0BAA2B,CAC3B,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,+BpB9gCY,CoB+gCZ,mBAAoB,CAd5B,sCAiBY,WAAY,CACZ,gBAAiB,CAlB7B,iCAsBY,cAAe,CACf,wBAA8B,CAvB1C,iDA8BgB,eAAgB,CAChB,wBAA8B,CACjC,mBhB3iCT,mBgBijCgC,ChBjjChC,mBgBijCgC,ChBjjChC,YgBijCgC,ChBhjChC,wBgBgjCgD,ChBhjChD,qBgBgjCgD,ChBhjChD,kBgBgjCgD,ChB/iChD,wBgB+iCyE,ChB/iCzE,qBgB+iCyE,ChB/iCzE,6BgB+iCyE,CACzE,wBpBxiCgB,CoByiChB,YAAa,CACb,kBAAmB,CACnB,qBpB/iCW,CoBgjCX,QAAS,CANb,qBAQQ,QAAS,CACT,cAAe,ClBxiCf,0BkB+hCR,mBAYQ,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAEzB,CAED,iBACI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,aAAc,CACd,YAAa,CAChB,sBAEG,wBpB/jCgB,CoBgkChB,iBAAkB,CAClB,kBAAmB,CACnB,QAAS,CACT,qDpBrkCiB,CoBqkCjB,6CpBrkCiB,CIRjB,mBgB8kCgC,ChB9kChC,mBgB8kCgC,ChB9kChC,YgB8kCgC,ChB7kChC,wBgB6kCgD,ChB7kChD,qBgB6kCgD,ChB7kChD,kBgB6kCgD,ChB5kChD,wBgB4kC4D,ChB5kC5D,qBgB4kC4D,ChB5kC5D,6BgB4kCyE,CAN7E,4BhBlkCI,mBgB0kCsC,ChB1kCtC,mBgB0kCsC,ChB1kCtC,YgB0kCsC,ChBzkCtC,wBgBykCsD,ChBzkCtD,qBgBykCsD,ChBzkCtD,kBgBykCsD,CAClD,QAAS,ClBhkCT,0BkBujCR,4BAWY,OAAQ,CAsEf,CAjFL,8BAcY,QAAS,CACT,wBAA8B,CAC9B,yBAA0B,CAhBtC,+BhBlkCI,mBgBqlC0C,ChBrlC1C,mBgBqlC0C,ChBrlC1C,YgBqlC0C,ChBplC1C,wBgBolC0D,ChBplC1D,qBgBolC0D,ChBplC1D,kBgBolC0D,CAClD,QAAS,CApBrB,+BAuBY,eAAgB,ClB9kCpB,0BkBujCR,+BAyBgB,cAAe,CACf,UAAW,CACX,eAAgB,CAChB,oBAAqB,CACrB,2BAA4B,CAC5B,kBAAmB,CACnB,eAAgB,CAChB,sBAAuB,CAE9B,CAlCT,kCAoCY,QAAS,CACT,iBAAkB,CAClB,iBAAkB,CAtC9B,wCAwCgB,UAAW,CACX,WAAY,CAzC5B,yCA4CgB,iBAAkB,CAClB,UAAW,CACX,OAAQ,CACR,MAAO,CACP,wBpB9mCI,CoB+mCJ,SAAU,CACV,WAAY,CAlD5B,oCAsDY,cAAe,CACf,eAAgB,CAChB,WAAY,CACZ,eAAgB,CAChB,SAAU,CACV,aAAc,ClBlnClB,0BkBujCR,oCA6DgB,cAAe,CACf,WAAY,CACZ,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,sBAAuB,CACvB,kBAAmB,CACnB,eAAgB,CAKvB,CA1ET,0CAwEgB,apBjpCO,CEkBf,0BkBujCR,oCA6EgB,qBAAsB,CACtB,sBAAuB,CAE9B,CAIT,aACI,apBzpCkB,CoB0pClB,YAAa,CACb,iBAAkB,CAClB,oCpB5pCkB,CoB6pClB,cAAe,CACf,iBAAkB,CANtB,kBAQQ,eAAgB,CACnB,WhBrqCD,mBgByqCgC,ChBzqChC,mBgByqCgC,ChBzqChC,YgByqCgC,ChBxqChC,wBgBwqCgD,ChBxqChD,qBgBwqCgD,ChBxqChD,kBgBwqCgD,ChBvqChD,uBgBuqCkE,ChBvqClE,oBgBuqCkE,ChBvqClE,sBgBuqCkE,CAClE,QAAS,CAFb,uBAII,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CAPvB,0BASY,cAAe,CACf,apBzqCS,CEQb,0BkBupCR,0BAYgB,YAAa,CAEpB,CAdT,+BAgBQ,YAAa,CACb,qBpBnrCO,CoBorCP,kBAAmB,CACnB,wBpBjrCY,CIVhB,mBgB4rCoC,ChB5rCpC,mBgB4rCoC,ChB5rCpC,YgB4rCoC,ChB3rCpC,wBgB2rCoD,ChB3rCpD,qBgB2rCoD,ChB3rCpD,kBgB2rCoD,ChB1rCpD,uBgB0rCsE,ChB1rCtE,oBgB0rCsE,ChB1rCtE,sBgB0rCsE,CAClE,cAAe,ClB5qCf,0BkBupCR,+BAuBY,WAAY,CAuBnB,ClBrsCG,0BkBupCR,+BA0BY,WAAY,CAoBnB,CA9CL,sCA6BY,UAAW,CACX,WAAY,ClBrrChB,0BkBupCR,sCAgCgB,UAAW,CACX,WAAY,CAMnB,ClB9rCD,0BkBupCR,sCAoCgB,UAAW,CACX,WAAY,CAEnB,CAvCT,yCAyCY,aAAc,CAzC1B,uCA4CY,YAAa,CA5CzB,sCAiDY,wBpB1tCW,CoByqCvB,wCAoDY,YAAa,CApDzB,sCAuDY,aAAc,CACjB,wCC9tCG,iBAAkB,CAClB,QAAS,CACT,MAAO,CACP,wBrBDU,CqBEV,yBAA0B,CAC1B,WAAY,CACZ,0MAA2M,CATvN,yCAaY,kBAAmB,CACnB,gBAAiB,CACjB,gBAAiB,CAf7B,4CAmBY,iBAAkB,CAClB,QAAS,CACT,SAAU,CArBtB,+CAwBgB,cAAe,CACf,eAAgB,CAChB,gBAAiB,CA1BjC,8CA8BgB,uBrBrBK,CqBsBL,gBAAiB,CACjB,SAAU,CACV,cAAe,CAjC/B,iDAsCgB,iBAAkB,CAClB,aAAc,CACd,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,arBhCK,CqBTrB,6DA4CoB,eAAgB,CAChB,wBrBpCC,CqBqCD,kBAAmB,CACnB,cAAe,CACf,eAAgB,CACnB,iBCjDb,4BtBWgB,CsBVhB,gBAAiB,ClBAjB,mBkBCgC,ClBDhC,mBkBCgC,ClBDhC,YkBCgC,ClBAhC,wBkBAgD,ClBAhD,qBkBAgD,ClBAhD,kBkBAgD,ClBChD,wBkBD4D,ClBC5D,qBkBD4D,ClBC5D,6BkBDyE,CpBgBrE,0BoBnBR,iBAKQ,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CAwC9B,CA7CD,uBAQQ,atBGa,CEQb,0BoBnBR,uBAUY,YAAa,CAEpB,CAZL,mBAcQ,oBAAe,CACf,wBAAgB,CAfxB,6BlBQI,mBkBUsC,ClBVtC,mBkBUsC,ClBVtC,YkBUsC,ClBTtC,wBkBSsD,ClBTtD,qBkBSsD,ClBTtD,kBkBSsD,CAClD,OAAQ,CAnBhB,mDlBEI,mBkBoB4C,ClBpB5C,mBkBoB4C,ClBpB5C,YkBoB4C,ClBnB5C,wBkBmB4D,ClBnB5D,qBkBmB4D,ClBnB5D,kBkBmB4D,ClBlB5D,uBkBkB8E,ClBlB9E,oBkBkB8E,ClBlB9E,sBkBkB8E,CAClE,atBZK,CsBaL,UAAW,CACX,WAAY,CACZ,WAAY,CACZ,kBAAmB,CA3BnC,yDA6BoB,WAAY,CACZ,YAAa,CACb,uBAAY,CAAZ,eAAgB,CA/BpC,wDAkCoB,aAAc,CAlClC,0DAuCoB,wBtBtCG,CsBuCH,UtBhCL,CsBiCE,uCCvCT,wBvBDe,CuBEf,kBAAmB,CACnB,YAAa,CACb,eAAgB,CAChB,SAAU,CANlB,gDASY,mBAAoB,CAThC,6DAWgB,iBAAkB,CAClB,mBAAoB,CAZpC,iEAcoB,kBAAmB,CACnB,qBvBPL,CuBQK,WAAY,CACZ,UAAW,CAjB/B,gEAqBgB,YAAa,CACb,kBAAmB,CACnB,kDAAmD,CACnD,0BAA2B,CAC3B,2BAA4B,CAC5B,qBAAsB,CACtB,YAAa,CACb,UvBpBD,CINX,mBmB2B4C,CnB3B5C,mBmB2B4C,CnB3B5C,YmB2B4C,CnB1B5C,wBmB0B4D,CnB1B5D,qBmB0B4D,CnB1B5D,kBmB0B4D,CnBzB5D,oBmByB2E,CnBzB3E,iBmByB2E,CnBzB3E,mBmByB2E,CAC/D,2BAAgB,CAAhB,4BAAgB,CAAhB,yBAAgB,CAAhB,qBAAsB,CA9BtC,mEAiCoB,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAnCtC,kEnBEI,mBmBoCgD,CnBpChD,mBmBoCgD,CnBpChD,YmBoCgD,CnBnChD,wBmBmCgE,CnBnChE,qBmBmCgE,CnBnChE,kBmBmCgE,CnBlChE,uBmBkCkF,CnBlClF,oBmBkCkF,CnBlClF,sBmBkCkF,CAClE,QAAS,CACT,OAAQ,CACR,gBAAiB,CAzCrC,oEA2CwB,oBAAe,CACnB,sBAAgB,CA5CpC,yDAmDY,aAAc,CACd,YAAa,CApDzB,8FAwDwB,UAAW,CAxDnC,4GA0D4B,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,YAAa,CACb,mBAAoB,CA9DhD,+GAgEgC,aAAc,CAhE9C,yHnBQI,mBmB2DkE,CnB3DlE,mBmB2DkE,CnB3DlE,YmB2DkE,CnB1DlE,wBmB0DkF,CnB1DlF,qBmB0DkF,CnB1DlF,kBmB0DkF,CAClD,iBAAkB,CAClB,QAAS,CACT,WAAY,CACZ,gCAAyB,CAAzB,wBAAyB,CACzB,YAAa,CACb,UAAW,CACX,iBAAkB,CAClB,kBAAmB,CA3EvD,2HA6EwC,oBAAe,CACf,sBAAgB,CA9ExD,8HAiFwC,UvBzEzB,CuB0EyB,cAAe,CAlFvD,sIAqFwC,YAAa,CArFrD,wIAwFwC,aAAc,CAxFtD,gIA2FwC,qBvBnFzB,CuBRf,qIA8F4C,avB7FrB,CuB8FqB,eAAgB,CA/F5D,6IAkG4C,aAAc,CAlG1D,+IAqG4C,YAAa,CArGzD,sIAwG4C,qBvBhG7B,CuBRf,+HA4GwC,sCvBpGzB,CuBRf,uDAyHY,YAAa,CAzHzB,iEnBQI,mBmBmH8C,CnBnH9C,mBmBmH8C,CnBnH9C,YmBmH8C,CnBlH9C,wBmBkH8D,CnBlH9D,qBmBkH8D,CnBlH9D,kBmBkH8D,CAClD,iBAAkB,CAClB,QAAS,CACT,WAAY,CACZ,gCAAyB,CAAzB,wBAAyB,CACzB,YAAa,CACb,UAAW,CACX,iBAAkB,CAlIlC,mEAoIoB,oBAAe,CACf,sBAAgB,CArIpC,sEAwIoB,UvBhIL,CuBiIK,cAAe,CAzInC,uEA4IoB,sCvBpIL,CuBRf,0CAqJY,wBvBxIQ,CuByIR,WAAY,CACZ,kBAAmB,CAvJ/B,uDnBEI,mBmBwJ4C,CnBxJ5C,mBmBwJ4C,CnBxJ5C,YmBwJ4C,CnBvJ5C,wBmBuJ4D,CnBvJ5D,qBmBuJ4D,CnBvJ5D,kBmBuJ4D,CnBtJ5D,wBmBsJqF,CnBtJrF,qBmBsJqF,CnBtJrF,6BmBsJqF,CACzE,OAAQ,CACR,YAAa,CACb,4BAA6B,CAC7B,eAAgB,CAChB,+BvBnJI,CuBZpB,gEAiKoB,iBAAkB,CAjKtC,uEAmKwB,UAAW,CACX,iBAAkB,CAClB,UAAW,CACX,OAAQ,CACR,kCAA2B,CAA3B,0BAA2B,CAC3B,wBvBvKD,CuBwKC,WAAY,CACZ,SAAU,CA1KlC,mEA6KwB,eAAgB,CAChB,gBAAiB,CA9KzC,uHAkLoB,eAAgB,CnB1KhC,mBmB2KkD,CnB3KlD,mBmB2KkD,CnB3KlD,YmB2KkD,CnB1KlD,wBmB0KkE,CnB1KlE,qBmB0KkE,CnB1KlE,kBmB0KkE,CAClD,OAAQ,CACR,cAAe,CArLnC,2HAuLwB,oBAAe,CACf,wBAAgB,CAxLxC,qDA6LgB,YAAa,CA7L7B,qEA+LoB,gBAAiB,CA/LrC,oEAmMwB,wBvBvLJ,CuBwLI,YAAa,CACb,kBAAmB,CACnB,qBvB9LT,CuBRf,iFnBEI,mBmBsMwD,CnBtMxD,mBmBsMwD,CnBtMxD,YmBsMwD,CnBrMxD,wBmBqMwE,CnBrMxE,qBmBqMwE,CnBrMxE,kBmBqMwE,CnBpMxE,wBmBoMiG,CnBpMjG,qBmBoMiG,CnBpMjG,6BmBoMiG,CACzE,OAAQ,CAzMpC,0FA4MoC,gBAAiB,CACjB,avBlMf,CuBmMe,iBAAkB,CA9MtD,0FAiNoC,eAAgB,CAChB,gBAAiB,CAlNrD,4FAsNgC,iBAAkB,CAClB,kBAAmB,CAvNnD,iGAyNoC,qCvBxNb,CuByNa,kBAAmB,CACnB,WAAY,CACZ,UAAW,CACX,iBAAkB,CAClB,KAAM,CACN,OAAQ,CA/N5C,8FAkOoC,oBAAe,CACf,wBAAgB,CAChB,gBAAiB,CApOrD,sEA6OwB,gBAAiB,CA7OzC,yEAgPwB,wBvBpOJ,CuBqOI,YAAa,CACb,kBAAmB,CACnB,qBvB3OT,CuB4OS,QAAS,CApPjC,8FAuP4B,wBvB1OR,CuB2OQ,iBAAkB,CAClB,WAAY,CnBvPpC,mBmBwPwD,CnBxPxD,mBmBwPwD,CnBxPxD,YmBwPwD,CnBvPxD,wBmBuPwE,CnBvPxE,qBmBuPwE,CnBvPxE,kBmBuPwE,CnBtPxE,uBmBsP0F,CnBtP1F,oBmBsP0F,CnBtP1F,sBmBsP0F,CAClE,qDAAsD,CACtD,uBAAwB,CACxB,2BAA4B,CAC5B,oBAAqB,CACrB,SAAU,CACV,iBAAkB,CAhQ9C,6GAmQgC,YAAa,CACb,UAAW,CApQ3C,4FA0QgC,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,QAAS,CA7QzC,gGAgRgC,QAAS,CAhRzC,0FAsRgC,avBrRT,CuBsRS,eAAgB,CAChB,gBAAiB,CAxRjD,uDA+RgB,YAAa,CACb,4BvBpRI,CuBqRJ,QAAS,CACT,sBAAiB,CAAjB,mBAAiB,CAAjB,qBAAsB,CAlStC,4DAoSoB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,iBAAkB,CArStC,iDA2SY,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CA7SrB,8DnBEI,mBmB6S4C,CnB7S5C,mBmB6S4C,CnB7S5C,YmB6S4C,CnB5S5C,uBmB4S2D,CnB5S3D,oBmB4S2D,CnB5S3D,iBmB4S2D,CnB3S3D,wBmB2SoF,CnB3SpF,qBmB2SoF,CnB3SpF,6BmB2SoF,CACxE,UAAW,CACX,iBAAkB,CAClB,wBvBtSI,CuBuSJ,YAAa,CACb,qBvB5SD,CuBRf,sEnBQI,mBmB8SkD,CnB9SlD,mBmB8SkD,CnB9SlD,YmB8SkD,CnB7SlD,uBmB6SiE,CnB7SjE,oBmB6SiE,CnB7SjE,iBmB6SiE,CACjD,QAAS,CAvT7B,sFA0T4B,WAAY,CACZ,UAAW,CACX,kBAAmB,CA5T/C,8FnBQI,mBmByT0D,CnBzT1D,mBmByT0D,CnBzT1D,YmByT0D,CnBxT1D,wBmBwT0E,CnBxT1E,qBmBwT0E,CnBxT1E,kBmBwT0E,CAClD,QAAS,CAlUrC,mGAoUgC,eAAgB,CApUhD,oGAuUgC,avB5TX,CuBXrB,sFA2U4B,avBhUP,CuBiUO,gBAAiB,CA5U7C,qFA+U4B,cAAe,CACf,QAAS,CACT,avBvUP,CuBwUO,gBAAiB,CACjB,eAAgB,CAnV5C,iFA2VwB,oBAAoB,CAApB,iBAAoB,CAApB,mBAAoB,CACpB,kBAAmB,CACnB,YAAa,CA7VrC,qFA+V4B,UAAW,CACX,WAAY,CAhWxC,sFAmW4B,eAAgB,CAnW5C,kFAuWwB,gBAAiB,CACjB,WAAY,CACZ,SAAU,CACb,YAQjB,WAAY,CACZ,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,qCvBrXmB,CuBgXvB,cAOQ,oBAAe,CACf,wBAAgB,CACnB,SnBxXD,mBmB4XgC,CnB5XhC,mBmB4XgC,CnB5XhC,YmB4XgC,CnB3XhC,wBmB2XgD,CnB3XhD,qBmB2XgD,CnB3XhD,kBmB2XgD,CnB1XhD,uBmB0X4D,CnB1X5D,oBmB0X4D,CnB1X5D,sBmB0XkE,CADtE,aAGQ,YAAa,CAChB,eAID,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,wBvB3XgB,CuB4XhB,qBvBhYW,CuBiYX,kBAAmB,CALvB,kCAQQ,+BvBhYY,CuBiYZ,YAAa,CnB3YjB,mBmB4YoC,CnB5YpC,mBmB4YoC,CnB5YpC,YmB4YoC,CnB3YpC,uBmB2YmD,CnB3YnD,oBmB2YmD,CnB3YnD,iBmB2YmD,CnB1YnD,wBmB0Y4E,CnB1Y5E,qBmB0Y4E,CnB1Y5E,6BmB0Y4E,CACxE,QAAS,CAXjB,6CAaY,kBAAmB,CAb/B,0CnB5XI,mBmB4Y0C,CnB5Y1C,mBmB4Y0C,CnB5Y1C,YmB4Y0C,CnB3Y1C,uBmB2YyD,CnB3YzD,oBmB2YyD,CnB3YzD,iBmB2YyD,CACjD,QAAS,CAjBrB,uDAmBgB,WAAY,CACZ,UAAW,CACX,cAAe,CACf,kBAAmB,CACnB,wBvB9YI,CIXhB,mBmB0Z4C,CnB1Z5C,mBmB0Z4C,CnB1Z5C,YmB0Z4C,CnBzZ5C,wBmByZ4D,CnBzZ5D,qBmByZ4D,CnBzZ5D,kBmByZ4D,CnBxZ5D,uBmBwZwE,CnBxZxE,oBmBwZwE,CnBxZxE,sBmBwZ8E,CAxBlF,yDA0BoB,oBAAe,CACf,wBAAgB,CA3BpC,0DAgCoB,eAAgB,CAChB,avB1ZC,CuB2ZD,gBAAiB,CACjB,iBAAkB,CAnCtC,yDAsCoB,QAAS,CACT,avBhaC,CuBiaD,gBAAiB,CACjB,kBAAmB,CAzCvC,mEA4CoB,WAAY,CACZ,UAAW,CACX,iBAAkB,CAClB,eAAgB,CA/CpC,uEAiDwB,WAAY,CACZ,UAAW,CAlDnC,+CAwDY,cAAe,CACf,cAAe,CAzD3B,oDA2DgB,avBpbK,CuByXrB,kGAgEgB,mCAAgB,CAhEhC,sMAmEgB,wBAA8B,CACjC,YCvcT,qBxBOW,CwBNX,YAAa,CACb,iBAAkB,CAClB,WAAY,CAJhB,sBAMQ,QAAS,CACT,wBxBKY,CwBJZ,UAAW,CARnB,wBAYQ,QAAS,CACT,UAAW,CACX,WAAY,CACZ,WAAY,CACZ,kCxBNa,CwBOb,kBAAmB,CAjB3B,kEAmBQ,kCxBTa,CwBVrB,kEAuBQ,YAAa,CACb,SAAU,CACV,4BAA6B,CAC7B,uBxBhBa,CwBiBb,eAAgB,CAChB,SAAU,CA5BlB,uFA8BY,YAAa,CA9BzB,qBAkCQ,UAAW,CACX,mCxBzBa,CwB0Bb,QAAS,CACZ,oBpB7BD,mBoBkCsC,CpBlCtC,mBoBkCsC,CpBlCtC,YoBkCsC,CpBjCtC,wBoBiCsD,CpBjCtD,qBoBiCsD,CpBjCtD,kBoBiCsD,CAClD,QAAS,CACT,kBAAW,CAAX,cAAe,CAJvB,gCAMY,QAAS,CANrB,yBAYY,UAAW,CAZvB,kCAgBgB,YAAa,CAhB7B,yCAqBgB,axBjDI,CwB4BpB,+CAuBoB,YAAa,CAvBjC,uCA2BgB,iDAAkD,CAClD,2BAA4B,CAC5B,0BAA2B,CAC3B,SAAU,CACV,cAAe,CACf,SAAU,CACV,eAAgB,CAChB,UAAW,CACX,iBAAkB,CAClB,QAAS,CACT,eAAgB,CAChB,cAAe,CAtC/B,8CAwCoB,UAAW,CACX,iBAAkB,CAClB,wBxBxEC,CwByED,WAAY,CACZ,QAAS,CACT,UAAW,CACX,UAAW,CACX,SAAU,CACV,2BAA4B,CAhDhD,8CAmDoB,UAAW,CACX,iBAAkB,CAClB,wBxBnFC,CwBoFD,UAAW,CACX,UAAW,CACX,UAAW,CACX,SAAU,CACV,2BAA4B,CA1DhD,wBAgEY,kBxB9FS,CwB+FT,UAAW,CAjEvB,yBAoEY,wBxBhGQ,CwB4BpB,yBAuEY,WAAY,CAvExB,wCAyEgB,axBvGK,CwBwGL,gBAAiB,CACjB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,YAAa,CACb,aAAc,CACjB,apBpHT,mBqBDgC,CrBChC,mBqBDgC,CrBChC,YqBDgC,CrBEhC,wBqBFgD,CrBEhD,qBqBFgD,CrBEhD,kBqBFgD,CrBGhD,sBqBHiE,CrBGjE,mBqBHiE,CrBGjE,qBqBHiE,CACjE,kBAAe,CAAf,cAAe,CACf,OAAQ,CACR,6BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CACnB,eAAgB,CALpB,mBAQQ,YAAa,CARrB,mBrBQI,mBqBIsC,CrBJtC,mBqBIsC,CrBJtC,YqBIsC,CrBHtC,wBqBGsD,CrBHtD,qBqBGsD,CrBHtD,kBqBGsD,CAClD,qBzBLO,CyBMP,azBHa,CyBIb,OAAQ,CACR,YAAa,CACb,iBAAkB,CAClB,wBzBNY,CyBOZ,aAAc,CACd,cAAe,CACf,cAAe,CACf,eAAgB,CAtBxB,qBAyBY,oBAAe,CACf,wBAAgB,CA1B5B,yBA8BY,wBzB7BW,CyB8BX,UAAW,CA/BvB,2BAkCgB,qBAAgB,CAlChC,8BAyCY,wBzBxCW,CyByCX,UzBlCG,CyBmCH,oBzB1CW,CyBDvB,gCA8CgB,qBAAgB,CACnB,SAMT,qBzB7CW,CyB8CX,YAAa,CACb,iBAAkB,CrBrDlB,mBqBsDgC,CrBtDhC,mBqBsDgC,CrBtDhC,YqBsDgC,CrBrDhC,uBqBqD+C,CrBrD/C,oBqBqD+C,CrBrD/C,iBqBqD+C,CrBpD/C,wBqBoDwE,CrBpDxE,qBqBoDwE,CrBpDxE,6BqBoDwE,CACxE,QAAS,CACT,kBAAmB,CANvB,oBASQ,QAAS,CATjB,wBrB5CI,mBqByDsC,CrBzDtC,mBqByDsC,CrBzDtC,YqByDsC,CrBxDtC,wBqBwDsD,CrBxDtD,qBqBwDsD,CrBxDtD,kBqBwDsD,CAClD,OAAQ,CACR,azBzDa,CyB0CrB,4BAkBY,kBAAmB,CACnB,WAAY,CACZ,UAAW,CApBvB,0BAwBY,QAAS,CACT,cAAe,CAEf,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,sBAAuB,CAhCnC,2BAoCY,eAAgB,CAChB,eAAgB,CArC5B,uBA2CY,oBAAoB,CAApB,iBAAoB,CAApB,mBAAoB,CACpB,azBtFS,CyB0CrB,6BA+CgB,cAAe,CA/C/B,mCAoDgB,kBAAmB,CvBrF3B,0BuBiCR,iBAwDY,gBAAiB,CAExB,CvB3FG,0BuBiCR,SA6DQ,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,sBAAsB,CAAtB,mBAAsB,CAAtB,qBAAsB,CACtB,OAAQ,CAhEhB,iBAmEY,gBAAiB,CrB/GzB,mBqBgH0C,CrBhH1C,mBqBgH0C,CrBhH1C,YqBgH0C,CrB/G1C,wBqB+G0D,CrB/G1D,qBqB+G0D,CrB/G1D,kBqB+G0D,CAClD,6BAA2B,CAA3B,6BAA2B,CAA3B,8BAA2B,CAA3B,0BAA2B,CAC3B,QAAS,CAtErB,uBAwEgB,QAAS,CACZ,CAKb,aACI,qBzB3HW,CyB4HX,iBAAkB,CAClB,YAAa,CACb,kBAAmB,CACnB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,QAAS,CACT,2BAAgB,CAAhB,4BAAgB,CAAhB,yBAAgB,CAAhB,qBAAsB,CAP1B,yBrB1HI,mBqBoIsC,CrBpItC,mBqBoIsC,CrBpItC,YqBoIsC,CrBnItC,wBqBmIsD,CrBnItD,qBqBmIsD,CrBnItD,kBqBmIsD,CAClD,OAAQ,CACR,eAAgB,CAZxB,8BAeY,UAAW,CACX,wBzBrIQ,CyBsIR,iBAAkB,CAjB9B,wCAmBgB,UAAW,CACX,wBzBjJO,CyBkJP,iBAAkB,CArBlC,0CAuBoB,SAAU,CAvB9B,0CA0BoB,SAAU,CA1B9B,0CA6BoB,SAAU,CA7B9B,0CAgCoB,QAAS,CAhC7B,0CAmCoB,QAAS,CAnC7B,+BAwCY,cAAe,CAxC3B,gCA2CY,azBlKS,CyBmKT,eAAgB,CAChB,cAAe,CAClB,0BC7KG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAQG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,UAAW,CACX,eAAgB,CAChB,aAAc,CACjB,0BAOG,iBAAkB,CAClB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,kBAAmB,CACnB,UAAW,CACX,aAAc,CACjB,iCAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,iCAOG,iBAAkB,CAClB,UAAW,CACX,aAAc,CACjB,8BAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,2BAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,2BAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,2BAOG,wBAAyB,CACzB,UAAW,CACX,aAAc,CACjB,4BAOG,wBAAyB,CACzB,UAAW,CACX,aAAc,CACjB,8BAOG,uBAAwB,CACxB,UAAW,CACX,aAAc,CACjB,gCAQG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,2BAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,0BAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,OAKL,kCAAmC,CACtC,UAGG,qCAAsC,CACzC,UAGG,qCAAsC,CACzC,gBAGG,kCAAmC,CACnC,2BAA4B,CAC/B,oBCvSO,aAAc,CAKjB,kCAFO,aAAc,CAL1B,4BAUY,eAAgB,CACnB,wxCAiBD,2BAA4B,CAC5B,wBAAyB,CACzB,0BAA2B,CAC3B,UAAW,CACX,WAAY,CACZ,SAAU,CACV,iBAAkB,CAClB,SAAU,CApBlB,w9CAuBY,YAAa,CAvBzB,4oBA+BQ,2DAA4D,CAC5D,UAAW,CACX,SAAU,CAjClB,4/BAqCY,8EAA+E,CArC3F,4oBA6CQ,mEAAoE,CACpE,OAAQ,CACR,SAAU,CA/ClB,4/BAkDY,2EAA4E,CAC/E,0CAMD,2BAA4B,CAC5B,wBAAyB,CACzB,0BAA2B,CAC3B,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,SAAU,CACV,OAAQ,CACR,kCAA2B,CAA3B,0BAA2B,CAC3B,sC3BzEO,C2B0EP,kBAAmB,CACnB,SAAU,CAblB,sDAgBY,YAAa,CAhBzB,wFAmBY,WAAY,CACf,qBAID,wDAAyD,CACzD,SAAU,CACb,qBAEG,oEAAqE,CACrE,UAAW,CACd,kBCnGD,wB5BWgB,C4BVhB,kBAAmB,CAFvB,yBAIQ,eAAgB,CAChB,WAAY,CACZ,QAAS,CACT,oBAAc,CAPtB,+BASY,wB5BIQ,C4BbpB,qCAYoB,cAAe,CACf,eAAgB,CAChB,a5BHC,C4BID,2HAA4H,CAC5H,gBAAiB,CAhBrC,kCAsBgB,gC5BVI,C4BZpB,6CAyBoB,kBAAmB,CAzBvC,qCA6BoB,qBAAsB,CACtB,2HAA4H,CAC5H,cAAe,CACf,gBAAiB,CAhCrC,uFAsCY,WAAY,CAtCxB,4CA0CgB,wB5BzCO,C4BDvB,kDA6CwB,U5BrCT,C4BRf,4CAkDgB,qB5B1CD,C4BRf,mDAqDwB,a5BpDD,C4BqDC,eAAgB,CAtDxC,qDAyDwB,a5B9CH,C4BXrB,wDA4DwB,a5BlDH,C4BmDG,eAAgB,CA7DxC,2DAgEwB,a5BtDH,C4BVrB,uDAoEwB,eAAgB,CAChB,a5BlED,C4BHvB,sDAwEwB,eAAgB,CAChB,a5BnEF,C4BNtB,gEAmF4B,eAAgB,CAnF5C,gEAsF4B,eAAgB,CAtF5C,gEAyF4B,eAAgB,CAzF5C,gEA2F4B,eAAgB,CACnB,iBC3FrB,oEAAqE,CACrE,WAAY,CACZ,YAAa,CACb,yDAA0D,CAC1D,oBAAqB,CACrB,6DAA8D,CANlE,2BAQQ,WAAY,CARpB,qCAUY,kBAAmB,CACnB,iEAAmE,CACnE,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,qB7BNG,C6BOH,wB7BHQ,C6BIR,eAAgB,CAhB5B,uFAmBgB,qC7BlBO,C6BmBP,sC7BnBO,C6BDvB,iGAsBoB,a7BrBG,C6BsBH,eAAgB,CAvBpC,+FA0BoB,qB7BlBL,C6BRf,iHA4BwB,YAAa,CA5BrC,6GA+BwB,aAAc,CA/BtC,8CAoCgB,UAAW,CACX,gEAAiE,CACjE,mEAAoE,CACpE,kBAAmB,CAvCnC,yCA2CgB,sC7B/BI,C6BgCJ,iEAAkE,CAClE,iBAAkB,CAClB,UAAW,CACX,WAAY,CA/C5B,gDAiDoB,YAAa,CAjDjC,kDAoDoB,aAAc,CApDlC,0CAwDgB,aAAc,CACd,mEAAoE,CACpE,a7BhDK,C6BiDL,gBAAiB,CACjB,kBAAmB,CA5DnC,2CA+DgB,aAAc,CACd,mEAAoE,CACpE,uB7BvDK,C6BwDL,gBAAiB,CACjB,kBAAmB,CACtB,aAMT,eAAgB,CACnB,YAGG,QAAS,CADb,sBAGQ,YAAa,CACb,gBAAiB,CAJzB,gCAQY,iBAAkB,CAClB,cAAe,CACf,eAAgB,CAChB,a7B7ES,C6B8ET,WAAY,CAZxB,uCAcgB,4BAA6B,CAC7B,WAAY,CACZ,a7BnFK,C6BoFL,+B7BpFK,C6BmErB,6CAmBiB,+B7BtFI,C6BmErB,sCAwBgB,WAAY,CACf,czBpGT,mByB0GgC,CzB1GhC,mByB0GgC,CzB1GhC,YyB0GgC,CzBzGhC,wByByGgD,CzBzGhD,qByByGgD,CzBzGhD,kByByGgD,CzBxGhD,wByBwGyE,CzBxGzE,qByBwGyE,CzBxGzE,6ByBwGyE,CACzE,OAAQ,CACR,+B7BlGgB,C6BmGhB,mBAAoB,CAJxB,iBAMQ,eAAgB,CANxB,wBASQ,WAAY,CACZ,QAAS,CAVjB,4CAagB,qC7BvHO,C6BwHP,a7BxHO,C6ByHP,WAAY,CACZ,kBAAmB,CACnB,cAAe,CACf,gBAAiB,CAlBjC,mDAoBoB,wB7B9HG,C6B+HH,U7BxHL,C6ByHK,eAAgB,CACnB,+BAQT,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CAHjB,mDAMgB,wB7B7IO,C6B8IP,a7B9IO,C6B+IP,iBAAkB,CAClB,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,YAAa,CACb,4BAA6B,CAChC,eAMT,eAAgB,CAChB,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CALb,kBAOQ,iBAAkB,CAClB,mEAAoE,CACvE,sBAID,mBAAoB,CACpB,QAAS,CAFb,0CAKY,qC7B3KW,C6B4KX,a7B5KW,C6B6KX,WAAY,CACZ,kBAAmB,CACnB,cAAe,CACf,gBAAiB,CAV7B,iDAYgB,wB7BlLO,C6BmLP,U7B5KD,C6B6KC,eAAgB,CACnB,OCrLT,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,UAAW,CACX,SAAU,CALd,cAQQ,eAAgB,CAChB,KAAM,CACN,MAAO,CACP,UAAW,CACX,qB9BHO,C8BIP,gD9BHM,C8BGN,wC9BHa,C8BVrB,kCAgBY,oEAAqE,CAhBjF,wCAqBgB,gCAAmC,CArBnD,2CAwBoB,kCAAyC,CAxB7D,6CA2BwB,kCAAyC,CA3BjE,6CA+BwB,qCAAgB,CA/BxC,iDAmCwB,wBAA8B,CAnCtD,mDAsC4B,wBAA8B,CAtC1D,mDA0C4B,mCAAgB,CA1C5C,oC1BQI,mB0B4C0C,C1B5C1C,mB0B4C0C,C1B5C1C,Y0B4C0C,C1B3C1C,wB0B2C0D,C1B3C1D,qB0B2C0D,C1B3C1D,kB0B2C0D,CAClD,2DAA4D,CArDxE,yDAwDgB,wBAAQ,CAAR,gBAAiB,CAxDjC,wBA6DY,oBAAe,CACf,sBAAgB,CA9D5B,oCAkEY,QAAS,CACT,SAAU,CACV,WAAY,CApExB,wC1B4BI,gE0B2CgG,C1B1ChG,W0B0C+G,CAvEnH,yCA4EY,sC9BpEG,C8BqEH,wBAAyB,CACzB,eAAgB,CAChB,kBAAmB,C1BvE3B,mB0BwE0C,C1BxE1C,mB0BwE0C,C1BxE1C,Y0BwE0C,C1BvE1C,wB0BuE0D,C1BvE1D,qB0BuE0D,C1BvE1D,kB0BuE0D,CAClD,OAAQ,CACR,eAAgB,CAChB,kBAAmB,CAnF/B,uDAsFgB,sBAAuB,CACvB,WAAY,CACZ,WAAY,CACZ,aAAc,CACd,SAAU,C1BlFtB,mB0BmF8C,C1BnF9C,mB0BmF8C,C1BnF9C,Y0BmF8C,C1BlF9C,wB0BkF8D,C1BlF9D,qB0BkF8D,C1BlF9D,kB0BkF8D,CAClD,QAAS,CA5FzB,qEA+FoB,oBAAe,CACf,sBAAgB,CAhGpC,8DAqGoB,qBAAgB,CAChB,sBAAgB,CAtGpC,4DA0GoB,0BAAoB,CAApB,0BAAoB,CAApB,mBAAoB,CACpB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,OAAQ,CACR,aAAc,CACd,cAAe,CA9GnC,2DAmHgB,eAAgB,CAChB,kCAA2B,CAA3B,0BAA2B,CAC3B,iBAAkB,CAClB,SAAU,CACV,sC9B/GD,C8BgHC,wBAAyB,CACzB,SAAU,CACV,iBAAkB,CAClB,wCAAiC,CAAjC,gCAAiC,CACjC,QAAS,CACT,OAAQ,CACR,YAAa,CACb,kBAAmB,CACnB,yBAAO,CAAP,sBAAO,CAAP,iBAAkB,CA8CrB,uEA1CO,MAAO,CACP,WAAY,CArIhC,qEAyIoB,cAAe,CACf,2BAAoB,CAApB,mBAAoB,C1BlIpC,mB0BmIkD,C1BnIlD,mB0BmIkD,C1BnIlD,Y0BmIkD,C1BlIlD,wB0BkIkE,C1BlIlE,qB0BkIkE,C1BlIlE,kB0BkIkE,CAClD,WAAY,CACZ,2B9BrIL,C8BsIK,4BAA6B,CAC7B,OAAQ,CACR,cAAe,CACf,SAAU,CAjJ9B,yEAoJwB,0BAA2B,CAC3B,qBAAsB,CACtB,sBAAuB,CAtJ/C,uEA0JwB,0BAAgB,CA1JxC,2EA8JwB,U9BtJT,C8BuJS,iCAAW,CAAX,yBAA0B,CAS7B,uFANO,kCAAW,CAAX,0BAA2B,CAlKvD,6EAsK4B,qBAAgB,CAtK5C,wEA2KwB,eAAgB,CA3KxC,iEAkLoB,SAAU,CACV,kBAAmB,CACnB,sBAAW,CAAX,cAAe,CApLnC,iC1BQI,mB0BkL0C,C1BlL1C,mB0BkL0C,C1BlL1C,Y0BkL0C,C1BjL1C,wB0BiL0D,C1BjL1D,qB0BiL0D,C1BjL1D,kB0BiL0D,CAClD,QAAS,C5BxKb,0B4BnBR,iCA8LgB,OAAQ,CAiMf,CA/XT,iDAkMgB,sC9B1LD,C8B2LC,sC9B3LD,C8B6LC,eAAgB,CAChB,kBAAmB,CAtMnC,2DAyMoB,WAAY,CACZ,4BAA6B,CAC7B,SAAU,C5BxLtB,0B4BnBR,kEAgNgC,YAAa,CAEpB,CAlNzB,kEAwNwB,kBAAmB,CACnB,gBAAiB,CACjB,+BAAgC,CAChC,wBAAyB,CACzB,4BAA6B,C5BzM7C,0B4BnBR,kEA+N4B,gBAAiB,CACjB,cAAe,CAEtB,CAlOrB,4DAsOoB,iBAAkB,CAClB,gBAAiB,CAvOrC,mEA0OwB,UAAW,CACX,iBAAkB,CAClB,SAAU,CACV,WAAY,CACZ,sC9BtOT,C8BuOS,OAAQ,CACR,UAAW,CACX,kCAAW,CAAX,0BAA2B,CAjPnD,2J1BQI,mB0BkP8C,C1BlP9C,mB0BkP8C,C1BlP9C,Y0BkP8C,C1BjP9C,wB0BiP8D,C1BjP9D,qB0BiP8D,C1BjP9D,kB0BiP8D,CAClD,OAAQ,CACR,eAAgB,CAChB,kBAAmB,CA7PnC,uKAgQoB,UAAW,CACX,WAAY,CACZ,kBAAmB,CAlQvC,4kBAwQoB,sBAAuB,CACvB,WAAY,CACZ,WAAY,CACZ,aAAc,CACd,SAAU,C1BpQ1B,mB0BqQkD,C1BrQlD,mB0BqQkD,C1BrQlD,Y0BqQkD,C1BpQlD,wB0BoQkE,C1BpQlE,qB0BoQkE,C1BpQlE,kB0BoQkE,CAClD,QAAS,CA9Q7B,ynBAiRwB,0BAAoB,CAApB,0BAAoB,CAApB,mBAAoB,CACpB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,OAAQ,CACR,aAAc,CACd,cAAe,CArRvC,iNA0RoB,eAAgB,CAChB,kCAA2B,CAA3B,0BAA2B,CAC3B,iBAAkB,CAClB,SAAU,CACV,sC9BtRL,C8BuRK,wBAAyB,CACzB,SAAU,CACV,iBAAkB,CAClB,wCAAiC,CAAjC,gCAAiC,CACjC,QAAS,CACT,OAAQ,CACR,YAAa,CACb,kBAAmB,CACnB,yBAAO,CAAP,sBAAO,CAAP,iBAAkB,CA6DrB,qPA1DO,MAAO,CACP,WAAY,CA3SpC,g7BAkTwB,cAAe,CACf,2BAAoB,CAApB,mBAAoB,C1B3SxC,mB0B4SsD,C1B5StD,mB0B4SsD,C1B5StD,Y0B4SsD,C1B3StD,wB0B2SsE,C1B3StE,qB0B2SsE,C1B3StE,kB0B2SsE,CAClD,WAAY,CACZ,2B9B9ST,C8B+SS,4BAA6B,CAC7B,QAAS,CACT,cAAe,CACf,SAAU,CACV,kBAAmB,CA3T3C,w8BA8T4B,2B9BtTb,C8BuTa,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,QAAS,CAjUrC,w8BAsU4B,0BAAgB,CAtU5C,g+BA0U4B,0BAA2B,CAC3B,qBAAsB,CACtB,sBAAuB,CA5UnD,w/BAgV4B,U9BxUb,C8ByUa,iCAAW,CAAX,yBAA0B,CAjVtD,ghCAoVgC,U9B5UjB,C8BRf,ghCAwVgC,qBAAgB,CACnB,woCAGG,kCAAW,CAAX,0BAA2B,CA5V3D,o9BAiW4B,eAAgB,CAjW5C,mOAwWwB,SAAU,CACV,kBAAmB,CACnB,sBAAW,CAAX,cAAe,CA1WvC,uDAiXoB,UAAW,CACX,WAAY,CACZ,kBAAmB,CAnXvC,qEAuXoB,QAAS,CAvX7B,mCA4XgB,oBAAe,CACf,sBAAgB,CA7XhC,sCAmYY,WAAY,CACZ,SAAU,CApYtB,2D1B4BI,U0B2W0C,C1B1W1C,W0B0WyD,CAvY7D,4CA2YgB,uBAAY,CAAZ,eAAgB,CA3YhC,mDAiZgB,+DAAgE,CAjZhF,uEAqZwB,mEAAoE,CACpE,2B9B9YT,C8B+YS,cAAe,CACf,eAAgB,CAxZxC,8EA2Z4B,eAAgB,CAChB,U9BpZb,CEWP,2B4BnBR,uCAmagB,cAAe,CACf,KAAM,CACN,sBAAuB,CACvB,WAAY,CACZ,oEAAqE,CACrE,WAAY,CACZ,uCAAgC,CAAhC,+BAAgC,CAChC,iBAAkB,CAClB,SAAU,CA3a1B,sDA8aoB,YAAa,C1B5a7B,mB0B6agD,C1B7ahD,mB0B6agD,C1B7ahD,Y0B6agD,C1B5ahD,wB0B4agE,C1B5ahE,qB0B4agE,C1B5ahE,kB0B4agE,C1B3ahE,wB0B2ayF,C1B3azF,qB0B2ayF,C1B3azF,6B0B2ayF,CACzE,gD9BtaN,C8BsaM,wC9BtaC,C8BVrB,yDAmbwB,a9BlbD,C8BmbC,eAAgB,CAChB,gBAAiB,CArbzC,wDAybwB,qBAAgB,CAChB,wBAAgB,CA1bxC,mDA+boB,YAAa,CACb,WAAY,CACZ,QAAS,CACT,aAAc,CAlclC,uEAsc4B,a9B5bP,C8B6bO,SAAU,CAvctC,4CA6coB,uCAAgC,CAAhC,+BAAgC,CAChC,MAAO,CACP,SAAU,CACV,kBAAmB,CACnB,SAAU,CAEb,CCndjB,gBACI,wB/BSiB,C+BRjB,mEAAoE,CAFxE,2B3BEI,mB2BGoC,C3BHpC,mB2BGoC,C3BHpC,Y2BGoC,C3BFpC,wB2BEoD,C3BFpD,qB2BEoD,C3BFpD,kB2BEoD,C3BDpD,wB2BC6E,C3BD7E,qB2BC6E,C3BD7E,6B2BC6E,CACzE,QAAS,CACT,8C/BCO,C+BAP,mBAAoB,CACpB,kBAAmB,CAT3B,4CAYgB,WAAY,CAZ5B,6BAgBY,U/BRG,C+BSH,QAAS,CACT,cAAe,C7BCnB,0B6BnBR,2BAqBY,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAEzB,CAxBL,kBA2BQ,oBAAe,CACf,wBAAgB,CA5BxB,gCAgCQ,eAAgB,CAChB,gBAAiB,CACjB,U/B1BO,C+B2BP,kBAAmB,C3B3BvB,mB2B4BsC,C3B5BtC,mB2B4BsC,C3B5BtC,Y2B4BsC,C3B3BtC,wB2B2BsD,C3B3BtD,qB2B2BsD,C3B3BtD,kB2B2BsD,CAClD,OAAQ,CACR,mEAAoE,C7BnBpE,0B6BnBR,gCAwCY,iBAAkB,CAEzB,CA1CL,mBA6CQ,gBAAiB,CACjB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,C7B7BT,0B6BnBR,mBAmDY,OAAQ,CAUf,CA7DL,sBAsDY,2B/B9CG,C+B+CH,mEAAoE,CAvDhF,gCAyDgB,2B/BjDD,C+BkDC,SAAU,CA1D1B,qBAgEQ,kBAAmB,CACnB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,sEAAuE,CACvE,eAAgB,CAChB,gBAAiB,CACjB,mEAAoE,C7BlDpE,0B6BnBR,4CA6EwB,mBAAS,CAAT,mBAAS,CAAT,YAAa,CA7ErC,uCAiFoB,YAAa,CAChB,CClFjB,sCAEI,iBAAkB,CAFtB,0EAKQ,kBAAmB,CA6ItB,kGA1IO,iBAAkB,CAClB,kBAAmB,CAT/B,4GAcgB,iBAAkB,CAdlC,oI5BQI,mB4BSkD,C5BTlD,mB4BSkD,C5BTlD,Y4BSkD,C5BRlD,wB4BQkE,C5BRlE,qB4BQkE,C5BRlE,kB4BQkE,CAClD,QAAS,CAlB7B,4IAqBwB,UAAW,CACX,WAAY,CACZ,kBAAmB,CAvB3C,sJA0B4B,UAAW,CACX,WAAY,CA3BxC,8TAgC4B,iBAAkB,CAClB,+DAAgE,CAChE,iEAAkE,CAClE,iEAAkE,CAClE,kEAAmE,CACnE,eAAgB,C9BlBpC,2B8BnBR,8TAwCgC,+DAAgE,CAChE,gEAAiE,CAExE,CA3CzB,sKA8C4B,YAAa,CA9CzC,wJAkD4B,aAAc,CAlD1C,sIAwDoB,gBAAiB,CACjB,eAAgB,CAChB,uChChDC,CgCVrB,0IA6DwB,QAAS,CACT,ahCnDH,CgCoDG,cAAe,CACf,gBAAiB,CACjB,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,sBAAuB,CACvB,oBAAqB,CACrB,2BAA4B,CAtEpD,0GA6EY,YAAa,CA7EzB,sIAgFgB,mCAA4B,CAA5B,2BAA4B,CAC5B,kCAAW,CAAX,0BAA2B,CAjF3C,8KAoFoB,iCAAW,CAAX,yBAA0B,CApF9C,0LAuFwB,iChCtFD,CgCuFC,qChCvFD,CgCDvB,8OA4FgC,aAAc,CA5F9C,gOAgGgC,YAAa,CAhG7C,4FAyGY,SAAU,CACV,SAAU,CACV,WAAY,CACZ,iBAAkB,CAClB,6CAAsC,CAAtC,qCAAsC,CACtC,0BAAiB,CAAjB,kBAAmB,C9B3FvB,0B8BnBR,4FAiHgB,YAAa,CAEpB,CAnHT,gHAsHY,MAAO,CACP,UAAW,CACX,OAAQ,CACR,aAAc,CACd,QAAS,CA1HrB,oKA6HgB,UAAW,CACX,SAAU,CACV,wBhC9HO,CgCDvB,oHAoIY,QAAS,CACT,SAAU,CACV,SAAU,CACV,SAAU,CACV,UAAW,CAxIvB,oHA4IY,QAAS,CACT,WAAY,CACZ,SAAU,CACV,QAAS,CACT,SAAU,CACb,iEAQG,mEAAoE,CACpE,eAAgB,CAChB,iBAAkB,CAN9B,+DAUY,mEAAoE,CACpE,ahCrJS,CgCsJT,eAAgB,CAZ5B,mEAegB,ahCnKO,CgCoJvB,2FAuBY,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,wBhC9KW,CgC+KX,UhCxKG,CgCyKH,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,WAAY,CACZ,kBAAmB,CAhC/B,+FAmCgB,oBAAe,CACf,qBAAgB,CApChC,qEA0CY,mEAAoE,CACpE,WAAY,CACZ,cAAe,CACf,SAAU,CACV,eAAgB,CAChB,uBhC1LS,CgC2IrB,6FAmDY,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,kBAAe,CAAf,cAAe,CACf,2DAA4D,CAC5D,eAAgB,CAxD5B,mHA2DgB,mEAAoE,CACpE,iBAAkB,CAClB,kChCxMK,CgCyML,mEAAoE,CACpE,eAAgB,CAChB,eAAgB,CAChB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,OAAQ,CACR,uBhC/MK,CgC2IrB,uHAuEoB,yEAAe,CACf,wBAAgB,CACnB,qBAUT,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,QAAS,CAJjB,yBAOY,WAAY,CACZ,SAAU,CACV,kBAAmB,CAT/B,yCAYgB,eAAgB,CAZhC,0BAkBQ,uBhC9Oa,CgC4NrB,4BAqBY,mEAAoE,CACpE,eAAgB,CAChB,kBAAmB,CAvB/B,sCA2BY,eAAgB,CAChB,QAAS,CACT,6DAA8D,CA7B1E,yCAgCgB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,QAAS,CAlCzB,iDAqCoB,YAAa,CArCjC,mDA0CoB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CACnB,gEAAiE,CACjE,+DAAgE,CAChE,mEAAoE,CACpE,wBhCtRG,CgCqOvB,2CAqDoB,oBAAe,CACf,qBAAgB,CAtDpC,oDA2DwB,eAAgB,CAChB,eAAgB,CAChB,iBAAkB,CA7D1C,mDAiEwB,eAAgB,CAChB,mEAAoE,CACpE,eAAgB,CACnB,6BASb,UAAW,CAFnB,4CAOY,6ChC9SS,CgC8ST,qChC9SS,CgC+ST,iBAAkB,CAR9B,uDAWgB,YAAa,CAX7B,oEAcoB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,iBAAkB,CAClB,QAAS,CACT,WAAY,CACZ,SAAU,CACV,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CArBvC,sEAwBwB,yEAAe,CACf,wBAAgB,CAzBxC,4EA6BwB,UAAW,CACX,WAAY,CACZ,kBAAmB,CA/B3C,wEAmCwB,wBhCxUJ,CgCyUI,UAAW,CACX,UAAW,CArCnC,uEAyCwB,cAAe,CACf,ahChVH,CgCsSrB,0EA8CwB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,cAAe,CACf,OAAQ,CAhDhC,sEAoDwB,uBhC3VH,CgC4VG,cAAe,CACf,aAAc,CACd,cAAe,CAvDvC,8CA+DY,aAAc,CA/D1B,8DAkEgB,WAAY,CACZ,aAAc,CAnE9B,4EAsEoB,uBAAgB,CAAhB,eAAgB,CAChB,uBAAY,CAAZ,eAAgB,CAvEpC,gGA0EwB,sBAAW,CAAX,cAAe,CA1EvC,sGA6E4B,WAAY,CACZ,sBAAuB,CAC1B,sCAYb,2HAA4H,CAC5H,iBAAkB,CAClB,wBhCjYQ,CgCkYR,iBAAkB,CAN9B,yCASgB,kEAAmE,CACnE,eAAgB,CAChB,eAAgB,CAChB,ahCpZO,CgCwYvB,wCAgBgB,kEAAmE,CACnE,iBAAkB,CAClB,oBAAqB,CACrB,eAAgB,CAChB,uBhCnZK,CgCoZR,gBC7ZT,UAAW,CACX,aAAc,CACd,aAAc,CACd,iBAAkB,CAClB,YAAa,CALjB,+BAQQ,WAAY,CACf,eAID,wBjCZmB,CiCanB,YAAa,CACb,iBAAkB,CAClB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,C7BdtB,mB6BegC,C7BfhC,mB6BegC,C7BfhC,Y6BegC,C7BdhC,wB6BcgD,C7BdhD,qB6BcgD,C7BdhD,kB6BcgD,C7BbhD,uB6BakE,C7BblE,oB6BakE,C7BblE,sB6BakE,CAClE,WAAY,CANhB,qBASQ,WAAY,CACZ,aAAc,CACd,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAmB,CAZ3B,2BAgBQ,SAAU,CAhBlB,6BAoBQ,UjCxBO,CiCyBP,eAAgB,CArBxB,gCAwBY,wBAAyB,CACzB,gBAAiB,CACjB,eAAgB,CA1B5B,+BA8BY,cAAe,CACf,gBAAiB,CA/B7B,0CAmCY,eAAgB,C7B7CxB,mB6B8CwC,C7B9CxC,mB6B8CwC,C7B9CxC,Y6B8CwC,C7B7CxC,wB6B6CwD,C7B7CxD,qB6B6CwD,C7B7CxD,kB6B6CwD,C7B5CxD,uB6B4C0E,C7B5C1E,oB6B4C0E,C7B5C1E,sB6B4C0E,CAClE,QAAS,CArCrB,8CAwCgB,WAAY,CACf,YAMT,wBjC9CgB,CiC+ChB,WAAY,CACZ,iBAAkB,C7B3DlB,mB6B4DgC,C7B5DhC,mB6B4DgC,C7B5DhC,Y6B4DgC,C7B3DhC,wB6B2DgD,C7B3DhD,qB6B2DgD,C7B3DhD,kB6B2DgD,C7B1DhD,uB6B0D4D,C7B1D5D,oB6B0D4D,C7B1D5D,sB6B0DkE,CAJtE,+BAOQ,iBAAkB,CAClB,QAAS,CACT,UAAW,C7BjEf,mB6BkEoC,C7BlEpC,mB6BkEoC,C7BlEpC,Y6BkEoC,C7BjEpC,wB6BiEoD,C7BjEpD,qB6BiEoD,C7BjEpD,kB6BiEoD,C7BhEpD,oB6BgEmE,C7BhEnE,iB6BgEmE,C7BhEnE,mB6BgEmE,CAC/D,OAAQ,CACR,eAAgB,CAChB,kBAAmB,CACnB,kCjC9Da,CiC+Db,qBjCjEO,CiCkEP,kBAAmB,CACnB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,gBAAiB,CAlBzB,mCAqBY,UAAW,CACX,WAAY,CACZ,kBAAmB,CAvB/B,iCA2BY,oBAAe,CACf,wBAAgB,CA5B5B,6CAgCY,sBAAuB,CACvB,ajCjFS,CiCkFT,WAAY,CACZ,aAAc,CACd,SAAU,C7BtFlB,mB6BuF0C,C7BvF1C,mB6BuF0C,C7BvF1C,Y6BuF0C,C7BtF1C,wB6BsF0D,C7BtF1D,qB6BsF0D,C7BtF1D,kB6BsF0D,CAClD,QAAS,CAtCrB,kD7BlDI,0B6B2FqD,C7B3FrD,0B6B2FqD,C7B3FrD,mB6B2FqD,C7B1FrD,wB6B0FqE,C7B1FrE,qB6B0FqE,C7B1FrE,kB6B0FqE,CACzD,OAAQ,CACR,aAAc,CACd,cAAe,CA5C/B,iDAiDY,eAAgB,CAChB,kCAA2B,CAA3B,0BAA2B,CAC3B,iBAAkB,CAClB,SAAU,CACV,kCjCrGS,CiCsGT,qBjCxGG,CiCyGH,SAAU,CACV,iBAAkB,CAClB,wCAAiC,CAAjC,gCAAiC,CACjC,QAAS,CACT,OAAQ,CACR,YAAa,CACb,iBAAkB,CAsCrB,6DAlCO,MAAO,CACP,WAAY,CAlE5B,uDAsEgB,cAAe,CACf,2BAAoB,CAApB,mBAAoB,C7BzHhC,mB6B0H8C,C7B1H9C,mB6B0H8C,C7B1H9C,Y6B0H8C,C7BzH9C,wB6ByH8D,C7BzH9D,qB6ByH8D,C7BzH9D,kB6ByH8D,CAClD,WAAY,CACZ,ajC1HK,CiC2HL,4BAA6B,CAC7B,OAAQ,CACR,cAAe,CACf,SAAU,CA9E1B,2DAiFoB,0BAA2B,CAC3B,qBAAsB,CACtB,sBAAuB,CAnF3C,6DAuFoB,wBAAyB,CACzB,iCAAW,CAAX,yBAA0B,CAK7B,yEAFO,kCAAW,CAAX,0BAA2B,CA3FnD,0DAgGoB,cAAe,CAhGnC,uDAuGgB,SAAU,CACV,kBAAmB,CACnB,sBAAW,CAAX,cAAe,CAzG/B,wBA+GQ,UAAW,C7BvKf,mB6BwKoC,C7BxKpC,mB6BwKoC,C7BxKpC,Y6BwKoC,C7BvKpC,wB6BuKoD,C7BvKpD,qB6BuKoD,C7BvKpD,kB6BuKoD,C7BtKpD,uB6BsKsE,C7BtKtE,oB6BsKsE,C7BtKtE,sB6BsKsE,CAClE,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,sBAAuB,CACvB,WjC/JU,CiCgKV,kBjC/JO,CiCgKP,gBAAiB,CArHzB,qCAwHY,UAAW,CACX,gCjCvKQ,CiCwKR,YAAa,CACb,iBAAkB,CA3H9B,wCA8HgB,eAAgB,CAChB,gBAAiB,CA/HjC,uCAmIgB,ajClLK,CiCmLL,eAAgB,CAChB,cAAe,CACf,QAAS,CAtIzB,sCA2IY,wBjCxLQ,CiCyLR,kBAAmB,CACnB,uBAAwB,CACxB,aAAc,CACd,iEAAkE,CA/I9E,kDAkJgB,iBAAkB,CAlJlC,oDAqJoB,yEAAe,CACf,wBAAgB,CAChB,iBAAkB,CAClB,6DAA8D,CAC9D,SAAU,CAzJ9B,mEA8JoB,iBAAkB,CAClB,6DAA8D,CAC9D,UAAW,CACX,UAAW,CACX,cAAe,CAlKnC,qEAqKwB,eAAgB,CArKxC,8EAyKwB,YAAa,CAzKrC,6EA8K4B,aAAc,CA9K1C,mFAkL4B,YAAa,CAlLzC,yFAwL4B,aAAc,CAxL1C,mFA4L4B,YAAa,CA5LzC,wDAkMoB,mEAAoE,CACpE,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAClB,iBAAkB,CAtMtC,+DAyMwB,iBAAkB,CAClB,UAAW,CACX,wBjCpQD,CiCqQC,WAAY,CACZ,SAAU,CACV,QAAS,CACT,UAAW,CACX,iBAAkB,CAhN1C,gEAqNoB,iBAAkB,CAClB,oLAAqL,CACrL,qBjCzQL,CiC0QK,uBjCxQC,CiCyQD,WAAY,CAzNhC,sEA4NwB,uBAAY,CAAZ,eAAgB,CA5NxC,mDAkOgB,eAAgB,CAChB,cAAe,CAnO/B,qDAsOoB,cAAe,CACf,gBAAiB,CAvOrC,2CA4OgB,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CACX,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,cAAe,CAhP/B,iDAmPoB,uBAAY,CAAZ,eAAgB,CAnPpC,kDAwPgB,iBAAkB,CAClB,cAAe,CACf,eAAgB,CAChB,ajC1SK,CiC2SL,gBAAiB,CACjB,gBAAiB,CA7PjC,wDAgQoB,ajCzTG,CiC0TH,eAAgB,CAjQpC,uCAwQY,UAAW,CACX,sBAAuB,CAzQnC,iCA6QY,iBAAkB,CAClB,iBAAkB,CAClB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,aAAc,CACd,ajChUS,CiCiUT,cAAe,CACf,mBAAS,CAAT,mBAAS,CAAT,YAAa,CAnRzB,+EAuRgB,iBAAkB,CAClB,UAAW,CACX,wBjCxUK,CiCyUL,UAAW,CACX,UAAW,CACX,OAAQ,CACR,kCAAW,CAAX,0BAA2B,CA7R3C,wCAiSgB,UAAW,CAjS3B,uCAqSgB,WAAY,CArS5B,sC7BxDI,mB6BkWwC,C7BlWxC,mB6BkWwC,C7BlWxC,Y6BkWwC,C7BjWxC,wB6BiWwD,C7BjWxD,qB6BiWwD,C7BjWxD,kB6BiWwD,C7BhWxD,uB6BgW0E,C7BhW1E,oB6BgW0E,C7BhW1E,sB6BgW0E,CAClE,QAAS,CACT,eAAgB,CA5S5B,mDA+SgB,wBjC5VI,CiC6VJ,kBAAmB,CACnB,kCjCjWK,CiCkWL,WAAY,CACZ,UAAW,C7B3WvB,mB6B4W4C,C7B5W5C,mB6B4W4C,C7B5W5C,Y6B4W4C,C7B3W5C,wB6B2W4D,C7B3W5D,qB6B2W4D,C7B3W5D,kB6B2W4D,C7B1W5D,uB6B0WwE,C7B1WxE,oB6B0WwE,C7B1WxE,sB6B0W8E,C/B3V1E,0B+BuCR,wBAyTY,UAAW,CAElB,CAIL,SAEQ,QAAS,CACT,ajCjXa,CiCkXhB,WAID,WAAY,CACZ,gBAAiB,CACpB,W7B3XG,mB6B8XkC,C7B9XlC,mB6B8XkC,C7B9XlC,Y6B8XkC,C7B7XlC,wB6B6XkD,C7B7XlD,qB6B6XkD,C7B7XlD,kB6B6XkD,CAClD,QAAS,CAFb,iBAKQ,UAAW,CACX,WAAY,CACZ,WAAY,CACZ,qBjCrYO,CiCsYP,iBAAkB,CAClB,iBAAkB,CACrB,4BC9YG,wBlCWY,CkCVZ,iEAAkE,CAClE,iBAAkB,CAClB,kBAAmB,CAL3B,uCAQY,eAAgB,CAR5B,uCAYY,mEAAoE,CACpE,8BlCDQ,CEOZ,0BgCnBR,uCAgBgB,WAAY,CACZ,cAAe,CAatB,ChC1BD,0BgCJR,mDAsBoB,iBAAkB,CAtBtC,kDA0BoB,kBAAmB,CACnB,WAAY,CACf,CA5BjB,kCAiCY,4BAA6B,CAC7B,WAAY,CACZ,eAAgB,CAChB,eAAgB,CApC5B,6CAuCgB,SAAU,CACV,alC7BK,CkCXrB,gDA2CoB,eAAgB,CAChB,mEAAoE,CACpE,eAAgB,CAChB,gBAAiB,CACjB,alCrCC,CkCsCD,kBAAmB,CACnB,UAAW,CACX,eAAgB,CAlDpC,kDAqDwB,alC3CH,CkCVrB,wDA0D4B,alCzDL,CkCDvB,+CAkEoB,oBAAe,CACf,wBAAgB,CAnEpC,0D9BEI,mB8BqEgD,C9BrEhD,mB8BqEgD,C9BrEhD,Y8BqEgD,C9BpEhD,wB8BoEgE,C9BpEhE,qB8BoEgE,C9BpEhE,kB8BoEgE,C9BnEhE,wB8BmEyF,C9BnEzF,qB8BmEyF,C9BnEzF,6B8BmEyF,CACzE,OAAQ,CACR,eAAgB,CAChB,mEAAoE,CA1ExF,2BAiFQ,aAAc,CACd,cAAe,CACf,alCxEa,CIHjB,mB8B4EsC,C9B5EtC,mB8B4EsC,C9B5EtC,Y8B4EsC,C9B3EtC,wB8B2EsD,C9B3EtD,qB8B2EsD,C9B3EtD,kB8B2EsD,CAClD,kBAAe,CAAf,cAAe,CACf,2DAA4D,CAtFpE,8BAyFY,mEAAoE,CACpE,wClChFS,CkCiFT,qEAAsE,CA3FlF,0CA8FgB,cAAe,CA9F/B,yCAkGgB,eAAgB,CAChB,WAAY,CACf,0CAGG,oEAAqE,CACrE,mBAAoB,CAWvB,sDARO,kBAAmB,CACnB,eAAgB,CACnB,qDAGG,mBAAoB,CACpB,cAAe,CAjHnC,iBAwHQ,mEAAoE,CACpE,eAAgB,CAChB,gBAAiB,CACjB,alCjHa,CkCkHb,gBAAiB,CA5HzB,0BAgIQ,iEAAkE,CAClE,kBAAmB,CAjI3B,gCAoIY,mEAAoE,CACpE,eAAgB,CAChB,kBAAmB,CAtI/B,yCA0IY,sEAAuE,CAC1E,cC1IJ,kBAAmB,CACvB,eAGI,iBAAkB,CAClB,aAAc,CACd,kEAAmE,CACnE,mEAAoE,CACpE,eAAgB,CACpB,2BAIS,WAAY,CAFtB,6CAIe,4BAA6B,CAC7B,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,SAAU,CACV,WAAY,CACZ,uBAAY,CAAZ,eAAgB,CAR/B,mEAWyB,uDAAwD,CAC5D,4BCtBb,mEAAoE,CACpE,eAAgB,CAChB,uEAAwE,CACxE,uBpCKa,CoCVrB,mDAUY,YAAa,CACb,kBAAmB,CACnB,wBpCAQ,CoCCR,qBpCLG,CoCRf,gDAiBY,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CAnBrB,mDAsBgB,iBAAkB,CAClB,mEAAoE,CACpE,eAAgB,CAChB,eAAgB,CAChB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,OAAQ,CACR,apClBK,CoCmBL,UAAW,CA9B3B,qDAiCoB,yEAAe,CACf,wBAAgB,CAlCpC,2DAsCoB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,OAAQ,CACR,SAAU,CAzC9B,8DA4CwB,apClCH,CoCmCG,eAAgB,CA7CxC,6DAiDwB,uBpCvCH,CoCwCG,eAAgB,CAChB,mEAAoE,CACvE,kBASb,UAAW,CACX,mEAAoE,CAH5E,yBAMY,UAAW,CACX,WAAY,CACf,YClEL,oBAAqB,CACrB,sBAAuB,CAF3B,uBAKQ,kBAAmB,CACnB,iBAAkB,CAN1B,0BASY,cAAe,CACf,oBAAqB,CACrB,eAAgB,CAChB,eAAgB,CAChB,arCHS,CqCVrB,yBAiBY,yBAA0B,CAC1B,uBrCRS,CqCST,0BAA2B,CAC3B,kBAAmB,CApB/B,eAyBQ,kBAAmB,CACtB,gBCvBD,iBAAkB,CAClB,gDAAiD,CACjD,2BAA4B,CAC5B,0BAA2B,CAC3B,qBAAsB,CACtB,SAAU,CACb,yCAMe,UtCPD,CsCEf,+CAOoB,atChBG,CsCSvB,sDAcQ,gBAAiB,CACjB,eAAgB,CAfxB,wKAkBY,kBAAmB,CACnB,iBAAkB,CAnB9B,wLAsBgB,gBAAiB,CACjB,kBAAmB,CACnB,WAAY,CACZ,YAAa,CACb,mBAAiB,CAAjB,gBAAiB,CACjB,uCtC7BD,CEWP,0BoCTR,wLA6BoB,WAAY,CACZ,YAAa,CAQpB,CAtCb,4MAkCoB,QAAS,CACT,WAAY,CACZ,eAAgB,CApCpC,oNAwCgB,QAAS,CACT,UAAW,CACX,UAAW,CA1C3B,4EA+CY,cAAe,CA/C3B,oGlCRI,mBkC0D4C,ClC1D5C,mBkC0D4C,ClC1D5C,YkC0D4C,ClCzD5C,wBkCyD4D,ClCzD5D,qBkCyD4D,ClCzD5D,kBkCyD4D,ClCxD5D,uBkCwD8E,ClCxD9E,oBkCwD8E,ClCxD9E,sBkCwD8E,CAClE,QAAS,CACT,UtCtDD,CsCEf,0GAuDoB,cAAe,CACf,eAAgB,CAxDpC,gHA4DoB,cAAe,CA5DnC,sGAiEgB,cAAe,CACf,eAAgB,CAChB,2CtCrED,CsCEf,0GAsEoB,cAAe,CACf,gBAAiB,CACjB,SAAU,CACV,aAAc,CACd,iBAAkB,CA1EtC,sCAkFY,atCjFS,CsCDrB,kDAqFgB,atCrFK,CsCArB,oDAuFoB,atCvFC,CsCArB,0DAyFwB,atClGD,CsCSvB,mDA+FgB,6BtC7FI,CsC8FP,cAMT,wBtCnGgB,CsCoGhB,kBAAmB,CACnB,YAAa,CAHjB,iBAKQ,eAAgB,CAChB,eAAgB,CANxB,gCAUY,atC9GS,CsC+GT,cAAe,CAClB,UlCzHL,mBkC8HgC,ClC9HhC,mBkC8HgC,ClC9HhC,YkC8HgC,ClC7HhC,wBkC6HgD,ClC7HhD,qBkC6HgD,ClC7HhD,kBkC6HgD,ClC5HhD,uBkC4HkE,ClC5HlE,oBkC4HkE,ClC5HlE,sBkC4HkE,CAClE,OAAQ,CAFZ,YAKQ,oBAAe,CACf,wBAAgB,CANxB,aAUQ,gBAAiB,CACjB,atC/Ha,CsCgIhB,cAKG,wBtCnIY,CsCiIpB,2BAIY,iBAAkB,CAClB,4BAA6B,CAC7B,+BtCxIQ,CIVhB,mBkCmJwC,ClCnJxC,mBkCmJwC,ClCnJxC,YkCmJwC,ClClJxC,wBkCkJwD,ClClJxD,qBkCkJwD,ClClJxD,kBkCkJwD,ClCjJxD,wBkCiJoE,ClCjJpE,qBkCiJoE,ClCjJpE,6BkCiJiF,CAPrF,8BASgB,eAAgB,CAThC,yBAaY,aAAc,CACd,YAAa,CAdzB,qClCtII,mBkCsJ8C,ClCtJ9C,mBkCsJ8C,ClCtJ9C,YkCsJ8C,ClCrJ9C,wBkCqJ8D,ClCrJ9D,qBkCqJ8D,ClCrJ9D,kBkCqJ8D,CAClD,OAAQ,CAjBxB,uBAsBQ,wBtCvJY,CsCwJZ,kBAAmB,CAvB3B,kCAyBY,QAAS,CACT,UAAW,CACX,UAAW,CA3BvB,0DA+BgB,WAAY,CA/B5B,sEAiCoB,wBtCnKA,CsCkIpB,wEAqCwB,iBAAkB,CAClB,WAAY,CACZ,YAAa,CAvCrC,4DA2CoB,oBAAe,CACf,wBAAgB,CA5CpC,qEA+CoB,qBtCrLL,CsCsLK,iBAAkB,CAClB,iBAAkB,CAClB,UAAW,ClC9L3B,mBkC+LgD,ClC/LhD,mBkC+LgD,ClC/LhD,YkC+LgD,ClC9LhD,wBkC8LgE,ClC9LhE,qBkC8LgE,ClC9LhE,kBkC8LgE,ClC7LhE,wBkC6LyF,ClC7LzF,qBkC6LyF,ClC7LzF,6BkC6LyF,CACzE,kBAAmB,CApDvC,0EAsDwB,cAAe,CAtDvC,2EAyDwB,cAAe,CACf,eAAgB,CAChB,atCxMD,CsC6IvB,4EAgEwB,atCpMH,CsCqMG,eAAgB,CAjExC,wEAsEoB,qBtC5ML,CsC6MK,YAAa,CAvEjC,0EAyEwB,atC7MH,CsC8MG,gBAAiB,CA1EzC,8ElCtII,mBkCmNsD,ClCnNtD,mBkCmNsD,ClCnNtD,YkCmNsD,ClClNtD,wBkCkNsE,ClClNtE,qBkCkNsE,ClClNtE,kBkCkNsE,CAClD,OAAQ,CACR,cAAe,CA/EvC,0EAmFwB,oBAAe,CAnFvC,wFAsFwB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACpB,mCASb,kBAAmB,CACnB,wBtCrOY,CsCsOZ,YAAa,CAJrB,+CAOY,wBtCxOQ,CsCyOR,WAAY,CACZ,kBAAmB,CAT/B,qCAYY,oBAAe,CACf,wBAAgB,CAb5B,4ClCtOI,mBkCsP0C,ClCtP1C,mBkCsP0C,ClCtP1C,YkCsP0C,ClCrP1C,wBkCqP0D,ClCrP1D,qBkCqP0D,ClCrP1D,kBkCqP0D,CAClD,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,gBAAiB,CAlB7B,iDAoBgB,kBAAmB,CACnB,UAAW,CACX,WAAY,CACZ,qBtC7PD,CsCsOf,kDA0BgB,cAAe,CA1B/B,mDA8BY,eAAgB,CA9B5B,sDAgCgB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,iBAAkB,CAlClC,yDlC5OI,mBkCgRgD,ClChRhD,mBkCgRgD,ClChRhD,YkCgRgD,ClC/QhD,wBkC+QgE,ClC/QhE,qBkC+QgE,ClC/QhE,kBkC+QgE,ClC9QhE,wBkC8QyF,ClC9QzF,qBkC8QyF,ClC9QzF,6BkC8QyF,CACzE,OAAQ,CACR,mEAAoE,CACpE,+BtCzQA,CsC0QA,cAAe,CAxCnC,oEA0CwB,WAAY,CpCrQ5B,0BoC2NR,yDA6CwB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,KAAM,CAEb,CAjDjB,0CAqDY,iEAAkE,CAClE,kBAAmB,CACnB,UAAW,CACX,gBAAiB,ClC9RzB,mBkC+R0C,ClC/R1C,mBkC+R0C,ClC/R1C,YkC+R0C,ClC9R1C,wBkC8R0D,ClC9R1D,qBkC8R0D,ClC9R1D,kBkC8R0D,CAClD,QAAS,CACT,kBAAmB,CA3D/B,qCA8DY,cAAe,CACf,atClSS,CsCmST,gBAAiB,CACjB,iBAAkB,CAjE9B,8CAoEY,+DAAgE,CAChE,gEAAiE,CACpE,WAKL,iBAAkB,CAClB,SAAU,CACV,kBAAmB,CpCxSf,2BoCqSR,WAKQ,UAAW,CAElB,CAGD,+CAEI,iBAAkB,CAFtB,yDAKQ,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CAL3B,qFASQ,iBAAkB,CAClB,iEAAkE,CAClE,+DAAgE,CpC1ThE,2BoC+SR,qFAaY,iEAAkE,CAClE,+DAAgE,CAiBvE,CpC9UG,2BoC+SR,qFAiBY,iEAAkE,CAClE,+DAAgE,CAavE,CpC9UG,2BoC+SR,qFAqBY,iEAAkE,CAClE,UAAW,CASlB,CpC9UG,2BoC+SR,qFAyBY,QAAS,CACT,SAAU,CAKjB,CpC9UG,0BoC+SR,qFA6BW,YAAa,CAEnB,CA/BL,yEAkCQ,2CAAoC,CAApC,mCAAoC,CACpC,kBAAmB,CACnB,UtC9VO,CsC+VP,YAAa,CACb,sFAAyF,CAAzF,4DAAyF,CACzF,WAAY,CACZ,iBAAkB,CAxC1B,6EA2CY,QAAS,CACT,cAAe,CA5C3B,+EAiDY,2CtC3WG,CsC4WH,cAAe,CACf,eAAgB,CAChB,iBAAkB,CAClB,WAAY,CArDxB,6FAwDgB,cAAe,CACf,iBAAkB,CAClB,MAAO,CACP,OAAQ,CACR,0CAAmC,CAAnC,kCAAmC,CACnC,4BAAiB,CAAjB,oBAAqB,CA7DrC,6DAmEQ,iBAAkB,CAClB,SAAU,CACV,QAAS,CACT,kCAAW,CAAX,0BAA2B,CAtEnC,6FA2EY,iBAAkB,CAClB,UAAW,CACX,YAAa,CACb,WAAY,CACZ,6CAAsC,CAAtC,qCAAsC,CACtC,0BAAmB,CAAnB,kBAAmB,CACnB,WAAY,CACZ,eAAgB,CpCjYpB,0BoC+SR,6FAoFgB,YAAa,CAEpB,CAtFT,6FAyFY,iBAAkB,CAClB,6CAAsC,CAAtC,qCAAsC,CACtC,UAAW,CACX,SAAU,CACV,SAAU,CACV,0BAAmB,CAAnB,kBAAmB,CACnB,YAAa,CpC9YjB,0BoC+SR,6FAiGgB,YAAa,CAEpB,CAnGT,8LAuGY,aAAc,CACd,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,UAAW,CAzGvB,6FA6GY,iBAAkB,CAClB,KAAM,CACN,QAAS,CACT,WAAY,CACf,aAOL,eAAgB,ClClbhB,mBkCmbkC,ClCnblC,mBkCmbkC,ClCnblC,YkCmbkC,ClClblC,wBkCkbkD,ClClblD,qBkCkbkD,ClClblD,kBkCkbkD,CAClD,QAAS,CAHb,iBAMQ,WAAY,CpC5aZ,0BoCsaR,aASQ,QAAS,CACT,eAAgB,CAVxB,iBAYY,gEAAiE,CACpE,CAIT,gBACI,atChciB,CsC+brB,kBAIQ,mEAAoE,CACpE,gBAAiB,CACjB,kBAAmB,CpC7bnB,0BoCubR,kBAQY,gBAAiB,CACjB,iBAAkB,CAEzB,CAXL,4BAcQ,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CACT,gBAAiB,CpCxcjB,0BoCubR,4BAmBY,2DAA4D,CAoBnE,CAvCL,+BAuBY,mEAAoE,CACpE,iBAAkB,CAClB,gBAAiB,CAzB7B,sCA4BgB,iBAAkB,CAClB,UAAW,CACX,SAAU,CACV,UAAW,CACX,wBtC/dK,CsCgeL,QAAS,CACT,UAAW,CACX,kBAAmB,CAEtB,WAMT,iBAAkB,CAClB,WAAY,CACZ,UAAW,CACX,kBAAmB,CACnB,6CAAsC,CAAtC,qCAAsC,CACtC,0BAAiB,CAAjB,kBAAmB,CpCtff,0BoCgfR,WAQQ,kEAAmE,CAY1E,CpCrfO,2BoCieR,WAYQ,SAAU,CAQjB,CpCrfO,0BoCieR,WAgBQ,+DAAgE,CAChE,iEAAkE,CAClE,kEAAmE,CAE1E,CAED,WACI,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,kBAAmB,CACnB,6CAAsC,CAAtC,qCAAsC,CACtC,0BAAiB,CAAjB,kBAAmB,CpC5gBf,0BoCsgBR,WAQQ,kEAAmE,CAU1E,CpCzgBO,2BoCufR,WAWQ,SAAU,CAOjB,CpCzgBO,0BoCufR,WAcQ,+DAAgE,CAChE,8DAA+D,CAC/D,kEAAmE,CAE1E,CAED,0BACI,iBAAkB,CAClB,gDAAiD,CACjD,wBtCphBgB,CsCqhBhB,2BAA4B,CAC5B,0BAA2B,CAC3B,qBAAsB,CACtB,SAAU,CAPd,sCAUQ,mBAAoB,CAV5B,wDAcgB,YAAa,CAd7B,mDAkBgB,oBAAe,CACf,wBAAgB,CAnBhC,iDA0BY,WAAY,CACZ,UAAW,CA3BvB,iDA+BY,QAAS,CACT,SAAU,CAhCtB,+BAqCQ,kBAAmB,CACnB,eAAgB,CAChB,yBAAO,CAAP,sBAAO,CAAP,iBAAkB,CpCljBlB,0BoC2gBR,+BA0CY,eAAgB,CAEvB,CC1kBL,kEAGY,UvCKG,CuCJH,WAAY,CACZ,UAAW,CALvB,gFAQgB,kBAAmB,CACnB,wBvCGI,CuCFJ,YAAa,CACb,UAAW,CACX,WAAY,CACZ,kBAAmB,CAbnC,yEnCEI,mBmCe4C,CnCf5C,mBmCe4C,CnCf5C,YmCe4C,CnCd5C,wBmCc4D,CnCd5D,qBmCc4D,CnCd5D,kBmCc4D,CnCb5D,wBmCaqF,CnCbrF,qBmCaqF,CnCbrF,6BmCaqF,CACzE,OAAQ,CACR,cAAe,CAnB/B,8EAsBoB,cAAe,CACf,eAAgB,CAChB,gBAAiB,CAxBrC,2EA4BoB,oBAAe,CACf,sBAAgB,CAChB,iCAA0B,CAA1B,yBAA0B,CAC1B,gCAAY,CAAZ,wBAAyB,CA/B7C,iFAmCwB,iCAA0B,CAA1B,yBAA0B,CAC1B,gCAAY,CAAZ,wBAAyB,CApCjD,8DA2CY,UAAW,CACX,kBAAmB,CACnB,YAAa,CACb,iBAAkB,CAClB,eAAgB,CA/C5B,yEAkDgB,iBAAkB,CAClB,YAAa,CACb,YAAa,CACb,YAAa,CArD7B,yEAyDgB,iBAAkB,CAClB,UAAW,CACX,WAAY,CACZ,YAAa,CACb,gCAAW,CAAX,wBAAyB,CA7DzC,yEAiEgB,iBAAkB,CAClB,aAAc,CACd,UAAW,CACX,YAAa,CACb,+BAAW,CAAX,uBAAwB,CArExC,yEAyEgB,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,YAAa,CACb,gCAAW,CAAX,wBAAyB,CA7EzC,sFAmFwB,yCAAW,CAAX,iCAAkC,CAnF1D,sEA0FY,qCvCzFW,CuCDvB,wEA8FY,wBvC5Fa,CuCFzB,mEAkGY,wBvC3FQ,CuCPpB,sEAsGY,wBvCnGW,CuCoGd,enCrGL,mBmCyGgC,CnCzGhC,mBmCyGgC,CnCzGhC,YmCyGgC,CnCxGhC,wBmCwGgD,CnCxGhD,qBmCwGgD,CnCxGhD,kBmCwGgD,CnCvGhD,wBmCuGyE,CnCvGzE,qBmCuGyE,CnCvGzE,6BmCuGyE,CACzE,OAAQ,CAFZ,kBAKQ,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,oBAAqB,CACrB,sBAAuB,CAd/B,oBAkBQ,cAAe,CACf,gBAAiB,CACjB,avCnHa,CuC+FrB,qBAwBQ,cAAe,CACf,eAAgB,CAChB,gBAAiB,CrCjHjB,0BqCuFR,eA6BQ,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAEzB,CAED,mBnCpII,mBmCsIsC,CnCtItC,mBmCsIsC,CnCtItC,YmCsIsC,CnCrItC,uBmCqIgD,CnCrIhD,oBmCqIgD,CnCrIhD,iBmCqIqD,CAFzD,sBAKY,8BvCrIQ,CuCsIR,cAAe,CA+BlB,kCA5BO,6BvCzII,CuC0IJ,kBAAmB,CAWtB,8CARO,kBAAmB,CACnB,eAAgB,CACnB,6CAGG,mBAAoB,CACpB,cAAe,CAnBnC,kCAwBgB,cAAe,CAxB/B,iCA4BgB,WAAY,CACZ,eAAgB,CAChB,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,sBAAuB,CAnCvC,kBAmDQ,cAAe,CACf,avCrLa,CuCsLb,iBAAkB,CAClB,kBAAmB,CACnB,QAAS,CACT,eAAgB,CAChB,gBAAiB,CAzDzB,yBA4DY,UAAW,CACX,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,wBvCjMS,CuCkMT,kBAAmB,CACnB,QAAS,CACT,QAAS,CACZ,MnCxML,mBmC6MkC,CnC7MlC,mBmC6MkC,CnC7MlC,YmC6MkC,CnC5MlC,wBmC4MkD,CnC5MlD,qBmC4MkD,CnC5MlD,kBmC4MkD,CAClD,OAAQ,CACR,avCpNmB,CuCqNnB,cAAe,CAJnB,QAOQ,oBAAe,CACf,wBAAgB,CARxB,WAYQ,eAAgB,CACnB,cnC/ND,mBmCoOoC,CnCpOpC,mBmCoOoC,CnCpOpC,YmCoOoC,CnCnOpC,wBmCmOoD,CnCnOpD,qBmCmOoD,CnCnOpD,kBmCmOoD,CnClOpD,wBmCkO6E,CnClO7E,qBmCkO6E,CnClO7E,6BmCkO6E,CACzE,OAAQ,CACR,kBAAW,CAAX,cAAe,CAJvB,mBAMY,avC/NS,CuCgOT,cAAe,CAP3B,oBAWY,eAAgB,CAChB,avCtOS,CuCuOT,cAAe,CAClB,+DAQO,iBAAkB,CAJlC,sEAOoB,UAAW,CACX,SAAU,CACV,iBAAkB,CAClB,UAAW,CACX,yBvCrPA,CuCsPA,SAAU,CACV,KAAM,CACN,kCAAW,CAAX,0BAA2B,CAd/C,qEAkBoB,UAAW,CACX,iBAAkB,CAClB,UAAW,CACX,QAAS,CACT,+BvChQA,CuCiQA,8BvCjQA,CuCkQA,SAAU,CACV,UAAW,CACX,gCAAW,CAAX,wBAAyB,CA1B7C,8EnC9OI,mBmC4QkD,CnC5QlD,mBmC4QkD,CnC5QlD,YmC4QkD,CnC3QlD,wBmC2QkE,CnC3QlE,qBmC2QkE,CnC3QlE,kBmC2QkE,CAClD,OAAQ,CA/B5B,kFAkCwB,UAAW,CACX,WAAY,CACZ,kBAAmB,CApC3C,uFAuC4B,UAAW,CACX,WAAY,CAxCxC,gFA6CwB,QAAS,CACT,eAAgB,CAChB,avC3RH,CuC4RG,cAAe,CACf,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,oBAAqB,CACrB,sBAAuB,CAvD/C,gFA2DwB,oBAAe,CACf,wBAAgB,CA5DxC,iEnC9OI,mBmC+SkD,CnC/SlD,mBmC+SkD,CnC/SlD,YmC+SkD,CnC9SlD,wBmC8SkE,CnC9SlE,qBmC8SkE,CnC9SlE,kBmC8SkE,CAClD,OAAQ,CAlE5B,qEAqEwB,yBAAO,CAAP,sBAAO,CAAP,iBAAkB,CArE1C,iFAuE4B,avC5TL,CuC6TK,cAAe,CACf,aAAc,CrC5SlC,2BqCmOR,iFA2EgC,YAAa,CASpB,CrCvTjB,0BqCmOR,iFA8EgC,cAAe,CACf,gBAAiB,CA/EjD,mFAiFoC,oBAAe,CAClB,CAlFjC,mEAwFwB,oBAAe,CACf,wBAAgB,CAzFxC,gFAiGgB,kBAAmB,CAjGnC,8DAwGgB,UAAW,CACX,eAAgB,CAChB,mBAAoB,CACpB,oBAAqB,CACrB,2BAA4B,CAC5B,sBAAuB,CrChV/B,0BqCmOR,6DAmHgB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAkB,CAAlB,oBAAkB,CAAlB,iBAAkB,CAClB,QAAS,CAQhB,CrChWD,0BqCmOR,yEA0HoB,UAAW,CAElB,CrC/VL,2BqCmOR,0DAgIgB,iBAAkB,CAuBzB,CAIT,gBACI,eAAgB,CADpB,uBnC/YI,mBmCkZoC,CnClZpC,mBmCkZoC,CnClZpC,YmCkZoC,CnCjZpC,wBmCiZoD,CnCjZpD,qBmCiZoD,CnCjZpD,kBmCiZoD,CnChZpD,wBmCgZ6E,CnChZ7E,qBmCgZ6E,CnChZ7E,6BmCgZ6E,CACzE,QAAS,CACT,WAAY,CACZ,QAAS,CACT,kBAAmB,CAP3B,8BAUY,YAAa,CAVzB,0BAaY,mEAAoE,CACpE,eAAgB,CrC5YpB,0BqC8XR,uBAiBgB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAE7B,CApBL,kBAsBQ,avC5Za,CuC6Zb,cAAe,CACf,aAAc,CAxBtB,sBA2BQ,cAAe,CACf,iBAAkB,CA5B1B,uBnC/YI,mBmC8aoC,CnC9apC,mBmC8aoC,CnC9apC,YmC8aoC,CnC7apC,wBmC6aoD,CnC7apD,qBmC6aoD,CnC7apD,kBmC6aoD,CnC5apD,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CmCgbxD,sBAAsB,CAAtB,mBAAsB,CAAtB,qBAAsB,CACtB,kBAAe,CAAf,cAAe,CACf,QAAS,CACT,kBAAmB,CAnC3B,4BAsCQ,wBvC1aY,CuC2aZ,kBAAmB,CAvC3B,2CAyCY,avC/aS,CuCgbZ,cAKL,QAAS,CnCxbT,mBmCybkC,CnCzblC,mBmCybkC,CnCzblC,YmCybkC,CnCxblC,wBmCwbkD,CnCxblD,qBmCwbkD,CnCxblD,kBmCwbkD,CAClD,+BvCtbgB,CuCmbpB,0BAKQ,aAAc,CALtB,yBAQQ,WAAY,CACZ,gBAAiB,CATzB,kBAYQ,WAAY,CACZ,YAAa,CrCzbb,0BqC4aR,cAgBQ,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAa,CAAb,oBAAa,CAAb,iBAAkB,CAEzB,CAED,cACI,eAAgB,CAChB,cAAe,CACf,gBAAiB,CACpB,QAGG,kBAAmB,CACnB,eAAgB,CAChB,wBvCjdgB,CuCkdnB,enC5dG,mBmC8dgC,CnC9dhC,mBmC8dgC,CnC9dhC,YmC8dgC,CnC7dhC,wBmC6dgD,CnC7dhD,qBmC6dgD,CnC7dhD,kBmC6dgD,CnC5dhD,wBmC4dyE,CnC5dzE,qBmC4dyE,CnC5dzE,6BmC4dyE,CACzE,qCvChemB,CuCienB,avCjemB,CuCkenB,YAAa,CAJjB,oBAMQ,cAAe,CANvB,qBASQ,eAAgB,CAChB,cAAe,CACf,gBAAiB,CACpB,eAGD,YAAa,CADjB,iBAIQ,oBAAe,CACf,wBAAgB,CAChB,8BvCveY,CuCweZ,kBAAmB,CAP3B,kBAUQ,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,QAAS,CAZjB,qBnCreI,mBmCmf0C,CnCnf1C,mBmCmf0C,CnCnf1C,YmCmf0C,CnClf1C,wBmCkf0D,CnClf1D,qBmCkf0D,CnClf1D,kBmCkf0D,CAClD,QAAS,CACT,cAAe,CACf,eAAgB,CAChB,avCrfS,CuCsfZ,wBAMD,6DAA8D,CAC9D,+DAAgE,CAChE,UAAW,CACd,WAID,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,kBAAmB,CACnB,wBvCzgBkB,CuC0gBlB,8DAA+D,CAC/D,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,cAAe,CARnB,mBAWQ,yEAAe,CACf,sBAAgB,CAChB,aAAc,CAbtB,wBAeY,YAAa,CAfzB,iBAmBQ,2EAA4E,CAC5E,0EAA2E,CAC3E,YAAa,CArBrB,sBAuBY,aAAc,CACjB,kBAML,qCvCziBmB,CuC0iBnB,UAAW,CACX,YAAa,CACb,yBvC5iBmB,CuC6iBnB,avC7iBmB,CuC8iBnB,kBAAmB,CACnB,cAAe,CACf,eAAgB", "file": "style.css", "sourcesContent": [":root{\n    --theme-color: #5565FE;\n    --theme-color-rgb: 82, 99, 255;\n}\n\n$primary-color: #5565FE;\n$secondary-color: #ff7456;\n$success-color: #27AF4D;\n// $warning-color: #FFC412;\n$warning-color: #FDB448;\n$danger-color: #FF4B4B;\n$info-color: #48bffd;\n$white: #ffffff;\n$black: #000000;\n$title-color: #2263eb;\n$light-color: #808B97;\n$gray-color: #E5E8EA;\n$section-bg: #F5F6F7;\n$login-card: 530px;\n$login-br: 15px; \n\n// font family\n$dm-sons: 'DM Sans'; \n\n// breakpoints\n$min-breakpoints: (md:768px, lg: 992px,\n    xl: 1200px,\n    2xl: 1366px,\n);\n\n$max-breakpoints: (2xs: 360px,\n    xs: 480px,\n    sm: 575px,\n    md: 767px,\n    lg: 991px,\n    xl: 1199px,\n    2xl: 1366px,\n    3xl: 1460px,\n    4xl: 1660px,\n);\n", "/**=====================\n     Typography scss\n==========================**/\n\nbody {\n     font-family: $dm-sons;\n     position: relative;\n     font-size: 14px;\n     margin: 0;\n     background-color: $white;\n     color: $title-color;\n     // overflow-x: hidden;\n}\n\nul {\n     padding-left: 0;\n     margin-bottom: 0;\n}\n\nli {\n     display: inline-block;\n     font-size: 14px;\n}\n\np {\n     font-size: 14px;\n     line-height: 18px;\n     margin: 0;\n}\n\na {\n     color: var(--theme-color);\n     transition: 0.5s ease;\n     text-decoration: none;\n\n     &:hover {\n          color: var(--theme-color);\n          text-decoration: none;\n          transition: 0.5s ease;\n     }\n\n     &:focus {\n          outline: none;\n     }\n}\n\nbutton {\n     &:focus {\n          outline: none;\n     }\n}\n\n:focus {\n     outline: none;\n}\n\nlabel{\n     font-size: 16px; \n     color: $light-color;\n}\n\nh1 {\n     font-size: calc(22px + (60 - 22) * ((100vw - 320px) / (1920 - 320)));\n     font-weight: 500;\n     line-height: 1.1;\n     text-transform: capitalize;\n     margin: 0;\n}\n\nh2 {\n     font-size: calc(20px + (26 - 20) * ((100vw - 320px) / (1920 - 320)));\n     font-weight: 600;\n     text-transform: capitalize;\n     margin: 0;\n}\n\nh3 {\n     font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n     font-weight: 500;\n     line-height: 1.2;\n     margin: 0;\n}\n\nh4 {\n     font-size: calc(17px + (18 - 17) * ((100vw - 320px) / (1920 - 320)));\n     line-height: 1.2;\n     margin: 0;\n     font-weight: 400;\n}\n\nh5 {\n     font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n     line-height: 1.2;\n     margin: 0;\n     font-weight: 400;\n}\n\nh6 {\n     font-size: calc(13px + (14 - 13) * ((100vw - 320px) / (1920 - 320)));\n     line-height: 1.2;\n     margin: 0;\n     font-weight: 400;\n}\n\nspan {\n     display: inline-block;\n}\n\n.theme-color {\n     color: var(--theme-color) !important;\n}\n\n.theme-bg-color {\n     background: var(--theme-color) !important;\n}\n\n.col-custom-3{\n     width: 29%;\n     @include mq-max(2xl){\n          width: 36%;\n     }\n     @include mq-max(xl){\n          width: 300px;\n          position: relative;\n     }\n}\n\n.col-custom-9{\n     width: 71%;\n     @include mq-max(2xl){\n          width: 64%;\n     }\n     @include mq-max(xl){\n          width: 100%;\n     }\n}\n\n.w-max{\n     width: max-content !important;\n}\n\n.w-auto{\n     width: auto !important;\n}", "/**=====================\n    breakpoint mixins scss\n==========================**/\n// min width\n@mixin mq-min($breakpoint) {\n    @if map-has-key($min-breakpoints, $breakpoint) {\n        $breakpoint-value: map-get($min-breakpoints, $breakpoint);\n\n        @media (min-width: $breakpoint-value) {\n            @content;\n        }\n    }\n\n    @else {\n        @warn 'Invalid breakpoint: #{$breakpoint}.';\n    }\n}\n\n// max width\n@mixin mq-max($breakpoint) {\n    @if map-has-key($max-breakpoints, $breakpoint) {\n        $breakpoint-value: map-get($max-breakpoints, $breakpoint);\n\n        @media (max-width: ($breakpoint-value)) {\n            @content;\n        }\n    }\n\n    @else {\n        @warn 'Invalid breakpoint: #{$breakpoint}.';\n    }\n}\n\n// min and max\n@mixin mq-between($lower, $upper) {\n    @if map-has-key($max-breakpoints, $lower) and map-has-key($min-breakpoints, $upper) {\n        $lower-breakpoint: map-get($max-breakpoints, $lower);\n        $upper-breakpoint: map-get($min-breakpoints, $upper);\n\n        @media (min-width: $lower-breakpoint) and (max-width: ($upper-breakpoint - 1)) {\n            @content;\n        }\n    }\n\n    @else {\n        @if (map-has-key($max-breakpoints, $lower)==false) {\n            @warn 'Your lower breakpoint was invalid: #{$lower}.';\n        }\n\n        @if (map-has-key($min-breakpoints, $upper)==false) {\n            @warn 'Your upper breakpoint was invalid: #{$upper}.';\n        }\n    }\n}", "/**=====================\n     Reset scss\n==========================**/\n\n// Section Space Scss\n\nsection,\n.section-t-space {\n    padding-top: calc(30px + (70 - 30) * ((100vw - 320px) / (1920 - 320)));\n    overflow: hidden;\n}\n\n.section-b-space {\n    padding-bottom: calc(30px + (70 - 30) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.section-big-space {\n    padding-top: calc(30px + (150 - 30) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.content-t-space {\n    padding-top: calc(20px + (50 - 20) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.content-t-space2 {\n    padding-top: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.content-b-space {\n    padding-bottom: calc(20px + (50 - 20) * ((100vw - 320px) / (1920 - 320)));\n}\n\n\n.content-b-space2 {\n    padding-bottom: calc(20px + (30 - 20) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.container-fluid-xl {\n    padding: calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320))) calc(15px + (100 - 15) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.container-fluid-lg {\n    padding: 0 calc(15px + (200 - 15) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.container-fluid-md {\n    padding: 0 calc(15px + (245 - 15) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.container-fluid-sm {\n    padding: 0 calc(15px + (385 - 15) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.container-fluid-xs {\n    padding: 0 calc(15px + (450 - 15) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.section-bg {\n    background-color: $section-bg;\n}\n\n.p-sticky {\n    // position: sticky;\n    // top: 120px;\n}\n\n// Number Spin Button Scss\ninput::-webkit-outer-spin-button,\ninput::-webkit-inner-spin-button {\n    -webkit-appearance: none;\n    margin: 0;\n}\n\n/* Firefox */\ninput[type=number] {\n    -moz-appearance: textfield;\n}\n\n\n// Rating Scss\n.rate {\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 4px;\n    margin-top: 4px;\n\n    small {\n        font-size: 14px;\n        font-weight: 500;\n        line-height: 1;\n    }\n\n    img {\n        &.star {\n            width: 13px;\n            height: 13px;\n        }\n    }\n}\n\n\n// Title Scss\n.title {\n    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    padding-bottom: calc(7px + (14 - 7) * ((100vw - 320px) / (1920 - 320)));\n    border-bottom: 1px solid $gray-color;\n    position: relative;\n    margin-bottom: calc(16px + (30 - 16) * ((100vw - 320px) / (1920 - 320)));\n    gap: 4px;\n\n    &.dark-title {\n        color: $white;\n        border-bottom: 1px solid rgba($white, 0.10%);\n\n    }\n\n    &:before {\n        content: \"\";\n        position: absolute;\n        background-color: var(--theme-color);\n        height: 4px;\n        width: 93px;\n        bottom: -2.5px;\n        left: 0;\n    }\n\n    h2 {\n        font-weight: 700;\n        line-height: calc(30px + (41 - 30) * ((100vw - 320px) / (1920 - 320)));\n\n        @include mq-max(sm) {\n            width: 100%;\n            overflow: hidden;\n            display: -webkit-box;\n            -webkit-line-clamp: 1;\n            -webkit-box-orient: vertical;\n            text-overflow: ellipsis;\n        }\n    }\n\n    .view-all {\n        @include flex_common_1 ($dis: flex, $align: center);\n        gap: 4px;\n        font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n        font-weight: 500;\n        position: relative;\n        white-space: nowrap;\n\n        i {\n            --Iconsax-Size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n            --Iconsax-Color: #5565FE;\n            transform: translateX(0px);\n            transition: all 0.2s ease;\n        }\n\n        &:hover {\n            text-decoration: underline;\n\n            i {\n                transform: translateX(5px);\n                transition: all 0.2s ease;\n            }\n        }\n    }\n}\n\n.title-1 {\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n    position: relative;\n    margin: 0 auto;\n    width: calc(250px + (340 - 250) * ((100vw - 320px) / (1920 - 320)));\n    text-align: center;\n\n    h2 {\n        font-weight: 700;\n        line-height: calc(30px + (41 - 30) * ((100vw - 320px) / (1920 - 320)));\n    }\n\n    &:before {\n        content: \"\";\n        position: absolute;\n        background-color: var(--theme-color);\n        height: 4px;\n        width: 93px;\n        bottom: -12px;\n        left: 50%;\n        transform: translateX(-50%);\n    }\n}\n\n\n// Discount Tag Scss\n.discount-tag {\n    position: absolute;\n    top: 10px;\n    right: 10px;\n    background-color: $danger-color;\n    color: $white;\n    border-radius: 22px;\n    padding: 1px 10px;\n    font-size: 14px;\n    font-weight: 600;\n}\n\n\n// Newsletter Scss\n.newsletter-section {\n    .newsletter-content {\n        position: relative;\n        background-image: url(../../assets/images/newsletter.png);\n        background-repeat: no-repeat;\n        background-size: 100% 100%;\n        width: 100%;\n        padding-top: 1px;\n\n        @include mq-max(md) {\n            background-image: none;\n        }\n\n        .newsletter-detail {\n            color: $white;\n            padding: 100px 0 30px;\n            padding: calc(60px + (100 - 60) * ((100vw - 992px) / (1920 - 992))) 0 calc(20px + (30 - 20) * ((100vw - 992px) / (1920 - 992)));\n            padding-right: calc(40px + (100 - 40) * ((100vw - 992px) / (1920 - 992)));\n\n            @include mq-max(md) {\n                padding: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320)));\n                // padding-right: calc(40px + (100 - 40) * ((100vw - 992px) / (1920 - 992)));\n                background-image: url(../../assets/images/newsletter-1.png);\n                background-position: center;\n                background-size: cover;\n                background-size: unset;\n                border-radius: 20px;\n                background-repeat: no-repeat;\n            }\n\n            h2 {\n                text-transform: uppercase;\n                font-weight: 700;\n                line-height: calc(30px + (41 - 30) * ((100vw - 320px) / (1920 - 320)));\n            }\n\n            p {\n                font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n                line-height: 23px;\n                color: rgba($white, 0.60%);\n                margin: 0;\n                width: 100%;\n                overflow: hidden;\n                display: -webkit-box;\n                -webkit-line-clamp: 2;\n                -webkit-box-orient: vertical;\n                text-overflow: ellipsis;\n            }\n\n            .form-group {\n                @include flex_common_1 ($dis: flex, $align: center);\n                gap: 6px;\n                padding-top: 24px;\n                margin: 0;\n\n                .form-control {\n                    border-radius: 6px;\n                    background-color: $white;\n                    color: $light-color;\n\n                    @include mq-max(sm) {\n                        padding: 6px 10px;\n                    }\n                }\n\n                .btn {\n                    border-radius: 6px;\n                    width: max-content;\n\n                    span {\n                        padding: 0;\n                    }\n\n                    @include mq-max(sm) {\n                        padding: 5px 10px;\n                        font-size: 14px;\n\n                        span {\n                            display: none;\n                        }\n                    }\n                }\n            }\n\n            span {\n                padding-top: calc(10px + (60 - 10) * ((100vw - 768px) / (1920 - 768)));\n                font-size: 18px;\n\n                @include mq-max(md) {\n                    padding-top: 10px;\n                }\n            }\n        }\n\n        .newsletter-icons {\n            @include mq-max(md) {\n                display: none;\n            }\n\n            img {\n                position: absolute;\n                animation: mover 2s infinite alternate;\n                animation-delay: 1s;\n            }\n\n            .newsletter-1 {\n                top: 140px;\n                left: 70px;\n                height: 90px;\n            }\n\n            .newsletter-2 {\n                top: 50px;\n                right: 90px;\n                height: 16px;\n\n                @include mq-max(lg) {\n                    display: none;\n                }\n            }\n\n            .newsletter-3 {\n                bottom: -30px;\n                right: 330px;\n                height: 90px;\n\n                @include mq-max(2xl) {\n                    display: none;\n                }\n            }\n        }\n    }\n}\n\n\n// Custom Scroll Scss\n.custom-scroll {\n\n    &::-webkit-scrollbar {\n        width: 5px;\n    }\n\n    &::-webkit-scrollbar-track {\n        background: $gray-color;\n    }\n\n    &::-webkit-scrollbar-thumb {\n        background: rgba($primary-color, 0.20%);\n        border-radius: 4px;\n    }\n\n    &::-webkit-scrollbar-thumb:hover {\n        background: $primary-color;\n    }\n}\n\n\n// Position Sticky\n.position-sticky {\n    // position: sticky;\n    // top: 110px;\n    // margin-bottom: 30px; \n}\n\n\n// Text Scss\n.text-underline {\n    text-decoration: underline;\n}\n\n.text-dark {\n    color: $title-color;\n    font-weight: 500;\n}\n\n.text-white {\n    color: $white;\n}\n\n\n// Border Scss\n.border {\n    border: 1px solid $gray-color;\n}\n\n.b-top {\n    border-top: 1px solid $gray-color;\n}\n\n.b-bottom {\n    border-bottom: 1px solid $gray-color;\n}\n\n.b-left {\n    border-left: 1px solid $gray-color;\n}\n\n.b-right {\n    border-right: 1px solid $gray-color;\n}\n\n.border-dashed {\n    border: 1px dashed $gray-color;\n}\n\n.border-solid {\n    border: 1px solid $gray-color;\n    margin: 16px 0;\n}\n\n\n.b-top-dashed {\n    border-top: 1px dashed $gray-color;\n}\n\n.b-bottom-dashed {\n    border-bottom: 1px dashed $gray-color;\n}\n\n.b-left-dashed {\n    border-left: 1px dashed $gray-color;\n}\n\n.b-right-dashed {\n    border-right: 1px dashed $gray-color;\n}\n\n.br-12 {\n    border-radius: 12px;\n}\n\n.br-15 {\n    border-radius: 15px;\n}\n\n.br-10 {\n    border-radius: 10px;\n}\n\n.br-8 {\n    border-radius: 8px;\n}\n\n.br-6 {\n    border-radius: 6px;\n}\n\n\n.br-tl-0 {\n    border-top-left-radius: 0;\n}\n\n.br-tr-0 {\n    border-top-right-radius: 0;\n}\n\n.br-bl-0 {\n    border-bottom-left-radius: 0;\n}\n\n.br-br-0 {\n    border-bottom-right-radius: 0;\n}\n\n\n// Padding Scss\n.p-16 {\n    padding: 16px;\n}\n\n.p-20 {\n    padding: calc(14px + (20 - 14) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.pr-45 {\n    padding-right: 45px !important;\n}\n\n// Image Scss\n.img-45 {\n    width: 45px !important;\n    height: 45px !important;\n    border-radius: 100%;\n}\n\n.plus-minus,\n.increment {\n    background-color: $section-bg;\n    border-radius: 6px;\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 0;\n\n    i {\n        --Iconsax-Size: 19px;\n        --Iconsax-Color: #808B97;\n        cursor: pointer;\n        @include flex_common ($dis: flex, $align: center, $justify: center);\n        width: 29px;\n        height: 29px;\n    }\n\n    input {\n        color: $title-color;\n        border: none;\n        background-color: $section-bg;\n        text-align: center;\n        width: 30px;\n        height: 30px;\n    }\n\n    .add {\n        background-color: $primary-color;\n        --Iconsax-Color: #fff;\n        border-radius: 6px;\n    }\n}\n\n.danger-note {\n    background-color: rgba($danger-color, 0.10%);\n    color: $danger-color;\n    padding: 16px;\n    border-radius: 8px;\n\n    p {\n        font-size: 16px;\n    }\n\n    h3 {\n        color: $danger-color !important;\n        margin-bottom: 4px;\n    }\n}\n\n.dashed-border {\n    border-top: 1px dashed $gray-color;\n    margin: calc(14px + (20 - 14) * ((100vw - 768px) / (1920 - 768))) 0;\n}\n\n.note {\n    margin-top: 16px;\n\n    label {\n        font-weight: 500;\n        color: $title-color;\n    }\n\n    p {\n        color: $light-color;\n    }\n}\n\n.input-group {\n    &:not(.has-validation) {\n        >:not(:last-child) {\n            &:not(.dropdown-toggle) {\n                &:not(.dropdown-menu) {\n                    border-radius: 8px !important;\n                }\n            }\n        }\n\n        >.dropdown-toggle {\n            &:nth-last-child(n+3) {\n                border-radius: 8px !important;\n            }\n        }\n    }\n\n    // >:not(:first-child){\n    //     &:not(.dropdown-menu){\n    //         &:not(.valid-tooltip){\n    //             &:not(.valid-feedback){\n    //                 &:not(.invalid-tooltip){\n    //                     &:not(.invalid-feedback){\n    //                         border-radius: 8px !important;\n    //                     }\n    //                 }\n    //             }\n    //         }\n    //     }\n    // }\n}\n\n@include mq-min(4xl) {\n    .row-cols-3xl-4>* {\n        -webkit-box-flex: 0;\n        -ms-flex: 0 0 auto;\n        flex: 0 0 auto;\n        width: 33.33333%;\n    }\n}\n\n.filter-div {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    a {\n        border-radius: 6px;\n        padding: 6px 12px;\n    }\n\n    @include mq-max(md) {\n        width: 100%;\n    }\n}\n\n.filter {\n    @include mq-max(xl) {\n        position: absolute;\n        top: 55px;\n        // left: -500px;\n        left: 12px;\n        transition: all 0.3s ease;\n        z-index: 3;\n        width: 290px;\n        transform: translateY(-10px);\n        opacity: 0;\n        visibility: hidden;\n\n        &.open {\n            left: 12px;\n            transition: all 0.3s ease;\n            transform: translateY(0px);\n            opacity: 1;\n            visibility: visible;\n        }\n    }\n}\n\n.time-slot {\n    border: none;\n    background: none;\n    padding: 0;\n    font-size: 16px;\n    color: #808B97 !important;\n}\n\n.error-wrapper {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    min-height: 100vh;\n    text-align: center;\n}\n\n.no-data-content {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    gap: 8px;\n    padding: 20px;\n\n    img {\n        margin-bottom: 20px;\n    }\n\n    p {\n        color: rgba($title-color, 0.8);\n        font-size: calc(17px + (18 - 17) * ((100vw - 320px) / (1920 - 320)));\n    }\n\n    .btn {\n        margin-top: 25px;\n        width: max-content;\n\n        i {\n            --Iconsax-Size: calc(20px + (24 - 20) * ((100vw - 320px) / (1920 - 320)));\n            --Iconsax-Color: #fff;\n            transform: translateX(0px);\n            transition: all 0.2s ease;\n        }\n\n        &:hover {\n            i {\n                transform: translateX(-5px);\n                transition: all 0.2s ease;\n            }\n        }\n    }\n}", "/**=====================\n     Common scss\n==========================**/\n\n/* ======= Display Flex Css Start ======= */\n@mixin flex_common ($dis: flex, $align: center, $justify: center) {\n    display: $dis;\n    align-items: $align;\n    justify-content: $justify;\n}\n\n@mixin flex_common_1 ($dis: flex, $align: center) {\n    display: $dis;\n    align-items: $align;\n}\n/* ======= Display Flex Css End ======= */\n\n/* ======= Gap Flex Css Start ======= */\n@mixin flex_wrap ($dis: flex, $wrap: wrap, $gap: 15px) {\n    display: $dis;\n    flex-wrap: $wrap;\n    gap: $gap;\n}\n/* ======= Gap Flex Css End ======= */\n\n/*======= position css starts  ======= */\n@mixin pos($pos: absolute, $content: \"\") {\n    content: $content;\n    position: $pos;\n}\n\n@mixin pseudowh($width: 100%, $height: 100%) {\n    width: $width;\n    height: $height;\n}\n\n@mixin center($position) {\n    @if $position==\"vertical\" {\n        top: 50%;\n        transform: translateY(-50%);\n    }\n\n    @else if $position==\"horizontal\" {\n        left: 50%;\n        transform: translateX(-50%);\n    }\n\n    @else if $position==\"both\" {\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n    }\n}\n/*======= position css ends  ======= */\n\n/*======= font awesome css start  ======= */\n@mixin font($weight: 900, $family: $fontawesome) {\n    font-family: $family;\n    font-weight: $weight;\n}\n/*======= font awesome css ends  ======= */\n\n/*======= align css starts  ======= */\n@mixin rtl($property, $ltr-value, $rtl-value) {\n    #{$property}: $ltr-value;\n\n    [dir=\"rtl\"] & {\n        #{$property}: $rtl-value;\n    }\n}\n// @include rtl(float, left, right);\n/*======= align css ends  ======= */", "/**=====================\n     Accordion scss\n==========================**/\n\n.accordion {\n     .accordion-item {\n          border: none;\n          background-color: transparent;\n\n          .accordion-button {\n               background-color: transparent;\n               width: max-content;\n               padding: 0;\n               border: none;\n               box-shadow: none;\n\n               &:not(.collapsed) {\n                    &:after {\n                         background-image: url(../../assets/images/svg/arrow.svg);\n                    }\n               }\n          }\n\n          .accordion-body {\n               padding: 0;\n          }\n     }\n}\n\n.filter {\n     .accordion {\n          margin-bottom: 10px;\n\n          .accordion-item {\n               .accordion-header {\n                    border-bottom: 1px solid $gray-color;\n                    padding-bottom: 8px;\n\n                    .accordion-button {\n                         width: 100%;\n                         @include flex_common($dis: flex, $align: center, $justify: space-between);\n                         font-weight: 500;\n                         color: $title-color;\n\n                         &:after {\n                              background-size: 16px;\n                         }\n                    }\n               }\n\n               .accordion-body {\n                    padding: 16px 0;\n\n                    .filter-body {\n                         .service {\n                              border-radius: 8px;\n                              padding: 16px;\n                              background-color: $white;\n                              display: flex;\n                              flex-direction: column;\n                              gap: 10px;\n                         }\n\n                         .form-group {\n                              margin: 0;\n                         }\n\n                         .search-provider {\n                              margin-top: 10px;\n                         }\n                    }\n\n                    .accordion {\n                         .accordion-item {\n                              .search-div {\n                                   @include flex_common($dis: flex, $align: center, $justify: space-between);\n                                   background-color: $white;\n                                   border-radius: 8px;\n                                   padding: 10px 16px;\n\n                                   i {\n                                        --Iconsax-Size: 18px;\n                                        --Iconsax-Color: #808b97;\n                                   }\n\n                                   .form-group {\n                                        width: 100%;\n\n                                        input {\n                                             border: none;\n                                             width: 100%;\n                                        }\n                                   }\n\n                                   .accordion-button {\n                                        &:after {\n                                             background-size: 16px;\n                                        }\n                                   }\n                              }\n\n                              .accordion-body {\n                                   padding: 16px;\n\n                                   .search-body {\n                                        display: flex;\n                                        flex-direction: column;\n                                        gap: 12px;\n                                        height: 170px;\n                                        overflow: auto;\n\n                                        .form-check {\n                                             gap: 8px;\n                                             margin-left: 24px;\n                                             margin-bottom: 0;\n                                        }\n\n                                        ul {\n                                             width: 100%;\n                                             overflow: hidden;\n                                             text-overflow: ellipsis;\n                                             display: block;\n                                             white-space: nowrap;\n\n                                             li {\n                                                  position: relative;\n                                                  color: $light-color;\n                                                  font-size: 14px;\n                                                  line-height: 18px;\n                                                  padding: 0 10px;\n\n                                                  &:before {\n                                                       position: absolute;\n                                                       content: \"\";\n                                                       top: 4px;\n                                                       right: 0px;\n                                                       background-color: $light-color;\n                                                       width: 1px;\n                                                       height: 12px;\n                                                  }\n\n                                                  &:first-child {\n                                                       color: $title-color;\n                                                       padding-left: 0;\n                                                  }\n\n                                                  &:last-child {\n                                                       padding-right: 0;\n\n                                                       &:before {\n                                                            display: none;\n                                                       }\n                                                  }\n                                             }\n                                        }\n                                   }\n                              }\n                         }\n                    }\n               }\n          }\n     }\n\n     .card-footer {\n          padding-top: 0 !important;\n\n          button {\n               width: 100%;\n               justify-content: center;\n               border-radius: 8px;\n          }\n\n          &:before,\n          &:after {\n               display: none;\n          }\n     }\n}\n\n.faq-section,\n.terms-section,\n.privacy-section {\n     .accordion {\n          .accordion-item {\n               background-color: $white;\n               border-radius: 12px;\n\n               +.accordion-item {\n                    margin-top: 20px;\n               }\n\n               .accordion-header {\n                    .accordion-button {\n                         padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n                         display: flex;\n                         align-items: center;\n                         justify-content: space-between;\n                         width: 100%;\n                         line-height: 1.2;\n                         font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n                         font-weight: 500;\n                         gap: 4px;\n                         color: $title-color;\n\n                         i {\n                              --Iconsax-Size: calc(20px + (24 - 20) * ((100vw - 320px) / (1920 - 320)));\n                              --Iconsax-Color: #2263eb;\n                         }\n\n                         &:after {\n                              display: none;\n                         }\n\n                         .add {\n                              display: none;\n                         }\n\n                         .minus {\n                              display: flex;\n                         }\n\n                         &.collapsed {\n\n                              color: rgba($title-color, 0.7);\n\n                              .add {\n                                   display: flex;\n                              }\n\n                              .minus {\n                                   display: none;\n                              }\n                         }\n                    }\n               }\n\n               .accordion-body {\n                    padding: 20px;\n\n                    p {\n                         font-size: 18px;\n                         line-height: 1.5;\n                    }\n               }\n          }\n     }\n\n     ul,\n     ol {\n          list-style-type: circle;\n          padding-left: 1.25rem;\n\n\n          li {\n               display: list-item;\n               font-size: 17px;\n               color: rgba($title-color, 0.7);\n               line-height: 1.3 !important;\n               margin-bottom: 12px;\n          }\n     }\n}\n\n.up-down-image {\n     display: flex;\n     gap: 20px;\n\n     img {\n          border-radius: 12px;\n          background-color: $gray-color;\n     }\n}", "/**=====================\n    Animation scss\n==========================**/\n\n@keyframes mover {\n    0% {\n        transform: translateY(0);\n    }\n\n    100% {\n        transform: translateY(-10px);\n    }\n}\n\n@keyframes textShine {\n    0% {\n        background-position: 0% 50%;\n    }\n\n    100% {\n        background-position: 100% 50%;\n    }\n}\n\n@keyframes textChange { \n    0% { \n        content: \"User\"; \n    } \n    30% { \n        content: \"Provider\"; \n    } \n    55% { \n        content: \"Servicemen\"; \n    } \n    80% { \n        content: \"Admin\"; \n    } \n} \n\n@keyframes tada{\n    0% {\n        transform: scaleX(1);\n    }\n    10% {\n        transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);\n    }\n    20% {\n        transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);\n    }\n    30% {\n        transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);\n    }\n    50% {\n        transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);\n    }\n    70% {\n        transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);\n    }\n    90% {\n        transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);\n    }\n    40% {\n        transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);\n    }\n    60% {\n        transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);\n    }\n    80% {\n        transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);\n    }\n    100% {\n        transform: scaleX(1);\n    }\n}\n", "/**=====================\n    Booking scss\n==========================**/\n\n.booking-category{\n    .form-control{\n        border: none !important;\n    }\n    .category-body{\n        margin-top: 16px;\n        display: flex;\n        flex-direction: column;\n        gap: 10px;\n        .form-check{\n            justify-content: space-between;\n            gap: 4px;\n            ul{\n                @include flex_common_1 ($dis: flex, $align: center);\n                gap: 10px;\n                li{\n                    padding-left: 10px;\n                    position: relative;\n                    font-size: 16px;\n                    img{\n                        width: 24px;\n                        height: 24px;\n                    }\n                    &:before{\n                        content: \"\";\n                        position: absolute;\n                        top: 6px;\n                        left: 0;\n                        background-color: $gray-color;\n                        height: 16px;\n                        width: 1px;\n                    }\n                    &:first-child{\n                        &:before{\n                            display: none;\n                        }\n                    }\n                }\n            }\n        }\n    }\n    .booking-category-body{\n        height: unset !important;\n        overflow: unset !important;\n    }\n}\n\n.booking-sec{\n    .filter-div{\n        @include mq-max(md){\n            width: auto;\n        }\n        @include mq-max(sm){\n            width: 100%;\n        }\n    }\n    .select-dropdown{\n        h4{\n            font-weight: 500;\n        }\n        @include mq-max(sm){\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 12px;\n\n            .form-group {\n                width: 100%;\n            }\n        }\n    }\n    .service-title{\n        display: flex;\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 4px;\n        // h4{\n        //     margin-bottom: 4px;\n        // }\n\n        span{\n            font-size: 18px;\n            font-weight: 700;\n            line-height: 23px;\n            color: $title-color;\n        }\n        small{\n            font-size: 15px;\n            font-weight: 500;\n            line-height: 19px;\n        }\n        h4{\n            font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n        }\n    }\n    .status, .view-status{\n        @include flex_common ($dis: flex, $align: center, $justify: space-between);\n        margin-bottom: 16px;\n        .status-btn, h5{\n            font-size: 22px;\n            font-weight: 500;\n            line-height: 28px;\n            color: $primary-color;\n            border: none;\n            background-color: unset;\n            padding: 0;\n            &:hover{\n                text-decoration: underline;\n            }\n        }\n        .badge{\n            padding: 9px 16px;\n        }\n    }\n    .data{\n        display: flex;\n        flex-direction: column;\n        gap: 8px;\n        li{\n            @include flex_common ($dis: flex, $align: center, $justify: space-between);\n            font-size: calc(13px + (14 - 13) * ((100vw - 320px) / (1920 - 320)));\n        }\n        .label{\n            color: $light-color;\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 8px;\n        }\n        .value{\n            font-weight: 500;\n            &.location{\n                font-weight: 400 !important;\n            }\n        }\n        i{\n            --Iconsax-Size: 18px;\n            --Iconsax-Color: #5565FE;\n        }\n    }\n    .selected-men{\n        padding: 0;\n        .servicemen-list-item{\n            background-color: $section-bg;\n            border: none;\n            box-shadow: none;\n            min-width: 279px;\n            @include mq-max(2xs){\n                min-width: 262px;\n            }\n            .list{\n                width: 100%;\n                >div{\n                    width: 100%;\n                    ul{\n                        justify-content: space-between;\n                        li{\n                            .rate{\n                                &:before{\n                                    display: none;\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n    .ratio_70{\n        .bg-size{\n            min-height: 100px;\n            min-width: 100px;\n            width: auto;\n        }\n    }\n}\n\n.date-time-loaction-btn{\n    background-color: unset;\n    padding: 0;\n    border: none;\n}", "/**=====================\n    Breadcrumb scss\n==========================**/\n\n.breadcrumb{\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n    margin: 0;\n    margin-top: 10px;\n    .breadcrumb-item{\n        // font-size: 16px;\n        font-size: calc(13px + (16 - 13) * ((100vw - 320px) / (1920 - 320)));\n        color: $white;\n        +.breadcrumb-item{\n            &:before{\n                color: $white;\n            }\n        }\n    }\n}\n\n.breadcrumb-icon {\n\t--bs-breadcrumb-divider: '>';\n\n\tli {\n\t\tdisplay: inline-block;\n\t}\n}", "/**=====================\n    Button scss\n==========================**/\n\n.btn {\n    font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n    padding: calc(6px + (10 - 6) * ((100vw - 320px) / (1920 - 320))) calc(12px + (16 - 12) * ((100vw - 320px) / (1920 - 320)));\n    letter-spacing: 1px;\n    line-height: 26px;\n    gap: 4px;\n    white-space: nowrap;\n    width: 100%;\n    border-radius: 12px;\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n\n    &.btn-solid,\n    &.btn-solid:active {\n        background-color: var(--theme-color);\n        color: $white;\n        border-color: var(--theme-color);\n\n        i {\n            --Iconsax-Size: 20px;\n            --Iconsax-Color: white;\n        }\n    }\n\n    &.btn-outline {\n        border: 1px solid $primary-color;\n        color: $primary-color;\n\n        i {\n            --Iconsax-Size: 20px;\n            --Iconsax-Color: #0019ff;\n        }\n    }\n\n    &.btn-solid-gray {\n        background-color: $section-bg;\n        color: $title-color;\n\n    }\n\n    &.btn-dark-solid {\n        background-color: $title-color;\n        color: $white;\n    }\n\n    &:focus {\n        box-shadow: none;\n    }\n\n    &.disabled {\n        background-color: $gray-color;\n        color: $light-color;\n        font-weight: 500;\n    }\n\n    &.btn-solid-danger {\n        background-color: $danger-color;\n        color: $white;\n\n        i {\n            --Iconsax-Size: 20px;\n            --Iconsax-Color: white;\n        }\n    }\n\n    &.btn-solid-success {\n        background-color: $success-color;\n        color: $white;\n\n        i {\n            --Iconsax-Size: 20px;\n            --Iconsax-Color: white;\n        }\n    }\n}\n\n.badge {\n    font-size: 14px;\n    font-weight: 500;\n    border-radius: 15px;\n}\n\n// outline buttons//\n@each $text-name,\n$text-color in (primary, $primary-color),\n(secondary, $secondary-color),\n(success, $success-color),\n(danger, $danger-color),\n(info, $info-color),\n(light, $light-color),\n(title, $title-color),\n(warning, $warning-color) {\n    .text-#{$text-name} {\n        color: $text-color !important;\n    }\n}\n\n\n// badge background//\n@each $badge-name,\n$badge-color in (primary, $primary-color),\n(secondary, $secondary-color),\n(success, $success-color),\n(danger, $danger-color),\n(info, $info-color),\n(light, $light-color),\n(title, $title-color),\n(warning, $warning-color) {\n    .#{$badge-name}-badge {\n        color: $white !important;\n        background-color: $badge-color !important;\n    }\n}\n\n// badge light background//\n@each $badge-name,\n$badge-color in (primary, $primary-color),\n(secondary, $secondary-color),\n(success, $success-color),\n(danger, $danger-color),\n(info, $info-color),\n(light, $light-color),\n(title, $title-color),\n(warning, $warning-color) {\n    .#{$badge-name}-light-badge {\n        color: $badge-color !important;\n        background-color: rgba($badge-color, 0.10) !important;\n    }\n}", "/**=====================\n    Calendar scss\n==========================**/\n\n.flatpickr-calendar {\n    z-index: 3;\n    width: 324.875px;\n    padding: 16px;\n\n    &.open {\n        z-index: 6;\n    }\n\n    &:before,\n    &:after {\n        display: none;\n    }\n\n    .flatpickr-innerContainer {\n        font-size: 14px;\n        background-color: $section-bg;\n        border-radius: 8px;\n        @include flex_common ($dis: flex, $align: center, $justify: center);\n    }\n\n    .flatpickr-weekdays {\n        padding: 16px 0 0;\n        @include mq-max(xs){\n            padding-bottom: 16px;\n        }\n\n        .flatpickr-weekdaycontainer {\n            .flatpickr-weekday {\n                color: $primary-color;\n                font-size: 16px;\n                @include mq-max(xs){\n                    font-size: 13px;\n                }\n            }\n        }\n    }\n\n    .flatpickr-day {\n        max-width: 37px;\n        height: 35px;\n        line-height: 35px;\n        color: $title-color;\n        font-size: 16px;\n\n        @include mq-max(xs){\n           font-size: 13px;\n           max-width: 33px;\n           height: 32px;\n           line-height: 32px;\n        }\n\n        &:hover {\n            background-color: rgba($primary-color, 0.10);\n            color: $primary-color;\n            font-weight: 600;\n        }\n\n        &.today {\n            background-color: $primary-color;\n            color: $white;\n            border: none;\n        }\n\n        &.selected {\n            color: $white;\n            background-color: $primary-color;\n        }\n\n        &.prevMonthDay,\n        &.flatpickr-disabled,\n        &.flatpickr-disabled:hover,\n        &.nextMonthDay,\n        &.notAllowed,\n        &.notAllowed.prevMonthDay,\n        &.notAllowed.nextMonthDay {\n            color: $light-color;\n        }\n\n        &.inRange {\n            background-color: rgba($primary-color, 0.10);\n            box-shadow: none;\n\n            &.today {\n                background-color: $primary-color;\n            }\n        }\n    }\n\n    .dayContainer {\n        padding: 16px;\n        @include mq-max(xs){\n           padding: 0;\n        }\n    }\n\n    .flatpickr-months {\n        margin-bottom: 16px;\n\n        .flatpickr-month {\n            color: $title-color;\n            @include mq-max(xs){\n                height: 77px;\n            }\n        }\n\n        .flatpickr-current-month {\n            padding: 0;\n            gap: 10px;\n            @include flex_common ($dis: flex, $align: center, $justify: center);\n            @include mq-max(xs){\n                flex-direction: column;\n                height: 77px;\n            }\n\n            input {\n                &.cur-year {\n                    font-size: 16px;\n                    @include mq-max(xs){\n                        font-size: 14px;\n                    }\n                }\n            }\n        }\n\n        .flatpickr-prev-month,\n        .flatpickr-next-month {\n            background-color: $section-bg;\n            border-radius: 100%;\n            padding: 8px;\n            height: 30px;\n            width: 30px;\n            top: 18px;\n            @include flex_common ($dis: flex, $align: center, $justify: center);\n\n            &:hover {\n                svg {\n                    fill: $title-color;\n                }\n            }\n\n            svg {\n                width: 12px;\n                height: 12px;\n            }\n        }\n\n        .flatpickr-prev-month {\n            left: 16px !important;\n        }\n\n        .flatpickr-next-month {\n            right: 16px !important;\n        }\n\n        .flatpickr-monthDropdown-months {\n            background-color: $section-bg;\n            border-radius: 6px;\n            font-size: 16px;\n            padding: 6px 12px;\n            @include mq-max(xs){\n               font-size: 14px;\n            }\n\n        }\n\n        .numInputWrapper {\n            background-color: $section-bg;\n            border-radius: 6px;\n            font-size: 14px;\n            padding: 6px 12px;\n            width: 80px;\n        }\n    }\n\n    .numInputWrapper {\n        &:hover {\n            span {\n                border: none;\n            }\n        }\n\n        span {\n            &:hover {\n                background-color: unset;\n                border: none;\n            }\n\n            &.arrowUp {\n                &:after {\n                    top: 55%;\n                }\n            }\n\n            &.arrowDown {\n                &:after {\n                    top: 20%;\n                }\n            }\n        }\n    }\n\n    &.hasTime {\n        .flatpickr-time {\n            border: none;\n            line-height: unset;\n            height: 70px !important;\n            max-height: 70px !important;\n            gap: 10px;\n\n            &:after {\n                display: none;\n            }\n\n            .numInputWrapper {\n                height: 100%;\n                position: relative;\n                background: url(../../assets/images/time.png);\n                background-repeat: no-repeat;\n                background-position: center;\n                background-size: contain;\n                z-index: 0;\n                width: 30%;\n\n                input {\n\n                    &:hover,\n                    &:focus {\n                        background: transparent;\n\n                    }\n                }\n\n                span {\n                    right: 16px;\n\n                    &.arrowUp {\n                        &:after {\n                            border-bottom-color: $primary-color;\n                            border-width: 5px;\n                            top: unset;\n                            bottom: 13%;\n                        }\n                    }\n\n                    &.arrowDown {\n                        &:after {\n                            border-top-color: $primary-color;\n                            border-width: 5px;\n                            top: 13%;\n                        }\n                    }\n                }\n            }\n\n            input {\n                text-align: left;\n                padding-left: 20px;\n                font-size: 18px;\n                color: $primary-color;\n                font-weight: 500;\n\n            }\n\n            .flatpickr-time-separator {\n                width: 2%;\n                color: $primary-color;\n                font-size: 24px;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n            }\n\n            .flatpickr-am-pm {\n                height: 100%;\n                position: relative;\n                background: url(../../assets/images/time.png);\n                background-repeat: no-repeat;\n                background-position: center;\n                background-size: contain;\n                z-index: 0;\n                width: 29%;\n                font-size: 18px;\n                color: $primary-color;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n            }\n        }\n\n    }\n}\n\n.main-inline-calender {\n    &.input-group {\n        flex-direction: column;\n\n        .form-control {\n            width: 100%;\n            border-radius: 6px !important;\n            color: $primary-color;\n            padding: 0;\n            font-size: 18px;\n            font-weight: 500;\n            line-height: 23px;\n\n            &:focus {\n                border: none;\n            }\n\n            &:focus,\n            &:disabled {\n                box-shadow: unset;\n               border: none;\n               background-color: unset;\n            }\n        }\n\n        .form-control[readonly] {\n            box-shadow: unset;\n            border: none;\n            background-color: unset;\n        }\n\n        .flatpickr-calendar {\n            &.inline {\n                width: 100%;\n                border-radius: 6px !important;\n                @include mq-max(sm){\n                    max-width: 410px;\n                    min-width: 400px;\n                    left: 50%;\n                    transform: translateX(-50%);\n                }\n                @include mq-max(xs){\n                    min-width: 310px;\n                    max-width: min-content;\n                }\n                @include mq-max(2xs){\n                    min-width: 270px;\n                    max-width: min-content;\n                }\n            }\n        }\n\n        .flatpickr-innerContainer {\n            display: block;\n        }\n\n        .flatpickr-rContainer {\n            display: block;\n\n            .flatpickr-weekdays {\n                width: 100%;\n            }\n\n            .flatpickr-days {\n                width: 100%;\n\n                .dayContainer {\n                    width: 100%;\n                    max-width: none;\n                    min-width: unset;\n                    gap: 20px;\n\n                    @include mq-max(sm){\n                        gap: 14px;\n                    }\n\n                    @include mq-max(xs){\n                        gap:3px;\n                        padding: 4px;\n                     }\n\n                    @include mq-max(2xs){\n                       gap: 0;\n                    }\n\n\n                    .flatpickr-day {\n                        border-radius: 100%;\n                        width: 100%;\n                        color: $title-color;\n\n                        &:hover {\n                            color: $primary-color;\n                        }\n\n                        &.selected,\n                        &.today {\n                            color: $white;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n.inline-time-picker{\n    @include mq-max(sm){\n        max-width: 410px;\n        min-width: 400px;\n        margin-inline: auto;\n    }\n\n    @include mq-max(xs){\n        min-width: 310px;\n        max-width: min-content;\n    }\n    @include mq-max(2xs){\n        min-width: 270px;\n        max-width: min-content;\n    }\n}\n\n.inline-picker-btn{\n    @include mq-max(sm){\n        max-width: 410px;\n        min-width: 400px;\n        margin-inline: auto;\n    }\n    @include mq-max(xs){\n        min-width: 310px;\n        max-width: min-content;\n    }\n    @include mq-max(2xs){\n        min-width: 270px;\n        max-width: min-content;\n    }\n}", "/**=====================\n    Card scss\n==========================**/\n\n.card{\n    background-color: $white;\n    border-radius: 10px;\n    overflow: hidden;\n    border: 1px solid $gray-color;\n    // margin-bottom: 30px;\n    // margin-bottom: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320)));\n    margin: 0;\n    .card-footer{\n        padding: 16px;\n        background-color: transparent;\n        border: none;\n        gap: 8px;\n        @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    }\n    .card-img{\n        img{\n            width: 100%;\n            height: 100%;\n        }\n    }\n    &.dark-card{\n        background-color: rgba($white, 0.10%);\n        color: $light-color;\n        border: none;\n    }\n    &.gray-card{\n        background-color: $section-bg;\n        border: none;\n    }\n}", "/**=====================\n     Cart scss\n==========================**/\n\n.cart{\n    background-color: $section-bg;\n    .cart-header{\n        border-bottom: 1px solid $gray-color;\n        padding: 20px;\n        span{\n            color: $light-color;\n            margin-top: 4px;\n            font-size: 16px;\n        }\n    }\n    .cart-body{\n        padding: 20px;\n        h5{\n            font-weight: 500;\n        }\n        .cart-img{\n            img{\n                height: 250px;\n            }\n        }\n        .cart-item{\n            background-color: $white;\n            border: 1px solid $gray-color;\n            border-radius: 8px;\n            margin-bottom: 20px;\n\n            &:last-child{\n                margin: 0;\n            }\n            .cart-heading{\n                padding: 16px;\n                border-bottom: 1px solid $gray-color;\n                gap: 4px;\n                @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                @include mq-max(xs){\n                    flex-direction: column;\n                    align-items: flex-start;\n                }\n                \n                .cart-title{\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: 6px;\n                    p{\n                        margin: 0;\n                    }\n                    .rate{\n                        margin: 0;\n                        .star{\n                            width: 13px;\n                            height: 13px;\n                        }\n                    }\n                    .img-45{\n                        @include mq-max(xs){\n                            width: 35px !important;\n                            height: 35px !important;\n                        }\n                    }\n                }\n                .cart-action{\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: 12px;\n                    @include mq-max(xs){\n                        gap: 8px;\n                        margin-left: 40px;\n                    }\n                    button{\n                        background-color: unset;\n                        border: none;\n                        padding: 0;\n                        @include mq-max(xs){\n                            .edit, .delete{\n                                width: 30px;\n                                height: 30px;\n                                --Iconsax-Size: 17px;\n                            }\n                        }\n                    }\n                }\n            }\n            .cart-detail{\n                padding: 16px;\n                .selected-service{\n                    @include flex_common_1 ($dis: flex, $align: start);\n                    gap: 16px;\n                    @include mq-max(sm){\n                        flex-direction: column;\n                        align-items: start;\n                    }\n                    .selected-img{\n                        width: 160px;\n                        height: 160px;\n                        @include mq-max(lg){\n                            width: 120px;\n                            height: 120px;\n                        }\n                        @include mq-max(md){\n                            width: 100px;\n                            height: 100px;\n                        }\n                    }\n\n                    .mw-80{\n                        min-width: 80px;\n                    }\n\n                    .service-info{\n                        width: 100%;\n                        p{\n                            margin: 0;\n                        }\n                        .date {\n                            @include flex_common_1 ($dis: flex, $align: center);\n                    \n                            li {\n                                border-right: 1px solid $gray-color;\n                                padding: 0 10px;\n                                font-size: 13px;\n                    \n                                &:first-child {\n                                    padding-left: 0;\n                                }\n                    \n                                &:last-child {\n                                    border: none;\n                                    padding-right: 0;\n                                }\n                            }\n                        }\n                        .servicemen-list-item{\n                            width: max-content;\n                            min-width: 250px;\n                            @include mq-max(2xs){\n                                min-width: 220px;\n                            }\n\n                        }\n                    }\n                }\n                i{\n                    --Iconsax-Size: 18px;\n                    --Iconsax-Color: #2263eb;\n                }\n            }\n        }\n        .form-control{\n            padding-left: 16px;\n            border: 2px dashed $gray-color;\n            font-size: 16px;\n            font-weight: 500;\n            \n        }\n\n        .pattern-input{\n            border: 1px dashed $primary-color;\n            color: $primary-color;\n            background-color: rgba($primary-color, 0.10);\n            &:hover, &:focus{\n                background-color: rgba($primary-color, 0.10); \n                color: $primary-color;\n            }\n        }\n    }\n}\n\n.pattern-btn{\n    background-color: $white;\n    border: 2px dashed $gray-color;\n    color: $primary-color;\n    font-size: 14px;\n    font-weight: 500;\n    padding: calc(7px + (16 - 7) * ((100vw - 320px) / (1920 - 320))) 16px;\n    border-left: none;\n    margin-left: 0 !important;\n    position: relative;\n    border-radius: 8px;\n    &:after, &:before{\n        content: \"\";\n        position: absolute;\n        pointer-events: none;\n        border: solid transparent;\n        display: block;\n        border-width: 1px;            \n        width: 12px;\n        height: 12px;            \n        border-radius: 100%;\n        background-color: $section-bg;\n        z-index: 5;\n    }\n\n    &:after{\n        border-right-color: $gray-color;\n        border-left-color: $gray-color;\n        border-top-color: $gray-color;\n        left: -7px;\n        bottom: -4px;\n    }\n    &:before{\n        border-right-color: $gray-color;\n        border-left-color: $gray-color;\n        border-bottom-color: $gray-color;\n        left: -7px;\n        top: -4px;\n    }\n}\n\n.form-control.pattern-input{\n    border: 1px dashed $primary-color !important; \n    color: $primary-color;\n    background-color: rgba($primary-color, 0.10);\n    border-radius: 8px;\n    &:hover, &:focus{\n        background-color: rgba($primary-color, 0.10); \n        color: $primary-color;\n    }\n}\n.pattern-btn-1{\n    background-color: $white;\n    border: 1px dashed $primary-color;\n    background-color: rgba($primary-color, 0.10);\n    color: $primary-color;\n    font-size: 14px;\n    font-weight: 500;\n    padding: calc(7px + (16 - 7) * ((100vw - 320px) / (1920 - 320))) 16px;\n    border-left: none;\n    margin-left: 0 !important;\n    position: relative;\n    border-radius: 8px;\n    &:after, &:before{\n        content: \"\";\n        position: absolute;\n        pointer-events: none;\n        border: dashed transparent;\n        display: block;\n        border-width: 1px;            \n        width: 12px;\n        height: 12px;            \n        border-radius: 100%;\n        background-color: $section-bg;\n        z-index: 5;\n    }\n\n    &:after{\n        border-right-color: $primary-color;\n        border-left-color: $primary-color;\n        border-top-color: $primary-color;\n        left: -7px;\n        bottom: -4px;\n    }\n    &:before{\n        border-right-color: $primary-color;\n        border-left-color: $primary-color;\n        border-bottom-color: $primary-color;\n        left: -7px;\n        top: -4px;\n    }\n}\n\n.view{\n    margin-top: 8px;\n    background-color: rgba($primary-color, 0.10);\n    border-bottom-left-radius: 8px;\n    border-bottom-right-radius: 8px;\n    padding: 20px;\n    span{\n        color: $light-color;\n        font-size: 16px;\n        line-height: 20px;\n    }\n    .value{\n        font-size: 22px;\n        font-weight: 700;\n        line-height: 28px;\n        color: $primary-color;\n    }\n    .btn{\n        padding: calc(4px + (10 - 4) * ((100vw - 320px) / (1920 - 320))) calc(12px + (24 - 12) * ((100vw - 320px) / (1920 - 320)));\n        font-size: calc(14px + (18 - 14) * ((100vw - 320px) / (1920 - 320)));\n\n        i{\n            svg{\n                width: calc(14px + (20 - 14) * ((100vw - 320px) / (1920 - 320)));\n                height: calc(14px + (20 - 14) * ((100vw - 320px) / (1920 - 320)));\n            }\n        }\n    }\n}\n\n.price{\n    font-size: 20px;\n    font-weight: 700;\n    line-height: 26px;  \n    color: $primary-color;                 \n}\n.discount{\n    font-size: 14px;\n    font-weight: 500;\n    line-height: 18px;\n    color: $danger-color;\n    white-space: nowrap;\n}\n\n.bill-summary, .payment-summary{\n    .charge, .total{\n        background-color: $white;\n        border: 1px solid $gray-color;\n        border-radius: 8px;\n        padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n        display: flex;\n        flex-direction: column;\n        gap: 16px;\n        @include mq-max(sm){\n            gap: 6px;\n        }\n        &:before{\n            display: none;\n        }\n        p{\n            margin: 0;\n            font-size: 16px;               \n        }\n        li{\n            @include flex_common ($dis: flex, $align: center, $justify: space-between);\n            gap: 4px;\n            margin: 0;\n            @include mq-max(sm){\n                flex-direction: column;\n                align-items: start;\n                gap: 2px;\n            }\n        }\n    }\n    .charge{\n        p{\n            color: $light-color;\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 6px;\n            span{\n                color: $light-color;\n                font-weight: 400;\n            }\n        }\n        span{\n            color: $title-color;\n            font-weight: 500;\n            font-size: 16px;\n        } \n        border-bottom-right-radius: 0;\n        border-bottom-left-radius: 0;\n    }\n    .total{\n        position: relative;\n        border-top-right-radius: 0;\n        border-top-left-radius: 0;\n        p{\n            color: $title-color;\n            font-weight: 500;\n        }\n        span{\n            color: $primary-color;\n            font-weight: 700;\n            font-size: 20px;\n        }\n        &:after, &:before{\n            content: \"\";\n            position: absolute;\n            pointer-events: none;\n            border: solid transparent;\n            display: block;\n            border-width: 1px;            \n            width: 10px;\n            height: 15px;\n            top: -8px;\n            border-radius: 100%;\n            background-color: $section-bg;\n        }\n\n        &:after{\n            border-left-color: $gray-color;\n            border-top-color: $gray-color;\n            border-bottom-color: $gray-color;\n            right: -2px;\n        }\n        &:before{\n            border-right-color: $gray-color;\n            border-top-color: $gray-color;\n            border-bottom-color: $gray-color;\n            left: -2px;\n        }\n    }\n}\n\n.edit{\n    --Iconsax-Size: 20px;\n    --Iconsax-Color: #2263eb;\n    background-color: $section-bg;\n    border-radius: 100%;\n    height: 40px;\n    width: 40px;\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n}\n\n.delete{\n    --Iconsax-Size: 20px;\n    --Iconsax-Color: #FF4B4B;\n    background-color: rgba($danger-color, 0.10);\n    border-radius: 100%;\n    height: 40px;\n    width: 40px;\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n}\n\n.servoice-info-modal{\n    border: none;\n    background-color: unset;\n    padding: 0;\n    i{\n        --Iconsax-Size: 16px;\n        --Iconsax-Color: #5565FE;\n    }\n}\n\n.coupon-list{\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n    height: 670px;\n    overflow: auto;\n}\n\n.coupon-item{\n    border: 1px solid $gray-color;\n    border-radius: 12px;\n    .coupon-content{\n        position: relative;\n        gap: 8px;\n        padding: 20px;\n        text-align: left;\n        @include flex_common ($dis: flex, $align: center, $justify: space-between);\n        h5{\n            font-weight: 500;\n            line-height: 20px;\n            margin-bottom: 6px;\n\n        }\n        p{\n            color: $light-color !important;\n            line-height: 18px;\n            margin: 0;\n            span{\n                font-weight: 700;\n            }\n        }\n        .percent{\n            font-size: 18px;\n            width: 80px;\n            font-weight: 700;\n            line-height: 19px;\n            padding-left: 30px;\n            border-left: 1px dashed rgba($title-color, 0.40);\n        }\n        &:after, &:before{\n            content: \"\";\n            position: absolute;\n            pointer-events: none;\n            border: solid transparent;\n            display: block;\n            border-width: 1px;            \n            width: 12px;\n            height: 12px;            \n            border-radius: 100%;\n            background-color: $white;\n            border-right-color: $gray-color;\n            border-left-color: $gray-color;\n            border-bottom-color: $gray-color;\n            top: -3px;\n        }\n\n        &:after{\n            right: 95px;\n        }\n        &:before{\n            left: 40px;\n        }\n    }\n    .coupon-footer{\n        background-color: rgba($primary-color, 0.10);\n        display: flex;\n        justify-content: space-between;\n        gap: 4px;\n        padding: 20px;\n        border-bottom-left-radius: 12px;\n        border-bottom-right-radius: 12px;\n        padding-top: 35px;\n\n        p{\n            margin: 0;\n            color: $light-color !important;\n            span{\n                color: $title-color;\n            }\n        }\n\n        .use-code{\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 4px;\n            color: $primary-color;\n            font-weight: 500;\n            line-height: 18px;\n            i{\n                --Iconsax-Size: 18px;\n                --Iconsax-Color: #5565FE;\n               }\n        }\n    }\n}\n.circle{\n    background-image: url(../../assets/images/circle.png);\n    background-position: top left;\n    background-repeat: repeat-x;\n    background-size: contain;\n    height: 22px;\n    width: 100%;\n    margin: -14px 0;\n    position: relative;\n    z-index: 1;\n}\n\n.payment{\n    background-color: $section-bg;\n    border-radius: 15px;\n    .payment-header{\n        border-bottom: 1px solid $gray-color;\n        padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n        position: relative;\n        @include flex_common ($dis: flex, $align: start, $justify: space-between);\n        span{\n            color: $light-color;\n            margin-top: 4px;\n            font-size: 16px;\n        }\n        .back-icon{\n            --Iconsax-Size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n            --Iconsax-Color: #2263eb;\n            position: absolute;\n            top: calc(14px + (21 - 14) * ((100vw - 320px) / (1920 - 320)));;\n            left: 18px;\n        }\n\n        @include mq-max(md){\n            flex-direction: column;\n            align-items: start;\n            .edit-option{\n                padding-left: 24px;\n            }\n        }\n    }\n    .payment-body{\n        padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n        overflow: auto;\n        height: 450px;\n    \n        .payment-options{\n            .payment-option{\n                background-color: $white;\n                border: 1px solid $gray-color;\n                border-radius: 10px;\n                padding: calc(12px + (20 - 12) * ((100vw - 320px) / (1920 - 320)));\n                .payment-title{\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: calc(8px + (16 - 8) * ((100vw - 320px) / (1920 - 320)));\n                    a{\n                        color: $title-color;\n                    }\n                    h4{\n                        line-height: 23px;\n                        font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n                        &.wallet{\n                            &:hover{\n                                color: $primary-color;\n                                text-decoration: underline;\n                            }\n                        }\n                    }\n                    p{\n                        margin: 0;\n                        font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n                        color: $light-color;\n                        line-height: 20px;\n                        span{\n                            font-weight: 700;\n                            color: $success-color;\n                        }\n                    }\n\n                    .payment-img{\n                        min-width: calc(40px + (50 - 40) * ((100vw - 320px) / (1920 - 320)));\n                        width: calc(40px + (50 - 40) * ((100vw - 320px) / (1920 - 320)));\n                        height: calc(40px + (50 - 40) * ((100vw - 320px) / (1920 - 320)));\n                        background-color: $section-bg;\n                        border-radius: 8px;\n                        @include flex_common ($dis: flex, $align: center, $justify: center);\n                        img{\n                            width: calc(22px + (25 - 22) * ((100vw - 320px) / (1920 - 320)));\n                            height: calc(22px + (25 - 22) * ((100vw - 320px) / (1920 - 320)));\n                            object-fit: contain;                    \n                        }\n                        .active{\n                            display: none;\n                        }\n                        .deactive{\n                            display: block; \n                        }\n                    }\n                }\n                \n\n\n                .form-check{\n                    gap: 4px;\n                    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                    flex-direction: row-reverse;\n                    .form-radio-input{\n                        &:checked{\n                            ~.payment-title{\n                                .payment-img{\n                                    background-color: rgba($primary-color, 0.10);\n                                    .active{\n                                        display: block;\n                                    }\n                                    .deactive{\n                                        display: none;\n                                    }\n                                }\n                                a{\n                                    color: $primary-color;\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n       \n    }\n    .payment-footer{\n        padding: 20px;\n        border-top: 1px solid $gray-color;\n        .btn{\n            width: max-content;\n            padding: 10px 46px;\n            padding: calc(4px + (10 - 4) * ((100vw - 320px) / (1920 - 320))) calc(20px + (46 - 20) * ((100vw - 320px) / (1920 - 320)));\n            font-size: calc(14px + (18 - 14) * ((100vw - 320px) / (1920 - 320)));\n        }\n    }\n    .wallet-body{\n        padding: 20px;\n        .total-balance{\n            background-color: $primary-color;\n            background-image: url(../../assets/images/banner/3.png);\n            background-position: center;\n            background-repeat: no-repeat;\n            background-size: cover;\n            position: relative;\n            z-index: 1;\n            border-radius: 10px;\n            padding: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));\n            margin-bottom: 20px;\n            p{\n                color: rgba($white, 0.70);\n                font-size: 16px;\n                font-weight: 500;\n                margin: 0;\n                line-height: 20px;\n            }\n            h3{\n                font-weight: 700;\n                color: $white;\n                line-height: 31px;\n            }\n        }\n        .wallet-history{\n            overflow: auto;\n            height: 660px;\n            .history-list{\n                border: 1px solid $gray-color;\n                padding: 16px;\n                border-radius: 10px;\n                background-color: $white;\n                @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                gap: 4px;\n                box-shadow: 0px 4px 4px 0px rgba($title-color, 0.06);\n                \n                .status{\n                    font-size: 18px;\n                    font-weight: 500;\n                    margin: 0;\n                    line-height: 23px;\n                }\n\n                .date{\n                    font-size: 16px;\n                    color: $light-color;\n                    line-height: 20px;\n                }\n\n                .balance{\n                    font-size: 18px;\n                    font-weight: 700;\n                    margin: 0;\n                    line-height: 23px;\n                }\n\n                .credit, .debit{\n                    font-size: 16px;\n                    font-weight: 500;\n                    line-height: 20px;\n                }\n\n                .credit{\n                    color: $success-color;\n                }\n\n                .debit{\n                    color: $danger-color;\n                }\n\n                @include mq-max(md){\n                    flex-direction: column;\n                    align-items: start;\n                }\n\n            }\n        }\n        .select-date{\n            @include flex_common ($dis: flex, $align: center, $justify: space-between);\n            gap: 10px;\n            padding-bottom: 24px;\n            margin: 0;\n            h4{\n                color: $title-color;\n                font-weight: 500;\n            }\n            .date-pick{\n                @include flex_common_1 ($dis: flex, $align: center);\n                gap: 12px;\n                label{\n                    white-space: nowrap;\n                }\n                .input-group{\n                    input{\n                        border-radius: 8px;\n                        font-size: 13px;\n                        min-width: 232px;                    \n                    }\n                }\n            }\n            @include mq-max(md){\n                gap: 10px;\n                flex-direction: column;\n                align-items: start;\n                justify-content: unset;\n            }\n        }\n    }\n}", "/**=====================\n    Comment scss\n==========================**/\n\n.review-section {\n    .review-content {\n        .review-card {\n            border: 1px solid $gray-color;\n            padding: calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320)));\n            border-radius: 8px;\n            background-color: $section-bg;\n\n            +.review-card {\n                margin-top: 20px;\n            }\n\n            .review-detail {\n                display: flex;\n                flex-direction: column;\n                align-items: start;\n                gap: 8px;\n                position: relative;\n\n                .review-image-box {\n                    display: flex;\n                    align-items: start;\n                    gap: calc(8px + (16 - 8) * ((100vw - 320px) / (1920 - 320)));\n                    width: 90%;\n\n                    h4 {\n                        font-weight: 600;\n                        font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n                    }\n\n                    span {\n                        color: $light-color;\n                        font-weight: 500;\n                    }\n\n                    h4,\n                    span {\n                        width: 100%;\n                        overflow: hidden;\n                        display: -webkit-box;\n                        -webkit-line-clamp: 1;\n                        -webkit-box-orient: vertical;\n                        text-overflow: ellipsis;\n                        word-break: break-all;\n\n                    }\n                }\n            }\n\n            img {\n                border-radius: 100%;\n                height: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));\n                width: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));\n            }\n\n            .review {\n                padding-left: calc(41px + (48 - 41) * ((100vw - 576px) / (1920 - 576)));\n\n                p {\n                    font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n                    line-height: 1.25;\n                }\n\n                @include mq-max(sm) {\n                    padding-left: 0;\n                }\n            }\n\n            .favourite {\n                top: 0;\n                right: calc((-3px) + (15 - (-3)) * ((100vw - 320px) / (1920 - 320)));\n                left: unset;\n                padding: 0;\n                background-color: unset;\n\n                .nolike {\n                    --Iconsax-Size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n                    --Iconsax-Color: #808B97;\n\n                    &.hide {\n                        display: none;\n                    }\n                }\n\n                .like {\n                    height: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320))) !important;\n                    width: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320))) !important;\n                    display: none;\n\n                    &.show {\n                        display: block;\n                    }\n                }\n            }\n\n            .review-card {\n                border: unset;\n                padding: 0;\n                border-radius: unset;\n                background-color: unset;\n                margin-left: calc(15px + (55 - 15) * ((100vw - 320px) / (1920 - 320)));\n                margin-top: 24px;\n            }\n        }\n    }\n}\n\n.comment-section {\n    form {\n        border: 1px solid $gray-color;\n        padding: calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320)));\n        border-radius: 8px;\n        background-color: $section-bg;\n\n        .btn {\n            width: max-content;\n\n        }\n    }\n}", "/**=====================\n    Dropdown scss\n==========================**/\n\n.dropdown {\n  .onhover-show-div {\n    transition: all 0.3s linear;\n    position: absolute;\n    z-index: 3;\n    background-color: $white;\n    box-shadow: 0 0 7px 0px rgba($black, 0.1);\n    opacity: 0;\n    visibility: hidden;\n    transform: translate3d(0, -5%, 0);\n    top: 35px;\n    right: 14px;\n    padding: 14px;\n    border-radius: 4px;\n\n    li {\n      display: block;\n      cursor: pointer;\n      transition: all 0.3s;\n\n      &:hover {\n        transform: translateX(5px);\n\n        [dir=\"rtl\"] & {\n          transform: translateX(-5px);\n        }\n\n        a {\n          color: var(--theme-color);\n        }\n      }\n\n      +li {\n        margin-top: 5px;\n      }\n    }\n  }\n\n  &:hover {\n    .onhover-show-div {\n      opacity: 1;\n      visibility: visible;\n      transform: none;\n    }\n  }\n\n  &.select-dropdown-div {\n    .select-btn {\n      background: $white;\n      color: $title-color;\n      border: none;\n      padding: 12px;\n      gap: 12px;\n      // height: 44px;\n      height: calc(38px + (44 - 38) * ((100vw - 320px) / (1920 - 320)));\n      border-radius: 8px;\n      width: 100%;\n      @include flex_common ($dis: flex, $align: center, $justify: space-between);\n\n      i {\n        --Iconsax-Size: 18px !important;\n      }\n\n      span {\n        display: inline-flex;\n        align-items: center;\n        gap: 8px;\n        line-height: 1;\n        font-size: 15px;\n\n        img {\n          width: 22px;\n        }\n      }\n    }\n\n    .select-show-div {\n      transition: all ease-in-out .3s;\n      position: absolute;\n      z-index: 3;\n      background-color: $white;\n      transform: translate3d(0, -5%, 0);\n      top: 60px;\n      right: 0;\n      padding: 14px;\n      border-radius: 4px;\n      opacity: 0;\n      visibility: hidden;\n      width: 100%;\n\n      &.active {\n        opacity: 1;\n        visibility: visible;\n        transition: all ease-in-out .3s;\n      }\n\n      .select-show-div-item {\n        width: 100%;\n        cursor: pointer;\n        span {\n          @include flex_common_1 ($dis: flex, $align: center);\n          gap: 8px;\n          font-size: 15px;\n          img {\n            width: 22px;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Select Dropdown \n.select-dropdown {\n  @include flex_common ($dis: flex, $align: center, $justify: space-between);\n  gap: 4px;\n  padding-bottom: calc(12px + (24 - 12) * ((100vw - 320px) / (1920 - 320)));\n  margin-bottom: calc(12px + (24 - 12) * ((100vw - 320px) / (1920 - 320)));\n  border-bottom: 1px solid $gray-color;\n\n  .onhover-show-div {\n    min-width: 120px;\n    right: 14px;\n    padding: 14px;\n\n    [dir=\"rtl\"] & {\n      left: 14px;\n      right: unset;\n    }\n\n    li {\n      display: block;\n      cursor: pointer;\n      transition: all 0.3s;\n\n      &:hover {\n        color: var(--theme-color);\n        transform: translateX(5px);\n\n        [dir=\"rtl\"] & {\n          transform: translateX(-5px);\n        }\n      }\n\n      +li {\n        margin-top: 5px;\n      }\n    }\n  }\n\n  .form-group {\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 16px;\n\n    label {\n      color: $light-color;\n      font-size: 16px;\n    }\n  }\n}", "/**=====================\n     Form Wizard scss\n==========================**/\n\n.basic-wizard {\n\t.stepper-horizontal {\n        padding: 20px;\n\t\twidth: 100%;\n\t\tmargin: 0 auto;\n\t\tmargin-bottom: 24px;\n        border-bottom: 1px solid $gray-color;\n\t\tdisplay: flex;\n        justify-content: center;\n        gap: 20px;\n\n        @include mq-max(md){\n            flex-direction: column;\n            gap: 16px;\n        }\n\n\t\t.step {\n\t\t\tposition: relative;\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 12px;\n            padding-inline: 30px;\n            &:last-child{\n                &:after{\n                    display: none;\n                }\n            }\n\n            &:after{\n                content: \"\";\n                border-bottom: 1px dashed $primary-color;\n                height: 1px;\n                width: 50px;\n                position: absolute;\n                top: 50%;\n                right: -35px;\n                @include mq-max(md){\n                    display: none;\n                }\n            }\n\n            .step-title {\n\t\t\t\tfont-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tcolor: $light-color;\n\t\t\t}        \n\n            &.stepper{\n                .step-circle{\n\t\t\t\t    border: 1px dashed $primary-color;\n                    color: $primary-color;\n                }\n                .step-title{\n                    color: $primary-color;\n                    font-weight: 600;\n                }\n            }\n\n\t\t\t&:first-child {\n\t\t\t\tpadding-left: 0;\n                @include mq-max(md){\n                    padding-right: 0;\n                }\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tpadding-right: 0;\n                @include mq-max(md){\n                    padding-left: 0;\n                }\n\t\t\t}\n\n\t\t\t&:last-child .step-bar-left,\n\t\t\t&:last-child .step-bar-right {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t\t.step-circle {\n\t\t\t\twidth:calc(40px + (45 - 40) * ((100vw - 320px) / (1920 - 320)));\n                height:calc(40px + (45 - 40) * ((100vw - 320px) / (1920 - 320)));\n                border-radius: 8px;\n                background-color: white;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n\t\t\t\tfont-size: 18px;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t&.done {\n\t\t\t\t.step-circle {\n\t\t\t\t\tbackground-color: $primary-color;\n                    border: 1px solid $primary-color;\n\t\t\t\t\tcolor: $white;\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t&:before {\t\t\t\n                        content: \"\";\n                        position: absolute;                       \n                        top: calc(10px + (12 - 10) * ((100vw - 320px) / (1920 - 320)));\n                        left: calc(10px + (12 - 10) * ((100vw - 320px) / (1920 - 320)));\n                        background-image: url(../../assets/images/svg/tick.svg);\n                        background-repeat: no-repeat;\n                        background-position: center;\n                        background-size: contain;\n                        z-index: 0;\n                        width: 20px;\n                        height: 20px;\n                    }\n\n\t\t\t\t\t* {\n\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.step-title {\n\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t}\n                ~.step{\n                    .step-circle{\n                        border: 1px dashed $primary-color;\n                        color: $primary-color;\n                    }\n                    .step-title{\n                        color: $primary-color;\n                        font-weight: 600;\n                    }\n                }\n\t\t\t}\n\t\t\t.step-title,\n\t\t\t.step-optional {\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\n\t}\n}\n\n.service-booking{\n    padding: calc(20px + (30 - 20) * ((100vw - 320px) / (1920 - 320)));\n    width: 100%;\n    ul{\n        .booking-list{\n            padding-bottom: 35px;\n            position: relative;\n            &:before{\n                position: absolute;\n            content: \"\";\n            border: 1px dashed $light-color;\n            opacity: 0.3;\n            top: 12px;\n            left: 3px;\n            height: 100%;\n            }\n            &:last-child{\n                padding: 0;\n                &:before{\n                    display: none;\n                }\n            }\n        }\n    }\n    h3{\n        font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n        font-weight: 500;\n        line-height: 23px;\n        color: $title-color;\n        margin-bottom: 12px;\n    }\n    .delivery-location{\n        background-color: $white !important;\n        border: 1px solid $gray-color !important;\n        border-radius: 10px;\n        margin-bottom: 0;\n\n        .location-header{\n            padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n            @include flex_common ($dis: flex, $align: center, $justify: space-between);\n            gap: 4px;\n            border-bottom: 1px solid $gray-color;\n            \n            @include mq-max(sm){\n                flex-direction: column;\n                align-items: start;\n            }\n\n            .loaction-icon{\n                border-radius: 100%;\n                height: 50px;\n                min-width: 50px;\n                width: 50px;\n                background-color: $gray-color;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n                @include mq-max(sm){\n                    height: 35px;\n                    min-width: 35px;\n                    width: 35px;\n                }\n                img{\n                    height: 24px;\n                    @include mq-max(sm){\n                        height: 16px;\n                    }\n                }\n            }\n            .active-icon{\n                border: 1px solid $primary-color;\n                border-radius: 100%;\n                height: 50px;\n                min-width: 50px;\n                width: 50px;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n                img{\n                    height: 24px;\n                    background-color: $primary-color;\n                    border: 1px solid $white;\n                    padding: 10px;\n                    border-radius: 100%;\n                    height: 42px;\n                    min-width: 42px;\n                    width: 42px;\n                    @include flex_common ($dis: flex, $align: center, $justify: center);\n                }\n            }\n            .name{\n               h4{\n                font-weight: 500;\n                font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n               } \n               span{\n                font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n               }\n            }\n            .badge{\n                @include mq-max(sm){\n                    margin-left: 50px;\n                }\n            }\n        }\n        .address{\n            padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n            p{\n                font-size: 16px;\n            }\n            .btn-outline{\n                border-width: 1px;\n                width: max-content;\n                padding:  9px 18px;\n            }\n        }\n        .action{\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 16px;\n            padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n            padding-top: 0;\n            width: max-content;\n            position: relative;\n            .btn{\n                width: max-content;\n                padding: calc(3px + (5 - 3) * ((100vw - 320px) / (1920 - 320))) 12px;\n                border-radius: 8px;\n                font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n            }\n            .radio{\n                position: absolute;\n                top: 1px;\n                left: calc(17px + (20 - 17) * ((100vw - 320px) / (1920 - 320)));\n                width: calc(104px + (115 - 104) * ((100vw - 320px) / (1920 - 320)));\n                height: calc(33px + (37 - 33) * ((100vw - 320px) / (1920 - 320)));\n                opacity: 0;\n                cursor: pointer;\n            }\n        }\n        .radio:checked {\n            ~button{\n                background-color: $primary-color;\n                color: $white;\n            }\n        }\n    }\n    input[type=\"radio\"]{\n        &:checked{\n            ~.delivery-location{\n               .btn-outline{\n                background-color: $primary-color;\n                color: $white;\n               } \n            }\n        }\n    }\n    .add-location{\n        color: $primary-color;\n        background-color: transparent;\n        border: none;\n        cursor: pointer;\n        font-weight: 500;\n        font-size: 16px;\n        &:hover{\n            text-decoration: underline;\n        }\n    }\n    .booking-data{\n        margin-top: -8px;\n        width: 100%;\n        margin-left: calc(14px + (24 - 14) * ((100vw - 320px) / (1920 - 320)));\n\n        .select-option{\n            flex-direction: row;\n            background-color: transparent;\n            border: none;\n            background-color: $white;\n            padding: 0 0 20px 0 ;\n            gap: calc(4px + (40 - 4) * ((100vw - 320px) / (1920 - 320)));\n            flex-wrap: wrap;\n            .form-check{\n                border: none;\n                padding: 0;\n                flex-direction: row;\n                min-height: 1.5rem;\n            }\n        }\n        .text-success{\n            font-size: 18px;\n        }\n        .servicemen-list-item{\n            width: max-content;\n            min-width: 276px;\n            background-color: $section-bg !important;\n            border: none !important;\n            box-shadow: none !important;\n            @include mq-max(xs){\n                min-width: 188px;\n                width: auto;\n            }\n        }\n    }\n    .form-control{\n        background-color: $white;\n        border-radius: 10px;\n        padding: 16px;\n        color: $title-color;\n        &:hover, &:focus{\n            color: $title-color;\n            background-color: $white;\n        }\n    }\n}\n\n.activity-dot{\n    min-width: 8px;\n    width: 8px;\n    height: 8px;\n    background-color: $light-color;\n    border-radius: 100%;\n    outline: 1px solid $light-color;\n    position: relative;\n    z-index: 0;\n\n    &:after{\n        content: \"\";\n        position: absolute;\n        height: 16px;\n        width: 16px;\n        border: 1px solid $light-color;\n        border-radius: 100%;\n        top: -4px;\n        left: -4px;\n    }\n}\n\n.wizard-footer{\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 10px;\n    border-top: 1px dashed $gray-color;\n    padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320))) calc(47px + (60 - 47) * ((100vw - 320px) / (1920 - 320)));\n    .btn-outline{\n        border-width: 1px;\n    }\n    button{\n        width: max-content;\n        padding: calc(4px + (10 - 4) * ((100vw - 320px) / (1920 - 320))) calc(12px + (24 - 12) * ((100vw - 320px) / (1920 - 320)));\n        font-size: calc(14px + (18 - 14) * ((100vw - 320px) / (1920 - 320)));\n    }\n}\n\n.date-time-picket-sec{\n    background-color: $white;\n    border-radius: 10px;\n    border: 1px solid $gray-color;\n    padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n}\n.date-time-picker{\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 12px;\n    @include mq-max(md){\n        flex-wrap: wrap;\n    }\n    input{\n        color: $title-color !important;\n    }\n}\n\n.input-icon{\n    position: absolute;\n    top: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320)));\n    left: 16px;\n    --Iconsax-Size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));\n    --Iconsax-Color: #808B97;\n    z-index: 5;\n}\n\n.edit-option{\n    background-color: unset;\n    border: none;\n    padding: 0;\n    color: $primary-color;\n    text-decoration: underline;\n    font-size: 18px;\n}\n\n.selected-men{\n    background-color: $white;\n    padding: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n    border-radius: 10px;\n    @include flex_common_1 ($dis: flex, $align: center);\n    flex-wrap: wrap;\n    gap: 12px;\n}\n", "/**=====================\n    Form scss\n==========================**/\n\n.form-control {\n    border: 1px solid rgba($white, 0.10%);\n    background-color: rgba($white, 0.10%);\n    font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n    color: $white;\n    padding: calc(7px + (10 - 7) * ((100vw - 320px) / (1920 - 320))) 16px;\n    width: 100%;\n    border-radius: 8px;\n\n    ::placeholder {\n        color: $gray-color;\n    }\n\n    &.first-child {\n        border-top-left-radius: 30px;\n        border-bottom-left-radius: 30px;\n    }\n\n    &.last-child {\n        border-top-right-radius: 30px;\n        border-bottom-right-radius: 30px;\n    }\n\n    &:focus,\n    &:hover {\n        background-color: rgba($white, 0.10%);\n        color: $title-color;\n        box-shadow: none;\n        border: 1px solid rgba($white, 0.10%);\n    }\n\n    &.form-control-gray {\n        background-color: $section-bg;\n        color: $title-color;\n\n        &:focus,\n        &:hover {\n            background-color: $section-bg;\n            color: $title-color;\n            box-shadow: none;\n        }\n\n        ::placeholder {\n            color: $gray-color;\n        }\n    }\n\n    &.form-control-white {\n        background-color: $white;\n        color: $title-color;\n        border-radius: 8px;\n        // border: 1px solid $gray-color;\n\n        &:focus,\n        &:hover {\n            background-color: $white;\n            color: $title-color;\n            box-shadow: none;\n        }\n\n        ::placeholder {\n            color: $gray-color;\n        }\n    }\n}\n\ntextarea {\n    &.form-control {\n        min-height: calc(39px + (46 - 39) * ((100vw - 320px) / (1920 - 320)));\n        ;\n    }\n}\n\n.comment-section {\n    textarea {\n        &.form-control {\n            min-height: 110px;\n        }\n    }\n}\n\n.form-group {\n    margin-bottom: 24px;\n\n    &:last-child {\n        margin-bottom: 0;\n    }\n\n    label {\n        font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n        font-weight: 500;\n        line-height: 23px;\n        margin-bottom: 8px;\n        position: relative;\n    }\n}\n\n.form-check {\n    padding: 0;\n    min-height: unset;\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 12px;\n\n    .form-check-input,\n    .form-radio-input {\n        cursor: pointer;\n        position: relative;\n        border: none;\n        width: 20px;\n        height: 20px;\n        box-shadow: none;\n        background-color: transparent;\n        @include flex_common_1 ($dis: flex, $align: center);\n        margin-top: 0;\n\n        &:focus {\n            box-shadow: none;\n        }\n\n        &:before {\n            transition: transform 0.4s cubic-bezier(0.45, 1.8, 0.5, 0.75);\n            transform: rotate(-45deg) scale(0, 0);\n            content: \"\";\n            position: absolute;\n            left: 3px;\n            top: 5px;\n            z-index: 1;\n            width: 0.75rem;\n            height: 0.4rem;\n            border: 2px solid $white;\n            border-top-style: none;\n            border-right-style: none;\n        }\n\n        &:after {\n            content: \"\";\n            position: absolute;\n            top: -1px;\n            left: -1px;\n            width: 20px;\n            height: 20px;\n            background: $white;\n            border: 2px solid $gray-color;\n            cursor: pointer;\n            border-radius: 4px;\n        }\n\n        &:checked {\n            &:before {\n                transform: rotate(-45deg) scale(1, 1);\n            }\n\n            &:after {\n                background-color: $primary-color;\n                border-color: $primary-color;\n            }\n        }\n    }\n\n    .form-radio-input {\n        margin: 0;\n        padding: 0;\n        height: 10px;\n\n        &:after {\n            border-radius: 100%;\n            left: 0;\n            top: -5px;\n        }\n\n        &:before {\n            border: none;\n            background-color: $primary-color;\n            border-radius: 100%;\n            width: 10px;\n            height: 10px;\n            top: 0px;\n            left: 5px;\n\n        }\n\n        ~label {\n            color: $light-color;\n        }\n\n        &:checked {\n            &:after {\n                border: 6px solid rgba($primary-color, 0.12%);\n                background-color: $white;\n            }\n\n            ~label {\n                font-weight: 500;\n                color: $title-color;\n            }\n        }\n\n        &.solid {\n            &:checked:after {\n                border: 1px solid $primary-color;\n            }\n        }\n    }\n\n    p {\n        font-size: 16px;\n    }\n\n    label {\n        font-size: 14px;\n    }\n}\n\n.form-select {\n    background-color: $section-bg;\n    border: none;\n    color: $title-color;\n    font-size: 16px;\n    padding: 8px 70px 8px 12px;\n    border-radius: 6px;\n\n    &:focus {\n        box-shadow: none;\n    }\n\n    &.form-select-white {\n        background-color: $white;\n        color: $light-color;\n        border-radius: 8px;\n        padding: 10px 46px;\n\n        &:focus,\n        &:hover {\n            background-color: $white;\n            color: $light-color;\n            box-shadow: none;\n        }\n    }\n}\n\n.input-group {\n    .form-control {\n        padding: calc(7px + (10 - 7) * ((100vw - 320px) / (1920 - 320))) 16px calc(7px + (10 - 7) * ((100vw - 320px) / (1920 - 320))) 46px;\n    }\n\n    &.phone-field {\n        gap: 10px;\n\n        .contact-btn {\n            border-radius: 8px;\n        }\n\n    }\n}\n\n.search-form {\n    margin-top: 30px;\n    width: 30%;\n    margin-inline: auto;\n    padding: 10px;\n    border-radius: 40px !important;\n    border: 1px solid rgba($white, 0.20%);\n\n    .form-control {\n        border-radius: 40px;\n        color: $white;\n    }\n\n    ::placeholder {\n        color: $light-color;\n    }\n\n    i {\n        --Iconsax-Size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n        --Iconsax-Color: #808B97;\n        position: absolute;\n        right: 16px;\n        top: 50%;\n        transform: translateY(-50%);\n    }\n\n    @include mq-max(3xl) {\n        width: 40%;\n    }\n\n    @include mq-max(lg) {\n        width: 70%;\n        padding: 4px;\n    }\n\n    @include mq-max(sm) {\n        width: 90%;\n        margin-top: 16px;\n    }\n}\n\n.avatar-group {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 20px;\n\n    .avatar {\n        width: 40px;\n        height: 40px;\n\n        img {\n            border-radius: 100%;\n            width: 100%;\n            height: 100%;\n        }\n\n        &:first-child {\n            margin-right: -20px;\n        }\n\n        &:nth-child(2) {\n            width: 45px;\n            height: 45px;\n            position: relative;\n            z-index: 1;\n        }\n\n        &:last-child {\n            margin-left: -20px;\n        }\n    }\n}", "/**=====================\n    Home Section scss\n==========================**/\n\n.home-section {\n    position: relative; \n    background-image: url(../../assets/images/bg.jpg);  \n    background-repeat: no-repeat;\n    background-position: center;\n    background-size: cover; \n    z-index: 0; \n    padding-top: calc(60px + (70 - 60) * ((100vw - 320px) / (1920 - 320)));\n\n    .home-icon {\n        img {\n            position: absolute;\n            animation: mover 2s infinite alternate;\n            z-index: -1;\n\n            &.image-1 {\n                top: 180px;\n                right: -65px;\n                animation-delay: 1s;\n                width: 140px;\n                overflow: hidden;\n                @include mq-max(sm){\n                    display: none;\n                 }\n            }\n\n            &.image-2 {\n                top: calc(200px + (280 - 200) * ((100vw - 992px) / (1920 - 992)));\n                left: 60px;\n                animation-delay: 2s;\n                height: 120px;\n                @include mq-max(lg){\n                   display: none;\n                }\n            }\n\n            &.image-3 {\n                animation-delay: 3s;\n                height: 140px;\n                animation-delay: 3s;\n                height: 140px;\n                filter: brightness(0.5);\n                transform: rotate3d(3, 2, 1, 180deg);\n                animation: unset;\n                top: calc(220px + (320 - 220) * ((100vw - 992px) / (1920 - 992)));\n                right: calc(20px + (240 - 20) * ((100vw - 992px) / (1920 - 992)));\n                @include mq-max(lg){\n                    display: none;\n                }\n            }\n        }\n    }\n\n    .home-slider {\n        margin-top: calc(220px + (490 - 220) * ((100vw - 320px) / (1920 - 320)));\n\n        @include mq-max(lg){\n            margin-top: calc(320px + (340 - 320) * ((100vw - 320px) / (1920 - 320)));\n        }\n\n        @include mq-max(md){\n            margin-top: calc(200px + (300 - 200) * ((100vw - 320px) / (1920 - 320)));\n        }\n\n        .service-slider {\n            .swiper-wrapper {\n                 height: 372px;\n                 transition-timing-function : linear;\n                @include mq-max(lg){\n                    height: 332px;\n                }\n                @include mq-max(sm){\n                    height: 300px;\n                }\n                .swiper-slide {\n                    position: relative;\n                    padding-top: 10px;\n\n                    .service-card {\n                        height: 100%;\n                        .img-box {\n                            height: 100%;\n                            .bg-size {\n                                border-top-right-radius: 25px;\n                                border-top-left-radius: 25px;\n                                width: 100%;\n                                height: 100%;\n                                background-position: top center !important;\n                            }\n                        }\n\n                        .service-content {\n                            background: linear-gradient(180deg, rgba(0, 10, 28, 0) 0%, rgba(0, 10, 28, 0.****************) 56%, rgba(0, 10, 28, 0.9332107843137255) 100%);\n                            position: absolute;\n                            bottom: 0;\n                            left: 0;\n                            right: 0;\n                            margin: 0 auto;\n                            text-align: center;\n                            color: rgba($white, 0.60%);\n                            padding: 90px 0 16px;\n                            font-size: 18px;\n\n                            span {\n                                text-align: center;\n                                position: relative;\n\n                                &:before {\n                                    content: \"\";\n                                    position: absolute;\n                                    width: 5px;\n                                    height: 5px;\n                                    background-color: rgba($white, 0.60%);\n                                    border-radius: 100%;\n                                    top: 50%;\n                                    left: -12px;\n                                    transform: translateY(-50%);\n                                }\n                            }\n                        }\n                    }\n\n                    &:nth-child(even) {\n                        padding-top: calc(40px + (100 - 40) * ((100vw - 320px) / (1920 - 320)));\n                    }\n                }\n            }\n\n        }\n    }\n\n    .home-contain {\n        position: absolute;\n        top: calc(80px + (160 - 80) * ((100vw - 320px) / (1920 - 320)));\n        text-align: center;\n        left: 0px;\n        right: 0;\n        width: 90%;\n        text-align: center;\n        margin: 0 auto;\n\n        @include mq-min(lg) {\n            left: -40px;\n        }\n        @include mq-max(lg){\n            top: 90px;\n        }\n\n        h1 {\n            color: $white;\n            line-height: 1;\n            @include mq-max(lg) {\n                width: 70%;\n                margin-inline: auto;\n            }\n            @include mq-max(md) {\n                width: 90%;\n            }\n            @include mq-max(sm) {\n                width: 100%;\n            }\n        }\n\n        p {\n            color: $light-color;\n            font-size: 20px;\n            padding-top: 50px;\n            width: 45%;\n            line-height: 23px;\n            margin: 0 auto;\n            \n            @include mq-max(4xl){\n                width: 55%;\n            }\n            @include mq-max(2xl){\n                font-size: 18px;\n            }\n            @include mq-max(xl){\n                width: 65%;\n                padding-top: 40px;\n            }\n            @include mq-max(lg){\n                display: none;\n            }\n        }\n\n        .home-form-group {\n            padding-top: 40px;\n            // width: 40%;\n            width: 700px;\n            margin: 0 auto;\n\n            @include mq-min(lg){\n                padding-top: calc(15px + (30 - 15) * ((100vw - 992px) / (1920 - 992)));\n            }\n\n            @include mq-max(md){\n               display: none;\n            }\n\n            .input-group {\n                padding: 10px;\n                border-radius: 40px !important;\n                border: 1px solid rgba($white, 0.20%);\n                border-radius: 0;\n                @include flex_common_1 ($dis: flex, $align: center);\n                gap: 12px;\n                flex-wrap: nowrap;\n\n                .position-relative {\n                    i {\n                        --Iconsax-Size: 18px;\n                        --Iconsax-Color: #808B97;\n                        position: absolute;\n                        top: 50%;\n                        transform: translateY(-50%);\n                        left: 14px;\n                    }\n\n                    .form-control {\n                        padding: 10px 16px 10px 37px;\n                        color: $white;\n                        \n                    }\n                    ::placeholder{\n                        color: $light-color;\n                    }\n                }\n                .btn {\n                    border-top-right-radius: 30px;\n                    border-bottom-right-radius: 30px;\n                }\n            }\n\n        }\n\n        .home-animation {\n            position: relative;\n            line-height: 1;\n            margin-left: 35px;\n            text-transform: uppercase;\n            font-size: calc(22px + (60 - 22) * ((100vw - 320px) / (1920 - 320)));\n            letter-spacing: 2px;\n            font-weight: 900;\n            background: linear-gradient(to right,\n                    rgba(251, 64, 64, 1) 20%, rgba(255, 84, 156, 1) 30%, rgba(114, 94, 254, 1) 70%, rgba(36, 119, 255, 1) 80%);\n            -webkit-background-clip: text;\n            background-clip: text;\n            -webkit-text-fill-color: transparent;\n            text-fill-color: transparent;\n            background-size: 150% auto;\n            animation: textShine 1s ease-in-out infinite alternate;\n\n            .shape {\n                border-radius: 35px;\n                position: absolute;\n                top: -21px;\n                left: -27px;\n                z-index: -1;\n                width: calc(375px + (560 - 375) * ((100vw - 992px) / (1920 - 992)));\n                height: calc(81px + (108 - 81) * ((100vw - 992px) / (1920 - 992)));\n                @include mq-max(lg){\n                    // width: calc(260px + (560 - 260) * ((100vw - 320px) / (1920 - 320)));\n                    // height: calc(46px + (81 - 46) * ((100vw - 320px) / (1920 - 320)));\n\n                    // // width: 260px;\n                    // // height: 46px;\n                    // // width: 330px;\n                    // // height: 58px;\n\n                    // top: -10px;\n                    // left: -15px;\n\n                    width: calc(250px + (530 - 250) * ((100vw - 320px) / (1920 - 320)));\n                    height: calc(46px + (90 - 46) * ((100vw - 320px) / (1920 - 320)));\n                    top: -12px;\n                    left: -23px;\n                \n                }\n            }\n\n            @include mq-max(lg) {\n               margin-top: 25px;\n               margin-left: 0;\n            }\n        }\n    }\n\n    &.ratio_18 { \n        .home-contain {\n            width: 100%;\n            top: calc(80px + (140 - 80) * ((100vw - 320px) / (1920 - 320)));\n\n            h2 {\n                font-size: calc(20px + (42 - 20) * ((100vw - 320px) / (1920 - 320)));\n                font-weight: 700;\n                background: linear-gradient(to right,\n                        rgba(251, 64, 64, 1) 20%, rgba(255, 84, 156, 1) 30%, rgba(114, 94, 254, 1) 50%, rgba(36, 119, 255, 1) 80%);\n                -webkit-background-clip: text;\n                background-clip: text;\n                -webkit-text-fill-color: transparent;\n                text-fill-color: transparent;\n                background-size: 150% auto;\n                width: max-content;\n                margin: 0 auto;\n                position: relative;\n                &:before, &:after{\n                    position: absolute;\n                    content: \"\";\n                    background-color: $primary-color;\n                    // height: 34px;\n                    height: calc(14px + (34 - 14) * ((100vw - 320px) / (1920 - 320)));\n                    width: 4px;\n                    border-radius: 6px;\n                    top: 4px;\n                    @include mq-max(2xl){\n                        top: 7px;\n                    }\n                }\n                &:before{\n                    // left: -35px;     \n                    left: calc(-25px + ((-40) - (-25)) * ((100vw - 320px) / (1920 - 320)));            \n                }\n                &:after{\n                    // right: -35px; \n                    right: calc(-25px + ((-40) - (-25)) * ((100vw - 320px) / (1920 - 320)));            \n\n                }\n                span{\n                    &:before, &:after{\n                        position: absolute;\n                        content: \"\";\n                        background-color: $primary-color;\n                        // height: 58px;\n                        height: calc(28px + (58 - 28) * ((100vw - 320px) / (1920 - 320)));\n                        width: 4px;\n                        border-radius: 6px;\n                        top: -8px;\n                        @include mq-max(2xl){\n                            top: -3px;\n                        }\n                    }\n                    &:before{\n                        // left: -25px;  \n                        left: calc(-15px + ((-30) - (-15)) * ((100vw - 320px) / (1920 - 320)));            \n\n                    }\n                    &:after{\n                        // right: -25px;  \n                        right: calc(-15px + ((-30) - (-15)) * ((100vw - 320px) / (1920 - 320)))\n                    }\n                }\n            }\n\n            p {\n                font-size: 16px;\n                line-height: 24px;\n                max-width: 680px;\n                width: 90%;\n                padding-top: calc(20px + (30 - 20) * ((100vw - 991px) / (1920 - 991)));\n                @include mq-max(lg) {\n                    display: none;\n                }\n            }\n            h3{\n                color: $white;\n                margin-top: 30px;\n                font-size: 30px;\n\n                @include mq-max(lg) {\n                    display: none;\n                }\n            }\n        }\n        .bg-size{\n            &:before{\n                @include mq-max(xl){\n                    padding-bottom: calc((170px) + ((-60) - (170)) * ((100vw - 320px) / (1920 - 320)));\n                }\n                \n                @include mq-max(lg){\n                    padding-bottom: calc((60px) + ((-70) - (60)) * ((100vw - 320px) / (1920 - 320)));\n                }\n\n                @include mq-min(xl){\n                    padding-bottom: calc((110px) + ((-70) - (110)) * ((100vw - 991px) / (1920 - 991)))\n                }\n            }\n        }\n    }\n}", "/**=====================\n    Invoice scss\n==========================**/\n\n.top_rw {\n    background-color: #f4f4f4;\n}\n\nbutton {\n    padding: 5px 10px;\n    font-size: 14px;\n}\n\n.invoice-box {\n    max-width: 890px;\n    margin: auto;\n    padding: 10px;\n    border: 1px solid #eee;\n    box-shadow: 0 0 10px rgba(0, 0, 0, .15);\n    font-size: 14px;\n    line-height: 24px;\n    font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;\n    color: #555;\n}\n\n.invoice-box table {\n    width: 100%;\n    line-height: inherit;\n    text-align: left;\n    border-bottom: solid 1px #ccc;\n}\n\n.invoice-box table td {\n    padding: 5px;\n    vertical-align: middle;\n}\n\n.invoice-box table tr td:nth-child(2) {\n    text-align: right;\n}\n\n.invoice-box table tr.top table td {\n    padding-bottom: 20px;\n}\n\n.invoice-box table tr.top table td.title {\n    font-size: 45px;\n    line-height: 45px;\n    color: #333;\n}\n\n.invoice-box table tr.information table td {\n    padding-bottom: 40px;\n}\n\n.invoice-box table tr.heading td {\n    background: #eee;\n    border-bottom: 1px solid #ddd;\n    font-weight: bold;\n    font-size: 12px;\n}\n\n.invoice-box table tr.details td {\n    padding-bottom: 20px;\n}\n\n.invoice-box table tr.item td {\n    border-bottom: 1px solid #eee;\n}\n\n.invoice-box table tr.item.last td {\n    border-bottom: none;\n}\n\n.invoice-box table tr.total td:nth-child(2) {\n    border-top: 2px solid #eee;\n    font-weight: bold;\n}\n\n@media (max-width: 600px) {\n    .invoice-box table tr.top table td {\n        width: 100%;\n        display: block;\n        text-align: center;\n    }\n\n    .invoice-box table tr.information table td {\n        width: 100%;\n        display: block;\n        text-align: center;\n    }\n}\n\n/**=====================\n    Invoice scss\n==========================**/", "/**=====================\n     Loader scss\n==========================**/\n\n#loader {\n    position: absolute;\n    background: white;\n    width: 100%;\n    height: 100vh;\n    top: 0px;\n    left: 0px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    z-index: 1050;\n  }\n  \n  .notLoaded {\n    height: 100vh;\n    overflow: hidden;\n    position: fixed;\n    margin: 0px;\n    width: 100%;\n  }\n  \n  .page-loader .page-loader-wrapper{\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n    height: 100%;\n    gap: 12px;\n  }\n  \n  .img{\n      background-image: url('../images/logo.png');\n      background-position: center;\n      background-size: cover;\n      background-repeat: no-repeat;\n      width: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));\n      height: calc(30px + (40 - 30) * ((100vw - 320px) / (1920 - 320)));\n      animation: 2s anim-lineXY ease-out infinite;\n  }", "/**=====================\n    Modal scss\n==========================**/\n\n.modal-backdrop{\n    z-index: 5;\n}\n\n.modal {\n    z-index: 6;\n    .modal-dialog {\n        .modal-content {\n            border-radius: 12px;\n\n            .modal-header {\n                padding: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));\n                border: none;\n                margin-bottom: 12px;\n                margin: 0;\n\n                .btn-close {\n                    opacity: 0.9;\n                    background-size: 12px;\n                    &:focus{\n                        border: none;\n                        box-shadow: none;\n                    }\n                }\n            }\n\n            .modal-body {\n                padding: 0 calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));\n                &.custom-scroll{\n                    overflow: auto;\n                    height: 630px;\n\n                }\n            \n                .card{\n                    margin: 0;\n                }\n\n                .modal-body-content {\n                    background-color: $section-bg;\n                    border-radius: 10px;\n                    width: 100%;\n                    height: calc(200px + (250 - 200) * ((100vw - 320px) / (1920 - 320)));\n                    @include flex_common ($dis: flex, $align: center, $justify: center);\n                    margin-bottom: 16px;\n                    padding: 20px 0;\n\n                    img {\n                        height: 100%;\n                    }\n                }\n\n                .cancel-content{\n                    background-color: $section-bg;\n                    border-radius: 10px;\n                    width: 100%;\n                    padding: 20px;\n                    label{\n                        font-size: 18px;\n                        font-weight: 500;\n                        list-style: 23px;\n                        color: $title-color;\n                        margin-bottom: 6px;\n                    }\n                }\n\n                p {\n                    font-size: 16px;\n                    color: $title-color;\n                }\n\n                .btn {\n                    &.btn-solid {\n                        width: 100%;\n                        justify-content: center;\n                        border-radius: 12px;\n                    }\n                }\n            }\n            .modal-footer{\n                border: none;\n                padding: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));\n                flex-wrap: nowrap;\n                gap: 12px;\n                .btn{\n                    margin: 0;\n                    padding: calc(7px + (10 - 7) * ((100vw - 320px) / (1920 - 320))) 16px;\n                }\n                .status-note{\n                    width: 100%;\n                    border-radius: 0;\n                    margin: 0;\n                }\n                @include mq-max(sm){\n                    // flex-wrap: wrap;\n                }\n            }\n        }\n    }\n\n    &.reset-modal {\n        .modal-dialog {\n            .modal-content {\n                .modal-body {\n                    text-align: center;\n                }\n            }\n        }\n    }\n\n    &.book-now {\n        .modal-dialog {\n            .modal-content {\n                .modal-body {\n\n                    .service-item {\n                        padding: 20px;\n                        border-radius: 8px;\n                        align-items: start;\n                        margin-bottom: 20px;\n\n                        &:last-child {\n                            margin-bottom: 0;\n                        }\n\n                        .serviceman {\n                            @include flex_common_1 ($dis: flex, $align: center);\n                            gap: 16px;\n\n                            .serviceman-detail {\n                                width: max-content;\n                                @include flex_common_1 ($dis: flex, $align: center);\n                                gap: 12px;\n                                background-color: $section-bg;\n                                border-radius: 12px;\n                                padding: 12px 16px;\n\n                                h6 {\n                                    color: $light-color;\n                                }\n\n                                p {\n                                    margin: 0;\n                                    font-size: 14px;\n                                    font-weight: 500;\n                                }\n\n                                .rate {\n                                    .star {\n                                        width: 15px !important;\n                                        height: 15px !important;\n                                    }\n                                }\n                            }\n                        }\n\n                        .btn-outline {\n                            width: max-content;\n                            border-radius: 8px;\n                            margin-top: 4px;\n                            font-size: 16px;\n                            padding: 9px 18px;\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    &.book-service {\n        .modal-dialog {\n            .modal-content {\n                .modal-body {\n                    .services {\n                        label {\n                            color: $title-color;\n                            margin-bottom: 8px;\n                            font-weight: 500;\n                        }\n\n                        .service {\n                            @include flex_common_1 ($dis: flex, $align: center);\n                            gap: 10px;\n                            background-color: $section-bg;\n                            padding: 20px;\n                            border-radius: 10px;\n\n                            img {\n                                width: 80px;\n                                height: 80px;\n                            }\n\n                            span {\n                                font-size: 22px;\n                                font-weight: 700;\n                                color: $primary-color;\n                            }\n                        }\n\n\n\n                    }\n                }\n            }\n        }\n    }\n\n    &.location-modal {\n        .modal-content{\n            .modal-body{\n                padding-bottom: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));;\n            }\n        }\n        .location-map {\n            border-radius: 12px;\n            background-color: $section-bg;\n            padding: 0;\n            margin: 0;\n            gap: 0;\n            overflow: hidden;\n            .map{\n                height: 100%;\n                overflow: hidden;\n                iframe{\n                    height: 100%;\n                }\n            }\n\n            label {\n                color: $title-color;\n                font-size: 18px;\n            }\n\n            .location-detail {\n                padding: 20px;\n            }\n\n            .category {\n                @include flex_common_1 ($dis: flex, $align: center);\n                flex-wrap: wrap;\n                gap: calc(11px + (40 - 11) * ((100vw - 320px) / (1920 - 320)));\n\n                .form-check {\n                    @include mq-max(sm){\n                        gap: 8px;\n                    }\n                    label {\n                        color: $light-color;\n                        font-size: 16px;\n                    }\n\n                    .form-radio-input {\n                        &:checked {\n                            ~label {\n                                color: $title-color;\n                            }\n                        }\n                    }\n                }\n            }\n            i {\n                --Iconsax-Size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));\n                --Iconsax-Color: #808B97;\n            }\n            .input-icon{\n                position: absolute;\n                top: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320)));\n                left: 16px;\n            }\n        }\n    }\n\n    &.servicemen-list-modal{\n        \n        .search{\n            margin-bottom: 16px;\n            input{\n                border-radius: 30px !important;\n            }\n        }\n        .select-dropdown{\n            border: none;\n            margin: 0;\n            padding-bottom: 16px;\n            @include mq-max(lg){\n                flex-direction: column;\n                align-items: flex-start;\n                gap: 12px;\n                .form-group{\n                    width: 100%;\n                }\n            }\n        }\n    }\n\n    &.servicemen-detail-modal{\n        .modal-dialog {\n            .modal-content {\n                \n                .modal-header{\n                    position: relative;\n                    .modal-title{\n                        padding-left: 24px;\n                    }\n                    i{\n                        --Iconsax-Size: 18px;\n                        --Iconsax-Color: #2263eb;                        \n                    }\n                    button{\n                        padding: 0;\n                        border: none;\n                        background: unset;\n                        position: absolute;\n                        top: 50%;\n                        -webkit-transform: translateY(-50%);\n                        transform: translateY(-50%);\n                        left: 20px;\n                    }\n                }\n                .modal-body{\n                    .provider-content{\n                        padding: 0;\n                        border: none;\n                        p{\n                            text-align: start;\n                        }\n                    }\n                    i{\n                        --Iconsax-Size: 18px;\n                        --Iconsax-Color: #808B97;\n                    }\n                    .profile-bg{\n                        position: relative;\n                        background-image: url(../../assets/images/profile-bg.png);\n                        background-position: center;\n                        background-size: cover;\n                        background-repeat: no-repeat;\n                        z-index: 0;\n                        height: 100px;\n                    }\n                    .profile{\n                        z-index: 1;\n                        position: relative;\n                        margin-top: -60px;\n                        .img{\n                            border: 4px solid $white;\n                            width: 100px;\n                            height: 100px;\n                        }\n                        .rate{\n                            position: relative;\n                            margin: 0;\n                            padding-left: 8px;\n                            &:before{\n                                position: absolute;\n                                content: \"\";\n                                top: 7px;\n                                left: 0;\n                                background-color: #E5E8EA;\n                                width: 1px;\n                                height: 12px;\n                            }\n                            .star{\n                                width: 13px;\n                                height: 13px;\n                            }\n                        }\n                        \n                    }\n                    .profile-info{\n                        background-color: $section-bg;\n                        border-radius: 10px;\n                        column-count: 2;\n                        padding: 16px 20px;\n                        label{\n                            @include flex_common_1 ($dis: flex, $align: center);\n                            gap: 6px;\n                            line-height: 1;\n                        }\n                    }\n                    .information{\n                        p{\n                            font-size: 16px;\n                            font-weight: 500;\n                        }\n                        .btn{\n                            font-size: 16px;\n                            width: max-content;\n                            border-radius: 6px;\n                            padding: 6px 12px;\n                        }\n                    }\n                    .note{\n                        font-weight: 400;\n                        color: $light-color;\n                        margin-top: 8px;\n                    }\n                    .expert{\n                        @include flex_common_1 ($dis: flex, $align: center);\n                        gap: 30px;\n                        margin-top: 0.5rem;\n                        list-style: disc;\n                        padding-left: 30px;\n                        li{\n                            display: list-item;\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    &.service-charge-modal{\n        .modal-dialog {\n            .modal-content {\n                .modal-body{\n                    padding-bottom: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320))) !important;\n                    .bill-summary{\n                        .charge, .total{\n                            background-color: $section-bg;\n                        }\n                        .total{\n                            &:before, &:after{\n                                background-color: $white;\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    &.coupon-modal{\n        .modal-content{\n            .modal-body{\n                padding-bottom: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320))) !important;\n            }\n        }\n    }\n\n    &.confirm-modal, &.fail-modal{\n        .modal-body {\n            p {\n                color: $light-color !important;\n                line-height: 25px;\n                text-align: center;\n                margin: 0 calc(5px + (25 - 5) * ((100vw - 320px) / (1920 - 320)));\n            }\n        }\n    }\n\n    &.wallet-modal{\n        .modal-dialog{\n            .modal-content{\n                .modal-body{\n                    .add-money{\n                        background-color: $section-bg;\n                        padding: 20px;\n                        border-radius: 10px;\n                        label{\n                            color: $title-color;\n                        }\n                        .input-group{\n                            input{\n                                border-radius: 8px;\n                                border: none;\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    &.pending-modal, \n    &.accepted-modal, \n    &.ongoing-modal, \n    &.completed-modal{\n        .modal-body{\n            &.booking-sec{\n                .card{\n                    border: none;\n                }\n                .primary-badge{\n                    position: absolute;\n                    top: 20px;\n                    left: 20px;\n                    @include mq-max(lg){\n                        top: 10px;\n                        left: 10px;\n                    }\n                }\n                .status{\n                    margin-bottom: 4px;\n                    @include mq-max(lg){\n                        flex-direction: column;\n                        align-items: start;\n                        justify-content: start;\n                    }\n                    h5{\n                        font-size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));\n                        color: $title-color;\n                        &:hover{\n                            text-decoration: unset;\n                        }\n                    }\n                    .status-btn{\n                        font-size: 20px;\n                        &:hover{\n                            text-decoration: unset;\n                        }\n                    }\n                }\n                .view-status{\n                    align-items: start;\n                    margin-bottom: 10px;\n                    button{\n                        @include flex_common_1 ($dis: flex, $align: center);\n                        gap: 4px;\n                        border-radius: 4px;\n                        border: none;\n                        i{\n                            --Iconsax-Size: 18px;\n                            --Iconsax-Color: #5565FE;\n                        }\n                    }\n                }\n\n                .status-note{\n                    color: $danger-color;\n                    padding: calc(8px + (16 - 8) * ((100vw - 320px) / (1920 - 320)));\n                    border-radius: 8px;\n                    background-color: rgba($danger-color, 0.10);\n                    font-size: 14px;\n                    text-align: center;\n                    span{\n                        font-weight: 500;\n                    }\n                }\n                .bill-summary{\n                    label{\n                        color: $title-color;\n                        font-weight: 500;\n                        margin-bottom: 8px;\n                    }\n                    .charge, .total{\n                        background-color: $section-bg;\n                        border-color: $gray-color;\n                    }\n                    .charge{\n                        border-bottom: none;\n                        p{\n                            color: $light-color;\n                            flex-wrap: wrap;\n                        }\n                    }\n                    .total{\n                        &:before, &:after{\n                            background-color: $white;\n                        }\n                    }\n                    .total-amount{\n                        background-color: $section-bg;\n                        padding: 20px;\n                        border: 1px solid $gray-color;\n                        border-bottom: none;\n                        li{\n                            @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                            gap: 4px;\n                            font-size: 16px;\n                            &.total-amount-data{\n                                font-weight: 700;\n                            }\n                        }\n                    }\n                    .circle{\n                        height: 16px;\n                        margin: 0;\n                        margin-top: -8px;\n                        margin-bottom: -8px;\n                    }\n                }\n\n                .payment-summary{\n                    label{\n                        color: $title-color;\n                        font-weight: 500;\n                        margin-bottom: 8px;\n                    }\n                    .charge{\n                        background-color: $section-bg;\n                        border-color: $gray-color;\n                        border-radius: 8px;\n                        padding: 20px;\n                        display: flex;\n                        flex-direction: column;\n                        gap: 16px;\n                        position: relative;\n                        &:after, &:before{\n                            content: \"\";\n                            position: absolute;\n                            pointer-events: none;\n                            border: solid transparent;\n                            display: block;\n                            border-width: 1px;            \n                            width: 10px;\n                            height: 15px;\n                            bottom: 48px;\n                            border-radius: 100%;\n                            background-color: $white;\n                        }\n                \n                        &:after{\n                            border-left-color: $gray-color;\n                            border-top-color: $gray-color;\n                            border-bottom-color: $gray-color;\n                            right: -2px;\n                        }\n                        &:before{\n                            border-right-color: $gray-color;\n                            border-top-color: $gray-color;\n                            border-bottom-color: $gray-color;\n                            left: -2px;\n                        }\n                    }\n                    .circle{\n                        height: 18px;\n                        margin: -11px 0;\n                    }\n                }\n\n                .extra-service{\n                    label{\n                        color: $title-color;\n                        font-weight: 500;\n                        margin-bottom: 8px;\n                    }\n                    .total-amount{\n                        @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                        gap: 4px;\n                        border: 1px solid $gray-color;\n                        border-radius: 12px;\n                        padding: 16px;\n                        h4{\n                            font-weight: 500;\n                            line-height: 23px;\n                            margin-bottom: 6px;\n                        }\n                        p{\n                            margin: 0;\n                            color: $primary-color;\n                            line-height: 18px;\n                        }\n                        .receipt{\n                            background-color: rgba($primary-color, 0.10);\n                            border-radius: 4px;\n                            height: 50px;\n                            width: 50px;\n                            @include flex_common ($dis: flex, $align: center, $justify: center);\n                            .receipt-img{\n                                width: 25px;\n                                height: 25px;\n                            }\n                        }\n                    }\n                }\n                .profile-view{\n                    background-color: rgba($primary-color, 0.10);\n                    border-radius: 5px;\n                    @include flex_common ($dis: flex, $align: center, $justify: center);\n                    width: 30px;\n                    height: 30px;\n                    border: none;\n                    i{\n                        --Iconsax-Size: 18px;\n                        --Iconsax-Color: #5565FE;\n                    }\n                }\n                .chat{\n                    background-color: $section-bg;\n                    border-radius: 5px;\n                    @include flex_common ($dis: flex, $align: center, $justify: center);\n                    width: 30px;\n                    height: 30px;\n                    border: none;\n                    i{\n                        --Iconsax-Size: 18px;\n                        --Iconsax-Color: #808B97;\n                    }\n                }\n            }\n        }\n    }\n\n    &.status-modal{\n        .modal-content{\n            .modal-header{\n                .modal-back{\n                    position: absolute;\n                    top: calc(16px + (29 - 16) * ((100vw - 320px) / (1920 - 320)));\n                    left: 24px;\n                    background-color: unset;\n                    padding: 0;\n                    border: none;\n                }\n                i{\n                    --Iconsax-Size: 18px;\n                    --Iconsax-Color: #2263eb;\n                }\n                .modal-title{\n                    padding-left: 25px;\n                }\n            }\n            .modal-body{\n                padding: calc(15px + (25 - 15) * ((100vw - 320px) / (1920 - 320)));\n                .pattern-btn-1{\n                    padding: 16px 20px;\n                    &:before, &:after{\n                        background-color: $white;\n                    }\n                }\n                .form-control.pattern-input{\n                    position: relative;\n                    padding: 16px 20px;\n                    vertical-align: middle;\n                    &:after, &:before{\n                        content: \"\";\n                        position: absolute;\n                        pointer-events: none;\n                        border: dashed transparent;\n                        display: block;\n                        border-width: 1px;            \n                        width: 12px;\n                        height: 12px;            \n                        border-radius: 100%;\n                        background-color: $white;\n                        z-index: 1;\n                    }\n                \n                    &:after{\n                        border-top-color: $primary-color;;\n                        border-left-color: $primary-color;;\n                        border-bottom-color: $primary-color;;\n                        right: -100px;\n                        top: 50%;\n                        transform: translateY(-50%);\n                    }\n                    &:before{\n                        border-top-color: $primary-color;;\n                        border-right-color: $primary-color;;\n                        border-bottom-color: $primary-color;;\n                        left: -3px;\n                        top: 50%;\n                        transform: translateY(-50%);\n                    \n                    }\n                }\n\n                .status-history{\n                    margin-top: 24px;\n                    ul{\n                        padding-left: 10px;\n                        display: flex;\n                        flex-direction: column;\n                        gap: 30px;\n                        li{\n                            @include flex_common_1 ($dis: flex, $align: center);\n                            gap: 16px;\n                            @include mq-max(xs){\n                                flex-wrap: wrap;\n                                gap: 8px;\n                            }\n                            i{\n                                --Iconsax-Size: 24px;\n                                --Iconsax-Color: #E5E8EA;\n                            }\n                            p{\n                                margin: 0;\n                            }\n                            .status-time{\n                                line-height: 20px;\n                                color: $light-color;\n                                white-space: nowrap;\n                            }\n                            .status-title{\n                                font-size: 15px;\n                                line-height: 20px;\n                            }\n                            .status-des{\n                                font-size: 15px;\n                                line-height: 20px;\n                                color: $light-color;\n                            }\n                            .status-main{\n                                padding-left: 16px;\n                                position: relative;\n                                width: 100%;\n                                @include mq-max(xs){\n                                    padding-left: 42px;\n                                }\n                                &:before{\n                                    content: \"\";\n                                    position: absolute;\n                                    top: 50%;\n                                    left: 0;\n                                    transform: translateY(-50%);\n                                    background-color: $gray-color;\n                                    height: 26px;\n                                    width: 1px;\n                                    @include mq-max(xs){\n                                        display: none;\n                                    }\n                                }\n                                .dashed-border{\n                                    position: absolute;\n                                    content: \"\";\n                                    height: 1px;\n                                    width: 100%;\n                                    left: 0;\n                                }\n                            }\n\n                            &.recent{\n                                .activity-dot{\n                                    background-color: $primary-color;\n                                    outline-color: $primary-color;\n                                    &:after{\n                                        border: 1px dashed $primary-color;\n                                    }\n                                }\n                                i{\n                                    --Iconsax-Size: 24px;\n                                    --Iconsax-Color: #5565FE;\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    &.start-service-modal, &.restart-service-modal{\n        .modal-body{\n            text-align: center;\n            .modal-body-content{\n                flex-direction: column;\n                .ellipse{\n                    width: 220px;\n                    padding-bottom: 20px;\n                }\n            }\n        }\n    }\n\n    &.pause-service-modal{\n        .modal-body{\n            text-align: center;\n            .modal-body-content{\n                position: relative;\n                flex-direction: column;\n                .ellipse{\n                    width: 100px;\n                    height: auto !important;\n                }\n                .hold{\n                    position: absolute;\n                    top: 40px;\n                    right: 70px;\n                    height: auto !important;\n                    \n                    @include mq-max(sm){\n                        height: 40px !important;\n                    }\n                    @include mq-max(xs){\n                        right: 10px;\n                    }\n                }\n            }\n        }\n    }\n\n    &.completed-service-modal{\n        .modal-content{\n            .modal-body{\n                text-align: center;\n                .modal-body-content{\n                    position: relative;\n                    .success-tick{\n\n                    }\n                    .girl-on-chair{\n\n                    }\n                    // img{\n                    //     height: auto !important;\n                    // }\n                    .success-tick{\n                        top: 50px;\n                        left: 35%;\n                        -webkit-transform: translateX(-30%);\n                        transform: translateX(-35%);\n                        position: absolute;\n                        height: 40px;\n                    }\n                    .girl-on-chair{\n                        position: absolute;\n                        top: 20px;\n                        left: 50%;\n                        transform: translateX(-50%);\n                        height: 210px;\n                    }\n                }\n            }\n        }\n    }\n \n    &.review-modal{\n        .modal-content{\n            .modal-body{\n                .rate-content{\n                    background-color: $section-bg;\n                    padding: 20px;\n                    border-radius: 12px;\n                    p{\n                        text-align: center;\n                        margin: 0;\n                        color: $light-color;\n                    } \n                    .form-group{\n                        label{\n                            font-size: 18px;\n                            font-weight: 500;\n                            color: $title-color;\n                            margin-bottom: 8px;\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    &.profile-update-modal{\n        .update-img{\n            display: flex;\n            margin: 0 auto;\n            width: max-content;\n            position: relative;\n            margin-bottom: -50px;\n            img{\n                width: 120px;\n                height: 120px;\n                border-radius: 100%;\n                border: 2px solid $white;\n            }\n            .update-profile{\n                --Iconsax-Size: 20px;\n                --Iconsax-Color: #5565FE;\n                background-color: $section-bg;\n                border: 2px solid $white;\n                height: 40px;\n                width: 40px;\n                border-radius: 100%;\n                position: absolute;\n                bottom: 0px;\n                right: 0px;\n                cursor: pointer;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n            }\n        }\n        .update-detail{\n            background-color: $section-bg;\n            border-radius: 10px;\n            padding: 20px;\n            padding-top: 70px;\n        }\n\n        input[type=\"file\"] {\n            display: none;\n        }\n\n    }\n}\n.update-detail{\n\n    label{\n        font-size: 18px;\n        font-weight: 500;\n        color: $title-color;\n        margin-bottom: 4px;\n    }\n    .form-group{\n        position: relative;\n        .form-control{\n            border: none;\n            padding-left: 46px;\n        }\n        i {\n            --Iconsax-Size: 20px;\n            --Iconsax-Color: #808B97;\n            position: absolute;\n            top: 43px;\n            left: 15px;\n            \n        }\n        .toggle-password{\n            position: absolute;\n            top: 45px;\n            right: 15px;\n            left: unset;\n            cursor: pointer;\n            i{\n                position: static;\n            }\n            .eye-slash{\n                display: none;\n            }\n            &.eye{\n                i.eye{\n                    display: block;\n                }\n                i.eye-slash{\n                    display: none;\n                }\n            }\n            &.eye-slash{\n                i.eye-slash{\n                    display: block;\n                }\n                i.eye{\n                    display: none;\n                }\n            }\n        }\n    }\n}\n\n.select-option {\n    border: 1px solid $gray-color;\n    padding: 16px;\n    border-radius: 10px;\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n    color: $light-color;\n\n    .form-check {\n        display: flex;\n        flex-direction: row-reverse;\n        justify-content: space-between;\n        border-bottom: 1px solid $gray-color;\n        padding-bottom: 12px;\n\n        &:last-child {\n            border: none;\n            padding-bottom: 0;\n        }\n\n        Label {\n            font-size: 16px;\n            color: $light-color !important;\n        }\n    }\n\n    .form-radio-input {\n        &:checked {\n            ~label {\n                font-weight: 500;\n                color: $title-color !important;\n            }\n        }\n    }\n}\n\n.select-servicemen {\n    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    border: 1px solid $gray-color;\n    padding: 16px;\n    border-radius: 10px;\n    background-color: $white;\n    gap: 12px;\n    p {\n        margin: 0;\n        font-size: 16px;\n    }\n    @include mq-max(sm){\n        flex-direction: column;\n        align-items: start;\n    }\n}\n\n.servicemen-list{\n    display: flex;\n    flex-direction: column;\n    gap: 16px;\n    overflow: auto;\n    height: 450px;\n}\n.servicemen-list-item{\n    border: 1px solid $gray-color;\n    padding: 10px 12px;\n    border-radius: 10px;\n    gap: 12px;\n    box-shadow: 0px 4px 4px 0px rgba($title-color, 0.06);\n    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    .list{\n        @include flex_common_1 ($dis: flex, $align: center);\n        gap: 12px;\n        @include mq-max(sm){\n            gap: 6px;\n        }\n        p{\n            margin: 0;\n            color: $light-color !important;\n            font-size: 14px !important;\n        }  \n        ul{\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 12px;\n        }\n        h5{\n            font-weight: 500;\n            @include mq-max(2xs){\n                font-size: 14px;\n                width: 70px;\n                overflow: hidden;\n                -webkit-line-clamp: 1;\n                -webkit-box-orient: vertical;\n                white-space: nowrap;\n                text-align: left;\n                text-overflow: ellipsis;\n            }\n        }\n        .rate{\n            margin: 0;\n            position: relative;\n            padding-left: 12px;\n            .star{\n                width: 13px;\n                height: 13px;\n            }\n            &:before{\n                position: absolute;\n                content: \"\";\n                top: 2px;\n                left: 0;\n                background-color: $gray-color;\n                width: 1px;\n                height: 12px;\n            }\n        }  \n        .detail{\n            font-size: 16px;\n            font-weight: 500;\n            border: none;\n            background: none;\n            padding: 0;\n            line-height: 1;\n            @include mq-max(2xs){\n                font-size: 14px;\n                width: 100px;\n                overflow: hidden;\n                display: -webkit-box;\n                -webkit-line-clamp: 1;\n                -webkit-box-orient: vertical;\n                text-overflow: ellipsis;\n                white-space: nowrap;\n                text-align: left;\n            }\n            &:hover{\n                color: $primary-color;\n            }\n        }    \n        .img-45{\n            @include mq-max(sm){\n                width: 35px !important;\n                height: 35px !important;\n            }\n        }\n    }\n}\n\n.status-note{\n    color: $danger-color;\n    padding: 16px;\n    border-radius: 8px;\n    background-color: rgba($danger-color, 0.10);\n    font-size: 14px;\n    text-align: center;\n    span{\n        font-weight: 500;\n    }\n}\n\n.emoji-tab{\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n    gap: 10px;\n   .emoji-icon{\n    text-align: center;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n        h4{\n            margin-top: 8px;\n            color: $light-color;\n            @include mq-max(xs){\n                display: none;             \n            }                \n        }\n     .emojis{\n        padding: 11px;\n        background-color: $white;\n        border-radius: 12px;\n        border: 1px solid $gray-color;\n        @include flex_common ($dis: flex, $align: center, $justify: center);\n        cursor: pointer;\n        @include mq-max(xs){\n            padding: 9px;\n        }\n        @include mq-max(2xs){\n            padding: 7px;\n        }\n        .emoji{\n            width: 50px;\n            height: 50px;\n            @include mq-max(xs){\n                width: 30px;\n                height: 30px;                \n            }\n            @include mq-max(2xs){\n                width: 25px;\n                height: 25px;\n            }                      \n        }\n        .deactive{\n            display: block;\n        }\n        .active{\n            display: none;\n        }\n    }\n    &.active{\n        .emojis{\n            border: 2px solid $primary-color;\n        }\n        .deactive{\n            display: none;\n        }\n        .active{\n            display: block;\n        }\n    }\n   }\n}", "/**=====================\nOffer scss\n==========================**/\n\n\n.offer-section {\n    .offer-content {\n        .sale-tag {\n            position: absolute;\n            top: 25px;\n            left: 0;\n            background-color: $danger-color;\n            padding: 4px 36px 4px 16px;\n            color: white;\n            clip-path: polygon(0 0, 28% 0%, 67% -24%, 83% 7%, 84% 14%, 84% 27%, 84% 0%, 84% 32%, 82% 41%, 81% 49%, 81% 78%, 81% 74%, 80% 44%, 84% 65%, 84% 74%, 84% 77%, 82% 93%, 79% 100%, 84% 100%, 68% 100%, 0 100%);\n\n        }\n        .offer-img{\n            border-radius: 10px;\n            min-height: 240px;\n            max-height: 250px;\n        }\n\n        .offer-detail {\n            position: absolute;\n            top: 75px;\n            left: 20px;\n\n            h3 {\n                font-size: 24px;\n                font-weight: 700;\n                line-height: 33px;\n            }\n\n            p {\n                color: rgba($title-color, 0.5);\n                line-height: 20px;\n                width: 70%;\n                margin-top: 4px;\n\n            }\n\n            .btn {\n                padding: 10px 20px;\n                line-height: 1;\n                width: max-content;\n                color: $title-color;\n\n                &.btn-outline {\n                    margin-top: 24px;\n                    border: 2px solid $title-color;\n                    border-radius: 30px;\n                    font-size: 16px;\n                    font-weight: 500;\n                }\n            }\n        }\n    }\n}", "/**=====================\n     Pagination scss\n==========================**/\n\n.pagination-main{\n    border-top: 1px solid $gray-color;\n    padding-top: 20px;\n    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    @include mq-max(sm){\n        justify-content: center;\n    }\n    label{\n        color: $light-color;\n        @include mq-max(sm){\n            display: none;\n        }\n    }\n    i {\n        --Iconsax-Size: 18px;\n        --Iconsax-Color: #2263eb;\n    }\n    .pagination{\n        @include flex_common_1 ($dis: flex, $align: center);\n        gap: 8px;\n        .page-item{\n            .page-link{\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n                color: $light-color;\n                width: 30px;\n                height: 30px;\n                border: none;\n                border-radius: 100%;\n                &:focus{\n                    border: none;\n                    outline: none;\n                    box-shadow: none;\n                }\n                span{\n                    line-height: 1;\n                }\n            }\n            &.active{\n                .page-link{\n                    background-color: $primary-color;\n                    color: $white;\n                }\n            }\n        }\n    }\n}", "/**=====================\n     Profile Setting scss\n==========================**/\n\n.profile-body-wrapper{\n    .profile-wrapper{\n        background-color: $primary-color;\n        border-radius: 15px;\n        padding: 20px;\n        position: sticky;\n        top: 100px;\n    \n        .profile{\n            padding: 20px 0 40px;\n            .profile-img{\n                text-align: center;\n                margin-bottom: -40px;\n                img{\n                    border-radius: 100%;\n                    border: 2px solid $white;\n                    height: 80px;\n                    width: 80px;\n                }\n            }\n            .profile-detail{\n                padding: 20px;\n                border-radius: 12px;\n                background-image: url(../../assets/images/Base.png);\n                background-position: center;\n                background-repeat: no-repeat;\n                background-size: cover;\n                height: 120px;\n                color: $white;\n                @include flex_common ($dis: flex, $align: center, $justify: end);\n                flex-direction: column;\n    \n                h5{\n                    font-weight: 500;\n                    line-height: 20px;\n                    margin-bottom: 4px;\n                }\n                p{\n                    @include flex_common ($dis: flex, $align: center, $justify: center);\n                    margin: 0;\n                    gap: 4px;\n                    line-height: 18px;\n                    i{\n                        --Iconsax-Size: 16px;\n                    --Iconsax-Color: white;\n                    }\n                }\n            }\n        }\n\n        .profile-settings{\n            overflow: auto;\n            height: 527px;\n            .navbar{\n                .navbar-collapse{\n                    .menu-panel{\n                        width: 100%;\n                        .menu-wrapper{\n                            display: flex;\n                            flex-direction: column;\n                            gap: 16px;\n                            margin: 0 8px;\n                            border-bottom: unset;\n                            li {\n                                display: block;\n                    \n                                .nav-link {\n                                    @include flex_common_1 ($dis: flex, $align: center);\n                                    border-radius: 8px;\n                                    gap: 16px;\n                                    border: none;\n                                    transition: all 0.5s ease;\n                                    outline: none;   \n                                    width: 100%;\n                                    padding: 14px 16px;\n                                    white-space: nowrap;\n                                    i {\n                                        --Iconsax-Size: 24px;\n                                        --Iconsax-Color: white;\n                                    }             \n                                    span {                                    \n                                        color: $white;\n                                        font-size: 16px;\n                                    }\n                                    .active-icon{\n                                        display: none;\n                                    }\n                                    .deactive-icon{\n                                        display: block;\n                                    }\n                                    &.active{\n                                        background-color: $white;\n    \n                                        span{\n                                            color: $primary-color;\n                                            font-weight: 500;\n                                        }\n                                        .active-icon{\n                                            display: block;\n                                        }\n                                        .deactive-icon{\n                                            display: none;\n                                        }\n                                        &:hover{\n                                            background-color: $white;\n                                        }                                   \n                                    }\n                                    &:hover{\n                                        background-color: rgba($white, 0.10);\n    \n                                    }\n                                }\n    \n                            }\n                        }\n                    }\n                }\n            }\n        }\n\n        .profile-logout{\n            margin: 0 8px;\n            .nav-link {\n                @include flex_common_1 ($dis: flex, $align: center);\n                border-radius: 8px;\n                gap: 16px;\n                border: none;\n                transition: all 0.5s ease;\n                outline: none;   \n                width: 100%;\n                padding: 14px 16px;\n                i {\n                    --Iconsax-Size: 24px;\n                    --Iconsax-Color: white;\n                }             \n                span {                                    \n                    color: $white;\n                    font-size: 16px;\n                }               \n                &:hover{\n                    background-color: rgba($white, 0.10);\n\n                }\n            }\n        }\n\n    }\n    .profile-main{\n        .card{\n            background-color: $section-bg;\n            border: none;\n            border-radius: 10px;\n\n            .card-header{\n                @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                gap: 4px;\n                padding: 25px;\n                background-color: transparent;\n                border-radius: 0;\n                border-bottom: 1px solid $gray-color;\n                .title-3{\n                    position: relative;\n                    &:before{\n                        content: \"\";\n                        position: absolute;\n                        left: -25px;\n                        top: 50%;\n                        transform: translateY(-50%);\n                        background-color: $primary-color;\n                        height: 35px;\n                        width: 4px;\n                    }\n                    h3{\n                        font-weight: 700;\n                        line-height: 26px;\n                    }\n                }\n                a, button{\n                    font-weight: 500;\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: 4px;\n                    font-size: 18px;\n                    i{\n                        --Iconsax-Size: 20px;\n                        --Iconsax-Color: #5565FE;\n                    }\n                }\n            }\n            .card-body{\n                padding: 25px;\n                &.service-booking{\n                    min-height: 760px;\n                }\n                .widgets{\n                    .card{\n                        border: 1px solid $gray-color;\n                        padding: 20px;\n                        border-radius: 10px;\n                        background-color: $white;\n                        .widget-data{\n                            @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                            gap: 4px;\n                            .data{\n                                h5{\n                                    line-height: 20px;\n                                    color: $light-color;\n                                    margin-bottom: 4px;\n                                }\n                                h3{\n                                    font-weight: 700;\n                                    line-height: 26px;\n                                }\n                            }\n                            .data-icon{\n                                position: relative;\n                                padding-right: 10px;\n                                .dot{\n                                    background-color: rgba($primary-color, 0.10);\n                                    border-radius: 100%;\n                                    height: 30px;\n                                    width: 30px;\n                                    position: absolute;\n                                    top: 0;\n                                    right: 0;\n                                }\n                                i{\n                                    --Iconsax-Size: 38px;\n                                    --Iconsax-Color: #5565FE;\n                                    padding-top: 10px;\n                                }\n                            }\n                        }\n                    }\n                }\n\n                .profile-data{\n                    h3{\n                        line-height: 26px;\n                    }\n                    .card{\n                        border: 1px solid $gray-color;\n                        padding: 20px;\n                        border-radius: 10px;\n                        background-color: $white;\n                        margin: 0;\n\n                        .profile-setting-img{\n                            background-color: $section-bg;\n                            border-radius: 8px;\n                            height: 100%;\n                            @include flex_common ($dis: flex, $align: center, $justify: center);\n                            background-image: url(../../assets/images/setting.png);\n                            background-position: top;\n                            background-repeat: no-repeat;\n                            background-size: auto;\n                            z-index: 0;\n                            position: relative;\n\n                            .girl-on-chair{\n                                height: 400px;\n                                width: auto;\n                            }\n\n                        }\n                        .form-group{\n                            .value{\n                                font-size: 18px;\n                                font-weight: 500;\n                                line-height: 23px;\n                                margin: 0;\n                            }\n                            &:last-child{\n                                margin: 0;\n                            }\n                        }\n\n                        .login-detail{\n                            h4{\n                                color: $primary-color;\n                                font-weight: 500;\n                                line-height: 23px;\n                            }\n                        }\n                    }\n                }\n            }\n            .card-footer{\n                padding: 25px;\n                border-top: 1px solid $gray-color;\n                gap: 16px;\n                justify-content: start;\n                .btn{\n                    width: max-content;\n                    padding: 10px 30px;\n                }\n            }\n        }\n\n        .review-main{\n            display: flex;\n            flex-direction: column;\n            gap: 16px;\n            .review-list{\n                @include flex_common ($dis: flex, $align: start, $justify: space-between);\n                width: 100%;\n                border-radius: 8px;\n                border: 1px solid $gray-color;\n                padding: 20px;\n                background-color: $white;\n                .review{\n                    @include flex_common_1 ($dis: flex, $align: start);\n                    gap: 16px;\n                    .review-img{\n                        img{\n                            height: 45px;\n                            width: 45px;\n                            border-radius: 100%;\n                        }\n                    }\n                    .review-note{\n                        .name-date{\n                            @include flex_common_1 ($dis: flex, $align: center);\n                            gap: 14px;\n                            span{\n                                font-weight: 500;\n                            }\n                            small{\n                                color: $light-color;\n                            }\n                        }\n                        h5{\n                            color: $light-color;\n                            line-height: 20px;\n                        }\n                        p{ \n                            font-size: 16px;\n                            margin: 0;\n                            color: $title-color;\n                            line-height: 20px;\n                            margin-top: 10px;\n\n                        }\n                    }\n                }\n\n                .notify-time{\n                    .rate{\n                        justify-content: end;\n                        margin-bottom: 16px;\n                        margin-top: 0;\n                        img{\n                            width: 13px;\n                            height: 13px;\n                        }\n                        span{\n                            font-weight: 500;\n                        }\n                    }\n                    button{\n                        background: unset;\n                        border: none;\n                        padding: 0;\n                    }\n                }\n            }\n        }\n    }\n}\n\n.edit-modal{\n    height: 40px;\n    width: 40px;\n    border: none;\n    border-radius: 6px;\n    background-color: rgba($primary-color, 0.10);\n    i{\n        --Iconsax-Size: 20px;\n        --Iconsax-Color: #5565FE;\n    }\n}\n\n.col-img{\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n    img{\n        height:594px ;\n    }\n}\n\n.notifications{\n    display: flex;\n    flex-direction: column;\n    border: 1px solid $gray-color;\n    background-color: $white;\n    border-radius: 10px;\n    \n    .notification-list{\n        border-bottom: 1px solid $gray-color;\n        padding: 20px;\n        @include flex_common ($dis: flex, $align: start, $justify: space-between);\n        gap: 12px;\n        &:last-child{\n            border-bottom: none;\n        }\n        .notify{\n            @include flex_common_1 ($dis: flex, $align: start);\n            gap: 14px;\n            .notify-icon{\n                height: 40px;\n                width: 40px;\n                min-width: 40px;\n                border-radius: 100%;\n                background-color: $section-bg;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n                i{\n                    --Iconsax-Size: 20px;\n                    --Iconsax-Color: #808B97;\n                }\n            }\n            .notify-note{\n                h5{\n                    font-weight: 500;\n                    color: $light-color;\n                    line-height: 20px;\n                    margin-bottom: 8px;\n                }\n                p{\n                    margin: 0;\n                    color: $light-color;\n                    line-height: 19px;\n                    margin-bottom: 16px;\n                }\n                .notify-img{\n                    height: 50px;\n                    width: 50px;\n                    border-radius: 4px;\n                    overflow: hidden;\n                    img{\n                        height: 100%;\n                        width: 100%;\n                    }\n                }\n            }\n        }\n        .notify-time{\n            min-width: 70px;\n            text-align: end;\n            span{\n                color: $light-color;\n            }\n        }\n        &:nth-child(1), &:nth-child(2){\n            i{\n                --Iconsax-Color: #2263eb !important;\n            }\n            h5, p{\n                color: $title-color !important;\n            }\n        }\n    }\n}\n\n", "/**=====================\n     Range Slider scss\n==========================**/\n\n.irs--round{\n    background-color: $white;\n    padding: 16px;\n    border-radius: 8px;\n    height: 80px;\n    .irs-line{\n        top: 10px;\n        background-color: $gray-color;\n        height: 5px;\n    }\n\n    .irs-handle{\n        top: 21px;\n        width: 14px;\n        height: 14px;\n        border: none;\n        background-color:rgba($title-color, 0.8);\n        border-radius: 100%;\n        &:hover, &.state_hover{\n        background-color:rgba($title-color, 0.8);\n        }\n    }\n    .irs-from, .irs-to, .irs-single{\n        bottom: -40px;\n        top: unset;\n        background-color: transparent;\n        color:rgba($title-color, 0.8);\n        font-weight: 500;\n        padding: 0;\n        &:before{\n            display: none;\n        }\n    }\n    .irs-bar{\n        height: 5px;\n        background-color:rgba($title-color, 0.65);\n        top: 26px;\n    }\n}\n\n#distance{\n    .distance{\n        @include flex_common_1 ($dis: flex, $align: center);\n        gap: 10px;\n        flex-wrap: wrap;\n        .form-group{\n            margin: 0;\n        }\n    }\n    .irs{\n        .irs-line{\n            \n            height: 4px;\n        }\n        .irs-handle {\n            &.single{\n                display: none;\n            }\n        }\n        &.irs--modern {\n            .irs-grid-pol{\n                color: $gray-color;\n                &.small{\n                    display: none;\n                }\n            }\n            .irs-single{\n                background: url(../../assets/images/svg/Frame.svg);\n                background-repeat: no-repeat;\n                background-position: center;\n                z-index: 0;\n                font-size: 30px;\n                padding: 0;\n                border-radius: 0;\n                width: 10px;\n                color: transparent;\n                top: -4px;\n                overflow: hidden;\n                cursor: pointer;\n                &:before{\n                    content: \"\";\n                    position: absolute;\n                    background-color: $title-color;\n                    border: none;\n                    left: 0px;\n                    bottom: 6px;\n                    height: 7px;\n                    width: 7px;\n                    border-radius: 30px 0 0 30px;\n                }\n                &::after{\n                    content: \"\";\n                    position: absolute;\n                    background-color: $title-color;\n                    right: -3px;\n                    bottom: 6px;\n                    height: 7px;\n                    width: 7px;\n                    border-radius: 0 30px 30px 0;\n                    \n                }\n            }\n        }\n        .irs-bar{\n            background: $title-color;\n            height: 4px;\n        }\n        .irs-line{\n            background-color: $gray-color;\n        }\n        .irs-grid{\n            height: 29px;\n            .irs-grid-text{\n                color: $title-color;\n                white-space: wrap;\n                width: min-content;\n                bottom: -12px;\n                line-height: 1;\n            }\n        }\n    }\n}\n\n", "/**=====================\n    Rating scss\n==========================**/\n\n.star-rating {\n    @include flex_common ($dis: flex, $align: center, $justify: start);\n    flex-wrap: wrap;\n    gap: 7px;\n    flex-direction: row;\n    font-size: 1.5em;\n\n    input {\n        display: none;\n    }\n\n    label {\n        @include flex_common_1 ($dis: flex, $align: center);\n        background-color: $white;\n        color: $light-color;\n        gap: 6px;\n        padding: 10px;\n        border-radius: 8px;\n        border: 1px solid $gray-color;\n        line-height: 0;\n        cursor: pointer;\n        font-size: 16px;\n        font-weight: 500;\n\n        i {\n            --Iconsax-Size: 18px;\n            --Iconsax-Color: #808B97;\n        }\n\n        &:hover {\n            background-color: $primary-color;\n            color: #fff;\n\n            i {\n                --Iconsax-Color: #fff;\n            }\n        }\n    }\n\n    :checked {\n        ~label {\n            background-color: $primary-color;\n            color: $white;\n            border-color: $primary-color;\n\n            i {\n                --Iconsax-Color: #fff;\n            }\n        }\n    }\n}\n\n.reviews {\n    background-color: $white;\n    padding: 16px;\n    border-radius: 8px;\n    @include flex_common ($dis: flex, $align: start, $justify: space-between);\n    gap: 12px;\n    margin-bottom: 16px;\n\n    &:last-child {\n        margin: 0;\n    }\n\n    .person-detail {\n        @include flex_common_1 ($dis: flex, $align: center);\n        gap: 6px;\n        color: $title-color;\n\n        img {\n            border-radius: 100%;\n            height: 45px;\n            width: 45px;\n        }\n\n        p {\n            margin: 0;\n            font-size: 16px;\n            // color: $title-color;\n            width: 100%;\n            overflow: hidden;\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;  \n            text-overflow: ellipsis;\n        }\n\n        h5 {\n            line-height: 1.5;\n            font-weight: 500;\n        }\n    }\n\n    .rating {\n        .rate {\n            justify-content: end;\n            color: $title-color;\n\n            small {\n                font-size: 16px;\n            }\n        }\n        .overview-list{\n            li{\n                white-space: nowrap;\n            }\n        }\n        @include mq-max(sm){\n            margin-left: 50px;\n        }\n    }\n    @include mq-max(sm){\n\n        flex-direction: column;\n        align-items: start;\n        justify-content: unset;\n        gap: 4px;\n\n        .rating{\n            margin-left: 50px;\n            @include flex_common_1 ($dis: flex, $align: center);\n            flex-direction: row-reverse;\n            gap: 10px;\n            .rate{\n                margin: 0;\n            }\n        }\n    }\n}\n\n.rating-bars {\n    background-color: $white;\n    border-radius: 8px;\n    padding: 16px;\n    white-space: nowrap;\n    display: flex;\n    gap: 10px;\n    flex-direction: column;\n    \n    .rating-bar {\n        @include flex_common_1 ($dis: flex, $align: center);\n        gap: 8px;\n        text-align: left;\n       \n        .bar {\n            width: 100%;\n            background-color: $section-bg;\n            border-radius: 4px; \n            .bar-item {\n                height: 8px;\n                background-color: $warning-color;\n                border-radius: 4px;\n                &-5{\n                    width: 84%;\n                } \n                &-4{\n                    width: 30%;\n                }\n                &-3{\n                    width: 12%;\n                }\n                &-2{\n                    width: 6%;\n                }\n                &-1{\n                    width: 3%;\n                }\n            }\n        }\n        .left{\n            min-width: 40px;\n        }\n        .right{\n            color: $light-color;\n            text-align: left;\n            min-width: 40px;\n        }\n    }\n}", "/**=====================\n    Ration scss\n==========================**/\n\n.ratio_18 {\n    .bg-size {\n        &:before {\n            padding-top: 18%;\n            content: \"\";\n            display: block;\n        }\n    }\n} \n\n.ratio_20 {\n    .bg-size {\n        &:before {\n            padding-top: 20%;\n            content: \"\";\n            display: block;\n        }\n    }\n} \n\n\n.ratio_24 {\n    .bg-size {\n        &:before {\n            padding-top: 24%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_36 {\n    .bg-size {\n        &:before {\n            padding-top: 36%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_40 {\n    .bg-size {\n        &:before {\n            padding-top: 40%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_45 {\n    .bg-size {\n        &:before {\n            padding-top: 45%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_46 {\n    .bg-size {\n        &:before {\n            content: \"\";\n            padding-top: 46%;\n            display: block;\n        }\n    }\n}\n\n.ratio_47 {\n    .bg-size {\n        &:before {\n            padding-top: 47.8%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_70 {\n    .bg-size {\n        &:before {\n            padding-top: 70%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio2_1 {\n    .bg-size {\n        &:before {\n            padding-top: 50%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio2_2 {\n    .bg-size {\n        &:before {\n            padding-top: 56%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio2_3 {\n    .bg-size {\n        &:before {\n            padding-top: 60%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio-68 {\n    .bg-size {\n        &:before {\n            padding-top: 68%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio3_2 {\n    .bg-size {\n        &:before {\n            padding-top: 66.66%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_landscape {\n    .bg-size {\n        &:before {\n            padding-top: 75%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio-80 {\n    .bg-size {\n        &:before {\n            padding-top: 80%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio-85 {\n    .bg-size {\n        &:before {\n            padding-top: 85%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_blog-list {\n    .bg-size {\n        &:before {\n            padding-top: 88.6%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_square {\n    .bg-size {\n        &:before {\n            padding-top: 100%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_94 {\n    .bg-size {\n        &:before {\n            padding-top: 94%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_115 {\n    .bg-size {\n        &:before {\n            padding-top: 115%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_125 {\n    .bg-size {\n        &:before {\n            padding-top: 125%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_124 {\n    .bg-size {\n        &:before {\n            padding-top: 124.7777778%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_asos {\n    .bg-size {\n        &:before {\n            padding-top: 127.7777778%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_asos_1 {\n    .bg-size {\n        &:before {\n            padding-top: 136.777778%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n\n.ratio_portrait {\n    .bg-size {\n        &:before {\n            padding-top: 150%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_163 {\n    .bg-size {\n        &:before {\n            padding-top: 163%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio1_2 {\n    .bg-size {\n        &:before {\n            padding-top: 200%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.b-top {\n    background-position: top !important;\n}\n\n.b-bottom {\n    background-position: bottom !important;\n}\n\n.b-center {\n    background-position: center !important;\n}\n\n.b_size_content {\n    background-size: contain !important;\n    background-repeat: no-repeat;\n}", "/**=====================\n    Swiper scss\n==========================**/\n\n.swiper {\n    [dir=\"rtl\"] & {\n        direction: ltr;\n\n        .swiper-slide {\n            direction: rtl;\n        }\n    }\n    .swiper-slide{\n        .card{\n            margin-bottom: 0;\n        }\n    }\n}\n\n.offer-section,\n.service-list-section,\n.service-package-section,\n.about-us-slider {\n\n    .swiper-button-next4,\n    .swiper-button-prev4,\n    .swiper-button-next1,\n    .swiper-button-prev1,\n    .swiper-button-next2,\n    .swiper-button-prev2,\n    .swiper-button-next3,\n    .swiper-button-prev3 {\n        background-repeat: no-repeat;\n        background-size: 80% auto;\n        background-position: center;\n        width: 28px;\n        height: 20px;\n        top: -10px;\n        position: absolute;\n        opacity: 1;\n\n        &:after {\n            display: none;\n        }\n    }\n\n    .swiper-button-prev4,\n    .swiper-button-prev1,\n    .swiper-button-prev2,\n    .swiper-button-prev3 {\n        background-image: url(\"../../assets/images/svg/preview.svg\");\n        right: 30px;\n        left: auto;\n        \n\n        &.swiper-button-disabled {\n            background-image: url(\"../../assets/images/svg/preview-disable.svg\") !important;\n        }\n    }\n\n    .swiper-button-next4,\n    .swiper-button-next1,\n    .swiper-button-next2,\n    .swiper-button-next3 {\n        background-image: url(\"../../assets/images/svg/next.svg\") !important;\n        right: 0;\n        left: auto;\n\n        &.swiper-button-disabled {\n            background-image: url(\"../../assets/images/svg/next-disable.svg\") !important;\n        }\n    }\n}\n\n.swiper-button-next5,\n    .swiper-button-prev5{\n        background-repeat: no-repeat;\n        background-size: 50% auto;\n        background-position: center;\n        width: 35px;\n        height: 35px;\n        position: absolute;\n        z-index: 1;\n        top: 50%;\n        transform: translateY(-50%);\n        background-color: rgba($white, 0.40);\n        border-radius: 100%;\n        opacity: 1;\n\n        &:after {\n            display: none;\n        }\n        &.swiper-button-disabled{\n            opacity: 0.7;\n        }\n    }\n\n    .swiper-button-prev5 {\n        background-image: url(\"../../assets/images/svg/left.svg\");\n        left: 20px;\n    }\n    .swiper-button-next5{\n        background-image: url(\"../../assets/images/svg/right.svg\") !important;\n        right: 20px;\n    }", "/**=====================\n    Table scss\n==========================**/\n\n.table-responsive{\n    border: 1px solid $gray-color;\n    border-radius: 12px;\n    .table{\n        overflow: hidden;\n        border: none;\n        margin: 0;\n        --bs-table-bg: unset;\n        thead{\n            background-color: $section-bg;\n            tr{\n                th{\n                    font-size: 14px;\n                    font-weight: 500;\n                    color: $light-color;   \n                    padding: calc(12px + (16 - 12) * ((100vw - 320px) / (1920 - 320))) calc(12px + (16 - 12) * ((100vw - 320px) / (1920 - 320)));\n                    line-height: 18px;\n                }\n            }\n        }\n        tbody{\n            tr{\n                border-bottom: 1px dashed $gray-color;\n                \n                &:last-child{\n                    border-bottom: none;\n                }\n\n                td{\n                    vertical-align: middle;\n                    padding: calc(12px + (16 - 12) * ((100vw - 320px) / (1920 - 320))) calc(12px + (16 - 12) * ((100vw - 320px) / (1920 - 320)));\n                    font-size: 14px;\n                    line-height: 16px;\n                }\n            }\n        }\n        >:not(:first-child), \n        >:not(caption)>*>*{\n            border: none;\n        }\n        &.wallet-table{\n            thead{\n                background-color: $primary-color;\n                tr{\n                    th{\n                        color: $white;   \n                    }\n                }\n            }\n            tbody{\n                background-color: $white;\n                tr{\n                    .id{\n                        color: $primary-color;\n                        font-weight: 500;\n                    }\n                    .date{\n                        color: $light-color;\n                    }\n                    .balance{\n                        color: $title-color;\n                        font-weight: 500;\n                    }\n                    .particular{\n                        color: $title-color;\n                    }\n\n                    .credit{\n                        font-weight: 500;\n                        color: $success-color;\n                    }\n                    .debit{\n                        font-weight: 500;\n                        color: $danger-color;\n                    }\n                }\n            }\n        }\n        &.booking-table{\n            thead{\n                tr{\n                    th{\n                        &:nth-child(1){\n                            min-width: 130px;\n                        }\n                        &:nth-child(2){\n                            min-width: 250px;\n                        }\n                        &:nth-child(3){\n                            min-width: 130px;\n                        }&:nth-child(4){\n                            min-width: 130px;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}", "/**=====================\n    Tabs scss\n==========================**/\n\n.custom-nav-tabs{\n    margin-top: calc(16px + (30 - 16) * ((100vw - 320px) / (1920 - 320)));\n    border: none;\n    display: grid;\n    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;\n    overflow: auto hidden;\n    gap: calc(12px + (20 - 12) * ((100vw - 320px) / (1920 - 320)));\n    .nav-item{\n        border: none;\n        .nav-link{\n            border-radius: 10px;\n            padding:  calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320)));\n            display: flex;\n            flex-direction: column;\n            background-color: $white;\n            border: 1px solid $gray-color;\n            margin-bottom: 0;\n\n            &.active, &:hover{\n                background-color: rgba($primary-color,0.10);\n                border: 1px solid rgba($primary-color,0.01);\n                span{\n                    color: $primary-color;\n                    font-weight: 500;\n                }\n                img{\n                    background-color: $white;\n                    &.inactive{\n                        display: none;\n                    }\n                    &.active{\n                        display: block;\n                    }\n                }\n            }\n            .img-box{\n                width: 100%;\n                height: calc(70px + (95 - 70) * ((100vw - 320px) / (1920 - 320)));\n                min-width: calc(70px + (95 - 70) * ((100vw - 320px) / (1920 - 320)));\n                margin-bottom: 12px;\n            }\n            img{\n                // margin: 0 auto;\n                background-color: rgba($gray-color,0.4);\n                padding: calc(20px + (25 - 20) * ((100vw - 320px) / (1920 - 320)));\n                border-radius: 4px;                \n                width: 100%;\n                height: 100%;\n                &.active{\n                    display: none;\n                }\n                &.inactive{\n                    display: block;\n                }\n            }\n            span{\n                margin: 0 auto;\n                font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n                color: $title-color;\n                line-height: 26px;\n                white-space: nowrap;\n            }\n            small{\n                margin: 0 auto;\n                font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n                color: rgba($title-color,0.6);\n                line-height: 20px;\n                white-space: nowrap;\n            }\n        }\n    }\n}\n\n.tab-content{\n    margin-top: 30px;\n}\n\n.review-tab{\n    margin: 0;\n    .tab-pane{\n        padding: 20px;\n        min-height: 290px;\n    } \n    .nav-item{\n        .nav-link{\n            padding: 10px 20px;\n            font-size: 16px;\n            font-weight: 500;\n            color: $light-color;\n            border: none;\n            &.active{\n                background-color: transparent;\n                border: none;\n                color: $title-color;\n                border-bottom: 2px solid $title-color;\n                &:hover{\n                 border-bottom: 2px solid $title-color;\n\n                }\n            }\n            &:hover{\n                border: none;\n            }\n        }\n    }\n}\n\n.favorite-tab{\n    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    gap: 4px;\n    border-bottom: 1px solid $gray-color;\n    padding-bottom: 16px;\n    h3{\n        font-weight: 700;\n    }\n    .nav-tabs{\n        border: none;\n        gap: 12px;\n        .nav-item{\n            .nav-link{\n                background-color: rgba($primary-color,0.10);\n                color: $primary-color;\n                border: none;\n                border-radius: 10px;\n                font-size: 18px;\n                padding: 9px 27px;\n                &.active{\n                    background-color: $primary-color;\n                    color: $white;\n                    font-weight: 500;\n                }\n            }\n        }\n    }\n}\n\n.provider-detail-tab{\n    .nav-tabs{\n        flex-direction: column;\n        gap: 16px;\n        .nav-item{\n            .nav-link{\n                border: 2px solid $primary-color;\n                color: $primary-color;\n                border-radius: 8px;\n                width: 100%;\n                font-size: 18px;\n                font-weight: 500;\n                padding: 12px;\n                background-color: transparent;\n            }\n        }\n    }\n}\n\n.overview-list{\n    list-style: disc;\n    padding-left: 20px;\n    display: flex;\n    flex-direction: column;\n    gap: 12px;\n    li{\n        display: list-item;\n        font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n    }\n}\n\n.provider-service-tab{\n    padding-bottom: 16px;\n    gap: 12px;\n    .nav-item{\n        .nav-link{\n            background-color: rgba($primary-color,0.10);\n            color: $primary-color;\n            border: none;\n            border-radius: 30px;\n            font-size: 16px;\n            padding: 6px 16px;\n            &.active{\n                background-color: $primary-color;\n                color: $white;\n                font-weight: 500;\n            }\n        }\n    }\n}\n", "/**=====================\n    Header scss\n==========================**/\n\nheader {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    z-index: 4;\n\n    &.active {\n        position: sticky;\n        top: 0;\n        left: 0;\n        width: 100%;\n        background-color: $black;\n        box-shadow: 0 8px 10px rgba($title-color, 0.05);\n\n        .container-fluid-xl {\n            padding: 0 calc(12px + (100 - 12) * ((100vw - 320px) / (1920 - 320)));\n        }\n\n        .navbar {\n            .onhover-show-div {\n                background-color: $white !important;\n\n                li {\n                    color: rgba($title-color, 0.6) !important;\n\n                    a {\n                        color: rgba($title-color, 0.6) !important;\n                    }\n\n                    i {\n                        --Iconsax-Color: #2263eb99 !important;\n                    }\n\n                    &:hover {\n                        color: $title-color !important;\n\n                        a {\n                            color: $title-color !important;\n                        }\n\n                        i {\n                            --Iconsax-Color: #2263eb !important;\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    .custom-navbar {\n        .logo-content {\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: calc(4px + (24 - 4) * ((100vw - 320px) / (1920 - 320)));\n\n            .navbar-toggler-icon {\n                filter: invert(1);\n            }\n        }\n\n        i {\n            --Iconsax-Size: 18px;\n            --Iconsax-Color: White;\n        }\n\n        .navbar-brand {\n            margin: 0;\n            padding: 0;\n            font-size: 0;\n\n            img {\n                @include pseudowh($width: calc(80px + (116 - 80) * ((100vw - 320px) / (1920 - 320))), $height: auto);\n            }\n        }\n\n        .location-dropdown {\n            border: 1px solid rgba($white, 0.10%);\n            background-color: #1d2537;\n            padding: 7px 9px;\n            border-radius: 100%;\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 6px;\n            padding: 5px 8px;\n            border-radius: 30px;\n\n            .location-btn {\n                background: transparent;\n                color: white;\n                border: none;\n                line-height: 1;\n                padding: 0;\n                @include flex_common_1 ($dis: flex, $align: center);\n                gap: 12px;\n\n                .loaction-svg {\n                    --Iconsax-Size: 24px;\n                    --Iconsax-Color: White;\n\n                }\n\n                .arrow {\n                    ---Iconsax-Size: 18px;\n                    --Iconsax-Color: White;\n                }\n\n                span {\n                    display: inline-flex;\n                    align-items: center;\n                    gap: 6px;\n                    line-height: 1;\n                    font-size: 16px;\n                }\n            }\n\n            .onhover-show-div {\n                min-width: 120px;\n                transition: all 0.3s linear;\n                position: absolute;\n                z-index: 3;\n                border: 1px solid rgba($white, 0.10%);\n                background-color: #1d2537;\n                opacity: 0;\n                visibility: hidden;\n                transform: translate3d(0, -5%, 0);\n                top: 45px;\n                right: 0;\n                padding: 15px;\n                border-radius: 12px;\n                width: max-content;\n\n\n                [dir=\"rtl\"] & {\n                    left: 0;\n                    right: unset;\n                }\n\n                .location {\n                    cursor: pointer;\n                    transition: all 0.3s;\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    border: none;\n                    color: rgba($white, 0.6);\n                    background-color: transparent;\n                    gap: 8px;\n                    font-size: 15px;\n                    padding: 0;\n\n                    img {\n                        border-radius: 0 !important;\n                        width: auto !important;\n                        height: auto !important;\n                    }\n\n                    i {\n                        --Iconsax-Color: #ffffff99;\n                    }\n\n                    &:hover {\n                        color: $white;\n                        transform: translateX(5px);\n\n                        [dir=\"rtl\"] & {\n                            transform: translateX(-5px);\n                        }\n\n                        i {\n                            --Iconsax-Color: #fff;\n                        }\n                    }\n\n                    +li {\n                        margin-top: 16px;\n                    }\n                }\n            }\n\n            &:hover {\n                .onhover-show-div {\n                    opacity: 1;\n                    visibility: visible;\n                    transform: none;\n                }\n            }\n        }\n\n        .nav-right {\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 12px;\n\n            @include mq-max(md) {\n                gap: 6px;\n            }\n\n            .nav-item-right {\n                border: 1px solid rgba($white, 0.10%);\n                background-color: rgba($white, 0.10%);\n\n                padding: 7px 9px;\n                border-radius: 100%;\n\n                &.login-btn {\n                    border: none;\n                    background-color: transparent;\n                    padding: 0;\n\n                    a {\n                        span {\n                            @include mq-max(md) {\n                                display: none;\n                            }\n                        }\n                    }\n                }\n\n                .btn {\n                    &.btn-outline {\n                        border-radius: 30px;\n                        padding: 8px 30px;\n                        border-color: var(--theme-color);\n                        color: var(--theme-color);\n                        background-color: transparent;\n\n                        @include mq-max(lg) {\n                            padding: 4px 12px;\n                            font-size: 16px;\n                        }\n                    }\n                }\n\n                &:last-child {\n                    position: relative;\n                    margin-left: 12px;\n\n                    &:before {\n                        content: \"\";\n                        position: absolute;\n                        width: 1px;\n                        height: 20px;\n                        background-color: rgba($white, 0.30%);\n                        top: 50%;\n                        left: -12px;\n                        transform: translateY(-50%);\n                    }\n\n                }\n            }\n\n            .language-dropdown,\n            .currency-dropdown,\n            .profile-dropdown {\n                @include flex_common_1 ($dis: flex, $align: center);\n                gap: 6px;\n                padding: 5px 8px;\n                border-radius: 30px;\n\n                img {\n                    width: 24px;\n                    height: 24px;\n                    border-radius: 100%;\n                }\n\n                .language-btn,\n                .currency-btn,\n                .profile-btn {\n                    background: transparent;\n                    color: white;\n                    border: none;\n                    line-height: 1;\n                    padding: 0;\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: 12px;\n\n                    span {\n                        display: inline-flex;\n                        align-items: center;\n                        gap: 6px;\n                        line-height: 1;\n                        font-size: 16px;\n                    }\n                }\n\n                .onhover-show-div {\n                    min-width: 120px;\n                    transition: all 0.3s linear;\n                    position: absolute;\n                    z-index: 3;\n                    border: 1px solid rgba($white, 0.10%);\n                    background-color: #1d2537;\n                    opacity: 0;\n                    visibility: hidden;\n                    transform: translate3d(0, -5%, 0);\n                    top: 45px;\n                    right: 0;\n                    padding: 15px;\n                    border-radius: 12px;\n                    width: max-content;\n\n                    [dir=\"rtl\"] & {\n                        left: 0;\n                        right: unset;\n                    }\n\n                    .lang,\n                    .currency,\n                    .profile,\n                    .page-link {\n                        cursor: pointer;\n                        transition: all 0.3s;\n                        @include flex_common_1 ($dis: flex, $align: center);\n                        border: none;\n                        color: rgba($white, 0.6);\n                        background-color: transparent;\n                        gap: 10px;\n                        font-size: 15px;\n                        padding: 0;\n                        white-space: nowrap;\n\n                        a {\n                            color: rgba($white, 0.6);\n                            display: flex;\n                            align-items: center;\n                            gap: 10px;\n\n                        }\n\n                        i {\n                            --Iconsax-Color: #ffffff99;\n                        }\n\n                        img {\n                            border-radius: 0 !important;\n                            width: auto !important;\n                            height: auto !important;\n                        }\n\n                        &:hover {\n                            color: $white;\n                            transform: translateX(5px);\n\n                            a {\n                                color: $white;\n                            }\n\n                            i {\n                                --Iconsax-Color: #fff;\n                            }\n\n                            [dir=\"rtl\"] & {\n                                transform: translateX(-5px);\n                            }\n                        }\n\n                        +li {\n                            margin-top: 16px;\n                        }\n                    }\n                }\n\n                &:hover {\n                    .onhover-show-div {\n                        opacity: 1;\n                        visibility: visible;\n                        transform: none;\n                    }\n                }\n            }\n\n            .profile-dropdown {\n                img {\n                    width: 32px;\n                    height: 32px;\n                    border-radius: 100%;\n                }\n\n                .onhover-show-div {\n                    top: 53px;\n                }\n            }\n\n            i {\n                --Iconsax-Size: 18px;\n                --Iconsax-Color: White;\n            }\n        }\n\n        .navbar-toggler {\n            // margin-right: calc(10px + (26 - 10) * ((100vw - 320px) / (1920 - 320)));\n            border: none;\n            padding: 0;\n\n            .navbar-toggler-icon {\n                @include pseudowh($width: 25px, $height: 25px);\n            }\n\n            &:focus {\n                box-shadow: none;\n            }\n        }\n\n        .navbar-collapse {\n            .navbar-nav {\n                gap: calc(20px + (50 - 20) * ((100vw - 1200px) / (1920 - 1200)));\n\n                .nav-item {\n                    .nav-link {\n                        font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n                        color: rgba($white, 0.6);\n                        padding-left: 0;\n                        padding-right: 0;\n\n                        &.active {\n                            font-weight: 500;\n                            color: $white;\n                        }\n                    }\n                }\n            }\n\n            @include mq-max(xl) {\n                position: fixed;\n                top: 0;\n                background-color: white;\n                left: -320px;\n                width: calc(300px + (320 - 300) * ((100vw - 1200px) / (1920 - 1200)));\n                height: 100%;\n                transition: all 0.3s ease-in-out;\n                visibility: hidden;\n                opacity: 0;\n\n                .navbar-header {\n                    padding: 20px;\n                    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                    box-shadow: 0 8px 10px rgba($title-color, 0.05);\n\n                    h4 {\n                        color: $primary-color;\n                        font-weight: 600;\n                        line-height: 23px;\n                    }\n\n                    i {\n                        ---Iconsax-Size: 18px;\n                        --Iconsax-Color: #808B97;\n                    }\n                }\n\n                .navbar-nav {\n                    padding: 20px;\n                    height: 100%;\n                    gap: 16px;\n                    overflow: auto;\n\n                    .nav-item {\n                        .nav-link {\n                            color: $title-color;\n                            padding: 0;\n                        }\n                    }\n                }\n\n                &.show {\n                    transition: all 0.3s ease-in-out;\n                    left: 0;\n                    z-index: 2;\n                    visibility: visible;\n                    opacity: 1;\n\n                }\n\n            }\n        }\n    }\n}", "/**=====================\n    Footer scss\n==========================**/\n\n.footer-section{\n    background-color: $title-color;\n    padding: calc(30px + (70 - 30) * ((100vw - 320px) / (1920 - 320))) 0;\n\n    .copyright{\n        @include flex_common ($dis: flex, $align: center, $justify: space-between);\n        gap: 10px;\n        border-bottom: 1px solid rgba($white, 0.15%);\n        padding-bottom: 10px;\n        margin-bottom: 30px;\n        .footer-logo{\n            img{\n                height: 32px;\n            }\n        }\n        p{\n            color: $white;\n            margin: 0;\n            font-size: 18px;\n        }\n        @include mq-max(sm){\n            flex-direction: column;\n            align-items: start;\n        }\n    }\n\n    i {\n        --Iconsax-Size: 22px;\n        --Iconsax-Color: #5565FE;\n    }\n\n    .heading-footer{\n        font-weight: 400;\n        line-height: 26px;\n        color: $white;\n        margin-bottom: 14px;\n        @include flex_common_1 ($dis: flex, $align: center);\n        gap: 8px;\n        font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n        @include mq-max(sm){\n            margin-bottom: 6px;\n        }\n    } \n\n    ul{\n        margin-left: 30px;\n        display: flex;\n        flex-direction: column;\n        gap: 14px;\n\n        @include mq-max(sm){\n            gap: 6px;\n        }\n        li{\n            color: rgba($white, 0.7%);\n            font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n            .nav-link{\n                color: rgba($white, 0.7%);\n                padding: 0;\n            }\n        }\n    }\n\n    .btn{\n        border-radius: 13px;\n        width: max-content;\n        padding: 10px calc(40px + (50 - 40) * ((100vw - 320px) / (1920 - 320)));\n        margin-top: 40px;\n        margin-left: 30px;\n        font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n    }\n\n    @include mq-max(sm){\n        &.nav-folderized{\n            .nav{\n                &.open{\n                    ul{\n                        display: flex;\n                    }\n                }\n                ul{\n                    display: none;\n                }\n            }\n        }\n    }\n\n}\n", "/**=====================\n     About Us scss\n==========================**/\n\n.about-us-section,\n.about-us-section-1 {\n    position: relative;\n\n    .about-us-content {\n        margin-right: 0.3px;\n\n        [dir=\"rtl\"] & {\n            margin-left: 0.3px;\n            margin-right: unset;\n        }\n\n        .card {\n            .card-body {\n                padding: 35px 30px;\n\n                .card-title {\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: 10px;\n\n                    img {\n                        width: 70px;\n                        height: 70px;\n                        border-radius: 100%;\n\n                        &.star {\n                            width: 20px;\n                            height: 20px;\n                        }\n\n                        &.quote,\n                        &.quote-active {\n                            position: absolute;\n                            top: calc(20px + (30 - 20) * ((100vw - 1200px) / (1920 - 1200)));\n                            right: calc(20px + (30 - 20) * ((100vw - 1200px) / (1920 - 1200)));\n                            width: calc(30px + (50 - 30) * ((100vw - 1200px) / (1920 - 1200)));\n                            height: calc(30px + (50 - 30) * ((100vw - 1200px) / (1920 - 1200)));\n                            border-radius: 0;\n\n                            @include mq-max(xl) {\n                                width: calc(30px + (50 - 30) * ((100vw - 575px) / (1920 - 575)));\n                                height: calc(30px + (50 - 30) * ((100vw - 575px) / (1920 - 575)));\n                            }\n                        }\n\n                        &.quote-active {\n                            display: none;\n                        }\n\n                        &.quote {\n                            display: block;\n                        }\n                    }\n                }\n\n                .card-detail {\n                    padding: 24px 0 0;\n                    margin-top: 20px;\n                    border-top: 1px dashed rgba($title-color, 0.1%);\n\n                    p {\n                        margin: 0;\n                        color: $light-color;\n                        font-size: 16px;\n                        line-height: 24px;\n                        width: 100%;\n                        overflow: hidden;\n                        display: -webkit-box;\n                        text-overflow: ellipsis;\n                        -webkit-line-clamp: 3;\n                        -webkit-box-orient: vertical;\n                    }\n                }\n            }\n        }\n\n        .swiper-wrapper {\n            height: 310px;\n\n            .swiper-slide {\n                transition: all 200ms linear;\n                transform: translateY(50px);\n\n                &.swiper-slide-active {\n                    transform: translateY(0px);\n\n                    .card {\n                        border-color: rgba($primary-color, 0.30%);\n                        background-color: rgba($primary-color, 0.10%);\n\n                        .card-title {\n                            .quote-active {\n                                display: block;\n                            }\n\n                            .quote {\n                                display: none;\n                            }\n                        }\n                    }\n                }\n            }\n        }\n\n        .image-1 {\n            top: 140px;\n            left: 20px;\n            height: 16px;\n            position: absolute;\n            animation: mover 2s infinite alternate;\n            animation-delay: 1s;\n\n            @include mq-max(lg) {\n                display: none;\n            }\n        }\n\n        .swiper-pagination {\n            left: 0;\n            width: 70px;\n            right: 0;\n            margin: 0 auto;\n            bottom: 0;\n\n            .swiper-pagination-bullet {\n                height: 8px;\n                width: 8px;\n                background-color: $primary-color;\n            }\n        }\n\n        .swiper-button-next3 {\n            bottom: 0;\n            top: unset;\n            right: 43%;\n            z-index: 1;\n            left: unset;\n        }\n\n        .swiper-button-prev3 {\n            bottom: 0;\n            right: unset;\n            top: unset;\n            left: 43%;\n            z-index: 1;\n        }\n    }\n}\n\n.more-section,\n.feature-section {\n    .more-content {\n        h3 {\n            font-size: calc(20px + (22 - 20) * ((100vw - 320px) / (1920 - 320)));\n            font-weight: 600;\n            margin-bottom: 4px;\n        }\n\n        p {\n            font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n            color: $light-color;\n            font-weight: 500;\n\n            a {\n                color: $primary-color;\n            }\n        }\n    }\n\n\n    .feature-content {\n        .feature-box {\n            width: 45px;\n            height: 45px;\n            border-radius: 4px;\n            background-color: $primary-color;\n            color: $white;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin: auto;\n            margin-bottom: 12px;\n\n            i {\n                --Iconsax-Size: 24px;\n                --Iconsax-Color: #fff;\n            }\n\n        }\n\n        p {\n            font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n            margin: auto;\n            margin-top: 4px;\n            width: 90%;\n            font-weight: 500;\n            color: rgba($title-color, 0.6);\n        }\n\n        .feature-list {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-wrap: wrap;\n            gap: calc(8px + (16 - 8) * ((100vw - 320px) / (1920 - 320)));\n            margin-top: 16px;\n\n            .list-item {\n                padding: 8px calc(8px + (12 - 8) * ((100vw - 320px) / (1920 - 320)));\n                border-radius: 6px;\n                border: 1px solid rgba($title-color, 0.1);\n                font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n                font-weight: 500;\n                line-height: 1.2;\n                display: flex;\n                align-items: center;\n                gap: 8px;\n                color: rgba($title-color, 0.8);\n\n                i {\n                    --Iconsax-Size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));\n                    --Iconsax-Color: #5565FE;\n                }\n            }\n        }\n\n    }\n\n}\n\n.about-us {\n    .image-grp {\n        display: flex;\n        align-items: start;\n        gap: 20px;\n\n        img {\n            height: auto;\n            width: 50%;\n            border-radius: 12px;\n\n            &:nth-child(even) {\n                margin-top: 80px;\n            }\n        }\n    }\n\n    .content-detail {\n        color: rgba($title-color, 0.8);\n\n        p {\n            font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n            line-height: 1.2;\n            margin-bottom: 20px;\n        }\n\n        .item-lists {\n            list-style: none;\n            margin: 0;\n            gap: calc(12px + (20 - 12) * ((100vw - 320px) / (1920 - 320)));\n\n            li {\n                display: flex;\n                align-items: start;\n                gap: 16px;\n\n                &::before {\n                    display: none;\n                }\n\n\n                .icon-box {\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    border-radius: 100%;\n                    height: calc(30px + (35 - 30) * ((100vw - 320px) / (1920 - 320)));\n                    width: calc(30px + (35 - 30) * ((100vw - 320px) / (1920 - 320)));\n                    min-width: calc(30px + (35 - 30) * ((100vw - 320px) / (1920 - 320)));\n                    background-color: $primary-color;\n                }\n\n                i {\n                    --Iconsax-Size: 18px;\n                    --Iconsax-Color: #fff;\n                }\n\n                .detail {\n                    h4 {\n                        font-weight: 600;\n                        line-height: 1.2;\n                        margin-bottom: 4px;\n                    }\n\n                    p {\n                        line-height: 1.2;\n                        font-size: calc(15px + (17 - 15) * ((100vw - 320px) / (1920 - 320)));\n                        margin-bottom: 0;\n                    }\n                }\n            }\n        }\n    }\n}\n\n.about-us-section-1 {\n    .title-1 {\n        width: 100%;\n    }\n\n    .about-us-content {\n        .card {\n            box-shadow: 0 0 8px rgba($title-color, 0.15);\n            border-radius: 4px;\n\n            .card-body {\n                padding: 20px;\n\n                .card-detail {\n                    display: flex;\n                    flex-direction: column;\n                    gap: 10px;\n                    text-align: center;\n                    margin: 0;\n                    border: none;\n                    padding: 0;\n                    align-items: center;\n\n                    i {\n                        --Iconsax-Size: calc(20px + (24 - 20) * ((100vw - 320px) / (1920 - 320)));\n                        --Iconsax-Color: #5565FE;\n                    }\n\n                    .avatar {\n                        width: 50px;\n                        height: 50px;\n                        border-radius: 100%;\n                    }\n\n                    .hr {\n                        background-color: $gray-color;\n                        height: 2px;\n                        width: 25px;\n                    }\n\n                    h3 {\n                        font-size: 16px;\n                        color: $light-color;\n                    }\n\n                    .rate {\n                        justify-content: center;\n                        margin-top: 8px;\n                        gap: 8px;\n                    }\n\n                    p {\n                        color: rgba($title-color, 0.8);\n                        overflow: unset;\n                        display: block;\n                        font-size: 17px;\n                    }\n                }\n\n            }\n        }\n\n        .swiper {\n            padding: 0 4px;\n\n            .swiper-wrapper {\n                height: auto;\n                padding: 5px 0;\n\n                .swiper-slide {\n                    transform: unset;\n                    transition: none;\n\n                    &.swiper-slide-active {\n                        transform: none;\n\n                        .card {\n                            border: none;\n                            background-color: unset;\n                        }\n                    }\n                }\n            }\n        }\n\n    }\n}\n\n.our-work {\n    .our-work-content {\n        .work-box {\n            padding: calc(25px + (30 - 25) * ((100vw - 320px) / (1920 - 320))) calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));\n            border-radius: 6px;\n            background-color: $section-bg;\n            text-align: center;\n\n            h3 {\n                font-size: calc(20px + (28 - 20) * ((100vw - 320px) /(1920 - 320)));\n                font-weight: 600;\n                line-height: 1.2;\n                color: $primary-color;\n            }\n\n            p {\n                font-size: calc(14px + (16 - 14) * ((100vw - 320px) /(1920 - 320)));\n                text-align: center;\n                display: inline-block;\n                font-weight: 600;\n                color: rgba($title-color, 0.6);\n            }\n        }\n    }\n}", "/**=====================\n    Auth scss\n==========================**/\n\n.log-in-section {\n    width: 100%;\n    margin: 0 auto;\n    overflow: auto;\n    position: relative;\n    height: 100vh;\n\n    .login-content {\n        height: 100%;\n    }\n}\n\n.image-contain {\n    background-color: $primary-color;\n    padding: 50px;\n    text-align: center;\n    flex-direction: column;\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n    height: 100%;\n\n    .logo {\n        height: 50px;\n        margin: 0 auto;\n        display: flex;\n        margin-bottom: 30px;\n    }\n\n    .auth-image {\n        width: 40%;\n    }\n\n    .auth-content {\n        color: $white;\n        margin-top: 50px;\n\n        h2 {\n            text-transform: uppercase;\n            line-height: 41px;\n            font-weight: 700;\n        }\n\n        p {\n            font-size: 16px;\n            line-height: 20px;\n        }\n\n        .app-install {\n            margin-top: 30px;\n            @include flex_common ($dis: flex, $align: center, $justify: center);\n            gap: 16px;\n\n            img {\n                height: 50px;\n            }\n        }\n    }\n}\n\n.login-main {\n    background-color: $section-bg;\n    height: 100%;\n    position: relative;\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n\n    .language-dropdown {\n        position: absolute;\n        top: 40px;\n        right: 40px;\n        @include flex_common ($dis: flex, $align: center, $justify: end);\n        gap: 6px;\n        padding: 5px 8px;\n        border-radius: 30px;\n        border: 1px solid rgba($title-color, 0.10%);\n        background-color: $white;\n        margin-bottom: 30px;\n        width: max-content;\n        margin-left: auto;\n\n        img {\n            width: 24px;\n            height: 24px;\n            border-radius: 100%;\n        }\n\n        i {\n            --Iconsax-Size: 18px;\n            --Iconsax-Color: #2263eb;\n        }\n\n        .language-btn {\n            background: transparent;\n            color: $title-color;\n            border: none;\n            line-height: 1;\n            padding: 0;\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 12px;\n\n            span {\n                @include flex_common_1 ($dis: inline-flex, $align: center);\n                gap: 6px;\n                line-height: 1;\n                font-size: 16px;\n            }\n        }\n\n        .onhover-show-div {\n            min-width: 120px;\n            transition: all 0.3s linear;\n            position: absolute;\n            z-index: 3;\n            border: 1px solid rgba($title-color, 0.10%);\n            background-color: $white;\n            opacity: 0;\n            visibility: hidden;\n            transform: translate3d(0, -5%, 0);\n            top: 37px;\n            right: 0;\n            padding: 15px;\n            border-radius: 4px;\n\n\n            [dir=\"rtl\"] & {\n                left: 0;\n                right: unset;\n            }\n\n            .lang {\n                cursor: pointer;\n                transition: all 0.3s;\n                @include flex_common_1 ($dis: flex, $align: center);\n                border: none;\n                color: $title-color;\n                background-color: transparent;\n                gap: 8px;\n                font-size: 13px;\n                padding: 0;\n\n                img {\n                    border-radius: 0 !important;\n                    width: auto !important;\n                    height: auto !important;\n                }\n\n                &:hover {\n                    color: var(--theme-color);\n                    transform: translateX(5px);\n\n                    [dir=\"rtl\"] & {\n                        transform: translateX(-5px);\n                    }\n                }\n\n                +li {\n                    margin-top: 8px;\n                }\n            }\n        }\n\n        &:hover {\n            .onhover-show-div {\n                opacity: 1;\n                visibility: visible;\n                transform: none;\n            }\n        }\n    }\n\n    .login-card {\n        width: 100%;\n        @include flex_common ($dis: flex, $align: center, $justify: center);\n        flex-direction: column;\n        background-color: white;\n        width: $login-card;\n        border-radius: $login-br;\n        margin: 20px auto;\n\n        .login-title {\n            width: 100%;\n            border-bottom: 1px dashed $gray-color;\n            padding: 25px;\n            text-align: center;\n\n            h2 {\n                font-weight: 700;\n                line-height: 33px;\n            }\n\n            p {\n                color: $light-color;\n                padding-top: 6px;\n                font-size: 16px;\n                margin: 0;\n            }\n        }\n\n        .login-detail {\n            background-color: $section-bg;\n            border-radius: 10px;\n            width: calc(100% - 80px);\n            margin: 30px 0;\n            padding: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320)));\n\n            .form-group {\n                position: relative;\n\n                i {\n                    --Iconsax-Size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));\n                    --Iconsax-Color: #808B97;\n                    position: absolute;\n                    top: calc(42px + (45 - 42) * ((100vw - 320px) / (1920 - 320)));\n                    left: 15px;\n\n                }\n\n                .toggle-password {\n                    position: absolute;\n                    top: calc(42px + (45 - 42) * ((100vw - 320px) / (1920 - 320)));\n                    right: 15px;\n                    left: unset;\n                    cursor: pointer;\n\n                    i {\n                        position: static;\n                    }\n\n                    .eye-slash {\n                        display: none;\n                    }\n\n                    &.eye {\n                        i.eye {\n                            display: block;\n                        }\n\n                        i.eye-slash {\n                            display: none;\n                        }\n                    }\n\n                    &.eye-slash {\n                        i.eye-slash {\n                            display: block;\n                        }\n\n                        i.eye {\n                            display: none;\n                        }\n                    }\n                }\n\n                label {\n                    font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n                    font-weight: 500;\n                    line-height: 23px;\n                    margin-bottom: 8px;\n                    position: relative;\n\n                    &:before {\n                        position: absolute;\n                        content: \"\";\n                        background-color: $primary-color;\n                        height: 30px;\n                        width: 4px;\n                        top: -3px;\n                        left: -30px;\n                        border-radius: 4px;\n                    }\n                }\n\n                .form-control {\n                    border-radius: 8px;\n                    padding: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) 45px;\n                    background-color: $white;\n                    color: rgba($title-color, 0.8);\n                    border: none;\n\n                    &:focus {\n                        box-shadow: none;\n                    }\n                }\n            }\n\n            .forgot-pass {\n                margin-top: 12px;\n                text-align: end;\n\n                a {\n                    font-size: 16px;\n                    line-height: 20px;\n                }\n            }\n\n            .btn {\n                margin-top: 40px;\n                border-radius: 8px;\n                width: 100%;\n                justify-content: center;\n                font-size: 18px;\n\n                &:focus {\n                    box-shadow: none;\n                }\n            }\n\n            .not-member {\n                text-align: center;\n                font-size: 18px;\n                font-weight: 400;\n                color: $light-color;\n                line-height: 20px;\n                padding-top: 10px;\n\n                small {\n                    color: $primary-color;\n                    font-weight: 500;\n                }\n            }\n\n        }\n\n        .other-options {\n            width: 100%;\n            padding: 30px 40px 40px;\n        }\n\n        .options {\n            position: relative;\n            text-align: center;\n            width: max-content;\n            margin: 0 auto;\n            color: $light-color;\n            font-size: 18px;\n            display: flex;\n\n            &:before,\n            &:after {\n                position: absolute;\n                content: \"\";\n                background-color: $light-color;\n                width: 22px;\n                height: 1px;\n                top: 50%;\n                transform: translateY(-50%);\n            }\n\n            &:before {\n                left: -30px;\n            }\n\n            &:after {\n                right: -30px;\n            }\n        }\n\n        .social-media {\n            @include flex_common ($dis: flex, $align: center, $justify: center);\n            gap: 20px;\n            margin-top: 24px;\n\n            .social-icon {\n                background-color: $section-bg;\n                border-radius: 100%;\n                border: 1px solid rgba($title-color, 0.1);\n                height: 70px;\n                width: 70px;\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n            }\n        }\n\n        @include mq-max(sm) {\n            width: 100%;\n        }\n    }\n\n}\n\n.terms {\n    p {\n        margin: 0;\n        color: $light-color;\n    }\n}\n\n.login-img {\n    width: 250px;\n    padding-top: 30px;\n}\n\n.otp-field {\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 12px;\n\n    input {\n        width: 50px;\n        height: 50px;\n        border: none;\n        background-color: $white;\n        border-radius: 8px;\n        text-align: center;\n    }\n}", "/**=====================\n    Blog scss\n==========================**/\n\n.blog-section {\n    .blog-content {\n        background-color: $section-bg;\n        padding: calc(15px + (30 - 15) * ((100vw - 320px) / (1920 - 320)));\n        border-radius: 6px;\n        margin-bottom: 30px;\n\n        &:last-child {\n            margin-bottom: 0;\n        }\n\n        .blog-main {\n            padding: 0 calc(10px + (30 - 10) * ((100vw - 320px) / (1920 - 320)));\n            border-right: 1px solid $gray-color;\n\n            @include mq-max(md) {\n                border: none;\n                padding: 0 10px;\n            }\n\n            @include mq-min(md) {\n                &:first-child {\n                    padding-left: 10px;\n                }\n\n                &:last-child {\n                    padding-right: 10px;\n                    border: none;\n                }\n            }\n        }\n\n        .card {\n            background-color: transparent;\n            border: none;\n            border-radius: 0;\n            margin-bottom: 0;\n\n            .card-body {\n                padding: 0;\n                color: $light-color;\n\n                h4 {\n                    margin-top: 20px;\n                    font-size: calc(19px + (22 - 19) * ((100vw - 320px) / (1920 - 320)));\n                    font-weight: 500;\n                    line-height: 28px;\n                    color: $title-color;\n                    white-space: nowrap;\n                    width: 100%;\n                    overflow: hidden;\n\n                    a {\n                        color: $title-color;\n                    }\n\n                    &:hover {\n                        a {\n                            color: $primary-color;\n                        }\n                    }\n                }\n\n\n\n                i {\n                    --Iconsax-Size: 20px;\n                    --Iconsax-Color: #808B97;\n                }\n\n                .blog-footer {\n                    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                    gap: 4px;\n                    margin-top: 25px;\n                    font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n                }\n            }\n        }\n    }\n\n    .blog-detail {\n        line-height: 1;\n        margin-top: 2px;\n        color: $light-color;\n        @include flex_common_1 ($dis: flex, $align: center);\n        flex-wrap: wrap;\n        gap: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));\n\n        li {\n            font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n            border-right: 2px solid rgba($title-color, 0.4);\n            padding-right: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));\n\n            &:first-child {\n                padding-left: 0;\n            }\n\n            &:last-child {\n                padding-right: 0;\n                border: none;\n            }\n\n            [dir=\"rtl\"] & {\n                padding-left: calc(5px + (10 - 5) * ((100vw - 320px) / (1920 - 320)));\n                padding-right: unset;\n\n                &:first-child {\n                    padding-left: unset;\n                    padding-right: 0;\n                }\n\n                &:last-child {\n                    padding-right: unset;\n                    padding-left: 0;\n                }\n            }\n        }\n    }\n\n    h4 {\n        font-size: calc(19px + (22 - 19) * ((100vw - 320px) / (1920 - 320)));\n        font-weight: 500;\n        line-height: 28px;\n        color: $title-color;\n        white-space: wrap;\n    }\n\n    .detail-sec {\n        padding: calc(15px + (20 - 15) * ((100vw - 320px) / (1920 - 320)));\n        border-radius: 10px;\n\n        label {\n            font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n            font-weight: 500;\n            margin-bottom: 12px;\n        }\n\n        .overview-list {\n            padding-left: calc(16px + (30 - 16) * ((100vw - 320px) / (1920 - 320)));\n        }\n    }\n}", "/**=====================\n     Category scss\n==========================**/\n\n.category-img {\n     border-radius: 10px;\n}\n\n.category-span {\n     text-align: center;\n     display: block;\n     margin-top: calc(8px + (16 - 8) * ((100vw - 320px) / (1920 - 320)));\n     font-size: calc(14px + (20 - 14) * ((100vw - 320px) / (1920 - 320)));\n     font-weight: 500;\n}\n\n.accordion{\n     .accordion-item{\n          border: none;\n          .accordion-button{\n               background-color: transparent;\n               width: max-content;\n               padding: 0;\n               border: none;\n               box-shadow: none;\n               &:not(.collapsed){\n                    &:after{\n                         background-image: url(../../assets/images/svg/arrow.svg);\n                    }\n               }\n          }\n     }\n}", "/**=====================\n    Contact scss\n==========================**/\n\n.contact-section {\n    .heading-p {\n        font-size: calc(16px + (17 - 16) * ((100vw - 320px) / (1920 - 320)));\n        line-height: 1.3;\n        margin: 20px 0 calc(20px + (30 - 20) * ((100vw - 320px) / (1920 - 320)));\n        color: rgba($title-color, 0.7);\n    }\n\n    .contact-content {\n        .contact-us-form {\n            padding: 20px;\n            border-radius: 12px;\n            border: 1px solid $gray-color;\n            background-color: $white;\n        }\n\n        .contact-info {\n            display: flex;\n            flex-direction: column;\n            gap: 16px;\n\n            li {\n                border-radius: 6px;\n                font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n                font-weight: 500;\n                line-height: 1.2;\n                display: flex;\n                align-items: start;\n                gap: 8px;\n                color: $light-color;\n                width: auto;\n\n                i {\n                    --Iconsax-Size: calc(18px + (20 - 18) * ((100vw - 320px) / (1920 - 320)));\n                    --Iconsax-Color: #2263eb;\n                }\n\n                .detail {\n                    display: flex;\n                    flex-direction: column;\n                    gap: 4px;\n                    padding: 0;\n\n                    h5 {\n                        color: $title-color;\n                        font-weight: 500;\n                    }\n\n                    p {\n                        color: rgba($title-color, 0.7);\n                        line-height: 1.3;\n                        font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n                    }\n                }\n            }\n        }\n    }\n}\n\n.map-section {\n    .map {\n        width: 100%;\n        height: calc(200px + (400 - 200) * ((100vw - 320px) / (1920 - 320)));\n\n        iframe {\n            width: 100%;\n            height: 100%;\n        }\n    }\n}", "/**=====================\n    Policy scss\n==========================**/\n\n.list-items {\n    padding-left: 1.25rem;\n    list-style-type: circle;\n\n    .list-item {\n        margin-bottom: 20px;\n        display: list-item;\n\n        h6 {\n            font-size: 18px;\n            margin-bottom: .75rem;\n            font-weight: 500;\n            line-height: 1.5;\n            color: $title-color;\n        }\n\n        p {\n            font-size: 17px !important;\n            color: rgba($title-color, 0.7);\n            line-height: 1.3 !important;\n            margin-bottom: 16px;\n        }\n    }\n\n    li {\n        margin-bottom: 12px;\n    }\n}", "/**=====================\n     About Us scss\n==========================**/\n\n\n\n.expert-section {\n    position: relative;\n    background-image: url(../../assets/images/bg.jpg);\n    background-repeat: no-repeat;\n    background-position: center;\n    background-size: cover;\n    z-index: 0;    \n}\n.expert-content{\n\n    .dark-card{\n        .card-title{\n            a{\n                color: $white;\n                &:hover{\n                    color: $primary-color;\n                }\n            }\n        }\n    }\n\n    .dark-card, .gray-card {\n        overflow: visible;\n        margin-top: 60px;\n\n        .dark-card-img, .gray-card-img{\n            margin-bottom: 24px;\n            text-align: center;\n\n            img {\n                margin-top: -60px;\n                border-radius: 100%;\n                width: 130px;\n                height: 130px;\n                object-fit: cover;\n                border: 7px solid rgba($white, 0.06%);\n                 @include mq-max(sm) {\n                    width: 110px;\n                    height: 110px;\n                }\n\n                &.like{\n                    margin: 0;\n                    border: none;\n                    border-radius: 0;\n                }\n            }\n            .favourite{\n                top: 20px;\n                right: 20px;\n                left: unset;\n            }\n        }\n\n        .card-body {\n            padding: 0 15px;\n\n            .card-title {\n                @include flex_common ($dis: flex, $align: center, $justify: center);\n                gap: 12px;\n                color: $white;\n\n                h4 {\n                    font-size: 20px;\n                    font-weight: 500;\n                }\n\n                small {\n                    font-size: 14px;\n                }\n            }\n\n            .card-detail {\n                padding: 24px 0;\n                margin-top: 24px;\n                border-top: 2px dashed rgba($white, 0.10%);\n\n                p {\n                    font-size: 16px;\n                    line-height: 24px;\n                    width: 86%;\n                    margin: 0 auto;\n                    text-align: center;\n                }\n            }\n        }\n    }\n\n    .gray-card {\n        .card-body {\n            color: $light-color;\n\n            .card-title {\n                color: $title-color;\n                a{\n                    color: $title-color;\n                    &:hover{\n                        color: $primary-color;\n                    }\n                }\n            }\n\n            .card-detail {\n                border-top: 2px dashed $gray-color;\n            }\n        }\n    }\n}\n\n.reviews-main{\n    background-color: $section-bg;\n    border-radius: 10px;\n    padding: 20px;\n    h6{\n        font-weight: 500;\n        line-height: 1.5;\n    }\n    .overview-list{\n        li{\n            color: $light-color;\n            font-size: 14px;\n        }\n    }\n}\n\n.location {\n    @include flex_common ($dis: flex, $align: center, $justify: center);\n    gap: 6px;\n\n    i {\n        --Iconsax-Size: 18px;\n        --Iconsax-Color: #808B97;\n    }\n\n    h5 {\n        line-height: 20px;\n        color: $light-color;\n    }\n}\n\n.filter{\n    .card{\n        background-color: $section-bg;\n        .card-header{\n            padding: 25px 20px;\n            background-color: transparent;\n            border-bottom: 1px solid $gray-color;\n            @include flex_common ($dis: flex, $align: center, $justify: space-between);\n            h3{\n                font-weight: 700;\n            }\n        }\n        .card-body{\n            overflow: auto;\n            height: 903px;\n            .form-group{\n                @include flex_common_1 ($dis: flex, $align: center);\n                gap: 8px;\n            }\n        }\n    }\n    .provider-card{\n        background-color: $section-bg;\n        border-radius: 10px;\n        .favourite{\n            top: 30px;\n            right: 30px;\n            left: unset;\n        }\n        .provider-detail{\n            .provider-content{\n                border: none;\n                .profile-bg{\n                    background-color: $gray-color;\n                }\n                .profile{\n                    .img{\n                        border-radius: 6px;\n                        width: 110px;\n                        height: 110px;\n                    }\n                }\n                i {\n                    --Iconsax-Size: 16px;\n                    --Iconsax-Color: #808B97;\n                }\n                .delivered{\n                    background-color: $white;\n                    padding: 13px 16px;\n                    border-radius: 6px;\n                    width: 100%;\n                    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                    margin: 25px 0 10px;\n                    span{\n                        font-size: 16px;\n                    }\n                    small{\n                        font-size: 18px;\n                        font-weight: 700;\n                        color: $primary-color;\n                    }\n                }\n                .profile-detail{\n                    p{\n                        color: $title-color;\n                        text-align: left;\n                    }\n                }\n\n                .profile-info{\n                    background-color: $white;\n                    padding: 16px;\n                    p{\n                        color: $title-color;\n                        text-align: start;\n                    }\n                    label{\n                        @include flex_common_1 ($dis: flex, $align: center);\n                        gap: 6px;\n                        font-size: 14px;\n                    }\n\n                    i {\n                        --Iconsax-Size: 18px;\n                    }\n                    .btn-solid-gray{\n                        font-size: 14px;\n                        border-radius: 6px;\n                        padding: 6px 13px;\n                    }\n                }\n            }\n        }\n    }\n}\n\n.provider-detail{\n    .provider-content{\n        border-radius: 12px;\n        border: 1px solid $gray-color;\n        padding: 20px;\n\n        .profile-bg{\n            background-color: $section-bg;\n            height: 70px;\n            border-radius: 12px;\n        }\n        i {\n            --Iconsax-Size: 16px;\n            --Iconsax-Color: #FFC412;\n        }\n        .profile{\n            @include flex_common_1 ($dis: flex, $align: center);\n            flex-direction: column;\n            margin-top: -45px;\n            .img{\n                border-radius: 100%;\n                width: 80px;\n                height: 80px;\n                border: 4px solid $white;\n            }\n            .rate{\n                font-size: 16px;\n            }\n        }\n        .profile-detail{\n            margin-top: 10px;\n            ul{\n                display: flex;\n                flex-direction: column;\n                margin-bottom: 8px;\n                li{\n                    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                    gap: 4px;\n                    font-size: calc(16px + (18 - 16) * ((100vw - 320px) / (1920 - 320)));\n                    border-bottom: 1px solid $gray-color;\n                    padding: 10px 0;\n                    &:last-child{\n                        border: none;\n                    }\n                    @include mq-max(sm){\n                        flex-direction: column;\n                        align-items: start;\n                        gap: 0;\n                    }\n                }\n            }\n        }\n        .badge{\n            padding: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));\n            border-radius: 10px;\n            width: 100%;\n            text-align: start;\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 14px;\n            margin-bottom: 16px;\n        }\n        p{\n            font-size: 16px;\n            color: $light-color;\n            line-height: 26px;\n            text-align: center;\n        }\n        .badge-img{\n            width: calc(25px + (35 - 25) * ((100vw - 320px) / (1920 - 320)));\n            height: calc(25px + (35 - 25) * ((100vw - 320px) / (1920 - 320)));\n        }\n    }\n}\n\n.image-grp{\n    position: relative;\n    width: 90%;\n    margin-inline: auto;\n    @include mq-max(xl){\n        width: 100%;\n    }\n}\n\n\n.application-section,\n.service-provider-section {\n    position: relative;\n\n    .row {\n        align-items: center;\n    }\n\n    .position-absolute {\n        position: absolute;\n        bottom: calc(50px + (198 - 55) * ((100vw - 320px) / (1920 - 320)));\n        right: calc(20px + (60 - 20) * ((100vw - 320px) / (1920 - 320)));\n        @include mq-max(4xl){\n            bottom: calc(75px + (168 - 75) * ((100vw - 320px) / (1920 - 320)));\n            right: calc(10px + (50 - 10) * ((100vw - 320px) / (1920 - 320)))\n        }\n        @include mq-max(3xl){\n            bottom: calc(75px + (168 - 75) * ((100vw - 320px) / (1920 - 320)));\n            right: calc(10px + (50 - 10) * ((100vw - 320px) / (1920 - 320)))\n        }\n        @include mq-max(2xl){\n            bottom: calc(75px + (148 - 75) * ((100vw - 320px) / (1920 - 320)));\n            right: 10px;\n        }\n        @include mq-max(xl){\n            bottom: 0;\n            right: 0px;\n        }\n        @include mq-max(sm){\n           display: none;\n        }\n    }\n\n    .gif-content {\n        box-shadow: -8px -8px 22px #5250505e;\n        border-radius: 10px;\n        color: $white;\n        padding: 16px;\n        background: linear-gradient(180deg, rgba(139, 84, 255, 1) 0%, rgba(84, 101, 255, 1) 100%);\n        width: 140px;\n        text-align: center;\n\n        p {\n            margin: 0;\n            font-size: 12px;\n\n        }\n\n        h6 {\n            border-top: 1px dashed rgba($white, 0.1%);\n            margin-top: 4px;\n            padding-top: 4px;\n            position: relative;\n            height: 10px;\n\n            &:before {\n                content: \"User\";\n                position: absolute;\n                left: 0;\n                right: 0;\n                animation: textChange infinite 2.5s;\n                animation-delay: 2.5s;\n            }\n        }\n    }\n\n    .spark {\n        position: absolute;\n        top: -37px;\n        left: 60%;\n        transform: translateX(-60%);\n    }\n\n    .section-wrap {\n        .image-1 {\n            position: absolute;\n            z-index: -1;\n            bottom: 120px;\n            right: -45px;\n            animation: mover 2s infinite alternate;\n            animation-delay: 1s;\n            width: 140px;\n            overflow: hidden;\n            @include mq-max(sm){\n                display: none;\n            }\n        }\n\n        .image-2 {\n            position: absolute;\n            animation: mover 2s infinite alternate;\n            z-index: -1;\n            top: 210px;\n            left: 90px;\n            animation-delay: 2s;\n            height: 120px;\n            @include mq-max(lg){\n                display: none;\n            }\n        }\n\n        .vector-1,\n        .girl-img {\n            margin: 0 auto;\n            display: flex;\n            width: 100%;\n        }\n\n        .app-gif {\n            position: absolute;\n            top: 0;\n            left: 20%;\n            height: 100%;\n        }\n\n        \n    }\n}\n\n.app-install {\n    margin-top: 50px;\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 16px;\n    \n    img {\n        height: 60px;\n    }\n    @include mq-max(lg){\n        gap: 10px;\n        margin-top: 30px;\n        img{\n            height: calc(45px + (60 - 45) * ((100vw - 320px) / (1920 - 320)));\n        }\n    }\n}\n\n.content-detail {\n    color: $light-color;\n\n    p {\n        font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n        line-height: 30px;\n        margin-bottom: 16px;\n        @include mq-max(sm){\n            line-height: 24px;\n            margin-bottom: 8px;\n        }\n    }\n\n    .item-lists {\n        display: flex;\n        flex-direction: column;\n        gap: 12px;\n        margin-left: 30px;\n        @include mq-max(sm){\n            gap: calc(0px + (12 - 0) * ((100vw - 320px) / (1920 - 320)));\n        }\n\n        li {\n            font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n            position: relative;\n            line-height: 26px;\n\n            &:before {\n                position: absolute;\n                content: \"\";\n                width: 5px;\n                height: 5px;\n                background-color: $light-color;\n                top: 12px;\n                left: -16px;\n                border-radius: 100%;\n\n            }\n        }\n    }\n}\n\n.chart-img{\n    position: absolute;\n    bottom: 55px;\n    left: 120px;\n    border-radius: 13px;\n    animation: mover 2s infinite alternate;\n    animation-delay: 1s;\n    @include mq-min(lg){\n        width: calc(120px + (200 - 120) * ((100vw - 992px) / (1920 - 992)));\n    }\n\n    @include mq-max(xl){\n        left: 50px;\n    }\n\n    @include mq-max(lg){\n        left: calc(30px + (300 - 30) * ((100vw - 320px) / (1920 - 320)));\n        bottom: calc(11px + (205 - 11) * ((100vw - 320px) / (1920 - 320)));\n        width: calc(121px + (200 - 121) * ((100vw - 320px) / (1920 - 320)));\n    }\n}\n\n.group-img{\n    position: absolute;\n    top: 120px;\n    right: 30px;\n    border-radius: 13px;\n    animation: mover 2s infinite alternate;\n    animation-delay: 1s;\n    @include mq-min(lg){\n        width: calc(120px + (200 - 120) * ((100vw - 992px) / (1920 - 992)));\n    }\n    @include mq-max(xl){\n        right: 0px;\n    }\n    @include mq-max(lg){\n        right: calc(10px + (30 - 10) * ((100vw - 320px) / (1920 - 320)));\n        top: calc(90px + (210 - 90) * ((100vw - 320px) / (1920 - 320)));\n        width: calc(110px + (200 - 110) * ((100vw - 320px) / (1920 - 320)));\n    }\n}\n\n.service-provider-section {\n    position: relative;\n    background-image: url(../../assets/images/bg.png);\n    background-color: $section-bg;\n    background-repeat: no-repeat;\n    background-position: center;\n    background-size: cover;\n    z-index: 0;\n\n    .item-lists {\n        margin: 0 !important;\n\n        .item-list {\n            &:before {\n                display: none;\n            }\n\n            i {\n                --Iconsax-Size: 18px;\n                --Iconsax-Color: #5565FE;\n            }\n        }\n    }\n\n    .section-wrap {\n        .image-1 {\n            bottom: 50px;\n            right: 80px;\n        }\n\n        .image-2 {\n            top: 60px;\n            left: 70px;\n        }\n    }\n\n    .btn {\n        border-radius: 13px;\n        margin-top: 50px;\n        width: max-content;\n\n        @include mq-max(lg){\n            margin-top: 30px;\n        }\n    }\n}", "/**=====================\n     About Us scss\n==========================**/\n\n.service-package-section {\n    .service-package-content {\n        .service-detail {\n            color: $white;\n            margin: 30px;\n            width: auto;\n\n            .service-icon {\n                border-radius: 10px;\n                background-color: $gray-color;\n                padding: 12px;\n                width: 60px;\n                height: 60px;\n                margin-bottom: 20px;\n            }\n\n            .price {\n                @include flex_common ($dis: flex, $align: center, $justify: space-between);\n                gap: 4px;\n                margin-top: 6px;\n\n                span {\n                    font-size: 26px;\n                    font-weight: 700;\n                    line-height: 33px;\n                }\n\n                i {\n                    --Iconsax-Size: 24px;\n                    --Iconsax-Color: white;\n                    transform: translateX(0px);\n                    transition: all 0.2s ease;\n                }\n                &:hover{\n                    i{\n                        transform: translateX(5px);\n                        transition: all 0.2s ease;\n                    }\n                }\n            }\n        }\n\n        .service-bg {\n            width: 100%;\n            border-radius: 15px;\n            height: 200px;\n            position: relative;\n            overflow: hidden;\n\n            .service-1 {\n                position: absolute;\n                bottom: -90px;\n                right: -160px;\n                height: 250px;\n            }\n\n            .service-2 {\n                position: absolute;\n                top: -120px;\n                right: -90px;\n                height: 250px;\n                transform: rotate(230deg);\n            }\n\n            .service-3 {\n                position: absolute;\n                bottom: -100px;\n                left: -70px;\n                height: 250px;\n                transform: rotate(49deg);\n            }\n\n            .service-4 {\n                position: absolute;\n                top: -92px;\n                left: -80px;\n                height: 250px;\n                transform: rotate(190deg);\n\n            }\n            &:hover{\n                .service-icon{\n                    img{\n                        animation: tada 1.5s ease infinite;\n                    }\n                }\n            }\n        }\n\n        .service-bg-primary {\n            background-color: rgba($primary-color, 0.80%);\n        }\n\n        .service-bg-secondary {\n            background-color: $secondary-color;\n        }\n\n        .service-bg-info {\n            background-color: $info-color;\n        }\n\n        .service-bg-success {\n            background-color: $success-color;\n        }\n    }\n}\n.service-title {\n    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    gap: 8px;\n\n    h4 {\n        font-size: 18px;\n        font-weight: 500;\n        line-height: 23px;\n        width: 100%;\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-line-clamp: 1;\n        -webkit-box-orient: vertical;\n        word-break: break-all;\n        text-overflow: ellipsis;\n    }\n\n    span {\n        font-size: 14px;\n        line-height: 20px;\n        color: $light-color;\n    }\n\n    small {\n        font-size: 18px;\n        font-weight: 500;\n        line-height: 28px;\n    }\n    @include mq-max(sm){\n        flex-direction: column;\n        align-items: start;\n    }\n}\n\n.service-detail {\n    ul {\n        @include flex_common_1 ($dis: flex, $align: start);\n\n        li {\n            border-right: 1px solid $gray-color;\n            padding: 0 10px;\n\n            [dir=\"rtl\"] & {\n                border-left: 1px solid $gray-color;\n                border-right: unset;\n\n                &:first-child {\n                    padding-left: unset;\n                    padding-right: 0;\n                }\n    \n                &:last-child {\n                    padding-right: unset;\n                    padding-left: 0;\n                }\n            }\n\n            &:first-child {\n                padding-left: 0;\n            }\n\n            &:last-child {\n                border: none;\n                padding-right: 0;\n                width: 100%;\n                overflow: hidden;\n                display: -webkit-box;\n                -webkit-line-clamp: 1;\n                -webkit-box-orient: vertical;\n                text-overflow: ellipsis;\n            }\n        }\n        // @include mq-max(sm){\n        //     flex-direction: column;\n        //     align-items: start;\n        //     li{\n        //         padding: 0;\n        //         border: none;\n        //     }\n        // }\n    }\n\n    \n\n    p {\n        font-size: 14px;\n        color: $light-color;\n        position: relative;\n        padding: 0 0 0 22px;\n        margin: 0;\n        margin-top: 12px;\n        line-height: 23px;\n\n        &:before {\n            content: \"\";\n            position: absolute;\n            width: 5px;\n            height: 5px;\n            background-color: $light-color;\n            border-radius: 100%;\n            top: 10px;\n            left: 6px;\n        }\n    }\n}\n\n.time {\n    @include flex_common_1 ($dis: flex, $align: center);\n    gap: 4px;\n    color: $success-color;\n    font-size: 14px;\n\n    i {\n        --Iconsax-Size: 18px;\n        --Iconsax-Color: #27AF4D;\n    }\n\n    span {\n        font-weight: 500;\n    }\n}\n\n.date-time{\n    li{\n        @include flex_common ($dis: flex, $align: center, $justify: space-between);\n        gap: 4px;\n        flex-wrap: wrap;\n        span{\n            color: $light-color;\n            font-size: 16px;\n\n        }\n        small{\n            font-weight: 500;\n            color: $title-color;\n            font-size: 16px;\n        }\n    }\n}\n\n.service-list-section {\n      .service-list-content {\n        .card {\n            .card-footer {\n                position: relative;\n\n                &:before {\n                    content: \"\";\n                    width: 91%;\n                    position: absolute;\n                    height: 1px;\n                    border: 1px dashed $gray-color;\n                    left: 15px;\n                    top: 0;\n                    transform: translateY(-50%);\n                }\n\n                &:after {\n                    content: '';\n                    position: absolute;\n                    right: 14px;\n                    top: -3px;\n                    border-bottom: 2px solid $gray-color;\n                    border-right: 2px solid $gray-color;\n                    width: 6px;\n                    height: 6px;\n                    transform: rotate(312deg);\n                }\n\n                .footer-detail {\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: 8px;\n\n                    img {\n                        width: 45px;\n                        height: 45px;\n                        border-radius: 100%;\n\n                        &.star {\n                            width: 16px;\n                            height: 16px;\n                        }\n                    }\n\n                    p {\n                        margin: 0;\n                        font-weight: 500;\n                        color: $title-color;\n                        font-size: 14px;\n                        width: 100%;\n                        overflow: hidden;\n                        display: -webkit-box;\n                        -webkit-line-clamp: 1;\n                        -webkit-box-orient: vertical;\n                        word-break: break-all;\n                        text-overflow: ellipsis;\n                    }\n\n                    i {\n                        --Iconsax-Size: 18px;\n                        --Iconsax-Color: #FFC412;\n                    }\n                }\n\n                a {\n                    @include flex_common_1 ($dis: flex, $align: center);\n                    gap: 2px;\n\n                    &.btn {\n                        width: max-content;\n                        &.btn-outline {\n                            color: $primary-color;\n                            font-size: 18px;\n                            line-height: 1;\n                            @include mq-max(3xl){\n                                padding: 10px;\n                            }\n                            @include mq-max(md){\n                                font-size: 14px;\n                                padding: 10px 6px;\n                                i{\n                                    --Iconsax-Size: 17px;\n                                }\n                            }\n                        }\n                    }\n\n                    i {\n                        --Iconsax-Size: 19px;\n                        --Iconsax-Color: #0019ff;\n                    }\n                }\n            }\n        }\n\n        .service-detail-slider{\n            .service-img{\n                border-radius: 15px;\n                \n            }\n        }\n\n        .service-detail{\n            p{\n                width: 100%;\n                overflow: hidden;\n                display: -webkit-box;\n                -webkit-line-clamp: 2;\n                -webkit-box-orient: vertical;\n                text-overflow: ellipsis;\n            }\n        }\n\n        .select-dropdown{\n            @include mq-max(md){\n                flex-direction: column;\n                align-items: start;\n                gap: 12px;\n            }\n            \n            .form-group{\n                @include mq-max(md){\n                    width: 100%;\n                }\n            }\n        }\n        .col-custom-3{\n            @include mq-max(xl){\n                position: relative;\n            }\n            // .filter{\n            //     @include mq-max(xl){\n            //         position: absolute;\n            //         top: 55px;\n            //         // left: -500px;\n            //         left: 12px;\n            //         transition: all 0.3s ease;\n            //         z-index: 3;\n            //         width: 290px;\n            //         transform: translateY(-10px);\n            //         opacity: 0;\n            //         visibility: hidden;\n            //         &.open{\n            //             left: 12px;\n            //             transition: all 0.3s ease;\n            //             transform: translateY(0px);\n            //             opacity: 1;\n            //         visibility: visible;\n            //         }\n            //     }\n            // }\n        }\n    }\n}\n\n.detail-content{\n    margin-top: 24px;\n    .title{\n        @include flex_common ($dis: flex, $align: center, $justify: space-between);\n        gap: 10px;\n        border: none;\n        margin: 0;\n        padding-bottom: 4px;\n\n        &:before{\n            display: none;\n        }\n        h3{\n            font-size: calc(20px + (24 - 20) * ((100vw - 320px) / (1920 - 320)));\n            font-weight: 500;\n        }\n        @include mq-max(sm){\n                flex-direction: column;\n                align-items: start;\n        }\n    }\n    p{\n        color: $light-color;\n        font-size: 16px;\n        margin: 12px 0;\n    }\n    label{\n        font-size: 16px;\n        margin-bottom: 4px;\n    }\n    .lists{\n        @include flex_common ($dis: flex, $align: center);\n        justify-content: start;\n        flex-wrap: wrap;\n        gap: 12px;\n        margin-bottom: 24px;\n    }\n    .detail-sec{\n        background-color: $section-bg;\n        border-radius: 10px;\n        .overview-list{\n            color: $light-color;\n        }\n    }\n}\n\n.service-item{\n    gap: 16px;\n    @include flex_common_1 ($dis: flex, $align: center);\n    border-bottom: 1px solid $gray-color;\n    &:first-child{\n        padding-top: 0;\n    }\n    &:last-child{\n        border: none;\n        padding-bottom: 0;\n    }\n    img{\n        width: 120px;\n        height: 120px;\n    }\n    @include mq-max(sm){\n        flex-direction: column;\n        align-items: start;\n    }\n}\n\n.amount-value{\n    font-weight: 700;\n    font-size: 22px;\n    line-height: 40px;\n}\n\n.amount{\n    border-radius: 12px;\n    overflow: hidden;\n    border: 1px solid $gray-color;\n}\n.amount-header{\n    @include flex_common ($dis: flex, $align: center, $justify: space-between);\n    background-color: rgba($primary-color, 0.10%);\n    color: $primary-color;\n    padding: 16px;\n    span{\n        font-size: 16px;\n    }\n    small{\n        font-weight: 700;\n        font-size: 30px;\n        line-height: 40px;\n    }\n}\n.amount-detail{\n    padding: 16px;\n    \n    i{\n        --Iconsax-Size: 24px;\n        --Iconsax-Color: #2263eb;\n        border-right: 1px solid $gray-color;\n        padding-right: 10px;\n    }\n    ul{\n        display: flex;\n        flex-direction: column;\n        gap: 10px;\n        li{\n            @include flex_common_1 ($dis: flex, $align: center);\n            gap: 12px;\n            font-size: 16px;\n            font-weight: 500;\n            color: $title-color;\n        }\n    }\n}\n\n.service-img{\n    .favourite{\n        top: calc(10px + (30 - 10) * ((100vw - 320px) / (1920 - 320)));;\n        right: calc(10px + (30 - 10) * ((100vw - 320px) / (1920 - 320)));;\n        left: unset;\n    }\n}\n\n.favourite{\n    width: max-content;\n    border-radius: 100%;\n    background-color: $danger-color;\n    padding: calc(5px + (8 - 5) * ((100vw - 320px) / (1920 - 320)));\n    position: absolute;\n    top: 20px;\n    left: 20px;\n    cursor: pointer;\n\n    .nolike{\n        --Iconsax-Size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n        --Iconsax-Color: white;\n        display: block;\n        &.hide{\n            display: none;\n        }\n    }\n    .like{\n        height: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320))) !important;\n        width: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320))) !important;\n        display: none;\n        &.show{\n            display: block;\n        }\n    }\n}\n\n\n.servicemen-lists{\n    background-color: rgba($primary-color, 0.10);\n    width: 100%;\n    padding: 16px;\n    border: 1px dashed $primary-color;\n    color: $primary-color;\n    border-radius: 10px;\n    font-size: 16px;\n    font-weight: 500;\n}"]}