<?php

use Illuminate\Support\Facades\Route;
use Modules\Subscription\Http\Controllers\API\SubscriptionController;
use Modules\Subscription\Http\Controllers\API\AdminSubscriptionController;
use Modules\Subscription\Http\Controllers\API\BusinessSubscriptionController;
use Modules\Subscription\Http\Controllers\API\StripeController;

/*
    |--------------------------------------------------------------------------
    | API Routes
    |--------------------------------------------------------------------------
    |
    | Here is where you can register API routes for your application. These
    | routes are loaded by the RouteServiceProvider within a group which
    | is assigned the "api" middleware group. Enjoy building your API!
    |
*/

Route::get('/subscription/plans', [SubscriptionController::class, 'getPlans']);

Route::middleware(['auth:api'])->group(function () {
    Route::resource('subscription', SubscriptionController::class)->names('subscription');
    Route::post('/subscription/create', [SubscriptionController::class, 'purchase']);
    
    // Stripe subscription endpoints
    Route::prefix('stripe')->group(function () {
        Route::post('/create-checkout-session', [StripeController::class, 'createCheckoutSession']);
        Route::get('/verify-session/{sessionId}', [StripeController::class, 'verifySession']);
    });
    
    // Admin subscription management endpoints
    Route::middleware(['can:manage-subscriptions'])->prefix('admin')->group(function () {
        Route::post('/provider/update-tier', [AdminSubscriptionController::class, 'updateProviderTier']);
        Route::post('/provider/link-business', [AdminSubscriptionController::class, 'linkBusinessToProvider']);
        Route::post('/provider/unlink-business', [AdminSubscriptionController::class, 'unlinkBusinessFromProvider']);
        Route::get('/provider/business/{provider_id}', [AdminSubscriptionController::class, 'getProviderBusiness']);
        
        // Business subscription management endpoints
        Route::post('/business/assign-plan', [BusinessSubscriptionController::class, 'assignPlanToBusiness']);
        Route::post('/business/cancel-plan', [BusinessSubscriptionController::class, 'cancelBusinessPlan']);
        Route::get('/business/subscriptions', [BusinessSubscriptionController::class, 'listBusinessSubscriptions']);
        Route::get('/business/{businessUuid}/subscription', [BusinessSubscriptionController::class, 'getBusinessSubscription']);
    });
});

Route::group(['prefix' => 'stripe'], function () {
    Route::any('webhook', [SubscriptionController::class, 'handleWebhook']);
});