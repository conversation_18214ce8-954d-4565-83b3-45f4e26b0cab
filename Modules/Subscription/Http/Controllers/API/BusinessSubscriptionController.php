<?php

namespace Modules\Subscription\Http\Controllers\API;

use App\Exceptions\ExceptionHandler;
use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessSubscriptionResource;
use App\Models\Business;
use App\Models\BusinessSubscription;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Subscription\Entities\Plan;
use Modules\Subscription\Http\Requests\API\AssignBusinessPlanRequest;
use Modules\Subscription\Http\Requests\API\CancelBusinessPlanRequest;

class BusinessSubscriptionController extends Controller
{
    /**
     * Assign a subscription plan to a business
     *
     * @param AssignBusinessPlanRequest $request
     * @return JsonResponse
     */
    public function assignPlanToBusiness(AssignBusinessPlanRequest $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $business = Business::where('business_uuid', $request->business_uuid)->firstOrFail();
            $plan = Plan::findOrFail($request->plan_id);
            
            // Deactivate any existing subscription
            $existingSubscription = BusinessSubscription::where('business_uuid', $business->business_uuid)
                ->where('is_active', true)
                ->first();

            if ($existingSubscription) {
                $existingSubscription->update(['is_active' => false]);
            }

            // Create new subscription
            $subscription = new BusinessSubscription([
                'business_uuid' => $business->business_uuid,
                'plan_id' => $plan->id,
                'provider_id' => auth()->id(),
                'start_date' => now()->toDateString(),
                'total' => $plan->price,
                'allowed_max_services' => $plan->max_services,
                'allowed_max_addresses' => $plan->max_addresses,
                'allowed_max_servicemen' => $plan->max_servicemen,
                'allowed_max_service_packages' => $plan->max_service_packages,
                'is_active' => true,
            ]);

            // Calculate end date based on duration
            $subscription->end_date = $subscription->calculateEndDate($plan->duration);
            $subscription->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Business plan assigned successfully',
                'subscription' => new BusinessSubscriptionResource($subscription->load(['plan', 'business', 'provider'])),
            ]);
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Cancel a business subscription plan
     *
     * @param CancelBusinessPlanRequest $request
     * @return JsonResponse
     */
    public function cancelBusinessPlan(CancelBusinessPlanRequest $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $subscription = BusinessSubscription::where('business_uuid', $request->business_uuid)
                ->where('is_active', true)
                ->firstOrFail();

            $subscription->update(['is_active' => false]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Business plan cancelled successfully',
            ]);
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Get active subscription for a business
     *
     * @param string $businessUuid
     * @return JsonResponse
     */
    public function getBusinessSubscription(string $businessUuid): JsonResponse
    {
        try {
            $subscription = BusinessSubscription::with('plan')
                ->where('business_uuid', $businessUuid)
                ->where('is_active', true)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active subscription found for this business',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'subscription' => new BusinessSubscriptionResource($subscription->load(['plan', 'business', 'provider'])),
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * List all business subscriptions
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listBusinessSubscriptions(Request $request): JsonResponse
    {
        try {
            $perPage = $request->per_page ?? 10;
            $subscriptions = BusinessSubscription::with(['plan', 'business', 'provider'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'subscriptions' => BusinessSubscriptionResource::collection($subscriptions),
                'pagination' => [
                    'current_page' => $subscriptions->currentPage(),
                    'per_page' => $subscriptions->perPage(),
                    'total' => $subscriptions->total(),
                    'last_page' => $subscriptions->lastPage()
                ]
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }
}