<?php

namespace Modules\Subscription\Http\Controllers\API;

use App\Exceptions\ExceptionHandler;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Repositories\API\BusinessRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Modules\Subscription\Entities\Plan;
use Modules\Subscription\Entities\UserSubscription;
use Modules\Subscription\Http\Requests\API\LinkBusinessRequest;
use Modules\Subscription\Http\Requests\API\UnlinkBusinessRequest;
use Modules\Subscription\Http\Requests\API\UpdateProviderTierRequest;
use Modules\Subscription\Repositories\API\SubscriptionRepository;

class AdminSubscriptionController extends Controller
{
    protected $subscriptionRepository;
    protected $businessRepository;

    public function __construct(SubscriptionRepository $subscriptionRepository, BusinessRepository $businessRepository)
    {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->businessRepository = $businessRepository;
    }

    /**
     * Update a provider's subscription tier plan
     *
     * @param UpdateProviderTierRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProviderTier(UpdateProviderTierRequest $request)
    {

        // TODO: Add authorization check
        // $this->authorize('manage-provider-tiers');

        DB::beginTransaction();
        try {
            $provider = User::findOrFail($request->provider_id);
            $plan = Plan::findOrFail($request->plan_id);
            
            // Deactivate any existing subscription
            $existingSubscription = UserSubscription::where('user_id', $provider->id)
                ->where('is_active', true)
                ->first();

            if ($existingSubscription) {
                $existingSubscription->update(['is_active' => false]);
            }

            // Create new subscription
            $duration = $request->duration ?? $plan->duration;
            $subscription = new UserSubscription();
            $subscription->user_id = $provider->id;
            $subscription->user_plan_id = $plan->id;
            $subscription->start_date = now();
            $subscription->end_date = $subscription->calculateEndDate($duration);
            $subscription->total = $plan->price;
            $subscription->allowed_max_services = $plan->max_services;
            $subscription->allowed_max_addresses = $plan->max_addresses;
            $subscription->allowed_max_servicemen = $plan->max_servicemen;
            $subscription->allowed_max_service_packages = $plan->max_service_packages;
            $subscription->is_active = true;
            $subscription->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Provider tier plan updated successfully',
                'subscription' => $subscription,
            ]);
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Link a business to a provider
     *
     * @param LinkBusinessRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function linkBusinessToProvider(LinkBusinessRequest $request)
    {

        // TODO: Add authorization check
        // $this->authorize('manage-provider-businesses');

        DB::beginTransaction();
        try {
            $provider = User::findOrFail($request->provider_id);
            $business = $this->businessRepository->getByUuid($request->business_uuid);

            if (!$business) {
                throw new Exception('Business not found', 404);
            }

            // Check if provider has an active subscription
            $hasActiveSubscription = UserSubscription::where('user_id', $provider->id)
                ->where('is_active', true)
                ->exists();

            if (!$hasActiveSubscription) {
                throw new Exception('Provider does not have an active subscription plan', 400);
            }

            // Link business to provider using the business_uuid field
            $provider->update([
                'business_uuid' => $business->business_uuid
            ]);

            // Add notes if provided
            if ($request->has('notes')) {
                // You could store notes in a separate table if needed
                // For now, we'll assume there's no specific place to store notes
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Business linked to provider successfully',
            ]);
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Unlink a business from a provider
     *
     * @param UnlinkBusinessRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unlinkBusinessFromProvider(UnlinkBusinessRequest $request)
    {
        // TODO: Add authorization check
        // $this->authorize('manage-provider-businesses');

        DB::beginTransaction();
        try {
            $provider = User::findOrFail($request->provider_id);
            $business = $this->businessRepository->getByUuid($request->business_uuid);

            if (!$business) {
                throw new Exception('Business not found', 404);
            }

            // Verify that the provider is linked to this specific business
            if ($provider->business_uuid !== $business->business_uuid) {
                throw new Exception('This provider is not linked to the specified business', 400);
            }

            // Unlink business from provider by setting business_uuid to null
            $provider->update([
                'business_uuid' => null
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Business unlinked from provider successfully',
            ]);
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Get a provider's linked business
     *
     * @param int $providerId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProviderBusiness($providerId)
    {
        // TODO: Add authorization check
        // $this->authorize('manage-provider-businesses');

        try {
            $provider = User::findOrFail($providerId);
            
            if (!$provider->business_uuid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Provider is not linked to any business',
                ], 404);
            }

            $business = $this->businessRepository->getByUuid($provider->business_uuid);
            
            if (!$business) {
                return response()->json([
                    'success' => false,
                    'message' => 'Business not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new \App\Http\Resources\BusinessResource($business),
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }
}