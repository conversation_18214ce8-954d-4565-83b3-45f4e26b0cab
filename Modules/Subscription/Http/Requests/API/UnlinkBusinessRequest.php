<?php

namespace Modules\Subscription\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class UnlinkBusinessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Check if user has permission to manage provider businesses
        return $this->user()->can('manage-subscriptions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'provider_id' => [
                'required',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    if ($this->has('business_uuid')) {
                        $provider = \App\Models\User::find($value);
                        $business = \App\Models\Business::where('business_uuid', $this->business_uuid)->first();
                        
                        if (!$provider || !$business) {
                            return; // Other validation rules will catch this
                        }
                        
                        if ($provider->business_uuid !== $business->business_uuid) {
                            $fail('This provider is not linked to the specified business.');
                        }
                    }
                }
            ],
            'business_uuid' => 'required|exists:businesses,business_uuid',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'provider_id.required' => 'The provider ID is required.',
            'provider_id.exists' => 'The selected provider does not exist.',
            'business_uuid.required' => 'The business UUID is required.',
            'business_uuid.exists' => 'The selected business does not exist.',
        ];
    }
}