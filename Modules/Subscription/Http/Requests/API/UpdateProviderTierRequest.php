<?php

namespace Modules\Subscription\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProviderTierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Check if user has permission to manage subscriptions
        return $this->user()->can('manage-subscriptions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'provider_id' => 'required|exists:users,id',
            'plan_id' => 'required|exists:plans,id',
            'duration' => 'nullable|in:monthly,yearly',
            'notes' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'provider_id.required' => 'The provider ID is required.',
            'provider_id.exists' => 'The selected provider does not exist.',
            'plan_id.required' => 'The plan ID is required.',
            'plan_id.exists' => 'The selected plan does not exist.',
            'duration.in' => 'The duration must be either monthly or yearly.',
        ];
    }
}