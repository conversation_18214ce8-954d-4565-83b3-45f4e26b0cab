<?php

namespace Modules\Subscription\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class AssignBusinessPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Check if user has permission to manage subscriptions
        return $this->user()->can('manage-subscriptions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'business_uuid' => 'required|string|exists:businesses,business_uuid',
            'plan_id' => 'required|integer|exists:plans,id',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'business_uuid.required' => 'Business UUID is required',
            'business_uuid.exists' => 'Business not found',
            'plan_id.required' => 'Plan ID is required',
            'plan_id.exists' => 'Plan not found',
        ];
    }
}