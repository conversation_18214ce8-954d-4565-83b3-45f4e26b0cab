<?php

namespace Modules\Subscription\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class LinkBusinessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Check if user has permission to manage provider businesses
        return $this->user()->can('manage-subscriptions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'provider_id' => [
                'required',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    $provider = \App\Models\User::find($value);
                    if ($provider && $provider->business_uuid) {
                        $fail('This provider is already linked to a business.');
                    }
                }
            ],
            'business_uuid' => [
                'required',
                'exists:businesses,business_uuid',
                function ($attribute, $value, $fail) {
                    $business = \App\Models\Business::where('business_uuid', $value)->first();
                    $linkedProvider = \App\Models\User::where('business_uuid', $value)->first();
                    if ($linkedProvider && $linkedProvider->id != $this->provider_id) {
                        $fail('This business is already linked to another provider.');
                    }
                }
            ],
            'notes' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'provider_id.required' => 'The provider ID is required.',
            'provider_id.exists' => 'The selected provider does not exist.',
            'business_uuid.required' => 'The business UUID is required.',
            'business_uuid.exists' => 'The selected business does not exist.',
        ];
    }
}