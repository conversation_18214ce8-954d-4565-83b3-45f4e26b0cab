<?php

namespace Modules\EmailTracking\Services;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Config;
use Illuminate\Notifications\Notification;

class EmailTrackingService
{
    /**
     * Generate a unique tracking ID for an email.
     *
     * @return string
     */
    public function generateTrackingId(): string
    {
        return (string) Str::uuid();
    }

    /**
     * Generate a tracking pixel URL for email open tracking.
     *
     * @param string $trackingId
     * @param string $notificationId
     * @return string
     */
    public function generateTrackingPixelUrl(string $trackingId, string $notificationId): string
    {
        return URL::signedRoute('email-tracking.track-open-event', [
            'tracking_id' => $trackingId,
            'notification_id' => $notificationId,
        ]);
    }

    /**
     * Generate a trackable URL for email link click tracking.
     *
     * @param string $originalUrl
     * @param string $trackingId
     * @param string $notificationId
     * @return string
     */
    public function generateTrackableUrl(string $originalUrl, string $trackingId, string $notificationId): string
    {
        return URL::signedRoute('email-tracking.track-click', [
            'tracking_id' => $trackingId,
            'notification_id' => $notificationId,
            'url' => $originalUrl,
        ]);
    }

    /**
     * Add tracking pixel to HTML content.
     *
     * @param string $content
     * @param string $trackingPixelUrl
     * @return string
     */
    public function addTrackingPixel(string $content, string $trackingPixelUrl): string
    {
        // Add tracking pixel at the end of the email body
        $trackingPixel = '<img src="' . $trackingPixelUrl . '" alt="" width="1" height="1" style="display:none;">';
        
        // Check if there's a closing body tag and insert before it
        if (strpos($content, '</body>') !== false) {
            return str_replace('</body>', $trackingPixel . '</body>', $content);
        }
        
        // Otherwise append to the end
        return $content . $trackingPixel;
    }

    /**
     * Replace URLs in content with trackable URLs.
     *
     * @param string $content
     * @param string $trackingId
     * @param string $notificationId
     * @return string
     */
    public function makeLinksTrackable(string $content, string $trackingId, string $notificationId): string
    {
        // Regular expression to find links in HTML
        $pattern = '/<a\s+(?:[^>]*?\s+)?href=(["\'])(?<href>.*?)\1[^>]*>/i';
        
        return preg_replace_callback($pattern, function ($matches) use ($trackingId, $notificationId) {
            $originalUrl = $matches['href'];
            
            // Skip tracking for anchor links, javascript, and mailto links
            if (strpos($originalUrl, '#') === 0 || 
                strpos($originalUrl, 'javascript:') === 0 ||
                strpos($originalUrl, 'mailto:') === 0) {
                return $matches[0];
            }
            
            // Generate trackable URL
            $trackableUrl = $this->generateTrackableUrl($originalUrl, $trackingId, $notificationId);
            
            // Replace the original URL with the trackable one
            return str_replace($originalUrl, $trackableUrl, $matches[0]);
        }, $content);
    }
}