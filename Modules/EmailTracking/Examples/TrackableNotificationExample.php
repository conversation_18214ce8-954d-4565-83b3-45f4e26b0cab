<?php

namespace Modules\EmailTracking\Examples;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Modules\EmailTracking\Services\TrackableEmail;

/**
 * Example notification class demonstrating how to use the TrackableEmail trait.
 * 
 * This notification automatically excludes tracking for users with the 'Supreme Admin' role.
 */
class TrackableNotificationExample extends Notification
{
    use Queueable, SerializesModels, TrackableEmail;

    protected $data;

    /**
     * Create a new notification instance.
     *
     * @param array $data
     * @return void
     */
    public function __construct(array $data = [])
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mail = (new MailMessage)
            ->subject('Example Trackable Notification')
            ->greeting('Hello ' . ($notifiable->name ?? ''))
            ->line('This is an example of a trackable notification.')
            ->line('The email will be tracked unless the recipient has the \'Supreme Admin\' role.')
            ->action('Visit JobON', url('/'))
            ->line('Thank you for using JobON!');
            
        // Add tracking to the email
        // Note: This will automatically skip tracking for users with the 'Supreme Admin' role
        return $this->makeTrackable($mail, $notifiable);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return $this->data;
    }
}