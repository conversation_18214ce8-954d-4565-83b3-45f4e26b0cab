<?php

namespace Modules\EmailTracking\Http\Controllers\API;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Log;
use Modules\EmailTracking\Http\Requests\API\StatisticsRequest;
use Modules\EmailTracking\Http\Requests\API\AnalyticsRequest;
use Modules\EmailTracking\Repositories\EmailTrackingRepository;

class EmailTrackingController extends Controller
{
    protected $repository;

    public function __construct(EmailTrackingRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Track email open event.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function trackOpen(Request $request)
    {
        try {
            // Validate the signed URL
            if (!$request->hasValidSignature()) {
                return $this->invalidSignatureResponse();
            }

            // Get tracking data from the request
            $trackingId = $request->input('tracking_id');
            $notificationId = $request->input('notification_id');

            // Record the open event
            $this->repository->recordOpen([
                'tracking_id' => $trackingId,
                'notification_id' => $notificationId,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Return a transparent 1x1 pixel GIF
            return $this->transparentPixelResponse();
        } catch (\Exception $e) {
            Log::error('Email tracking open error: ' . $e->getMessage());
            return $this->transparentPixelResponse();
        }
    }

    /**
     * Track email link click event.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function trackClick(Request $request)
    {
        try {
            // Validate the signed URL
            if (!$request->hasValidSignature()) {
                return $this->invalidSignatureResponse();
            }

            // Get tracking data from the request
            $trackingId = $request->input('tracking_id');
            $notificationId = $request->input('notification_id');
            $originalUrl = $request->input('url');

            // Record the click event
            $this->repository->recordClick([
                'tracking_id' => $trackingId,
                'notification_id' => $notificationId,
                'original_url' => $originalUrl,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Redirect to the original URL
            return redirect()->away($originalUrl);
        } catch (\Exception $e) {
            Log::error('Email tracking click error: ' . $e->getMessage());
            return redirect()->away($request->input('url', '/'));
        }
    }

    /**
     * Get email tracking statistics.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStatistics(Request $request)
    {
        try {
            $filters = $request->only([
                'notification_type',
                'notifiable_type',
                'notifiable_id',
                'start_date',
                'end_date',
                'per_page',
            ]);

            $statistics = $this->repository->getStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            Log::error('Email tracking statistics error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve email tracking statistics.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * Get daily email metrics for performance visualization.
     *
     * @param \Modules\EmailTracking\Http\Requests\API\AnalyticsRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDailyMetrics(AnalyticsRequest $request)
    {
        try {
            $filters = $request->only([
                'notification_type',
                'notifiable_type',
                'notifiable_id',
                'start_date',
                'end_date',
            ]);

            $metrics = $this->repository->getDailyMetrics($filters);

            return response()->json([
                'success' => true,
                'data' => $metrics,
            ]);
        } catch (\Exception $e) {
            Log::error('Email daily metrics error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve email daily metrics.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * Get email distribution data for pie/donut charts.
     *
     * @param \Modules\EmailTracking\Http\Requests\API\AnalyticsRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEmailDistribution(AnalyticsRequest $request)
    {
        try {
            $filters = $request->only([
                'notification_type',
                'notifiable_type',
                'notifiable_id',
                'start_date',
                'end_date',
            ]);

            $distribution = $this->repository->getEmailDistribution($filters);

            return response()->json([
                'success' => true,
                'data' => $distribution,
            ]);
        } catch (\Exception $e) {
            Log::error('Email distribution error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve email distribution data.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Return a transparent 1x1 pixel GIF image.
     *
     * @return \Illuminate\Http\Response
     */
    protected function transparentPixelResponse()
    {
        // Transparent 1x1 pixel GIF
        $transparentPixel = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');

        return response($transparentPixel, 200)
            ->header('Content-Type', 'image/gif')
            ->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
            ->header('Pragma', 'no-cache')
            ->header('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
    }

    /**
     * Return an invalid signature response.
     *
     * @return \Illuminate\Http\Response
     */
    protected function invalidSignatureResponse()
    {
        return response('Invalid signature', Response::HTTP_FORBIDDEN);
    }
}