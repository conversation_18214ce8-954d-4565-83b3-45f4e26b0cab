<?php

namespace Modules\EmailTracking\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class EmailTrackingStatisticsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth('api')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => 'nullable|in:opens,clicks,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'notification_type' => 'nullable|string',
            'email' => 'nullable|email',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'type.in' => 'The type must be one of: opens, clicks, all',
            'end_date.after_or_equal' => 'The end date must be after or equal to the start date',
            'per_page.max' => 'The maximum number of records per page is 100',
        ];
    }
}