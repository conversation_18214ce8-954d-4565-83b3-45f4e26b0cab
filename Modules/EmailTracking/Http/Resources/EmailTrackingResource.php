<?php

namespace Modules\EmailTracking\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmailTrackingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $data = [
            'id' => $this->id,
            'tracking_id' => $this->tracking_id,
            'notification_id' => $this->notification_id,
            'notifiable_type' => $this->notifiable_type,
            'notifiable_id' => $this->notifiable_id,
            'email' => $this->email,
            'ip_address' => $this->ip_address,
            'user_agent' => $this->user_agent,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
        
        // Add clicked_at for click tracking records
        if ($this->resource->getTable() === 'email_tracking_clicks') {
            $data['clicked_at'] = $this->clicked_at ? $this->clicked_at->format('Y-m-d H:i:s') : null;
            $data['original_url'] = $this->original_url;
        }
        
        // Add opened_at for open tracking records
        if ($this->resource->getTable() === 'email_tracking_opens') {
            $data['opened_at'] = $this->created_at->format('Y-m-d H:i:s');
        }
        
        // Include notification data if available
        if ($this->notification) {
            $data['notification'] = [
                'id' => $this->notification->id,
                'type' => $this->notification->type,
                'data' => $this->notification->data,
                'created_at' => $this->notification->created_at->format('Y-m-d H:i:s'),
            ];
        }
        
        // Include notifiable data if available
        if ($this->notifiable) {
            $data['notifiable'] = [
                'id' => $this->notifiable->id,
                'type' => $this->notifiable_type,
            ];
            
            // Add name and email for User models
            if (method_exists($this->notifiable, 'getEmailForVerification')) {
                $data['notifiable']['email'] = $this->notifiable->email;
                $data['notifiable']['name'] = $this->notifiable->name ?? null;
            }
        }
        
        return $data;
    }
}