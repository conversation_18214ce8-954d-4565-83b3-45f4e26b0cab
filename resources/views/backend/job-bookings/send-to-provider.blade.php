@extends('backend.layouts.master')

@section('title', 'Send to Providers')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Send Project to Providers</h5>
                    <a href="{{ route('backend.job-bookings.index', $job->job_uuid) }}" class="btn btn-secondary">
                        <i data-feather="arrow-left"></i> Back
                    </a>
                </div>
                <div class="card-body">
                    <!-- Project Details -->
                    <div class="project-details bg-light p-4 rounded mb-4">
                        <h6 class="mb-3">Project Details:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>ID:</strong> #{{ \App\Helpers\ProjectCodeGenerator::fromUuid($job->job_uuid) }}</p>
                                <p><strong>Service:</strong> {{ ucfirst($job->service_category) }}</p>
                                <p><strong>Description:</strong> {{ $job->description ?? '-' }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Location:</strong> {{ implode(', ', array_filter([$job->address, $job->city, $job->state, $job->zip_code])) }}</p>
                                <p><strong>Schedule Date:</strong> {{ $job->schedule_date->format('Y-m-d') }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Form to select and send providers -->
                    <form method="POST" action="{{ route('backend.job-bookings.sendProviders') }}">
                        @csrf
                        <input type="hidden" name="job_uuid" value="{{ $job->job_uuid }}">

                        @if ($errors->any())
                            <div class="alert alert-danger mb-4">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <div class="mb-4">
                            <h6 class="mb-3">Available Providers:</h6>
                            <div class="providers-section bg-white p-4 rounded border">
                                @forelse ($businesses as $business)
                                <div class="provider-item mb-3 p-3 border rounded @if($loop->even) bg-light @endif">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               class="form-check-input" 
                                               name="provider_uuids[]" 
                                               value="{{ $business->business_uuid }}" 
                                               id="provider_{{ $business->business_uuid }}"
                                               @if(is_array(old('provider_uuids')) && in_array($business->business_uuid, old('provider_uuids'))) checked @endif>
                                        <label class="form-check-label w-100" for="provider_{{ $business->uuid }}">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong class="text-dark">{{ $business->name }}</strong>
                                                    <span class="badge bg-secondary-subtle text-dark ms-2">{{ $business->category }}</span>
                                                </div>
                                                <div class="text-muted">
                                                    {{ number_format($business->distance, 1) }} miles
                                                </div>
                                            </div>
                                            <div class="mt-2 d-flex flex-wrap gap-4 ps-4 text-muted">
                                                <div class="d-flex align-items-center">
                                                    <i data-feather="phone" class="me-1"></i>
                                                    <span>{{ $business->phone }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <i data-feather="mail" class="me-1"></i>
                                                    <span>{{ $business->email }}</span>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                @empty
                                    <div class="alert alert-warning mb-0">
                                        No providers found within 50 miles of the job location.
                                    </div>
                                @endforelse
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('backend.job-bookings.show', $job->job_uuid) }}" class="btn btn-outline-secondary">
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" @if($businesses->isEmpty()) disabled @endif>
                                <i data-feather="send" class="me-1"></i>
                                Send to Providers
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection