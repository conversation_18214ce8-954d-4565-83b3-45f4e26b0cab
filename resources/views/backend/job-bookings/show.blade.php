@extends('backend.layouts.master')

@section('title', 'Job Booking Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Job Details 
                        <span class="text-primary" data-bs-toggle="tooltip" title="ID: {{ $job->job_uuid }}">
                            #{{ \App\Helpers\ProjectCodeGenerator::fromUuid($job->job_uuid) }}
                        </span>
                    </h5>
                    <div>
                        <a href="{{ route('backend.job-bookings.index') }}" class="btn btn-secondary">
                            <i data-feather="arrow-left"></i> Back
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Job Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Basic Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150">Job Type</th>
                                    <td>{{ ucfirst(str_replace('_', ' ', $job->job_type)) }}</td>
                                </tr>
                                <tr>
                                    <th>Property Type</th>
                                    <td>{{ ucfirst($job->property_type) }}</td>
                                </tr>
                                <tr>
                                    <th>Service Category</th>
                                    <td>{{ $job->service_category }}</td>
                                </tr>
                                <tr>
                                    <th>Service Tasks</th>
                                    <td>
                                        @if(!empty($job->service_tasks))
                                            <ul class="list-unstyled mb-0">
                                                @foreach($job->service_tasks as $task)
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>{{ $task }}</li>
                                                @endforeach
                                            </ul>
                                        @else
                                            -
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Description</th>
                                    <td>{{ $job->description ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge bg-{{ $job->status == 'pending' ? 'warning' : 
                                            ($job->status == 'completed' ? 'success' : 
                                            ($job->status == 'cancelled' ? 'danger' : 'info')) }}">
                                            {{ ucfirst($job->status) }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h6>Schedule Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150">Date</th>
                                    <td>{{ $job->schedule_date->format('d M Y') }}</td>
                                </tr>
                                <tr>
                                    <th>Time Preference</th>
                                    <td>{{ ucfirst($job->time_preference) }}</td>
                                </tr>
                                <tr>
                                    <th>Frequency</th>
                                    <td>{{ ucfirst($job->frequency) }}</td>
                                </tr>
                                @if($job->recurring_frequency)
                                <tr>
                                    <th>Recurring</th>
                                    <td>{{ $job->recurring_frequency }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    <!-- Contact & Location -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Contact Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150">Name</th>
                                    <td>{{ $job->contact_name }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>{{ $job->contact_email }}</td>
                                </tr>
                                <tr>
                                    <th>Phone</th>
                                    <td>{{ $job->contact_phone }}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h6>Location</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150">Address</th>
                                    <td>{{ $job->address }}</td>
                                </tr>
                                <tr>
                                    <th>City</th>
                                    <td>{{ $job->city }}</td>
                                </tr>
                                <tr>
                                    <th>State</th>
                                    <td>{{ $job->state }}</td>
                                </tr>
                                <tr>
                                    <th>Zip Code</th>
                                    <td>{{ $job->zip_code }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Attached Assets -->
                    @if($job->assets->count() > 0)
                    <div class="row">
                        <div class="col-12">
                            <h6>Attached Files</h6>
                            <div class="row g-3">
                                @foreach($job->assets as $asset)
                                <div class="col-md-3">
                                    <div class="card">
                                        @if(Str::startsWith($asset->mime_type, 'image/'))
                                            <img src="{{ Storage::disk($asset->disk)->url($asset->file_path) }}" 
                                                class="card-img-top" alt="{{ $asset->file_name }}">
                                        @else
                                            <div class="card-body text-center">
                                                <i data-feather="file" class="mb-2" style="width: 32px; height: 32px;"></i>
                                            </div>
                                        @endif
                                        <div class="card-footer">
                                            <small class="text-muted">
                                                {{ $asset->file_name }}
                                                ({{ number_format($asset->file_size / 1024, 2) }} KB)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection