<?php

return [
    'booking_service_packages' => 'Service Packages',
    'confirm' => 'Confirm',
    'submit' => 'Submit',
    'provider_details' => 'Provider Details',
    'welcome_note' => 'Welcome to',
    'sign_in_note' => 'Please sign in with your personal account information',
    'home_services' => 'Home Services',
    'city' => 'City',
    'version' => 'Version',
    'total_bookings' => 'Total Bookings',
    'title' => 'Title',
    'state' => 'State',
    'publish' => 'Publish',
    'customer_info' => 'Customer Info',
    'summary' => 'Summary',
    'postal_code' => 'Postal Code',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'consumer_details' => 'Consumer Details',
    'area' => 'Area/Locality',
    'enter_email' => 'Enter Email',
    'search_here' => 'Search Here...',
    'no_data_found' => 'No Data Found',
    'document_no' => 'Document No.',
    'no_records_found' => 'No Records Found',
    'admin_panel' => 'Admin panel',
    'copyright' => ' © Fixit theme by pixelstrapp',
    'administration' => 'Administration',
    'login' => 'Login',
    'test_mail' => 'Test Mail',
    'instruction' => 'Instruction',
    'is_verified' => 'Is Verified',
    'user_info' => 'User Info',
    'total_servicemen' => 'Total Serviceman',
    'login' => [
        'password' => 'Enter password',
        'forgot_password' => 'Forgot password ?',
        'dont_account' => "Don't have an account ?",
        'reset_password' => 'Reset password',
        'back_to_login' => 'Back to login',
        'send_link' => 'Send password reset link',
    ],
    'total' => 'Total',
    'no_status_log_found' => 'No status log found',
    'servicemen_information' => 'Servicemen Information',
    'profile' => 'Profile',
    'country' => 'Country',
    'sub_total' => 'Sub Total',
    'tax_total' => 'Tax Total',
    'edit_profile' => 'Edit Profile',
    'logout' => 'Logout',
    'change_password' => 'Change Password',
    'confirm_password' => 'Confirm Password',
    'new_password' => 'New Password',
    'password' => 'Password',
    'phone' => 'Phone',
    'no' => 'No',
    'assign_service' => 'Assign Service',
    'name' => 'Name',
    'fullname' => 'Full Name',
    'role' => 'Roles & Permissions',
    'email' => 'Email',
    'bookings' => 'Bookings',
    'price' => 'Price',
    'more' => 'More',
    'edit' => 'Edit',
    'e-mail_address' => 'Enter Email',
    'current_password' => 'Current Password',
    'image' => 'Image',
    'image_alignment' => 'Image Alignment',
    'created_at' => 'Created',
    'updated_at' => 'Updated At',
    'action' => 'Action',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'key' => 'Key',
    'value' => 'Value',
    'select_status' => 'Select Status',
    'delete_message' => 'Are you sure want to delete ?',
    'delete_note' => 'This Item Will Be Deleted Permanently. You Can not Undo This Action.',
    'confirmation' => 'Confirmation',
    'previous' => 'Previous',
    'next' => 'Next',
    'download' => 'Download',
    'slug' => 'Slug',
    'action' => 'Action',
    'created_at' => 'Created',
    'updated_at' => 'Updated At',
    'description' => 'Description',
    'location' => 'Location',
    'call_us' => 'Call Us',
    'email_us' => 'Email Us',
    'message' => 'Message',
    'content' => 'Content',
    'seo' => 'SEO',
    'status' => 'Status',
    'general' => 'General',
    'cash' => 'Cash',
    'analytics' => 'Analytics',
    'meta_title' => 'Meta Title',
    'hexa_code' => 'Choose Color',
    'meta_description' => 'Meta Description',
    'enter_description' => 'Enter Description',
    'enable' => 'Enable',
    'this_month' => 'This Month',
    'activity_logs' => 'Activity Logs',
    'rank' => 'Rank',
    'recent_comments' => 'Recent Comments',
    'required_name_email' => 'Comment author must fill out name and email',
    'comment_approved' => 'Comment must be manually approved',
    'popular_pages' => 'Popular Pages',
    'delete_selected' => 'Delete Selected',
    'contacts_us' => 'Contacts Us',
    'appearance' => 'Appearance',
    'comments' => 'Comments',
    'world_map_message' => 'Below Map is displaying the world map.',
    'blog_coments' => 'Total Comments',
    'average_duration' => 'Average Duration',
    'bounce_rate' => 'Bounce Rate',
    'new_visitors' => 'New Visitors',
    'visitors' => 'Visitors',
    'recent_activity' => 'Recent Activity',
    'order' => 'Order',
    'icon' => 'Icon ',
    'countries' => 'Countries',
    'device_chart' => 'Device Chart',
    'social_link' => 'Social Links',
    'blogs_not_found' => 'Blogs not found!',
    'send_message' => 'Send Message',
    'view_more' => 'View more',
    'category' => 'Category',
    'snippet_preview' => 'Snippet Preview',
    'play_store_link' => 'Play Store Link',
    'app_store_link' => 'App Store Link',
    'facebook_link' => 'Facebook Link',
    'google_link' => 'Google Link',
    'twitter_link' => 'Twitter Link',
    'instagram_link' => 'Instagram Link',
    'rss_link' => 'RSS Link',
    'sub_title' => 'Sub Title',
    'content_1' => 'Content 1',
    'content_2' => 'Content 2',
    'content_3' => 'Content 3',
    'content_4' => 'Content 4',
    'icon_1' => 'Icon 1',
    'icon_2' => 'Icon 2',
    'icon_3' => 'Icon 3',
    'icon_4' => 'Icon 4',
    'type' => 'Type',
    'feature_title' => 'Feature Title',
    'feature_description' => 'Feature Description',
    'site_name' => 'Site Name',
    'site_url' => 'Site URL',
    'administration_email_address' => 'Administration Email Address',
    'timezone' => 'Timezone',
    'mailer' => 'Mailer',
    'mail_host' => 'Mailer Host',
    'mail_port' => 'Mailer Port',
    'mail_username' => 'Mailer Username',
    'mail_password' => 'Mail Password',
    'mail_encryption' => 'Mail Encryption',
    'mail_from_address' => 'Mail From Address',
    'mail_from_name' => 'Mail From Name',
    'display_homepage' => 'Your homepage displays',
    'latest_posts' => 'Your latest posts',
    'home_page' => 'Home Page',
    'post_page' => 'Post Page',
    'blog_per_page' => 'Blog pages show at most',
    'page_base' => 'Page Base',
    'blog_post_base' => 'Blog Post Base',
    'category_base' => 'Category Base',
    'tag_base' => 'Tag Base',
    'google_analytics_id' => 'Google Analytics ID',
    'facebook_pixel_id' => 'Facebook Pixel ID',
    'property_id' => 'Property ID',
    'newsletter_api_key' => 'NEWSLETTER API KEY',
    'newsletter_list_id' => 'NEWSLETTER LIST ID',
    'footer_message' => 'Hand crafted & made with',
    'tagline' => 'Tagline',
    'pending' => 'Pending',
    'published' => 'Published',
    'draft' => 'Draft',
    'inactive' => 'Inactive',
    'active' => 'Active',
    'contact_us_recive_mail_from' => 'Your email address',
    'contact_us_no_data' => 'Contact Emails Not Found',
    'clear_notifications' => 'Clear Notifications',
    'kb' => 'KB',
    'contact_mails' => 'All Notifications',
    'home' => 'Home',
    'work' => 'Work',
    'other' => 'Other',
    'address_category' => 'Category',
    'accept' => 'Approved',
    'rejected' => 'Rejected',
    'something_went_wrong' => 'Something went wrong!',
    'deleted_selected' => 'Delete Selected',
    'not_allow_for_creation' => 'not allow for creation',
    'disabled_action' => 'Oops! This action disabled in demo mode.',
    'minimum_order_amount' => 'Please ensure your Booking is at least :minBookingAmount before proceed.',
    'insufficient_vendor_wallet_balance' => 'The provider wallet balance is not sufficient for this booking.',
    'invalid_product' => 'Some of the products you\'ve selected are either out of stock or inactive.',
    'invalid_currency_code' => ':defaultCurrencyCode currency code is not support for PayPal.',
    'address' => [
        'create' => 'Create Address',
        'enter_alternative_name' => 'Enter Alternative Name',
        'enter_alternative_phone' => 'Enter Alternative Phone',
        'alternative_phone' => 'Alternative Phone',
        'alternative_name' => 'Alternative Name',
        'create_successfully' => 'Address Created Successfully',
        'update_successfully' => 'Address Updated Successfully',
        'delete_successfully' => 'Address Deleted Successfully',
        'id_not_valid' => 'This address id is not valid',
        'status_update_successfully' => 'Status updated successfully',
        'is_primary' => 'Is Primary',
        'primary_address_updated_successfully' => 'Primary Address Updated Successfully.',
        'edit' => 'Edit Address',
        'add' => 'Add Address',
        'address_id_not_valid' => 'This address id is not valid for the given user.',
        'invalid_address_id' => 'Invalid address id',
        'type_not_found' => 'Type not found!',
    ],

    'notification' => [
        'all' => 'Notifications',
        'url' => 'URL',
        'enter_url' => 'https://www.example.com',
        'notifications' => 'Notifications',
        'send' => 'Send Notifications',
        'notification_send_to' => 'Send To',
        'select_notification_send_to' => 'Select Notification Send To',
        'push_notification' => 'Push Notifications',
        'send_notifications' => 'Send Notifications',
        'list_notifications' => 'List Notifications',
        'notification_type' => 'Notification Type',
        'select_notification_type' => 'Select Notification Type',
        'image' => 'Image',
        'enter_title' => 'Enter Title',
        'title' => 'Title',
        'description' => 'Description',
        'enter_discription' => 'Enter Discription',
        'send_to' => 'Send Notification',
        'select_send_to' => 'All/Selected Service',
        'zone' => 'Zone',
        'select_zone' => 'Select Zone',
        'services' => 'Services',
        'select_service' => 'Select Services',
        'store' => 'Notifications Stored successfully',
        'destroy' => 'Notifications deleted successfully',
        'notification_not_found' => 'Notifications not found',
        'notification_sent' => 'Notification Sent Successfully',
        'single_destroy' => 'Notification Deleted Successfully',
    ],

    'review' => [
        'all' => 'Reviews',
        'service_reviews' => 'Service Reviews',
        'serviceman_reviews' => 'Serviceman reviews',
        'review' => 'Reviews',
        'store' => 'Your Review was successfully Submitted',
        'already_stored' => 'A review for this serviceman has already been submitted.',
        'review_already_stored' => 'A review for this service has already been submitted.',
        'unpossible_review' => 'Review possible for completed payment and booking.',
        'add_service_before_review' => 'Please book the service before adding a review.',
        'destroy' => 'Your Review was successfully Deleted',
        'multiple_destroy' => 'Reviews Deleted Successfully',
        'updated' => 'Review Updated Successfully',
        'deleted' => 'Review Deleted Successfully',
    ],

    'dashboard' => [
        'dashboard' => 'Dashboard',
        'home' => 'Home',
        'total_revenue' => 'Total Revenue',
        'total_providers' => 'Providers',
        'total_services' => 'Services',
        'total_bookings' => 'Bookings',
        'total_customers' => 'Customers',
        'total_servicemen' => 'Servicemen',
        'wallet_balance' => 'Balance',
        'top_providers' => 'Top Providers',
        'top_servicemen' => 'Top Servicemen',
        'top_services' => 'Top Services',
        'view_all' => 'View All',
        'average_revenue' => 'Average Revenue',
        'recent_booking' => 'Recent Booking',
        'latest_reviews' => 'Latest Reviews',
        'latest_blog' => 'Latest Blogs',
        'user_management' => 'User Management',
        'service_management' => 'Service Management',
        'booking_management' => 'Booking Management',
        'marketing_advertising' => 'Marketing & Advertising',
        'financial_management' => 'Financial Management',
        'content_management' => 'Content Management',
        'settings_management' => 'Settings Management',
    ],

    'commission_histories' => [
        'commission_history' => 'Commission History',
        'booking_no' => 'Booking No.',
        'provider_name' => 'Provider Name',
        'admin_commission' => 'Admin Commission',
        'provider_commission' => 'Provider Commission',
    ],

    'withdraw' => [
        'title' => 'Withdrawal',
        'amount' => 'Amount',
        'serviceman_withdraw_request' => 'Serviceman Withdraw Request',
        'serviceman_withdraw_requests' => 'Serviceman Withdraw Requests',
        'withdraw_request' => 'Withdraw Request',
        'withdraw_requests' => 'Withdraw Requests',
        'all' => 'All Withdraw Requests',
        'send_withdraw_request' => 'Send Withdraw Request',
        'payment_type' => 'Payment Type',
        'select_payment' => 'Select Payment Type',
        'message' => 'Message',
        'admin_message' => 'Admin Message',
        'enter_amount' => 'Enter Amount',
        'enter_message' => 'Enter Message',
        'status' => 'Status',
    ],

    'provider' => [
        'type' => 'Type',
        'permission_denied' => 'Permission denied',
        'details_updated' => 'Details Updated',
        'select_type' => 'Select Type',
        'provider_details' => 'Provider Details',
        'providers' => 'Providers',
        'provider' => 'Provider',
        'all' => 'All Providers',
        'create' => 'Create Provider',
        'all_transactions' => 'Transactions',
        'add' => 'Add Provider',
        'edit' => 'Edit Provider',
        'address' => 'Address',
        'image' => 'Image',
        'provider_address' => 'Provider Address',
        'company_details' => 'Company Details',
        'company_logo' => 'Company Logo',
        'company_name' => 'Company Name',
        'company_phone' => 'Company phone',
        'enter_company_name' => 'Enter Company Name',
        'enter_company_email' => 'Enter Company Email',
        'enter_company_phone' => 'Enter Company Phone',
        'company_email' => 'Company Email',
        'experience_interval' => 'Experience Interval',
        'select_experience_interval' => 'Select Experience Interval',
        'experience_duration' => 'Experience Duration',
        'enter_experience_duration' => 'Enter Experience Duration',
        'bank_details' => 'Bank Details',
        'bank_details' => 'Bank Details',
        'details_updated' => 'Provider Details Updated Successfully',
        'time_slot_created' => 'Time Slot Created Successfully.',
        'time_slot_updated' => 'Time Slot Updated Successfully.',
        'create_time_slot' => 'Please Create A New TimeSlots',
        'auth_is_not_provider' => 'Auth User Is Not Provider',
        'time_slot_status_updated' => 'Status Updated Successfully.',
        'service_not_found' => 'Service Not Found',
        'invalid_provider' => 'Invalid Provider',
        'time_slot_destroy' => 'Time Slot Deleted Successfully',
        'roles_destroy' => 'Roles Deleted Successfully',
        'store' => 'Provider Created Successfully.',
        'reserved_user_not_update' => 'This User Cannot be Update. It is System reserved.',
        'updated' => 'Provider Updated Successfully.',
        'destroy' => 'Provider Deleted Successfully',
        'reserved' => 'System reserved',
        'update_password' => 'User Password Update Successfully.',
        'user_updated' => 'User Updated Successfully.',
        'reserved' => 'System reserved.',
        'user_destroy' => 'User Deleted Successfully',
        'enter_description' => 'Enter Description',
        'enter_address' => 'Enter Address',
        'not_found' => 'Provider not found',
        'must_be_provider' => 'User must be provider',
        'zone_id_must_be_required' => 'Zone ids must be required',
        'zone_id_updated' => 'Provider Zone ids update successfully',
    ],

    'language' => [
        'languages' => 'Languages',
        'create' => 'Create Language',
        'delete' => 'Delete Language',
        'edit' => 'Edit Language',
        'all' => 'All Languages',
        'name' => 'Name',
        'locale' => 'Locale',
        'is_rtl' => 'RTL',
        'translate' => 'Translate',
        'enter_language_name' => 'Enter Language Name',
        'app_locale' => 'App Locale',
        'select_locale' => 'Select Locale',
        'select_app_locale' => 'Select App Locale',
        'create_successfully' => 'Language Create Successfully',
        'update_successfully' => 'Language Update Successfully',
        'delete_successfully' => 'Language Delete Successfully',
        'file_not_found' => 'Language File Not Found',
        'translate_file_update_successfully' => 'Translate File Update Successfully',
        'select_translate_file' => 'Select File To Be Translate',
    ],

    'bank_details' => [
        'bank_name' => 'Bank Name',
        'holder_name' => 'Holder Name',
        'account_number' => 'Account Number',
        'branch_name' => 'Branch Name',
        'ifsc_code' => 'IFSC CODE',
        'swift_code' => 'SWIFT CODE',
        'enter_bank_name' => 'Enter Bank Name',
        'enter_holder_name' => 'Enter Holder Name',
        'enter_account_number' => 'Enter Account Number',
        'enter_branch_name' => 'Enter Branch Name',
        'enter_ifsc_code' => 'Enter IFSC CODE',
        'enter_swift_code' => 'Enter SWIFT CODE',
        'detail_submitted' => 'Details Submitted Successfully.',
    ],

    'pages' => [
        'pages' => 'Pages',
        'all' => 'All Pages',
        'create' => 'Create Page',
        'add' => 'Add Page',
        'edit' => 'Edit Page',
        'store' => 'Page Created Successfully.',
        'updated' => 'Page Updated Successfully.',
        'destroy' => 'Page Deleted Successfully.',
        'meta_description' => 'Enter Meta Description',
        'meta_description' => 'Enter Meta Description',
        'roles_destroy' => 'Roles Deleted Successfully',
    ],

    'provider_time_slot' => [
        'time_slot_deleted_successfully' => 'Time Slot Deleted Successfully',
        'time_slots_deleted_successfully' => 'Time Slots Deleted Successfully',
        'provider_name' => 'Provider Name',
        'gap' => 'Gap',
        'interval' => 'Interval',
        'enter_interval' => 'Enter Interval',
        'all' => 'All Time Slots',
        'provider_time_slot' => 'Provider Time Slots',
        'provider_time_slots' => 'Provider Time Slots',
        'create' => 'Create Time Slot',
        'select_time_slot' => 'Select Time Slot',
        'time_slot' => 'Time Slot',
        'select_provider' => 'Select Provider',
        'provider' => 'Provider',
        'select_day_week' => 'Select Day Week',
        'day_week' => 'Day',
        'time_slots' => 'Time Slots',
        'start_time' => 'Start Time',
        'end_time' => 'End Time',
        'leave_dates' => 'Leave Dates',
        'select_leave_dates' => 'Select Leave Dates',
        'enter_gap' => 'Enter Gap',
        'edit' => 'Edit Time Slot',
        'time_unit' => 'Time Unit',
        'select_interval_time_in' => 'Select Interval Time In',
        'select_slot' => 'Select Slot',
        'select_start_time' => 'Select Start Time',
        'select_end_time' => 'Select End Time',
        'add_time_slot' => 'Add Time Slot',
        'select_start_time_first' => 'Please select start time first.',
        'end_time_must_be_start_time' => 'End time must be after start time.',
    ],

    'service_package' => [
        'all' => 'All Service Packages',
        'service_package' => 'Service Package',
        'service_packages' => 'Service Packages',
        'create' => 'Create Service Package',
        'enter_title' => 'Enter Title',
        'services' => 'Services',
        'price' => 'Price',
        'discount' => 'Discount',
        'description' => 'Description',
        'enter_price' => 'Enter Price',
        'enter_discount' => 'Enter Discount',
        'enter_description' => 'Enter Description',
        'select_services' => 'Select Services',
        'is_featured' => 'Is Featured',
        'edit' => 'Edit Service Package',
        'start_date' => 'Start Date',
        'select_start_date' => 'Select Start Date',
        'date' => 'Date',
        'select_date' => 'Select Date',
        'end_date' => 'End Date',
        'select_end_date' => 'Select End Date',
        'store' => 'Service Package Created Successfully.',
        'service_package_not_found' => 'Service Package Not Found',
        'updated' => 'Service Package Updated Successfully',
        'destroy' => 'Service Package Successfully Deleted',
    ],

    'additional_service' => [
        'additional_services' => 'Add on Services',
        'create' => 'Create Additional Service',
        'edit' => 'Edit Additional Service',
        'select_service' => 'Select Service',
        'created' => 'Additional Service Created Successfully.',
        'updated' => 'Additional Service Updated Successfully.',
        'deleted' => 'Additional Service Deleted Successfully.'
    ],

    'currency' => [
        'currencies' => 'Currencies',
        'created_at' => 'Created',
        'action' => 'Action',
        'currency' => 'Currency',
        'status' => 'Status',
        'create' => 'Create Currency',
        'code' => 'Code',
        'select_code' => 'Select Code',
        'symbol' => 'Symbol',
        'enter_symbol' => 'Enter Symbol',
        'decimal_number' => 'Decimal Number',
        'enter_number_of_decimal' => 'Enter Number of Decimal',
        'exchange_rate' => 'Exchange Rate',
        'enter_exchange_rate' => 'Enter Exchange Rate',
        'edit' => 'Edit Currency',
        'all' => 'All Currencies',
    ],

    'blog' => [
        'all' => 'All Blogs',
        'title' => 'Title',
        'blog' => 'Blog',
        'blogs' => 'Blogs',
        'action' => 'Action',
        'create' => 'Create Blog',
        'edit' => 'Edit Blog',
        'categories' => 'Categories',
        'image' => 'Image',
        'web_image' => 'Web Image',
        'category' => 'Category',
        'enter_title' => 'Enter Title',
        'description' => 'Description',
        'enter_description' => 'Enter Description',
        'meta_title' => 'Meta Title',
        'meta_description' => 'Meta Description',
        'enter_meta_description' => 'Enter Meta Description',
        'tags' => 'Tags',
        'select_categories' => 'Select Categories',
        'select_tags' => 'Select Tags',
        'enter_meta_title' => 'Enter Meta Title',
        'created_at' => 'Created',
        'featured' => 'Featured',
        'store' => 'Blog Created Successfully.',
        'updated' => 'Blog Updated Successfully.',
        'destroy' => 'Blog deleted successfully',
        'roles_destroy' => 'Roles Deleted Successfully',
    ],

    'tag' => [
        'all' => 'All Tags',
        'tag' => 'Tag',
        'tags' => 'Tags',
        'create' => 'Create Tag',
        'edit' => 'Edit Tag',
        'name' => 'Name',
        'enter_name' => 'Enter Name',
        'description' => 'Description',
        'enter_description' => 'Enter Description',
        'select_tags' => 'Select Tags',
        'stored' => 'Tag Created Successfully.',
        'updated' => 'Tag Updated Successfully.',
        'destroy' => 'Tag deleted successfully',
    ],

    'tax' => [
        'tax' => 'Tax',
        'taxes' => 'Taxes',
        'all' => 'All Taxes',
        'create' => 'Create Tax',
        'edit' => 'Edit Tax',
        'name' => 'Name',
        'rate' => 'Rate',
        'status' => 'Status',
        'enter_rate' => 'Enter Rate',
        'tax_banner_created' => 'Banner Create Successfully.',
        'store' => 'Tax Created Successfully.',
        'updated' => 'Tax Updated Successfully.',
        'destroy' => 'Tax deleted successfully',
    ],

    'service' => [
        'zone' => 'Zone',
        'images' => 'Images',
        'select_zone' => 'Select Zone',
        'per_serviceman_commission' => 'Per Serviceman Commmission',
        'enter_per_serviceman_commission' => 'Enter Per Serviceman Commmission',
        'service_rate' => 'Service Rate',
        'taxes' => 'Tax',
        'select_tax' => 'Select Tax',
        'discount' => 'Discount',
        'provider_name' => 'Provider Name',
        'enter_discount' => 'Enter Discount',
        'description' => 'Description',
        'enter_description' => 'Enter Description',
        'type' => 'Type',
        'price' => 'Price',
        'duration' => 'Duration',
        'enter_duration' => 'Enter Duration',
        'service' => 'Service',
        'services' => 'Services',
        'bg_color' => 'BG Color',
        'select_bg_color' => 'Select BG Color',
        'related_services' => 'Related Services',
        'select_services' => 'Select Services',
        'select_related_services' => 'Select Related Services',
        'all' => 'All Services',
        'create' => 'Create Service',
        'add' => 'Add Service',
        'edit' => 'Edit Service',
        'address' => 'Address',
        'enter_address' => 'Enter Address',
        'image' => 'Image',
        'enter_title' => 'Enter Title',
        'category' => 'Category',
        'select_category' => 'Select Category',
        'select_categories' => 'Select Categories',
        'provider' => 'Provider',
        'select_provider' => 'Select Provider',
        'is_featured' => 'Is Featured',
        'status' => 'Status',
        'select_type' => 'Select Type',
        'service_address' => 'Service Address',
        'is_random_related_services' => 'Random Related Services',
        'isMultipleServiceman' => 'is Multiple ServiceMan',
        'duration_unit' => 'Duration Unit',
        'select_duration_unit' => 'Select Duration Unit',
        'required_servicemen' => 'Required Servicemen',
        'enter_required_servicemen' => 'Enter Required Servicemen',
        'faqs' => "FAQ's",
        'question' => 'Question',
        'enter_question' => 'Enter Question',
        'answer' => 'Answer',
        'enter_answer' => 'Enter Answer',
        'add_faq' => 'Add FAQ',
        'action' => 'Action',
        'store' => 'Service Created Successfully.',
        'updated' => 'Service Updated Successfully.',
        'destroy' => 'Service deleted successfully',
        'service_not_found' => 'Service Not Found.',
        'service_address_store' => 'Addresses Added Successfully.',
        'invalid_service_id' => 'Invalid Service ID',
        'service_address_destroy' => 'Address Deleted Successfully.',
        'service_created_sucessfully' => 'Service Created Successfully.',
    ],

    'provider_site_service' => [
        'provider_site_services' => 'Provider Site Services',
        'provider_site_service' => 'Provider Site Service',
        'create' => 'Create Provider Site Service',
        'edit' => 'Edit Provider Site Service',
    ],

    'provider-document' => [
        'provider' => 'Provider',
        'provider-document' => 'Provider Document',
        'provider-documents' => 'Provider Documents',
        'all' => 'All Provider Documents',
        'create' => 'Create Provider Document',
        'add' => 'Add Provider Document',
        'edit' => 'Edit Provider Document',
        'address' => 'Address',
        'is_verify' => 'Document Verified',
        'image' => 'Image',
        'select-provider' => 'Select Provider',
        'document' => 'Document',
        'select-document' => 'Select Document',
        'document_number' => 'Document Number',
        'enter_document_number' => 'Enter Document Number',
        'status' => 'Status',
        'select_status' => 'Select Status',
    ],

    'account' => [
        'enter_address' => 'Enter Address',
        'update_profile' => 'Profile Update Successfully.',
        'updated_password' => 'Password Updated Successfully',
    ],

    'document' => [
        'document' => 'Documents',
        'documents' => 'Documents',
        'all' => 'All Documents',
        'create' => 'Create Document',
        'edit' => 'Edit Document',
        'add' => 'Add Document',
        'is_required' => 'Is Required Document',
        'enter_document_name' => 'Enter Document Name',
        'updated' => 'Documents Update Successfully',
        'document_not_found' => 'Documents Not Found',
        'document_not_uploaded' => 'Document Not Uploaded Yet',
        'document_uploaded_successfull' => 'Documents Uploaded Successfully',
        'store' => 'Document Created Successfully.',
        'updated' => 'Document Updated Successfully',
        'destroy' => 'Document Deleted Successfully',
    ],

    'categories' => [
        'cancel' => 'Cancel',
        'delete' => 'Delete',
        'categories' => 'Categories',
        'thumbnail' => 'Thumbnail',
        'web_thumbnail' => 'Web Thumbnail',
        'web_images' => 'Web Images',
        'category' => 'Category',
        'edit' => 'Edit Category',
        'parent_category' => 'Select Parent Category',
        'status' => 'Status',
        'enter_name' => 'Enter Name',
        'image' => 'Image',
        'description' => 'Description',
        'enter_description' => 'Enter Description',
        'is_featured' => 'Is Featured',
        'create' => 'Create Category',
        'enter_title' => 'Enter Title',
        'enter_meta_title' => 'Enter Meta Title',
        'edit_category' => 'Edit Category',
        'commission' => 'Commission',
        'enter_commission' => 'Enter Commission',
        'commission-type' => 'Commission Type',
        'select-commission-type' => 'Select Commission Type',
        'category-type' => 'Category Type',
        'select-category-type' => 'Select Category Type',
        'select-category' => 'Select Category Type',
        'select-categories' => 'Select Categories',
        'general' => 'GENERAL',
        'seo' => 'SEO',
        'no_category_created' => 'There Are No Categories',
        'no_category' => 'No Categories',
        'parent' => 'Parent',
        'store' => 'Category Added Successfully.',
        'updated' => 'Category Updated Successfully.',
        'destroy' => 'Category Deleted Successfully',
        'is_feature_updated' => 'Is Featured Updated Successfully',
    ],

    'provider_document' => [
        'create' => 'Create Provider Document',
        'provider_documents' => 'Providers Documents',
    ],

    'commission_history' => [
        'commission_history' => 'Commission History',
        'commission_histories' => 'Commission Histories',
        'only_compare_similar_service' => 'You can only compare similar services.',
    ],

    'confirmation' => [
        'title' => 'Confirmation',
        'confirmation_proceed' => 'Are you sure you want to proceed?',
    ],

    'booking' => [
        'please_asgning_required_servicemen' => 'The number of selected servicemen does not match the required total',
        'cancellation_restricted' => "You can't cancel this booking shortly before it starts.",
        'charge_added_successfully' => 'Booking extra charge added successfully',
        'payment_status' => 'Payment Status',
        'provider_name' => 'Provider Name',
        'service_name' => 'Service Name',
        'booking_status_id' => 'Status',
        'accepted' =>'Accepted',
        'assigned' => 'Assigned',
        'decline' => 'Decline',
        'start_again' => 'Start Again',
        'select_role' => 'Select Role',
        'invoice' => 'Invoice',
        'invoice_no' => 'No',
        'service_asigned_successfull' => 'Service Asigned Successfully',
        'invoice_service_name' => 'Service Name',
        'invoice_per_serviceman_charge' => 'Per Serviceman Charge',
        'invoice_platform' => 'Invoice Platform',
        'invoice_extra_charge' => 'Total Extra Charges',
        'customer_address' => 'Customer Address',
        'invoice_platform_fees' => 'Platform Fees',
        'invoice_price' => 'Price',
        'invoice_qty' => 'Qty',
        'invoice_subtotal' => 'Subtotal',
        'invoice_shipping_cost' => 'Shipping Cost',
        'invoice_grand_total' => 'Grand Total',
        'invoice_sub_total' => 'Sub Total',
        'invoice_tax' => 'Tax',
        'invoice_shipping' => 'Shipping',
        'invoice_total_payable' => 'Total Payable',
        'billing_address' => 'Billing Address',
        'shipping_address' => 'Shipping Address',
        'service_tax' => 'Service Tax',
        'service_amount' => 'Service Amount',
        'order_id_title' => 'Booking Id - ',
        'order_date_title' => 'Booking Date - ',
        'payment_method_title' => 'Payment Method - ',
        'sub_booking_no' => 'Sub Booking Number',
        'service_title' => 'Service Title',
        'service_provider' => 'Service Provider',
        'service_rate' => 'Service Rate',
        'total_extra_servicemen' => 'Total Extra Servicemen',
        'total_servicemen_charge' => 'Total Servicemen Charge',
        'sub_booking_details' => 'Sub Booking Details',
        'coupon_discount' => 'Coupon Discount',
        'service_discount' => 'Service Discount',
        'all' => 'All Bookings',
        'booking' => 'Booking',
        'bookings' => 'Bookings',
        'details' => 'Booking Details of',
        'created_at' => 'Created',
        'booking_number' => 'Booking Number',
        'consumer_name' => 'Consumer Name',
        'payment_method' => 'Payment Method',
        'payment_status' => 'Payment Status',
        'general' => 'General',
        'status' => 'Status',
        'amount' => 'Amount',
        'booking_amount' => 'Booking Amount',
        'booking_status' => 'Booking Status',
        'select_booking_status' => 'Booking Status',
        'assign' => 'Assign',
        'serviceman' => 'Select Serviceman',
        'select_serviceman' => 'Select Servicemen',
        'pending' => 'Pending',
        'on_going' => 'On Going',
        'on_the_way' => 'On The Way',
        'completed' => 'Completed',
        'cancel' => 'Cancel',
        'on_hold' => 'On Hold',
        'action' => 'Action',
        'do_not_have_the_serviceman_role' => 'One or more users do not have the "serviceman" role.',
        'wallet_balance_disabled' => 'The option to use wallet balance for booking is currently disabled.',
        'coupon_feature_disabled' => 'The coupon code cannot be used as the coupon feature is currently disabled.',
        'coupon_code_should_be_higher' => 'To apply coupon code :code, your booking total should be :min_spend or higher.',
        'max_usage_reached' => 'The coupon code :code has reached its maximum usage of :usage_per_customer times per consumer.',
        'usage_limit_reached' => 'The coupon code :code can only be used up to :usage_per_coupon times per coupon.',
        'coupon_code_should_be_higher' => 'To apply coupon code :code, your booking total should be :min_spend or higher.',
        'wallet_balance_unavailable' => 'Providers are unable to use wallet balance while creating booking.',
        'coupon_code_duration' => 'The coupon code :code was applicable from :start_date to :end_date',
        'inactive_payment_method' => 'The provided payment method is not currently active.',
        'invalid_booking_status' => 'Invalid Booking Status',
        'inactive_subscription_module' => 'Subscription module is inactive',
        'payment_method_not_found' => 'Selected payment method not found or not active',
        'invalid_details' => 'Provided details are invalid',
        'payment_module_not_found' => 'Payment module class or method not found.',
        'payment_method_disabled' => 'Selected payment module not found or not enable.',
        'invalid_payment_method' => 'Selected payment method is invalid',
        'insufficient_wallet_balance_booking' => 'The wallet balance is not sufficient for this booking.',
        'wallet_balance_disabled' => 'The option to use wallet balance for order is currently disabled.',
        'vendors_wallet_balance_disabled' => 'Vendors are unable to use wallet balance while creating orders.',
        'insufficient_wallet_balance_order' => 'The wallet balance is not sufficient for this order.',
        'insufficient_vendor_wallet_balance' => 'The provider wallet balance is not sufficient for this order.',
        'not_found_item' => 'This item is not found.',
        'subscription_module_not_found' => 'Subscription module is disabled or not found.',
        'insufficient_points' => 'The points is not sufficient for this order.',
        'servicemen_already_assigned' => 'Servicemen already assigned.',
        'service_assigned_successful' => 'Servicemen assigned successfully',
        'invalid_booking_number' => 'The provided booking number is not valid.',
        'charged_added_successful' => 'Charge Added Successfully',
        'service_proof_added' => 'Service proof added.',
        'booking_not_assigned' => 'This booking is not assigned to you',
        'servicemen_unavailable' => 'Serviceman is not available.',
        'servicemen_proof_updated' => 'Service Proof Updated.',
        'invalid_proof_id' => 'Invalid Proof ID Provided',
        'proof_not_provided' => 'Proof ID Not Provided',
    ],

    'settings' => [
        //new keys//
        'agora' => 'Agora',
        'agora_app_id' => 'App ID',
        'enter_agora_app_id' => 'Enter Agora App ID',
        'agora_certificate' => 'Agora Certificate',
        'enter_agora_certificate' => 'Enter Agora Certificate',
        //new keys//
        'enable_service_category_based_commissions' => '
*Enable the functionality to apply commissions based on service categories. To configure, navigate to the Service Category module and set the respective commission rates.',
        'force_update_in_app' => 'Force Update In App',
        'force_update_in_app_span' => '*Enable to require users to update the app to the latest version.',
        'maintenance_mode' => 'Maintenance Mode',
        'maintenance_mode_span' => '*Enable to put the site in maintenance mode.',
        'social_login_enable' => 'Social Login Status',
        'social_login_enable_span' => '*Enable to allow users to log in using social media accounts.',
        'blogs_enable' => 'Blogs Status',
        'blogs_enable_span' => '*Turn of to hide blogs from the user app and website.',
        'cancellation_restriction_hours' => 'Cancellation Restriction Hours',
        'enter_cancellation_restriction_hours' => 'Enter hours before the booking when cancellation is disabled.',
        'cancellation_restriction_hours_help_text' => '*Enter hours before the booking when cancellation is disabled.',
        'service_request' => 'Service Request',
        'per_serviceman_commission' => 'Per Serviceman Commision',
        'enter_per_serviceman_commission' => 'Enter Per Serviceman Commision',
        'default_tax_id' => 'Default Tax',
        'select_default_tax_id' => 'Select Default Tax',
        'additional_services' => 'Additional Services',
        'additional_services_span' => '*Enable Additional Services.',
        'is_category_based_commission' => 'Is Category Based Commission',
        'firebase_server_key' => 'Firebase Server Key',
        'enter_firebase_server_key' => 'Enter Firebase Server Key',
        'upload_logo_image_size' => '*Upload image size 180x50px recommended',
        'upload_favicon_image_size' => '*Upload image size 16x16px recommended',
        'minimum_required_booking_amount' => '*Please enter the minimum amount required for an booking to be processed.',
        'activation_vendor_commissions' => '*By activating provider commissions, the admin earns a commission based on provider earnings.',
        'min_amount_for_vendor_withdraw' => '*Payout Minimum for Providers: Specify the min amount providers can request for withdrawal.',
        'set_rate_admin_receive_commission' => '*Set the rate at which admin receives a commission from provider earnings.',
        'enable_service_category_based_commission' => '*Enable the functionality to apply commissions based on service categories. To configure, navigate to the Service Category module and set the respective commission rates.',
        'max_service_provider_can_create' => '*Maximum service providers can create without buying subscriptions',
        'max_addresses_provider_can_create' => '*Maximum addresses providers can create without buying subscriptions',
        'max_servicemen_provider_can_create' => '*Maximum servicemen providers can create without buying subscriptions',
        'max_service_packages_provider_can_create' => '*Maximum service packages providers can create without buying subscriptions',
        'free_trail_days' => '*Free trial period for subscription',
        'settings' => 'Settings',
        'services' => 'Services',
        'addresses' => 'Addresses',
        'default_credentials' => 'Demo Mode',
        'default_credentials_span' => '*Allow users to show or hide demo credentials on the login page of the admin website and login screens within the applications.',
        'subscription' => 'Subscription',
        'servicemen' => 'Servicemen',
        'to_mail' => 'To Mail',
        'send_test_mail' => 'Send Test Mail',
        'extra_charge_status' => 'Extra Charge Status',
        'extra_charge_status_span' => '*Extra Charge Status',
        'service_packages' => 'Service Packages',
        'platform_fees' => 'Platform Fees',
        'platform_fees_status' => 'Platform Fees Status',
        'platform_fees_status_span' => '*Enable the Platform Fees.',
        'platform_fees_type' => 'Platform Fees Type',
        'select_platform_fees_type' => 'Select Platform Fees Type',
        'enter_platform_fees' => 'Enter Platform Fees',
        'updated_successfully' => 'Settings Updated Successfully',
        'paypal' => 'PayPal',
        'status' => 'Status',
        'mollie' => 'Mollie',
        'site_name' => 'Site Name',
        'enter_site_name' => 'Enter Site Name',
        'google_recaptcha' => 'Google Recaptcha',
        'cash' => 'Cash',
        'cash_span' => '*Cash Status',
        'sandbox_mode' => 'Sandbox mode',
        'client_id' => 'Client Id',
        'enter_client_id' => 'Enter Client Id',
        'default_language_id' => 'Default Language',
        'secret_id' => 'Secret Id',
        'enter_secret_id' => 'Enter Secret Id',
        'stripe' => 'Stripe',
        'razorpay' => '₹ Razorpay',
        'mail_username' => 'Username',
        'payment_method' => 'Payment Method',
        'enter_copyright_text' => 'Enter Copyright Text',
        'category_based_commission' => 'Category Based Commission',
        'category_based_commission_note' => '*Enable the functionality to apply commissions based on service categories. To configure, navigate to the Service Category module and set the respective commission rates.',
        'min_withdraw_note' => '*Payout Minimum for Providers: Specify the min amount providers can request for withdrawal.',
        'commission_rate_note' => '*Set the rate at which admin receives a commission from provider earnings.',
        'commissions_status' => 'Status',
        'enter_min_withdraw_amount' => 'Enter Min Withdraw Amount',
        'min_withdraw_amount' => 'Min Withdraw Amount',
        'commission_rate' => 'Commission Rate',
        'enter_default_commission_rate' => 'Enter Default Commission Rate',
        'mail_password' => 'Password',
        'mail_from_name' => 'Email From Name',
        'mail_from_address' => 'Email From Address',
        'email_settings' => 'Email Settings',
        'select_mail_mailer' => 'Select Mail Mailer',
        'host' => 'Host',
        'enter_host' => 'Enter Host',
        'provider_commissions' => 'Provider Commissions',
        'default_creation_limits' => 'Default Creation Limits',
        'port' => 'Port',
        'secret' => 'Secret',
        'site_key' => 'Site Key',
        'enter_site_key' => 'Enter Site Key',
        'enter_port' => 'Enter Port',
        'enter_username' => 'Enter Username',
        'enter_password' => 'Enter Password',
        'enter_email_from_name' => 'Enter Email From Name',
        'enter_email_from_address' => 'Enter Email From Address',
        'mail_encryption' => 'Encryption',
        'select_encryption_type' => 'Select Encryption Type',
        'mailer' => 'Mailer',
        'timezone' => 'TimeZone',
        'select_timezone' => 'Select Timezone',
        'service_auto_approve_span' => '*Enable automatic acceptance of provider services without manual review.',
        'currency' => 'Currency',
        'sms_gateway' => 'SMS Gateway',
        'select_sms_gateway' => 'Select SMS Gateway',
        'enter_min_booking_amount' => 'Enter minimum booking amount',
        'min_booking_amount' => 'Min Booking Amount',
        'copyright_text' => 'Copyright Text',
        'timezone_span' => '*Please enter the minimum amount required for the booking to be processed.',
        'select_currency' => 'Select Currency',
        'general' => 'General',
        'activation' => 'Activation',
        'service_auto_approve' => 'Service Auto Approve',
        'email_configuration' => 'Email Configuration',
        'dark_logo' => 'Dark Logo',
        'light_logo' => 'Light Logo',
        'favicon' => 'Favicon',
        'smtp_setting' => 'SMTP Setting',
        'provider_auto_approve' => '*Provider Auto Approve',
        'auto_approve_provider' => 'Auto Approve Provider',
        'reading' => 'Reading',
        'permalinks' => 'Permalinks',
        'analytics' => 'Analytics',
        'newsletter' => 'Newsletter',
        'select_static_page' => 'A static page (select below)',
        'updated' => 'Setting Updated Successfully.',
        'subscription_enable' => 'Subscription',
        'wallet_enable' => 'Enable Wallet',
        'coupon_enable' => 'Enable Coupon',
        'coupon_span' => '*Allow customers to use coupons for payment at checkout.',
        'wallet_span' => '*Enable the use of wallet balance for payment during checkout.',
        'subscription_span' => '*Enable the use of provider can purchase plan and increase limit.',
        'mode' => 'Mode',
        'select_mode' => 'Select Mode',
        'enter_secret' => 'Enter Secret Key',
        'free_trial' => 'Free Trial',
        'free_trial_days' => 'Free Trial Days',
        'enter_free_trial_days' => 'Enter Free Trial Days',
        'firebase' => 'Firebase',
        'firebase_service_json' => 'Firebase Service Json',
        'google_map_api_key' => 'Google Map API Key',
        'enter_google_map_api_key' => 'Enter Google Map API Key',
        'test_mail_note' => "When setting up your email system (SMTP), make sure to do it carefully. If it's not done right, you'll encounter errors when placing orders, registering new users, or sending newsletters.",
        'test_mail_not_using_ssl' => "If you're not using SSL:",
        'test_mail_not_ssl_msg_1' => "Choose 'sendmail' for the Mail Driver if you run into problems with SMTP.",
        'test_mail_not_ssl_msg_2' => "Use the Mail Host settings provided by your email service's manual.",
        'test_mail_not_ssl_msg_3' => 'Set the Mail port to 587.',
        'test_mail_not_ssl_msg_4' => 'If there are issues with TLS, set the Mail Encryption to SSL.',
        'test_mail_using_ssl' => "If you're using SSL:",
        'test_mail_ssl_msg_1' => "Again, choose 'sendmail' if there are issues with SMTP.",
        'test_mail_ssl_msg_2' => "Use the Mail Host settings provided by your email service's manual.",
        'test_mail_ssl_msg_3' => 'Set the Mail port to 465.',
        'test_mail_ssl_msg_4' => 'Set the Mail Encryption to SSL.',
        'days_before_reminder' => 'Days Before Reminder',
        'enter_days_before_reminder' => 'Enter No Of Days Before Reminder',
        'subscription_reminder_note_span' => '*Message sent to remind the user about their subscription expiration.',
        'days_before_reminder_span' => '*Number of days before the subscription expires to start sending reminder notifications to the user.',
        'subscription_reminder_note' => 'Subscription Reminder Note',
        'enter_subscription_reminder_note' => 'Enter Subscription Reminder Note',
        'reminder_message' => 'Reminder Message',
        'enter_reminder_message' => 'Enter Reminder Message',
        'reminder_message_span' => 'This message will be sent to user prior the expiration of the subscription',
        'social_login' => 'Social Login',
        'client_secret' => 'Client Secret',
        'enter_client_secret' =>  'Enter Client Secret',
        'redirect_url' => 'Redirect URL',
        'social_redirect_url' => '*This redirect URL will be available in your Google Console account.',
        'enter_redirect_url' => 'Enter Redirect URL',
    ],

    'serviceman' => [
        'show_all' => 'Show All',
        'view_location' => 'View Location',
        'serviceman_list' => 'Serviceman List',
        'serviceman_location' => 'Serviceman Location',
        'locations' => 'Serviceman Locations',
        'servicemen_information' => 'Servicemen Information',
        'service' => 'Service Name',
        'customer' => 'Customer Name',
        'rating' => 'Rating',
        'description' => 'Description',
        'experience' => 'Experience',
        'enter_experience' => 'Enter Experience',
        'serviceman' => 'Serviceman',
        'servicemen' => 'Servicemen',
        'servicemans' => 'Servicemans',
        'create' => 'Create Serviceman',
        'edit' => 'Edit Serviceman',
        'all' => 'All Servicemen',
        'enter_name' => 'Enter Name',
        'enter_email' => 'Enter Email',
        'address' => 'Address',
        'country' => 'Country',
        'state' => 'State',
        'provider' => 'Provider',
        'image' => 'Image',
        'is_featured' => 'Is Featured',
        'status' => 'Status',
        'enter_current_password' => 'Enter Current Password',
        'enter_password' => 'Enter Password',
        're_enter_password' => 'Enter Confirm Password',
        'select_country' => 'Select Country',
        'select_state' => 'Select State',
        'enter_city' => 'Enter City',
        'enter_area' => 'Enter Area',
        'postal_code' => 'Postal Code',
        'enter_phone_number' => 'Enter Phone Number',
        'select-provider' => 'Select Provider',
        'general' => 'General',
        'invalid_address_id' => 'Invalid Address Id.',
        'store' => 'Serviceman Created Successfully.',
        'reserved_user_not_updated' => 'This User Cannot be Update. It is System reserved.',
        'updated' => 'Serviceman Updated Successfully.',
        'reserved' => 'System reserved.',
        'destroy' => 'Serviceman Deleted Successfully',
        'is_featured_updated' => 'Is Featured Updated Successfully',
        'password_updated' => 'Password Updated Successfully',
        'roles_destroy' => 'Roles Deleted Successfully',
        'bank_details' => 'Bank Details'
    ],

    'customer' => [
        'customer' => 'Customer',
        'customers' => 'Customers',
        'create' => 'Create Customer',
        'edit' => 'Edit Customer',
        'all' => 'All Customers',
        'enter_name' => 'Enter Name',
        'enter_email' => 'Enter Email',
        'address' => 'Address',
        'country' => 'Country',
        'state' => 'State',
        'provider' => 'Provider',
        'image' => 'Image',
        'is_featured' => 'Is Featured',
        'status' => 'Status',
        'enter_current_password' => 'Enter Current Password',
        'enter_password' => 'Enter Password',
        're_enter_password' => 'Enter Confirm Password',
        'select_country' => 'Select Country',
        'select_state' => 'Select State',
        'enter_city' => 'Enter City',
        'enter_area' => 'Enter Area',
        'postal_code' => 'Postal Code',
        'enter_phone_number' => 'Enter Phone Number',
        'select-provider' => 'Select Provider',
        'general' => 'General',
        'store' => 'Customer Created Successfully.',
        'updated' => 'Customer Updated Successfully.',
        'destroy' => 'Customer Deleted Successfully',
    ],

    'coupon' => [
        'discount' => 'Discount',
        'validity' => 'Validity',
        'zones' => 'Zones',
        'select_zones' => 'Select zones',
        'include_users' => 'Include Users',
        'select_users' => 'Select Users',
        'all' => 'All Coupons',
        'coupon' => 'Coupon',
        'coupons' => 'Coupons',
        'create' => 'Create Coupon',
        'general' => 'General',
        'restriction' => 'Restriction',
        'title' => 'Title',
        'usage' => 'Usage',
        'code' => 'Code',
        'action' => 'Action',
        'enter_coupon' => 'Enter Coupon',
        'commission-type' => 'Commission Type',
        'type' => 'Type',
        'select_type' => 'Select Type',
        'price' => 'Price',
        'percentage' => 'Percentage',
        'enter_price' => 'Enter Price',
        'enter_percentage' => 'Enter Percentage',
        'is_expired' => 'Is Expired',
        'expiration_status' => 'Expiration Status',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'is_first_order' => 'Is First Order',
        'is_apply_all ' => 'Is Apply All',
        'include_services' => 'Include Services',
        'select_services' => 'Select Services',
        'exclude_services' => 'Exclude Services',
        'minimum_spend' => 'Minimum Spend',
        'enter_minimum_spend' => 'Enter Minimum Spend',
        'is_unlimited' => 'Is Unlimited',
        'usage_per_coupon' => 'Usage Per Coupon',
        'usage_per_customer' => 'Usage Per Customer',
        'enter_value' => 'Enter Value',
        'is_apply_all' => 'Is Apply All',
        'edit' => 'Edit Coupon',
        'created' => 'Created',
    ],

    'teams' => [
        'teams' => 'Teams',
        'create_team' => 'Create Team',
        'edit_team' => 'Edit Team',
        'designation' => 'Designation',
        'store' => 'Team Created Successfully.',
        'updated' => 'Team Updated Successfully.',
        'destroy' => 'Team Deleted Successfully',
    ],

    'pricing_plans' => [
        'pricing_plans' => 'Pricing Plans',
        'create' => 'Create Plan',
        'edit' => 'Edit Plan',
        'price' => 'Price',
        'duration' => 'Duration',
        'content' => 'Content',
        'store' => 'Pricingplan Created Successfully.',
        'updated' => 'Pricingplan Updated Successfully.',
        'destroy' => 'Pricingplan Deleted Successfully.',
    ],

    'testimonials' => [
        'testimonials' => 'Testimonials',
        'name' => 'Name',
        'rating' => 'Rating',
        'image' => 'Image',
        'enter_name' => 'Enter Name',
        'enter_rating' => 'Enter Rating',
        'enter_description' => 'Enter Description',
        'create' => 'Create Testimonial',
        'edit' => 'Edit Testimonial',
        'description' => 'Description',
        'added' => 'Testimonial Added Sccessfully.',
        'updated' => 'Testimonial Updated Successfully.',
        'destroy' => 'Testimonial Deleted Successfully.',
    ],

    'subscribers' => [
        'subscribers' => 'Subscribers',
        'all' => 'Subscriber',
        'email' => 'Email',
        'create' => 'Create At'
    ],

    'faqs' => [
        'faqs' => 'FAQ\'s',
        'create' => 'Create FAQ',
        'edit' => 'Edit FAQ',
        'store' => 'FAQ Added Successfully.',
        'updated' => 'FAQ Updated Sccessfully.',
        'destroy' => 'FAQ Deleted Successfully.',
    ],

    'zone' => [
        'all' => 'All Zones',
        'search_location' => 'Search Location',
        'search_locations' => 'Search Your Location',
        'name' => 'Zone Name',
        'enter_name' => 'Enter Zone Name',
        'coordinates' => 'Coordinates',
        'place_points' => 'Place Points',
        'map' => 'Map',
        'select-zone' => 'Select Zone',
        'select_coordinates' => 'Select Coordinates',
        'select_place_points' => 'Select Place Points',
        'zones' => 'Zones',
        'create' => 'Create Zone',
        'edit' => 'Edit Zone',
        'store' => 'Zone Added Successfully.',
        'created' => 'Zone Created Successfully.',
        'updated' => 'Zone Updated Successfully.',
        'deleted' => 'Zone Deleted Successfully.',
        'map_instruction_paragraph_1' => 'You need at least three points to create a zone.',
        'map_instruction_paragraph_2' => 'Start adding pins to the map to outline a zone.',
        'map_instruction_title' => 'Click on the map to move to the desired location.',
        'map_instruction_heading' => 'Instructions',
    ],

    'data_not_found' => 'Data Not Found!',
    'social_links' => [
        'create' => 'Create Social Link',
        'add' => 'Add Social Link`',
        'edit' => 'Edit Social Link',
        'name' => 'Name',
        'icon' => 'Icon',
        'url' => 'Url',
        'theme_option' => 'Theme Option',
        'store' => 'Social Link Created Successfully.',
        'updated' => 'Social Link Updated Successfully.',
        'destroy' => 'Social Link Deleted Successfully.',
    ],

    'banner' => [
        'id' => 'Id',
        'title' => 'Title',
        'action' => 'Action',
        'banner' => 'Banner',
        'banners' => 'Banners',
        'create' => 'Create Banner',
        'image' => 'Banner Image',
        'status' => 'Status',
        'type' => ' Banner Type',
        'category' => 'Banner Category',
        'select_type' => 'Select Banner Type',
        'category_type' => 'Select Category Type',
        'edit' => 'Edit Banner',
        'all' => 'All Banners',
        'enter_title' => 'Enter Title',
        'is_offer' => 'Is offer',
        'created_at' => 'Created',
        'updated_at' => 'Updated At',
        'store' => 'Banner Create Successfully.',
        'updated' => 'Banner Updated Successfully.',
        'destroy' => 'Banner Deleted Successfully',
        'created_successfully' => 'Created Successfully',
    ],

    'roles' => [
        'role' => 'Role',
        'roles' => 'Roles',
        'add' => 'Add Role',
        'create' => 'Create Role',
        'edit' => 'Edit Role',
        'delete' => 'Delete Selected',
        'permissions' => 'Permissions',
        'select_role' => 'Select Role',
        'all' => 'All Roles',
        'enter_name' => 'Enter Name',
        'store' => 'Role Created Successfully.',
        'updated' => 'Role Updated Successfully',
        'destroy' => 'Role Deleted Successfully',
        'at_least_one_permission_required' => 'At least one permission is required',
        'reserved_role_not_updated' => 'This Role Cannot be Update. It is System reserved.',
    ],

    'users' => [
        'status_update_successfully' => 'Status Update Successfully',
        'expertise_in' => 'Expertise IN',
        'select_services' => 'Select Services',
        'known_languages' => 'Known Languages',
        'select_languages' => 'Select Languages',
        'select_expertise_services' => 'Select Expertise Services',
        'type' => 'Type',
        'select_type' => 'Select Type',
        'all' => 'All Users',
        'system_users' => 'System Users',
        'enter_area' => 'Enter Area or Locality',
        'postal_code' => 'Enter Postal Code',
        'users' => 'Users',
        'create' => 'Create User',
        'add' => 'Add   User',
        'edit' => 'Edit User',
        'change_password' => 'Change Password',
        'roles' => 'Roles',
        'select_country' => 'Select Country',
        'select_state' => 'Select State',
        'country' => 'Country',
        'state' => 'State',
        'store' => 'User Added Successfully',
        'updated' => 'User Profile Update Successfully.',
        'updated_password' => 'User Password Update Successfully.',
        'destroy' => 'User Deleted Successfully',
        'enter_name' => 'Enter Name',
        'enter_email' => 'Enter Email',
        'enter_city' => 'Enter City',
        'address' => 'Address',
        'enter_address' => 'Enter Address',
        'enter_phone_number' => 'Enter Phone Number',
        'enter_password' => 'Enter Password',
        're_enter_password' => 'Re-Enter Password',
    ],

    'wallet' => [
        'select_consumer' => 'Select Consumer',
        'select_provider' => 'Select Provider',
        'select_serviceman' => 'Select Serviceman',
        'add_withdraw_amount' => 'Add/Withdraw Amount',
        'wallet' => 'Wallet',
        'amount' => 'Amount',
        'add_note' => 'Add Note',
        'enter_note' => 'Enter Note',
        'add_amount' => 'Enter Amount',
        'default_balance' => '0.00',
        'balance' => 'Balance',
        'message' => 'Message',
        'credit' => 'Credit',
        'debit' => 'Debit',
        'create' => 'Manage Wallet',
        'pending_balance' => 'Pending Balance',
        'disabled' => 'The Wallet Feature is Disabled.',
        'permission_denied' => 'Permission Denied',
        'payment_module_not_found' => 'Payment module class or method not found.',
        'invalid_payment_method' => 'Selected payment method is invalid',
        'credited' => 'Credited Successfully',
        'ballance_is_null' => 'Balance cannot be null',
        'user_must_be_consumer' => 'user must be Consumer',
        'debited' => 'Debited Successfully',
        'insufficient_balance' => 'Balance is not sufficient for this withdrawal.',
        'wallet_credited' => 'Wallet Created Successfully.',
        'wallet_updated' => 'Wallet Updated Successfully.',
        'wallet_destroy' => 'Wallet deleted successfully',
        'status_updated' => 'Status updated successfully',
    ],

    'media' => [
        'media' => 'Media',
        'files' => 'Media Files',
        'upload' => 'Upload Media',
        'choose_photo' => 'Choose Photo',
        'select_filter' => 'Select filter',
        'back' => 'Back To Media',
        'dropzone' => 'Dropzone',
        'drop_message' => 'Drop files here or click to upload.',
        'sort_by_newest' => 'Sort By newest',
        'sort_by_oldest' => 'Sort By oldest',
        'sort_by_smallest' => 'Sort By smallest',
        'sort_by_largest' => 'Sort By largest',
        'select_file' => 'Select File',
        'upload_new' => 'Upload New',
        '0_file_selected' => '0 File Selected',
        'add' => 'Add',
        'clear' => 'Clear',
        'destroy' => 'Media Deleted Successfully.',
        'image_not_found' => 'Image not found',
        'image_destroy' => 'Image Successfully Deleted',
    ],

    'posts' => [
        'posts' => 'Posts',
        'all' => 'All Posts',
        'create' => 'Create Post',
        'category' => 'Categories',
        'add' => 'Add Post',
        'edit' => 'Edit Post',
        'comments' => 'Comments',
        'message' => 'Message',
        'approve' => 'Approve',
        'edit_comments' => 'Edit Comments ',
        'tags' => 'Tags',
        'create_tags' => 'Create Tags',
        'edit_tags' => 'Edit Tags',
        'featured' => 'Featured',
        'sticky' => 'Stick to the top of the blog',
        'excerpt' => 'Excerpt',
        'store' => 'Blog Added Sccessfully.',
        'updated' => 'Blog Updated Successfully.',
        'destroy' => 'Blog Deleted Successfully.',
        'comment_updated' => 'Comments Update Successfully.',
        'comment_destroy' => 'Comments Deleted Successfully.',
    ],

    'page' => [
        'app_icon' => 'App Icon',
        'pages' => 'Pages',
        'create' => 'Create Page',
        'edit' => 'Edit Page',
        'page' => 'Page',
        'all' => 'All Pages',
        'content' => 'Content',
        'meta_descripation' => 'Meta Descriptions',
        'meta_image' => 'Meta Image',
        'meta_title' => 'Meta Title',
        'placeholder_meta_title' => 'Enter Meta Title',
        'enter_title' => 'Enter Title',
        'placeholder_meta_descripation' => 'Enter Meta Descriptions',
    ],

    'sections' => [
        'sections' => 'Sections',
        'home_banner' => 'Home Banner',
        'about' => 'About',
        'app_features' => 'App Features',
        'how_it_work' => 'How It Work',
        'screenshots' => 'Screenshots',
        'team' => 'Team',
        'pricing_plans' => 'Pricing Plans',
        'faqs' => 'FAQ\'s',
        'recent_blogs' => 'Recent Blogs',
        'download' => 'Download',
        'contact' => 'Contact',
        'newsletter' => 'Newsletter',
        'updated' => 'Section Updated Successfully.',
    ],

    'plan' => [
        'plans' => 'Plans',
        'duration' => 'Duration',
        'select_duration' => 'Select Plan Duration',
        'all' => 'All Plans',
        'create' => 'Create Plan',
        'services' => 'Services',
        'addresses' => 'Addresses',
        'service_packages' => 'Service Packages',
        'servicemen' => 'Servicemen',
        'enter_max_services' => 'Enter Maximum Number Of Services',
        'enter_max_service_packages' => 'Enter Maximum Number Of Service Packages',
        'enter_max_addresses' => 'Enter Maximum Number Of Addresses',
        'enter_max_servicemen' => 'Enter Maximum Number Of Servicemen',
        'enter_plan_price' => 'Enter Plan Price',
        'edit' => 'Edit Plan',
        'max_addresses' => 'Max Addresses',
        'max_services' => 'Max Services',
        'subscriptions' => 'Subscriptions',
        'created_successfully' => 'Plan Created Successfully',
        'updated_successfully' => 'Plan Updated Successfully',
        'deleted_successfully' => 'Plan Deleted Successfully',
    ],

    'appearances' => [
        'home_page' => 'Home Page',
        'home_banner' => 'Home Banner',
        'appearances' => 'Appearances',
    ],

    'home_pages' => [
        'home_page' => 'Home Page',
        'home_banner' => 'Home Banner',
    ],

    'theme_options' => [
        'general' => 'General',
        'header' => 'Header',
        'footer' => 'Footer',
        'contact_us' => 'Contact Us',
        'about_us' => 'About Us',
        'terms_and_conditions'=>'Terms and Conditions',
        'pages'=>'Pages',
        'auth'=>'Authentication',
        'theme_options' => 'Theme Options',
        'appearance' => 'Appearance',
        'seo' => 'SEO',
        'comment' => 'Comment',
        'social_link' => 'Social Link',
        'blog_layouts' => 'Blog Layouts',
        'author' => 'Author',
        'blog_list' => 'Blog List',
        'blog_details' => 'Blog Detail',
        'primary_color' => 'Primary Color',
        'secondary_color' => 'Secondary Color',
        'favic_icon' => 'Favic Icon',
        'site_logo' => 'Site Logo',
        'copyright' => 'Copyright',
        'language' => 'Language',
        'meta_title' => 'Meta Title',
        'meta_description' => 'Meta Description',
        'meta_keywords' => 'Meta Keywords',
        'author_name' => 'Author Name',
        'meta_image' => 'Meta Image',
        'updat_successfully' => 'Theme Option Updated Successfully.',
        'og_image' => 'OG Image',
        'og_title' => 'OG Title',
        'meta_tags' => 'Meta Tags',
        'auth_images'=> 'Auth Images',
        'home' => 'Home',
        'categories' => 'Categories',
        'services' => 'Services',
        'booking' => 'Bookings',
        'blogs' => 'Blogs',
        'paginations' => 'Paginations',
        'provider_per_page' => 'Provider Per Page',
        'blog_per_page' => 'Blog Per Page',
        'service_per_page' => 'Service Per Page',
        'service_list_per_page' => 'Service List Per Page',
        'service_package_per_page' => 'Service Package Per Page',
        'categories_per_page' => 'Categories Per Page',
        'provider_list_per_page' => 'Provider List Per Page',

        'upload_logo_image_size' => '*Upload image size 180x50px recommended',
        'upload_favicon_image_size' => '*Upload image size 16x16px recommended',
        'minimum_required_booking_amount' => '*Please enter the minimum amount required for an booking to be processed.',
        'activation_vendor_commissions' => '*By activating provider commissions, the admin earns a commission based on provider earnings.',
        'min_amount_for_vendor_withdraw' => '*Payout Minimum for Providers: Specify the min amount providers can request for withdrawal.',

        'header_logo' => 'Header Logo',
        'footer_logo' => 'Footer Logo',
        'favicon_icon' => 'Favicon Icon',
        'site_title' => 'Site Title',
        'enter_site_title' => 'Enter Site Title',
        'site_tagline' => 'Site Tag line',
        'enter_site_tagline' => 'Enter Tag Line',

        'footer_copyright' => 'Footer Copyright',
        'enter_footer_copyright' => 'Enter Footer Copyright',
        'useful_link' => 'Useful Link',
        'select_useful_link' => 'Select Useful Link',
        'pages' => 'Pages',
        'select_pages' => 'Select Pages',
        'others' => 'Others',
        'select_others' => 'Select Others',
        'become_a_provider_enable' => 'Become a Provider',
        'description' => 'Description',
        'header_title' => 'Header Title',
        'enter_header_title' => 'Enter Header Title',
        'title' => 'Title',
        'enter_title'=>'Enter Title',
        'email' => 'Email',
        'enter_email' => 'Enter Email',
        'enter_description' => 'Enter Description',
        'contact' => 'Contact',
        'enter_contact' => 'Enter Contact',
        'location' => 'Location',
        'enter_location' => 'Enter Location',
        'google_map_embed_url' => 'Google Map Embed url',
        'enter_google_map_embed_url' => 'Enter Google Map Embed url',
        'enter_value' => 'Enter value',

        'left_bg_image'=>'Left Bg Image',
        'right_bg_image'=>'Right Bg Image',

    ],

    'payment_methods' => [
        'payment_methods' => 'Payment Methods',
        'not_found' => 'Payment Methods Not Found',
        'updated_msg' => ':name payment method update successfully',
        'invalid_msg' => 'Invalid payment methods',
        'config_file_not_found' => 'Payment configuration file not found!',
    ],

    'sms_gateways' => [
        'status' => 'Status',
        'sms_gateways' => 'SMS Gateways',
        'not_found' => 'SMS Gateway Not Found',
        'updated_msg' => ':name SMS Gateway update successfully',
        'invalid_msg' => 'Invalid SMS Gateway',
        'config_file_not_found' => 'SMS configuration file not found!',
    ],

    'layouts' => [
        'partials' => [
            'notification' => 'Notification',
            'your_order_ready_for_ship' => 'Your order ready for Ship..!',
            'download_complete' => 'Download Complete',
            '250_mb_trash_files' => '250 MB Trash Files',
        ],
    ],

    'clear_cache' => 'Clear Cache',

    'install' => [
        'requirements' => 'Requirements',
        'php_extensions' => 'PHP Extensions',
        'directories' => 'Directories',
        'permissions' => 'Permissions',
        'license' => 'License',
        'purchase_code' => 'Purchase Code',
        'configuration' => 'Configuration',
        'complete' => 'Complete',
        'installation_complete' => 'Installation Complete',
        'connection_details' => 'Connection Details',
        'install_successfull' => 'Home services installed successfully !',
        'go_to_home' => 'Go to your Front Page',
        'login_to_administration' => 'Login to Administration',
        'db_configure_message' => 'Please enter your database configuration details below.',
        'host' => 'Host',
        'port' => 'Port',
        'db_username' => 'DB Username',
        'db_password' => 'DB Password',
        'db_name' => 'Database Name',
        'administration_details_message' => 'Please enter your administration details below.',
        'first_name' => 'First Name',
        'last_name' => 'Last Name',
        'directories_fields_message' => 'Please make sure you have set the correct permissions for the directories listed below',
        'purchase_code_message' => 'Please enter your Purchase code below.',
        'requirement_field_message' => 'Please make sure the PHP extensions listed below are installed',
        'extensions' => 'Extensions',
    ],

    'transactions' => [
        'amount' => 'Amount',
        'type' => 'Type',
        'remark' => 'Remark',
        'detail' => 'Detail',
        'created_at' => 'Created',
    ],

    'transaction' => [
        'transactions' => 'Transactions',
        'amount' => 'Amount',
        'item_id' => 'Item ID',
        'transaction_id' => 'Transaction ID',
        'payment_method' => 'Payment Method',
        'payment_status' => 'Payment Status',
        'type' => 'Type',
    ],

    'job_booking' => [
        'job_bookings' => 'Job Bookings',
        'job_type' => 'Job Type',
        'property_type' => 'Property Type',
        'service_category' => 'Service Category',
        'service_tasks' => 'Service Tasks',
        'description' => 'Description',
        'schedule_date' => 'Schedule Date',
        'time_preference' => 'Time Preference',
        'frequency' => 'Frequency',
        'recurring_frequency' => 'Recurring Frequency',
        'contact_name' => 'Contact Name',
        'address' => 'Address',
        'status' => 'Status',
    ],

    'business' => [
        'businesses' => 'Businesses',
        'all' => 'All Businesses',
        'create' => 'Create Business',
        'edit' => 'Edit Business',
        'details' => 'Business Details',
        'category' => 'Category',
        'location' => 'Location',
        'address' => 'Address',
        'phone' => 'Phone',
        'email' => 'Email',
        'website' => 'Website',
        'hours' => 'Operating Hours',
        'services' => 'Services',
        'photos' => 'Photos',
        'photo' => 'Business Photo',
        'reviews' => 'Reviews',
        'no_reviews' => 'No reviews available',
        'name' => 'Name',
        'contact_info' => 'Contact Info',
        'back' => 'Back',
    ],

    'booking_status' => [
        'reserved_order_not_changed' => 'The selected order status is system reserved and cannot be changed.',
        'reserved_order_not_deleted' => 'The selected order status is system reserved and cannot be deleted.',
    ],

    'favorite_list' => [
        'store' => 'Successfully added in Your favorite List',
        'destroy' => 'Successfully Removed From Your Favorite List',
    ],

    'rate_app' => [
        'thankyou_contact_us' => 'Thank you for contact us, we will contact you shortly.',
        'details_stored' => 'Details Submitted Successfully.',
    ],

    'provider_wallet' => [
        'wallet_feature_disabled' => 'Wallet feature currently disabled. Turn it on in Settings > Activation.',
        'top_up_permission_denied' => 'Permission Denied',
        'payment_module_not_found' => 'Payment module class or method not found.',
        'invalid_payment_method' => 'Selected payment method is invalid',
        'min_withdraw_amount' => 'Make sure your requested amount is at least :minWithdrawAmount.',
        'insufficient_wallet_balance' => 'Your wallet balance is not enough to process this withdrawal.',
        'withdraw_request_submitted' => 'Withdraw request successfully generated',
        'add_payment_account_before_withdrawal' => 'Please create a payment account before applying for a withdrawal.',
        'add_paypal_email_before_withdrawal' => 'Please add a paypal email before applying for a withdrawal.',
        'add_bank_details_before_withdrawal' => 'Please complete a bank detail before applying for a withdrawal.',
        'roles_destroy' => 'Roles Deleted Successfully',
        'status_updated' => 'Status updated successfully',
        'wallet_destroy' => 'Wallet deleted successfully',
        'wallet_updated' => 'Wallet Updated Successfully.',
        'wallet_stored' => 'Wallet Created Successfully.',
        'insufficient_balance' => 'Balance is not sufficient for this withdrawal.',
        'debited' => 'Debited Successfully',
        'credited' => 'Credited Successfully',
        'user_must_be_provider' => 'user must be Provider',
        'select_provider_first' => 'Please select provider first',
    ],

    'serviceman_wallet' => [
        'wallet_feature_disabled' => 'Wallet feature currently disabled. Turn it on in Settings > Activation.',
        'top_up_permission_denied' => 'Permission Denied',
        'payment_module_not_found' => 'Payment module class or method not found.',
        'invalid_payment_method' => 'Selected payment method is invalid',
        'min_withdraw_amount' => 'Make sure your requested amount is at least :minWithdrawAmount.',
        'insufficient_wallet_balance' => 'Your wallet balance is not enough to process this withdrawal.',
        'withdraw_request_submitted' => 'Withdraw request successfully generated',
        'add_payment_account_before_withdrawal' => 'Please create a payment account before applying for a withdrawal.',
        'add_paypal_email_before_withdrawal' => 'Please add a paypal email before applying for a withdrawal.',
        'add_bank_details_before_withdrawal' => 'Please complete a bank detail before applying for a withdrawal.',
        'roles_destroy' => 'Roles Deleted Successfully',
        'status_updated' => 'Status updated successfully',
        'wallet_destroy' => 'Wallet deleted successfully',
        'wallet_updated' => 'Wallet Updated Successfully.',
        'wallet_stored' => 'Wallet Created Successfully.',
        'insufficient_balance' => 'Balance is not sufficient for this withdrawal.',
        'debited' => 'Debited Successfully',
        'credited' => 'Credited Successfully',
        'user_must_be_provider' => 'user must be Provider',
        'select_provider_first' => 'Please select provider first',
    ],

    'blog_category' => [
        'blog_category_store' => 'Category Created Successfully.',
        'blog_category_updated' => 'Blog Category Updated Successfully.',
        'is_feature_updated' => 'Is Featured Updated Successfully',
    ],

    'withdrawal_request' => [
        'add_payment_account_before_withdrawal' => 'Please create a payment account before applying for a withdrawal.',
        'add_paypal_email_before_withdrawal' => 'Please add a PayPal email before applying for a withdrawal.',
        'complete_bank_details_before_withdrawal' => 'Please complete your bank details before applying for a withdrawal.',
        'amount_must_be' => 'The requested amount must be at least :minWithdrawAmount.',
        'insufficient_wallet_balance' => 'Your wallet balance is insufficient for this withdrawal.',
        'request_sent' => 'Request Sent Successfully.',
        'withdraw_request_success' => 'Successfully :withdrawRequestStatus Request',
    ],

    'additional_service_invalid_with_id' => 'The selected additional service with ID :id is invalid.',

    'service_request' => [
        'service_requests' => 'Custom Jobs',
        'amount' => 'Amount',
        'create' => 'Create Service Request',
        'provider_name' => 'Provider Name',
        'provider_email' => 'Provider Email',
        'description' => 'Description',
        'status' => 'Status',
        'destroy' => 'Service Request Deleted successfully',
        'service_not_found' => 'Request Not Found!'
    ],
    'notify_templates' => [
        'notify_templates' => 'Notify Templates',
    ],
   'email_templates' => [
    'email' => 'Email',
    'email_templates' => 'Email Templates',
    'title' => 'Title',
    'content' => 'Content',
    'btn_text' => 'Button Text',
    'url' => 'URL',
    'save' => 'Save',
    'enter_title' => 'Enter Title',
    'enter_content' => 'Enter Content',
    'enter_btn_text' => 'Enter Button Text',
    'enter_url' => 'Enter URL',
    'edit' => 'Edit',
    'no_templates_available' => 'No templates available.',
    'name' => 'Name',
    'description' => 'Description',
    'actions' => 'Actions',
    'no_buttons_found' => 'No Buttons Found',
    'no_languages_found' => 'No Languages Found',
    '3_min_ago' => '3 minutes ago',
],
'sms_templates' => [
    'sms' => 'Sms',
    'sms_templates' => 'Sms Templates',
    'title' => 'Title',
    'content' => 'Content',
    'btn_text' => 'Button Text',
    'url' => 'URL',
    'save' => 'Save',
    'enter_title' => 'Enter Title',
    'enter_content' => 'Enter Content',
    'enter_btn_text' => 'Enter Button Text',
    'enter_url' => 'Enter URL',
    'edit' => 'Edit',
    'no_templates_available' => 'No templates available.',
    'name' => 'Name',
    'description' => 'Description',
    'actions' => 'Actions',
    'no_buttons_found' => 'No Buttons Found',
    'no_languages_found' => 'No Languages Found',
    '3_min_ago' => '3 minutes ago',
    'add_shortcode' => 'Add Shortcodes'
],
'push_notification_templates' => [
    'push_notification' => 'Push Notification',
    'push_notification_templates' => 'Push Notification Templates',
    'title' => 'Title',
    'content' => 'Content',
    'btn_text' => 'Button Text',
    'url' => 'URL',
    'save' => 'Save',
    'enter_title' => 'Enter Title',
    'enter_content' => 'Enter Content',
    'enter_btn_text' => 'Enter Button Text',
    'enter_url' => 'Enter URL',
    'edit' => 'Edit',
    'no_templates_available' => 'No templates available.',
    'name' => 'Name',
    'description' => 'Description',
    'actions' => 'Actions',
    'no_buttons_found' => 'No Buttons Found',
    'no_languages_found' => 'No Languages Found',
    '3_min_ago' => '3 minutes ago',
],
'custom_sms_gateways' =>
  [
    'custom_sms_gateways' => 'Custom SMS Gateways',
    'base_url' => 'Base Url',
    'enter_base_url' => 'Enter Base Url',
    'method' => 'Method',
    'select_method' => 'Select Method',
    'configs' => 'Configs',
    'enter_key' => 'Enter Key',
    'enter_value' => 'Enter Value',
    'sid' => 'ID',
    'enter_sid' => 'Enter ID',
    'auth_token' => 'Auth Token',
    'enter_auth_token' => 'Enter Auth Token',
    'from' => 'From',
    'enter_from' => 'Enter From',
    'select_is_config' => 'Select Configs',
    'custom_keys' => 'Custom Keys',
    'send_test_sms' => 'Send Test SMS',
    'phone_number' => 'Phone Number',
    'enter_phone_number' => 'Enter Phone Number',
    'message' => 'Message',
    'test_sms_message' => 'This is a test SMS message.',
    'close' => 'Close',
    'send_sms' => 'Send SMS',
  ],
'user_dashboard' => [
    'provider_details' => 'Provider Details',
    'customer_details' => 'Customer Details',
    'serviceman_details' => 'Serviceman Details',
    'general_info' => 'General Information',
    'services' => 'Services',
    'subscriptions' => 'Subscriptions',
    'bookings' => 'Bookings',
    'servicemen' => 'Servicemen',
    'withdraw_request' => 'Withdraw Request',
    'bank_details' => 'Bank Details',
    'reviews' => 'Reviews',
    'transactions' => 'Transactions',
    'ratings' => 'Ratings',
    'documents_list' => 'Documents List',
    'notifications' => 'Notifications',
    'availability' => 'Availability',
    'service_areas' => 'Service Areas',
    'shortcode_button' => 'Shortcode Button',
    'total_bookings' => 'Total Bookings',
    'total_servicemen' => 'Total Servicemen',
    'total_services' => 'Total Services',
    'wallet_balance' => 'Wallet Balance',
    'commission_history' => 'Commission History',
    'booking_status' => 'Booking Status',
    'provider_name' => 'Provider Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'country' => 'Country',
    'city' => 'City',
    'state' => 'State',
    'bank_name' => 'Bank Name',
    'holder_name' => 'Holder Name',
    'account_number' => 'Account Number',
    'branch_name' => 'Branch Name',
    'ifsc_code' => 'IFSC Code',
    'swift_code' => 'SWIFT Code',
    'not_found' => 'Not Found',
    'consumer_details' => 'Consumer Details',
    'pending' => 'Pending',
    'on_going' => 'On Going',
    'on_the_way' => 'On The Way',
    'completed' => 'Completed',
    'cancel' => 'Cancel',
    'on_hold' => 'On Hold',
],


'unverfied_users' => [
    'unverfied_users' => 'Unverified Users',
    'unverfied_provider' => 'Unverified Provider',
    'unverfied_serviceman' => 'Unverified Serviceman',
    'unverfied_consumer' => 'Unverified Customer',
],

    'auth' => [
        'password_has_been_changed' => 'Your password has been updated successfully.',
    ],

];