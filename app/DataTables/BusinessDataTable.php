<?php

namespace App\DataTables;

use App\Models\Business;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Services\DataTable;

class BusinessDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('name', function ($row) {
                return '<a href="'.route('backend.businesses.show', $row->business_uuid).'">'.$row->name.'</a>';
            })
            ->editColumn('category', function ($row) {
                return ucfirst($row->category);
            })
            ->editColumn('location', function ($row) {
                return $row->location;
            })
            ->editColumn('contact_info', function ($row) {
                return '<div class="contact-info">
                    <div class="phone">'.$row->phone.'</div>
                    <div class="email">'.$row->email.'</div>
                </div>';
            })
            ->editColumn('reviews', function ($row) {
                $reviews = $row->reviews;
                $totalReviews = count($reviews);
                $avgRating = $totalReviews > 0 ? 
                    array_sum(array_column($reviews, 'rating')) / $totalReviews : 0;
                
                return '<div class="reviews-info">
                    <div class="total">'.$totalReviews.' reviews</div>
                    <div class="rating">'.number_format($avgRating, 1).' ★</div>
                </div>';
            })
            ->addColumn('action', function ($row) {
                return '<a href="'.route('backend.businesses.show', $row->business_uuid).'" 
                          class="btn btn-sm btn-info">
                          <i data-feather="eye"></i>
                       </a>';
            })
            ->rawColumns(['name', 'contact_info', 'reviews', 'action']);
    }

    public function query(Business $model): QueryBuilder
    {
        return $model->newQuery();
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('businesses-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->parameters([
                'language' => [
                    'emptyTable' => __('static.no_records_found'),
                    'infoEmpty' => '',
                    'zeroRecords' => __('static.no_records_found'),
                ],
                'drawCallback' => 'function(settings) {
                    if (settings._iRecordsDisplay === 0) {
                        $(settings.nTableWrapper).find(".dataTables_paginate").hide();
                    } else {
                        $(settings.nTableWrapper).find(".dataTables_paginate").show();
                    }
                    feather.replace();
                }',
                'order' => [[0, 'desc']],
            ]);
    }

    protected function getColumns(): array
    {
        return [
            ['data' => 'name', 'name' => 'name', 'title' => __('static.business.name')],
            ['data' => 'category', 'name' => 'category', 'title' => __('static.business.category')],
            ['data' => 'location', 'name' => 'location', 'title' => __('static.business.location')],
            ['data' => 'contact_info', 'name' => 'phone', 'title' => __('static.business.contact_info'), 'orderable' => false],
            ['data' => 'reviews', 'name' => 'reviews', 'title' => __('static.business.reviews'), 'orderable' => false],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('static.action'),
                'orderable' => false,
                'searchable' => false,
                'width' => '100px'
            ]
        ];
    }

    protected function filename(): string
    {
        return 'Businesses_'.date('YmdHis');
    }
} 