<?php

namespace App\DataTables;

use App\Models\JobBooking;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Services\DataTable;

class JobBookingDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('job_uuid', function ($row) {
                $projectCode = \App\Helpers\ProjectCodeGenerator::fromUuid($row->job_uuid);
                return '<span class="text-primary fw-medium" title="'.$row->job_uuid.'">'.$projectCode.'</span>';
            })
            ->editColumn('contact_name', function ($row) {
                return '<div class="contact-info">
                    <div class="name">'.$row->contact_name.'</div>
                    <small class="text-muted d-block">'.$row->contact_email.'</small>
                    <small class="text-muted">'.$row->contact_phone.'</small>
                </div>';
            })
            ->editColumn('service_category', fn($row) => ucfirst($row->service_category ?? '-'))
            ->editColumn('status', function ($row) {
                $statusClasses = [
                    'pending' => 'bg-warning-subtle text-warning',
                    'in_progress' => 'bg-primary-subtle text-primary',
                    'completed' => 'bg-success-subtle text-success',
                    'scheduled' => 'bg-purple-subtle text-purple'
                ];
                $class = $statusClasses[$row->status] ?? 'bg-secondary-subtle text-secondary';
                return '<span class="badge rounded-pill ' . $class . '">' . 
                       ucfirst(str_replace('_', ' ', $row->status)) . '</span>';
            })
            ->editColumn('schedule_date', fn($row) => date('Y-m-d', strtotime($row->schedule_date)))
            ->addColumn('location', function ($row) {
                // Format the full address
                $fullAddress = $row->address;
                
                // Format the city, state, zip in a single line
                $cityStateZip = implode(', ', array_filter([
                    $row->city,
                    $row->state . ($row->zip_code ? ' ' . $row->zip_code : '')
                ]));

                return '<div class="location-info">
                    <div class="fw-medium">' . $fullAddress . '</div>
                    <div class="text-muted">' . $cityStateZip . '</div>
                </div>';
            })
            ->addColumn('action', function ($row) {
                return '
                    <div class="d-flex gap-2">
                        <a href="'.route('backend.job-bookings.show', $row->job_uuid).'" class="btn btn-sm btn-info" title="View Details">
                            <i data-feather="eye"></i>
                        </a>
                        <a href="'.route('backend.job-bookings.sendToProviderPage', $row->job_uuid).'" class="btn btn-sm btn-primary" title="Send to Providers">
                            <i data-feather="send"></i>
                        </a>
                    </div>';
            })
            ->rawColumns(['job_uuid', 'contact_name', 'location', 'status', 'action'])
            ->filter(function ($query) {
                if (request()->has('search') && request()->get('search')['value']) {
                    $searchValue = request()->get('search')['value'];
                    $query->where(function($q) use ($searchValue) {
                        $q->where('contact_name', 'like', "%{$searchValue}%")
                          ->orWhere('contact_email', 'like', "%{$searchValue}%")
                          ->orWhere('contact_phone', 'like', "%{$searchValue}%")
                          ->orWhere('address', 'like', "%{$searchValue}%")
                          ->orWhere('city', 'like', "%{$searchValue}%")
                          ->orWhere('state', 'like', "%{$searchValue}%")
                          ->orWhere('zip_code', 'like', "%{$searchValue}%")
                          ->orWhere('job_uuid', 'like', "%{$searchValue}%")
                          ->orWhere('service_category', 'like', "%{$searchValue}%");
                    });
                }
            });
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(JobBooking $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('job-bookings-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('<"d-flex justify-content-between align-items-center mb-3"<"d-flex align-items-center"l><"d-flex align-items-center gap-2"f>>rt<"d-flex justify-content-between align-items-center mt-3"<"text-muted"i><"d-flex align-items-center"p>>')
            ->orderBy(5, 'desc')
            ->parameters([
                'pageLength' => 10,
                'lengthMenu' => [[10, 25, 50, -1], [10, 25, 50, 'All']],
                'drawCallback' => 'function() { 
                    feather.replace();
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll(\'[title]\'));
                    tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }',
                'language' => [
                    'emptyTable' => __('static.no_records_found'),
                    'zeroRecords' => __('static.no_records_found'),
                    'paginate' => [
                        'previous' => '<i class="bi bi-chevron-left"></i>',
                        'next' => '<i class="bi bi-chevron-right"></i>'
                    ],
                    'lengthMenu' => 'Show _MENU_ entries',
                    'info' => 'Showing _START_ to _END_ of _TOTAL_ entries'
                ],
                'initComplete' => 'function() {
                    var searchInput = $(this).closest(".card").find(".dataTables_filter input");
                    searchInput.addClass("form-control");
                    searchInput.attr("placeholder", "Search by location, contact info...");

                    // Add custom styles
                    var style = document.createElement("style");
                    style.textContent = `
                        .location-info {
                            line-height: 1.4;
                        }
                        .location-info .fw-medium {
                            color: #344767;
                        }
                        .location-info .text-muted {
                            font-size: 0.875rem;
                        }
                    `;
                    document.head.appendChild(style);
                }',
                'classes' => [
                    'table' => 'table align-middle table-hover border-top',
                    'thead' => [
                        'bg-primary text-white'
                    ]
                ]
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    protected function getColumns(): array
    {
        return [
            [
                'data' => 'job_uuid',
                'name' => 'job_uuid',
                'title' => 'Project ID',
                'searchable' => true,
            ],
            [
                'data' => 'contact_name',
                'name' => 'contact_name',
                'title' => 'Contact Info',
                'searchable' => true,
                'orderable' => true,
            ],
            [
                'data' => 'service_category',
                'name' => 'service_category',
                'title' => 'Service Type',
                'searchable' => true,
            ],
            [
                'data' => 'location',
                'name' => 'address',
                'title' => 'Location',
                'searchable' => true,
                'orderable' => false,
            ],
            [
                'data' => 'status',
                'name' => 'status',
                'title' => 'Status',
                'searchable' => true,
            ],
            [
                'data' => 'schedule_date',
                'name' => 'schedule_date',
                'title' => 'Date',
                'width' => '120px',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => 'Actions',
                'orderable' => false,
                'searchable' => false,
                'width' => '200px',
            ],
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'JobBookings_' . date('YmdHis');
    }
} 