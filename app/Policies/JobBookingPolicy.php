<?php

namespace App\Policies;

use App\Models\JobBooking;
use App\Models\User;
use App\Enums\RoleEnum;
use Illuminate\Auth\Access\HandlesAuthorization;

class JobBookingPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any job bookings.
     */
    public function viewAny(User $user): bool
    {
        // Admins can view all job bookings
        if ($user->hasRole(RoleEnum::ADMIN) || $user->hasRole(RoleEnum::PROVIDER)) {
            return true;
        }

        // Consumers and providers can view job bookings (filtered by their own)
        return $user->hasRole(RoleEnum::CONSUMER);
    }

    /**
     * Determine whether the user can view the job booking.
     */
    public function view(User $user, JobBooking $jobBooking): bool
    {
        // Admins can view any job booking
        if ($user->hasRole(RoleEnum::ADMIN)) {
            return true;
        }

        // Consumers can view their own job bookings
        if ($user->hasRole(RoleEnum::CONSUMER) && $jobBooking->user_id === $user->id) {
            return true;
        }

        // Providers can view job bookings they have bid on, are assigned to, or that allow bidding
        if ($user->hasRole(RoleEnum::PROVIDER)) {
            // Check if provider has bid on this job booking
            $hasBid = $jobBooking->bids()->where('provider_id', $user->id)->exists();

            // Check if provider is assigned to this job booking
            $isAssigned = $jobBooking->isAssigned() &&
                         $jobBooking->acceptedBid() &&
                         $jobBooking->acceptedBid()->provider_id === $user->id;

            // Check if job booking allows bidding (open for new bids)
            $allowsBidding = $jobBooking->allowsBidding();

            return $hasBid || $isAssigned || $allowsBidding;
        }

        return false;
    }

    /**
     * Determine whether the user can create job bookings.
     */
    public function create(User $user): bool
    {
        // Only consumers can create job bookings
        return $user->hasRole(RoleEnum::CONSUMER);
    }

    /**
     * Determine whether the user can update the job booking.
     */
    public function update(User $user, JobBooking $jobBooking): bool
    {
        // Admins can update any job booking
        if ($user->hasRole(RoleEnum::ADMIN)) {
            return true;
        }

        // Consumers can update their own job bookings if not in final status
        if ($user->hasRole(RoleEnum::CONSUMER) && 
            $jobBooking->user_id === $user->id && 
            !$jobBooking->isFinal()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the job booking.
     */
    public function delete(User $user, JobBooking $jobBooking): bool
    {
        // Admins can delete any job booking
        if ($user->hasRole(RoleEnum::ADMIN)) {
            return true;
        }

        // Consumers can delete their own job bookings if in pending status
        if ($user->hasRole(RoleEnum::CONSUMER) && 
            $jobBooking->user_id === $user->id && 
            $jobBooking->isPending()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can accept bids on the job booking.
     */
    public function acceptBid(User $user, JobBooking $jobBooking): bool
    {
        // Only the job booking owner (consumer) can accept bids
        if (!$user->hasRole(RoleEnum::CONSUMER) || $jobBooking->user_id !== $user->id) {
            return false;
        }

        // Job booking must allow bidding and have pending bids
        return $jobBooking->canAcceptBids();
    }

    /**
     * Determine whether the user can reject bids on the job booking.
     */
    public function rejectBid(User $user, JobBooking $jobBooking): bool
    {
        // Only the job booking owner (consumer) can reject bids
        if ($jobBooking->user_id !== $user->id) {
            return false;
        }

        // Add any other conditions, e.g., job booking must be in a state where bids can be rejected.
        return true; // Or more specific logic like $jobBooking->canRejectBids();
    }

    /**
     * Determine whether the user can cancel the job booking.
     */
    public function cancel(User $user, JobBooking $jobBooking): bool
    {
        // Admins can cancel any job booking
        if ($user->hasRole(RoleEnum::ADMIN)) {
            return true;
        }

        // Consumers can cancel their own job bookings if cancellable
        if ($user->hasRole(RoleEnum::CONSUMER) && 
            $jobBooking->user_id === $user->id && 
            $jobBooking->canCancel()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view bids for the job booking.
     */
    public function viewBids(User $user, JobBooking $jobBooking): bool
    {
        // Admins can view bids for any job booking
        if ($user->hasRole(RoleEnum::ADMIN)) {
            return true;
        }

        // Consumers can view bids for their own job bookings
        if ($user->hasRole(RoleEnum::CONSUMER) && $jobBooking->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the job booking.
     */
    public function restore(User $user, JobBooking $jobBooking): bool
    {
        return $user->hasRole(RoleEnum::ADMIN);
    }

    /**
     * Determine whether the user can permanently delete the job booking.
     */
    public function forceDelete(User $user, JobBooking $jobBooking): bool
    {
        return $user->hasRole(RoleEnum::ADMIN);
    }
}
