<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;

class MailConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Get settings from database or environment
        $mailSettings = [
            'driver'     => env('MAIL_MAILER', 'smtp'),
            'host'       => env('MAIL_HOST', 'smtp.gmail.com'),
            'port'       => env('MAIL_PORT', 587),
            'username'   => env('MAIL_USERNAME'),
            'password'   => env('MAIL_PASSWORD'),
            'encryption' => env('MAIL_ENCRYPTION', 'tls'),
            'from'       => [
                'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'name'    => env('MAIL_FROM_NAME', 'MaidProfit')
            ],
        ];

        // Set mail configuration
        Config::set('mail', array_merge(
            Config::get('mail', []), 
            $mailSettings
        ));
    }
}
