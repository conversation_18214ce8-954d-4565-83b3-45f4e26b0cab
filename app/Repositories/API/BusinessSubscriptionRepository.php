<?php

namespace App\Repositories\API;

use App\Exceptions\ExceptionHandler;
use App\Models\Business;
use App\Models\BusinessSubscription;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Modules\Subscription\Entities\Plan;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Eloquent\BaseRepository;

class BusinessSubscriptionRepository extends BaseRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return BusinessSubscription::class;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Assign a subscription plan to a business
     *
     * @param string $businessUuid
     * @param int $planId
     * @param int $providerId
     * @return BusinessSubscription
     */
    public function assignPlanToBusiness(string $businessUuid, int $planId, int $providerId): BusinessSubscription
    {
        DB::beginTransaction();

        try {
            $business = Business::where('business_uuid', $businessUuid)->firstOrFail();
            $plan = Plan::findOrFail($planId);
            
            // Deactivate any existing subscription
            $existingSubscription = BusinessSubscription::where('business_uuid', $business->business_uuid)
                ->where('is_active', true)
                ->first();

            if ($existingSubscription) {
                $existingSubscription->update(['is_active' => false]);
            }

            // Create new subscription
            $subscription = new BusinessSubscription([
                'business_uuid' => $business->business_uuid,
                'plan_id' => $plan->id,
                'provider_id' => $providerId,
                'start_date' => now()->toDateString(),
                'total' => $plan->price,
                'allowed_max_services' => $plan->max_services,
                'allowed_max_addresses' => $plan->max_addresses,
                'allowed_max_servicemen' => $plan->max_servicemen,
                'allowed_max_service_packages' => $plan->max_service_packages,
                'is_active' => true,
            ]);

            // Calculate end date based on duration
            $subscription->end_date = $subscription->calculateEndDate($plan->duration);
            $subscription->save();

            DB::commit();

            return $subscription;
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Cancel a business subscription plan
     *
     * @param string $businessUuid
     * @return bool
     */
    public function cancelBusinessPlan(string $businessUuid): bool
    {
        DB::beginTransaction();

        try {
            $subscription = BusinessSubscription::where('business_uuid', $businessUuid)
                ->where('is_active', true)
                ->firstOrFail();

            $subscription->update(['is_active' => false]);

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Get active subscription for a business
     *
     * @param string $businessUuid
     * @return BusinessSubscription|null
     */
    public function getActiveSubscription(string $businessUuid): ?BusinessSubscription
    {
        try {
            return BusinessSubscription::with('plan')
                ->where('business_uuid', $businessUuid)
                ->where('is_active', true)
                ->first();
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Check if a business has an active subscription
     *
     * @param string $businessUuid
     * @return bool
     */
    public function hasActiveSubscription(string $businessUuid): bool
    {
        try {
            $subscription = BusinessSubscription::where('business_uuid', $businessUuid)
                ->where('is_active', true)
                ->where('end_date', '>=', Carbon::now()->toDateString())
                ->first();

            return $subscription !== null;
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * List all business subscriptions with pagination
     *
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function listSubscriptions(int $perPage = 10)
    {
        try {
            return BusinessSubscription::with(['plan', 'business', 'provider'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 500);
        }
    }
}