<?php

namespace App\Services;

use Illuminate\Support\Str;

class ProjectCodeGenerator
{
    /**
     * Generate a project code from a UUID
     * 
     * @param string $uuid
     * @return string
     */
    public function generateProjectCode(string $uuid): string
    {
        // Remove dashes and convert to base64
        $code = base64_encode(hex2bin(str_replace('-', '', $uuid)));
        
        // Remove padding characters and make URL safe
        $code = rtrim($code, '=');
        $code = strtr($code, '+/', '-_');
        
        // Add prefix for readability
        return 'JOB-' . $code;
    }

    /**
     * Decode a project code back to UUID
     * 
     * @param string $projectCode
     * @return string
     * @throws \InvalidArgumentException
     */
    public function decodeProjectCode(string $projectCode): string
    {
        try {
            // Remove prefix
            $code = str_replace('JOB-', '', $projectCode);
            
            // Make base64 safe and add padding
            $code = strtr($code, '-_', '+/');
            $code .= str_repeat('=', strlen($code) % 4);
            
            // Decode and convert back to UUID format
            $hex = bin2hex(base64_decode($code));
            return Str::uuid()->fromString($hex)->toString();
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Invalid project code format');
        }
    }
} 