<?php

namespace App\Services\ZipCode;

use App\Models\ZipCode as ZipCodeModel;

class ZipCode
{
    public function getZipCodesInRadiusRaw(string $zip, float $radius = 10): array
    {
        $origin = ZipCodeModel::where('zip_code', $zip)->first();
        if (!$origin || !$origin->lat || !$origin->lng) return [];

        $lat = (float)$origin->lat;
        $lng = (float)$origin->lng;

        $results = \DB::table('zip_codes')
            ->select('zip_code')
            ->selectRaw("(
                3959 * acos(
                    cos(radians(?)) *
                    cos(radians(lat)) *
                    cos(radians(lng) - radians(?)) +
                    sin(radians(?)) *
                    sin(radians(lat))
                )
            ) AS distance", [$lat, $lng, $lat])
            ->having('distance', '<=', $radius)
            ->orderBy('distance')
            ->get();

        return $results->pluck('zip_code')->toArray();
    }
}