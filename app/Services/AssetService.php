<?php

namespace App\Services;

use App\Models\Asset;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AssetService
{
    public function upload(
        UploadedFile $file,
        string $collectionName = null,
        array $customProperties = [],
        string $disk = null
    ): Asset {
        try {
            $fileName = $this->generateFileName($file);
            $filePath = $this->generateFilePath($fileName, $collectionName);
            
            $path = Storage::disk('public')->putFileAs(
                dirname($filePath),
                $file,
                basename($filePath)
            );

            if (!$path) {
                throw new \Exception('Failed to upload file');
            }

            return Asset::create([
                'uuid' => (string) Str::uuid(),
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $path,
                'file_key' => (string) Str::uuid(),
                'disk' => 'public',
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'collection_name' => $collectionName,
                'custom_properties' => $customProperties
            ]);

        } catch (\Exception $e) {
            \Log::error('Asset upload failed: ' . $e->getMessage());
            throw $e;
        }
    }

    protected function generateFileName(UploadedFile $file): string
    {
        return Str::uuid() . '.' . $file->getClientOriginalExtension();
    }

    protected function generateFilePath(string $fileName, ?string $collectionName): string
    {
        $basePath = date('Y/m/d');
        if ($collectionName) {
            $basePath = $collectionName . '/' . $basePath;
        }
        return $basePath . '/' . $fileName;
    }

    public function delete(Asset $asset): bool
    {
        if (Storage::disk('public')->exists($asset->file_path)) {
            Storage::disk('public')->delete($asset->file_path);
        }
        
        return $asset->delete();
    }
}