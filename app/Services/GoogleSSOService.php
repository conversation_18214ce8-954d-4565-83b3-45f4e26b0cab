<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class GoogleSSOService
{
    public function verifyAndGetUser(string $idToken): ?array
    {
        $googleApi = 'https://oauth2.googleapis.com/tokeninfo?id_token=' . $idToken;
        try {
            $response = Http::timeout(5)->get($googleApi);
            if ($response->ok()) {
                $data = $response->json();
                if (isset($data['email']) && isset($data['aud'])) {
                    return $data;
                }
            }
        } catch (\Exception $e) {
        }
        return null;
    }
} 