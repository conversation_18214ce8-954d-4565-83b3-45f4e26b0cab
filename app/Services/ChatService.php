<?php

namespace App\Services;

use App\Models\Chat;
use App\Models\Message;
use Illuminate\Support\Facades\Auth;
use App\Events\MessageSent;
use MongoDB\BSON\ObjectId;

class ChatService
{
    public function getOrCreateChat($userId)
    {
        $authId = Auth::id();
        $participants = [(int)$authId, (int)$userId];
        sort($participants);

        $chat = Chat::where('type', 'private')
            ->where('participants', 'all', $participants)
            ->where('participants', 'size', 2)
            ->first();

        if (!$chat) {
            $chat = Chat::create([
                'type' => 'private',
                'participants' => $participants,
            ]);
        }
        return $chat;
    }

    public function sendMessage($chatId, $data)
    {
        $userId = (int)Auth::id();
        $message = Message::create([
            'chat_id' => new ObjectId($chatId),
            'sender_id' => $userId,
            'type' => $data['type'] ?? 'text',
            'message' => $data['message'] ?? null,
            'media_url' => $data['media_url'] ?? null,
            'read_by' => [(int)$userId],
        ]);
   
        return $message;
    }

    public function getMessages($chatId, $search = null, $perPage = 20)
    {
        $query = Message::where('chat_id', new ObjectId($chatId))->orderBy('created_at', 'desc');
        if ($search) {
            $query->where('message', 'like', "%$search%");
        }
        return $query->paginate($perPage);
    }

    public function markAsRead($chatId)
    {
        $userId = (int)Auth::id();

        Message::where('chat_id', new ObjectId($chatId))
            ->where(function($query) use ($userId) {
                $query->where('read_by', '!=', $userId)
                    ->orWhereNull('read_by');
            })
            ->update([
                '$addToSet' => ['read_by' => $userId]
            ]);
    }

    public function deleteMessage($messageId)
    {
        $message = Message::findOrFail(new ObjectId($messageId));
        if ($message->sender_id != Auth::id()) {
            abort(403, 'Not allowed');
        }
        $message->delete();
    }

    /**
     * Get or create a chat with a provider based on business UUID
     *
     * @param string $businessUuid
     * @return \App\Models\Chat
     * @throws \Exception
     */
    public function getOrCreateChatByBusinessUuid($businessUuid)
    {
        $authId = (int)Auth::id();
        
        // Find the business by UUID
        $business = \App\Models\Business::where('business_uuid', $businessUuid)->first();
        
        if (!$business) {
            throw new \Exception('Business not found', 404);
        }
        
        // Find the provider associated with this business
        $provider = \App\Models\User::where('business_uuid', $businessUuid)->first();
        
        if (!$provider) {
            throw new \Exception('No provider associated with this business', 404);
        }
        
        // Create or get chat with this provider
        return $this->getOrCreateChat($provider->id);
    }
}