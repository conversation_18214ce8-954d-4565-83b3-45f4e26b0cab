<?php

namespace App\Helpers;

class ProjectCodeGenerator
{
    /**
     * Convert UUID to a friendly project code
     * Format: PRJ-XXXXX (where X is alphanumeric)
     */
    public static function fromUuid(string $uuid): string
    {
        // Take first 5 characters after removing hyphens
        $code = substr(str_replace('-', '', $uuid), 0, 5);
        
        // Convert to uppercase and add prefix
        return 'PRJ-' . strtoupper($code);
    }

    /**
     * Get original UUID from project code
     */
    public static function toUuid(string $projectCode): ?string
    {
        // Remove prefix and convert to lowercase
        $code = strtolower(str_replace('PRJ-', '', $projectCode));
        
        // Find UUID that matches this code
        return \App\Models\JobBooking::where('job_uuid', 'LIKE', $code . '%')->value('job_uuid');
    }
} 