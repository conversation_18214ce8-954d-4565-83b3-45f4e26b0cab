<?php

namespace App\Notifications;

use App\Enums\RoleEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\User;

class CertificateStatusNotification extends Notification
{
    use Queueable;

    private $user;
    private $action; // request_review, approved, rejected

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, string $action)
    {
        $this->user = $user;
        $this->action = $action;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        switch ($this->action) {
            case 'request_review':
                return (new MailMessage)
                    ->subject('Provider requested certificate review')
                    ->greeting('Hello Admin!')
                    ->line('Provider "' . $this->user->name . '" has requested a certificate review.')
                    ->action('View Provider', url('/admin/users/' . $this->user->id))
                    ->line('Please review the certificates.');
            case 'approved':
                return (new MailMessage)
                    ->subject('Your certificates have been approved')
                    ->greeting('Hello ' . $this->user->name . '!')
                    ->line('Your certificates have been approved by admin.')
                    ->line('Thank you for your cooperation.');
            case 'rejected':
                return (new MailMessage)
                    ->subject('Your certificates have been rejected')
                    ->greeting('Hello ' . $this->user->name . '!')
                    ->line('Your certificates have been rejected by admin.')
                    ->line('Please update your certificates and request review again.');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'action' => $this->action,
        ];
    }
} 