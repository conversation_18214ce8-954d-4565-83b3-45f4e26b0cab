<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\Business;

class ImportBusinessesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 0;
    public $tries = 1;

    protected $filePath;
    protected $userId;

    public function __construct($filePath, $userId)
    {
        $this->filePath = $filePath;
        $this->userId = $userId;
    }

    public function handle()
    {
        try {
            $fullPath = storage_path('app/public/' . $this->filePath);
            if (!file_exists($fullPath)) {
                Log::error('ImportBusinessesJob: File not found', ['path' => $fullPath]);
                return;
            }
            $handle = fopen($fullPath, 'r');
            if ($handle === false) {
                Log::error('ImportBusinessesJob: Cannot open file', ['path' => $fullPath]);
                return;
            }
            $buffer = '';
            while (($line = fgets($handle)) !== false) {
                $buffer .= $line;
            }
            fclose($handle);
            $json = json_decode($buffer, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('ImportBusinessesJob: Invalid JSON', ['error' => json_last_error_msg(), 'file' => $this->filePath]);
                return;
            }
            $businesses = isset($json[0]) ? $json : [$json];
            foreach ($businesses as $item) {
                if (!isset($item['name']) || !isset($item['address'])) {
                    Log::warning('ImportBusinessesJob: Missing required fields', ['item' => $item]);
                    continue;
                }
                Business::create([
                    'name' => $item['name'],
                    'address' => isset($item['address']) ? $this->cleanString($item['address']) : null,
                    'phone' => isset($item['phone']) ? $this->cleanString($item['phone']) : null,
                    'website' => $item['website'] ?? null,
                    'category' => $item['category'] ?? null,
                    'location' => $item['location'] ?? null,
                    'email' => $item['email'] ?? null,
                    'hours' => $item['hours'] ?? [],
                    'photos' => $item['photos'] ?? [],
                    'services' => $item['services'] ?? [],
                    'reviews' => $item['reviews'] ?? [],
                    'lat' => isset($item['lat']) ? $item['lat'] : null,
                    'lng' => isset($item['lng']) ? $item['lng'] : null,
                ]);
            }
            Log::info('ImportBusinessesJob: Import completed', ['user_id' => $this->userId, 'file' => $this->filePath]);
        } catch (\Throwable $e) {
            Log::error('ImportBusinessesJob: Exception', [
                'message' => $e->getMessage(),
                'file' => $this->filePath,
                'user_id' => $this->userId,
            ]);
        }
    }

    protected function cleanString($value)
    {
        return preg_replace('/[^\w\s\+\-\.,\(\)]/u', '', $value);
    }
} 