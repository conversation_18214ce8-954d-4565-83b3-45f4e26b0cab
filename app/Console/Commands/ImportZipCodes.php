<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ZipCode;
use Illuminate\Support\Facades\DB;

class ImportZipCodes extends Command
{
    protected $signature = 'zipcode:import';
    protected $description = 'Import zip codes from CSV to database';

    public function handle()
    {
        $csv = app_path('Services/ZipCode/zipcodes.csv');
        if (!file_exists($csv)) {
            $this->error("File not found: $csv");
            return 1;
        }

        $lines = file($csv, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $bar = $this->output->createProgressBar(count($lines));
        $bar->start();

        DB::beginTransaction();
        try {
            foreach ($lines as $line) {
                [$zip, $lat, $lng] = str_getcsv($line);
                ZipCode::updateOrCreate(
                    ['zip_code' => $zip],
                    ['lat' => (string)$lat, 'lng' => (string)$lng]
                );
                $bar->advance();
            }
            DB::commit();
            $bar->finish();
            $this->info("\nImported successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
        return 0;
    }
} 