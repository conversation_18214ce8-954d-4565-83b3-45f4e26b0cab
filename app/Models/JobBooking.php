<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use App\Services\ProjectCodeGenerator;
use App\Enums\JobBookingStatusEnum;
use App\Enums\BidStatusEnum;
use App\Events\JobBookingStatusChangedEvent;
use App\Events\JobBookingBidAcceptedEvent;
use App\Notifications\BidAcceptedNotification;
use App\Notifications\BidRejectedNotification;
use App\Notifications\JobBookingAssignedNotification;

class JobBooking extends Model
{
    use HasFactory;

    protected $table = 'job_bookings';

    protected $fillable = [
        'job_uuid',
        'project_code',
        'job_type',
        'property_type',
        'service_category',
        'service_tasks',
        'description',
        'schedule_date',
        'time_preference',
        'frequency',
        'recurring_frequency',
        'address',
        'city',
        'state',
        'zip_code',
        'contact_name',
        'contact_email',
        'contact_phone',
        'status',
        'user_id'
    ];

    protected $casts = [
        'schedule_date' => 'date',
        'service_tasks' => 'array',
    ];

    /**
     * Get the service tasks
     * 
     * @return array
     */
    public function getServiceTasksAttribute($value)
    {
        if (is_string($value)) {
            return json_decode($value, true) ?: [];
        }
        return is_array($value) ? $value : [];
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($job) {
            $job->job_uuid = (string) Str::uuid();

            // Generate project code
            $projectCodeGenerator = new ProjectCodeGenerator();
            $job->project_code = $projectCodeGenerator->generateProjectCode($job->job_uuid);

            // Set status based on whether job booking has user_id
            if ($job->user_id) {
                // If job booking has a registered user, set status to open
                $job->status = JobBookingStatusEnum::OPEN->value;
            } else {
                // If job booking is from guest contact, set status to pending
                $job->status = JobBookingStatusEnum::PENDING->value;
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assets()
    {
        return $this->belongsToMany(Asset::class, 'job_booking_assets')
                    ->withTimestamps();
    }

    /**
     * Get the bids for this job booking
     */
    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class, 'job_booking_id');
    }

    /**
     * Get the bookings for this job booking
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'job_booking_id');
    }

    /**
     * Get the jobs for this job booking
     */
    public function jobs(): HasMany
    {
        return $this->hasMany(Job::class, 'job_booking_id');
    }

    /**
     * Get the accepted bid for this job booking
     */
    public function acceptedBid(): HasOne
    {
        return $this->hasOne(Bid::class, 'job_booking_id')->where('status', BidStatusEnum::ACCEPTED->value);
    }

    /**
     * Get the assigned job for this job booking (assuming one job per booking for simplicity here).
     */
    public function assignedJob(): HasOne
    {
        // This assumes a Job model has a 'job_booking_id' and is created when a bid is accepted.
        // You might need to adjust this based on your actual Job model structure and how it's linked.
        // If a job is linked via the accepted bid, the relationship might be more complex, e.g., HasOneThrough.
        // For now, let's assume a direct link for simplicity or that it's derived from the accepted bid's job.
        // If Job is created from a Bid, then it might be: return $this->hasOneThrough(Job::class, Bid::class, 'job_booking_id', 'bid_id', 'id', 'id')->where('bids.status', BidStatusEnum::ACCEPTED);
        // Or, if the Job model has a direct job_booking_id and is the *only* job:
        return $this->hasOne(Job::class, 'job_booking_id'); // This would get *any* job, not necessarily the *assigned* one from an accepted bid.
    }

    /**
     * Get pending bids for this job booking
     */
    public function pendingBids(): HasMany
    {
        return $this->bids()->where('status', BidStatusEnum::REQUESTED->value);
    }

    /**
     * Get the assigned booking for this job booking
     */
    public function assignedBooking()
    {
        return $this->bookings()->first();
    }

    // ==================== STATUS HELPER METHODS ====================

    /**
     * Check if job booking allows bidding
     */
    public function allowsBidding(): bool
    {
        return JobBookingStatusEnum::allowsBidding($this->status);
    }

    /**
     * Check if job booking is in final status
     */
    public function isFinal(): bool
    {
        return JobBookingStatusEnum::isFinal($this->status);
    }

    /**
     * Check if job booking is pending
     */
    public function isPending(): bool
    {
        return $this->status === JobBookingStatusEnum::PENDING->value;
    }

    /**
     * Check if job booking is open for bids
     */
    public function isOpen(): bool
    {
        return $this->status === JobBookingStatusEnum::OPEN->value;
    }

    /**
     * Check if job booking is assigned
     */
    public function isAssigned(): bool
    {
        return $this->status === JobBookingStatusEnum::ASSIGNED->value;
    }

    /**
     * Check if job booking is in progress
     */
    public function isInProgress(): bool
    {
        return $this->status === JobBookingStatusEnum::IN_PROGRESS->value;
    }

    /**
     * Check if job booking is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === JobBookingStatusEnum::COMPLETED->value;
    }

    /**
     * Check if job booking is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === JobBookingStatusEnum::CANCELLED->value;
    }

    /**
     * Check if job booking is expired
     */
    public function isExpired(): bool
    {
        return $this->status === JobBookingStatusEnum::EXPIRED->value;
    }

    // ==================== WORKFLOW TRANSITION METHODS ====================

    /**
     * Mark job booking as open for bidding
     */
    public function markAsOpen(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::OPEN->value)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::OPEN->value;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Assign job booking to a provider via accepted bid
     */
    public function markAsAssigned($bidId = null): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::ASSIGNED->value)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::ASSIGNED->value;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Mark job booking as in progress
     */
    public function markAsInProgress(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::IN_PROGRESS->value)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::IN_PROGRESS->value;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Mark job booking as completed
     */
    public function markAsCompleted(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::COMPLETED->value)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::COMPLETED->value;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Cancel job booking
     */
    public function cancel(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::CANCELLED->value)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::CANCELLED->value;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Mark job booking as expired
     */
    public function markAsExpired(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::EXPIRED->value)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::EXPIRED->value;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    // ==================== WORKFLOW VALIDATION METHODS ====================

    /**
     * Check if job booking can transition to given status
     */
    public function canTransitionTo(string $newStatus): bool
    {
        $allowedTransitions = JobBookingStatusEnum::getNextStatuses($this->status);
        return in_array($newStatus, $allowedTransitions);
    }

    /**
     * Check if job booking can accept bids
     */
    public function canAcceptBids(): bool
    {
        $allowsBidding = $this->allowsBidding();
        $hasPendingBids = $this->bids()->where('status', BidStatusEnum::REQUESTED->value)->exists();

        \Log::info('JobBooking canAcceptBids check', [
            'job_booking_id' => $this->id,
            'status' => $this->status,
            'allows_bidding' => $allowsBidding,
            'has_pending_bids' => $hasPendingBids,
            'requested_status_value' => BidStatusEnum::REQUESTED->value,
            'total_bids' => $this->bids()->count(),
            'requested_bids_count' => $this->bids()->where('status', BidStatusEnum::REQUESTED->value)->count()
        ]);

        return $allowsBidding && $hasPendingBids;
    }

    /**
     * Check if job booking can be cancelled
     */
    public function canCancel(): bool
    {
        return !$this->isFinal();
    }

    /**
     * Check if job booking can be marked as in progress
     */
    public function canMarkAsInProgress(): bool
    {
        return $this->isAssigned();
    }

    /**
     * Check if job booking can be completed
     */
    public function canComplete(): bool
    {
        return $this->isInProgress();
    }

    /**
     * Check if job booking can be expired
     */
    public function canExpire(): bool
    {
        return $this->isOpen();
    }

    /**
     * Get workflow status display name
     */
    public function getStatusDisplayName(): string
    {
        return match ($this->status) {
            JobBookingStatusEnum::PENDING->value => 'Pending Review',
            JobBookingStatusEnum::OPEN->value => 'Open for Bids',
            JobBookingStatusEnum::ASSIGNED->value => 'Assigned to Provider',
            JobBookingStatusEnum::IN_PROGRESS->value => 'Work in Progress',
            JobBookingStatusEnum::COMPLETED->value => 'Completed',
            JobBookingStatusEnum::CANCELLED->value => 'Cancelled',
            JobBookingStatusEnum::EXPIRED->value => 'Expired',
            default => 'Unknown Status',
        };
    }

    /**
     * Get available actions for current status
     */
    public function getAvailableActions(): array
    {
        $actions = [];

        if ($this->canTransitionTo(JobBookingStatusEnum::OPEN->value)) {
            $actions[] = 'open_for_bidding';
        }

        if ($this->canAcceptBids()) {
            $actions[] = 'accept_bid';
        }

        if ($this->canMarkAsInProgress()) {
            $actions[] = 'start_work';
        }

        if ($this->canComplete()) {
            $actions[] = 'complete';
        }

        if ($this->canCancel()) {
            $actions[] = 'cancel';
        }

        if ($this->canExpire()) {
            $actions[] = 'expire';
        }

        return $actions;
    }

    /**
     * Accept a bid for this job booking
     */
    public function acceptBid($bidId, $notes = null): bool
    {
        if (!$this->canAcceptBids()) {
            return false;
        }

        // Find the bid and ensure it exists and is in the correct status
        $bid = $this->bids()->where('id', $bidId)->where('status', BidStatusEnum::REQUESTED->value)->first();
        if (!$bid) {
            return false;
        }

        // Use database transaction to ensure data consistency
        return \DB::transaction(function () use ($bid, $bidId, $notes) {
            try {
                // Accept the bid
                $bid->status = BidStatusEnum::ACCEPTED->value;
                if (!$bid->save()) {
                    \Log::error('Failed to save bid status', ['bid_id' => $bid->id]);
                    return false;
                }

                // Get rejected bids before updating them
                $rejectedBids = $this->bids()->where('id', '!=', $bidId)->where('status', BidStatusEnum::REQUESTED->value)->get();

                // Reject all other bids
                $this->bids()->where('id', '!=', $bidId)->where('status', BidStatusEnum::REQUESTED->value)->update(['status' => BidStatusEnum::REJECTED->value]);

                // Create a Job record to link the booking and bid (optional)
                try {
                    // Check if business_jobs table exists before creating Job record
                    if (\Schema::hasTable('business_jobs')) {
                        $job = Job::create([
                            'job_booking_id' => $this->id,
                            'bid_id' => $bid->id,
                            'customer_id' => $this->user_id,
                            'provider_id' => $bid->provider_id,
                            'status' => 'assigned',
                            'agreed_amount' => $bid->amount,
                            'estimated_completion_time' => $bid->estimated_completion_time,
                            'notes' => $notes
                        ]);
                        \Log::info('Job record created successfully', ['job_id' => $job->id]);
                    } else {
                        \Log::info('business_jobs table does not exist, skipping Job creation');
                    }
                } catch (\Exception $jobException) {
                    \Log::warning('Failed to create Job record during bid acceptance (continuing anyway)', [
                        'job_booking_id' => $this->id,
                        'bid_id' => $bid->id,
                        'error' => $jobException->getMessage()
                    ]);
                    // Continue without creating Job record - bid acceptance should still work
                }

                // Fire bid accepted event
                event(new JobBookingBidAcceptedEvent($this, $bid));

                // Mark job booking as assigned
                if (!$this->markAsAssigned($bidId)) {
                    \Log::error('Failed to mark job booking as assigned', ['job_booking_id' => $this->id]);
                    return false;
                }

                // Dispatch notifications (outside transaction to avoid blocking)
                \DB::afterCommit(function () use ($bid, $rejectedBids) {
                    try {
                        // Notify the winning provider
                        $bid->provider->notify(new BidAcceptedNotification($bid));

                        // Notify the customer about assignment
                        $this->user->notify(new JobBookingAssignedNotification($this, $bid));

                        // Notify rejected providers
                        foreach ($rejectedBids as $rejectedBid) {
                            $rejectedBid->provider->notify(new BidRejectedNotification($rejectedBid));
                        }
                    } catch (\Exception $notificationException) {
                        // Log notification failures but don't fail the main operation
                        \Log::warning('Failed to send bid acceptance notifications: ' . $notificationException->getMessage());
                    }
                });

                return true;
            } catch (\Exception $e) {
                \Log::error('Failed to accept bid', [
                    'job_booking_id' => $this->id,
                    'bid_id' => $bidId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                return false;
            }
        });
    }

    // ==================== QUERY SCOPES ====================

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get open job bookings
     */
    public function scopeOpen($query)
    {
        return $query->where('status', JobBookingStatusEnum::OPEN->value);
    }

    /**
     * Scope to get pending job bookings
     */
    public function scopePending($query)
    {
        return $query->where('status', JobBookingStatusEnum::PENDING->value);
    }

    /**
     * Scope to get assigned job bookings
     */
    public function scopeAssigned($query)
    {
        return $query->where('status', JobBookingStatusEnum::ASSIGNED->value);
    }

    /**
     * Scope to get active job bookings (not final)
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', JobBookingStatusEnum::getActiveStatuses());
    }

    /**
     * Scope to get completed job bookings
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', JobBookingStatusEnum::COMPLETED->value);
    }

    /**
     * Scope to get cancelled job bookings
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', JobBookingStatusEnum::CANCELLED->value);
    }

    /**
     * Scope to get job bookings that allow bidding
     */
    public function scopeAllowsBidding($query)
    {
        return $query->whereIn('status', [JobBookingStatusEnum::OPEN->value]);
    }

    /**
     * Scope to get job bookings with pending bids
     */
    public function scopeWithPendingBids($query)
    {
        return $query->whereHas('bids', function ($query) {
            $query->where('status', BidStatusEnum::REQUESTED->value);
        });
    }

    /**
     * Scope to get job bookings by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get job bookings by date range
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('schedule_date', [$startDate, $endDate]);
    }
}