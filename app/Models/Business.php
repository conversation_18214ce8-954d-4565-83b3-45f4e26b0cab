<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Business extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'business_uuid',
        'name',
        'email',
        'phone',
        'address',
        'logo',
        'banner',
        'latitude',
        'longitude',
        'about',
        'tax_id',
        'registration_number',
        'website',
        'social_media',
        'business_hours',
        'status',
    ];

    protected $casts = [
        'social_media' => 'array',
        'business_hours' => 'array',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->business_uuid = (string) Str::uuid();
        });
    }

    /**
     * Get the certificate assets for the business.
     */
    public function certificateAssets()
    {
        return $this->morphMany(CertificateAsset::class, 'certifiable');
    }

    /**
     * Get the providers associated with the business.
     */
    public function providers(): HasMany
    {
        return $this->hasMany(User::class, 'business_uuid', 'business_uuid');
    }

    /**
     * Get the active subscription for the business.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(BusinessSubscription::class, 'business_uuid', 'business_uuid')
            ->where('is_active', true);
    }

    /**
     * Get all subscriptions for the business.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(BusinessSubscription::class, 'business_uuid', 'business_uuid');
    }
}