<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Asset extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'uuid',
        'file_name',
        'file_path',
        'file_key',
        'disk',
        'mime_type',
        'file_size',
        'collection_name',
        'custom_properties'
    ];

    protected $casts = [
        'custom_properties' => 'array'
    ];

    public function getUrlAttribute(): string
    {
        return Storage::disk($this->disk)->url($this->file_path);
    }

    public function getFullPathAttribute(): string
    {
        return Storage::disk($this->disk)->path($this->file_path);
    }

    public function assetable()
    {
        return $this->morphTo();
    }
}