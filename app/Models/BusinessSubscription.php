<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class BusinessSubscription extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_uuid',
        'plan_id',
        'provider_uuid',
        'transaction_id',
        'start_date',
        'end_date',
        'max_jobs',
        'max_providers',
        'max_services',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'max_jobs' => 'integer',
        'max_providers' => 'integer',
        'max_services' => 'integer',
    ];

    /**
     * Get the business that owns the subscription.
     */
    public function business()
    {
        return $this->belongsTo(Business::class, 'business_uuid', 'business_uuid');
    }

    /**
     * Get the plan associated with the subscription.
     */
    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the provider associated with the subscription.
     */
    public function provider()
    {
        return $this->belongsTo(User::class, 'provider_uuid', 'id');
    }

    /**
     * Check if the subscription is expired.
     *
     * @return bool
     */
    public function isExpired()
    {
        return now()->greaterThan($this->end_date);
    }

    /**
     * Get the remaining days of the subscription.
     *
     * @return int
     */
    public function remainingDays()
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInDays($this->end_date);
    }
}