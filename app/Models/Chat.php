<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class Chat extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'chats';

    protected $fillable = [
        'type',
        'participants',
    ];

    public function setParticipantsAttribute($value)
    {
        $this->attributes['participants'] = array_map('intval', $value);
    }

    public function getParticipantsAttribute($value)
    {
        $arr = is_string($value) ? json_decode($value, true) : $value;
        return array_map('intval', $arr);
    }

    public function messages()
    {
        return $this->hasMany(Message::class, 'chat_id');
    }

    public function getParticipantsDataAttribute()
    {
        if (empty($this->participants)) {
            return collect();
        }
        return \App\Models\User::whereIn('id', $this->participants)->get();
    }
}