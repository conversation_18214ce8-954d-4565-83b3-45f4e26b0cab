<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class Message extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'messages';

    protected $fillable = [
        'chat_id',
        'sender_id',
        'type',
        'message',
        'media_url',
        'read_by',
    ];

    public function setReadByAttribute($value)
    {
        $this->attributes['read_by'] = array_map('intval', $value);
    }

    public function getReadByAttribute($value)
    {
        $arr = is_string($value) ? json_decode($value, true) : $value;
        return array_map('intval', $arr);
    }
    
    public function chat()
    {
        return $this->belongsTo(Chat::class, 'chat_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }
}