<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\JobBooking;
use App\Models\Business;

class NewJobRequest extends Mailable
{
    use Queueable, SerializesModels;

    public $job;
    public $business;

    public function __construct(JobBooking $job, Business $business)
    {
        $this->job = $job;
        $this->business = $business;
    }

    public function build()
    {
        \Log::info('Building NewJobRequest email', [
            'job_uuid' => $this->job->job_uuid,
            'business_email' => $this->business->email,
        ]);
        $city = $this->job->city ?? null;
        $state = $this->job->state ?? null;
        if ($city && $state) {
            $subject = 'Looking for cleaner in ' . $city . ', ' . $state . ' ASAP';
        } elseif ($city) {
            $subject = 'Looking for cleaner in ' . $city . ' ASAP';
        } else {
            $subject = 'Looking for cleaner ASAP';
        }
        return $this->subject($subject)
                    ->markdown('emails.job-request');
    }
} 