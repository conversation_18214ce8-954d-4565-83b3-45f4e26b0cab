<?php

namespace App\Enums;

enum JobBookingStatusEnum: string
{
    case PENDING = 'pending';
    case OPEN = 'open';
    case ASSIGNED = 'assigned';
    case IN_PROGRESS = 'in_progress';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';
    case EXPIRED = 'expired';

    /**
     * Get all available status values
     */
    public static function getAllStatuses(): array
    {
        return [
            self::PENDING->value,
            self::OPEN->value,
            self::ASSIGNED->value,
            self::IN_PROGRESS->value,
            self::COMPLETED->value,
            self::CANCELLED->value,
            self::EXPIRED->value,
        ];
    }

    /**
     * Get active statuses (not completed, cancelled, or expired)
     */
    public static function getActiveStatuses(): array
    {
        return [
            self::PENDING->value,
            self::OPEN->value,
            self::ASSIGNED->value,
            self::IN_PROGRESS->value,
        ];
    }

    /**
     * Get final statuses (completed, cancelled, or expired)
     */
    public static function getFinalStatuses(): array
    {
        return [
            self::COMPLETED->value,
            self::CANCELLED->value,
            self::EXPIRED->value,
        ];
    }

    /**
     * Check if status allows bidding
     */
    public static function allowsBidding(string $status): bool
    {
        return in_array($status, [self::PENDING->value, self::OPEN->value]);
    }

    /**
     * Check if status is final (no further changes allowed)
     */
    public static function isFinal(string $status): bool
    {
        return in_array($status, self::getFinalStatuses());
    }

    /**
     * Get the next possible statuses from current status
     */
    public static function getNextStatuses(string $currentStatus): array
    {
        return match ($currentStatus) {
            self::PENDING->value => [self::OPEN->value, self::CANCELLED->value],
            self::OPEN->value => [self::ASSIGNED->value, self::CANCELLED->value, self::EXPIRED->value],
            self::ASSIGNED->value => [self::IN_PROGRESS->value, self::CANCELLED->value],
            self::IN_PROGRESS->value => [self::COMPLETED->value, self::CANCELLED->value],
            default => [], // Final statuses have no next statuses
        };
    }
}
