<?php

namespace App\Enums;

enum BidStatusEnum: string
{
    case REQUESTED = 'requested';
    case ACCEPTED = 'accepted';
    case REJECTED = 'rejected';
    case WITHDRAWN = 'withdrawn';

    /**
     * Get all available status values
     */
    public static function getAllStatuses(): array
    {
        return [
            self::REQUESTED->value,
            self::ACCEPTED->value,
            self::REJECTED->value,
            self::WITHDRAWN->value,
        ];
    }

    /**
     * Get active statuses (not rejected or withdrawn)
     */
    public static function getActiveStatuses(): array
    {
        return [
            self::REQUESTED->value,
            self::ACCEPTED->value,
        ];
    }

    /**
     * Get final statuses (rejected or withdrawn)
     */
    public static function getFinalStatuses(): array
    {
        return [
            self::REJECTED->value,
            self::WITHDRAWN->value,
        ];
    }

    /**
     * Check if status is final (no further changes allowed)
     */
    public static function isFinal(string $status): bool
    {
        return in_array($status, self::getFinalStatuses());
    }

    /**
     * Check if status is active (can still be modified)
     */
    public static function isActive(string $status): bool
    {
        return in_array($status, self::getActiveStatuses());
    }
}
