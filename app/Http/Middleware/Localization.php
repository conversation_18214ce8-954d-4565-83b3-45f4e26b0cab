<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class Localization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Session::has('locale')) {
            app()->setLocale(Session::get('locale'));
        } elseif ($request->hasHeader("Accept-Language")) {
            $locale = $this->parseAcceptLanguageHeader($request->header('Accept-Language'));
            app()->setLocale($locale);
        } else {
            app()->setLocale(app()->getLocale());
        }

        return $next($request);
    }

    /**
     * Parse the Accept-Language header to extract a valid locale.
     *
     * @param string $acceptLanguage
     * @return string
     */
    private function parseAcceptLanguageHeader(string $acceptLanguage): string
    {
        // Split by comma to get individual language preferences
        $languages = explode(',', $acceptLanguage);

        foreach ($languages as $language) {
            // Remove quality values (e.g., ";q=0.9")
            $locale = trim(explode(';', $language)[0]);

            // Validate locale format (basic validation)
            if ($this->isValidLocale($locale)) {
                return $locale;
            }
        }

        // Fallback to default locale if no valid locale found
        return config('app.locale', 'en');
    }

    /**
     * Check if a locale string is valid.
     *
     * @param string $locale
     * @return bool
     */
    private function isValidLocale(string $locale): bool
    {
        // Basic validation: should be 2-5 characters, letters/hyphens/underscores only
        return preg_match('/^[a-zA-Z]{2}([_-][a-zA-Z]{2})?$/', $locale);
    }
}
