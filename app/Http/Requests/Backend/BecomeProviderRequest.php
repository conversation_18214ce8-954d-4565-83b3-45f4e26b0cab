<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class BecomeProviderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $provider = [
            'type' => 'required|string',
            'name' => 'required|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,deleted_at,id',
            'phone' => 'required|unique:users,phone,deleted_at,id',
            'experience_interval' => 'required|string',
            'experience_duration' => 'required|numeric',
            'password' => 'required|string|min:8',
            'confirm_password' => 'required|same:password',
            'company_name' => 'required_if:type,company',
            'image' => 'file',
            'company_logo' => 'required_if:type,company,file',
            'company_email' => 'required_if:type,company|unique:companies,email,deleted_at,id',
            'company_code' => 'required_if:type,company',
            'company_phone' => 'required_if:type,company|unique:companies,phone,deleted_at,id',
            'alternative_name' => 'max:255',
            'country_id' => 'required|exists:countries,id',
            'state_id' => 'required|exists:states,id',
            'city' => 'required|string',
            'area' => 'required|string',
            'postal_code' => 'required',
            'address' => 'required|string',
            'zones*' => ['nullable', 'required', 'exists:zones,id,deleted_at,NULL'],
        ];

        return $provider;
    }
}
