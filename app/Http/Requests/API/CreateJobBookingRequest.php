<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateJobBookingRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'jobData.jobType' => ['required', Rule::in(['send_bids', 'find_providers'])],
            'jobData.property.type' => ['required', Rule::in(['residential', 'commercial', 'industrial', 'home', 'business', 'other'])],
            'jobData.service.category' => 'required|string',
            'jobData.service.tasks' => 'required|array',
            'jobData.service.tasks.*' => 'required|string',
            'jobData.description' => 'nullable|string|max:1000',
            'jobData.schedule.date' => 'required|date',
            'jobData.schedule.timePreference' => ['required', Rule::in(['morning', 'afternoon', 'evening', 'flexible'])],
            'jobData.schedule.frequency' => ['required', Rule::in(['one-time', 'weekly', 'bi-weekly', 'monthly'])],
            'jobData.schedule.recurringFrequency' => 'nullable|string',
            'jobData.location.address' => 'required|string',
            'jobData.location.city' => 'nullable|string',
            'jobData.location.state' => 'nullable|string',
            'jobData.location.zipCode' => 'nullable|string',
            'jobData.contact.fullName' => 'required|string',
            'jobData.contact.email' => 'required|email',
            'jobData.contact.phone' => 'required|string',
            'jobData.assets' => 'nullable|array',
            'jobData.assets.*' => 'required|uuid|exists:assets,uuid'
        ];
    }
} 