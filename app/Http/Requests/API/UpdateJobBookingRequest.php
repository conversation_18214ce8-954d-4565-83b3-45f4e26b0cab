<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateJobBookingRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'jobData.jobType' => ['sometimes', Rule::in(['send_bids', 'find_providers'])],
            'jobData.property.type' => ['sometimes', Rule::in(['residential', 'commercial', 'industrial'])],
            'jobData.service.category' => 'required|string',
            'jobData.schedule.date' => 'sometimes|date',
            'jobData.schedule.timePreference' => ['sometimes', Rule::in(['morning', 'afternoon', 'evening', 'flexible'])],
            'jobData.schedule.frequency' => ['sometimes', Rule::in(['one-time', 'weekly', 'bi-weekly', 'monthly'])],
            'jobData.schedule.recurringFrequency' => 'nullable|string',
            'jobData.location.address' => 'sometimes|string',
            'jobData.location.city' => 'nullable|string',
            'jobData.location.state' => 'nullable|string',
            'jobData.location.zipCode' => 'nullable|string',
            'jobData.contact.fullName' => 'sometimes|string',
            'jobData.contact.email' => 'sometimes|email',
            'jobData.contact.phone' => 'sometimes|string',
            'jobData.status' => ['sometimes', Rule::in(['pending', 'in_progress', 'completed', 'cancelled'])],
            'jobData.assets' => 'nullable|array',
            'jobData.assets.*' => 'required|uuid|exists:assets,uuid'
        ];
    }
} 