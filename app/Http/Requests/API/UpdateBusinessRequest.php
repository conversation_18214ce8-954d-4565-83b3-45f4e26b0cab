<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBusinessRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'category' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:255',
            'website' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'hours' => 'nullable|array',
            'photos' => 'nullable|array',
            'photos.*' => 'required_with:photos|string|max:2048',
            'services' => 'nullable|array',
            'reviews' => 'nullable|array',
            'reviews.*.text' => 'required_with:reviews|string|max:1000',
            'reviews.*.rating' => 'required_with:reviews|integer|min:1|max:5',
            'lat' => 'nullable|numeric|between:-90,90',
            'lng' => 'nullable|numeric|between:-180,180',
        ];
    }
} 