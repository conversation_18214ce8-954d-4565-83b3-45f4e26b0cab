<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class RejectBidRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // You can add authorization logic here if needed, 
        // or rely on the controller's policy check.
        return true; 
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'bid_id' => 'required|exists:bids,id', // Ensure bid_id is provided and exists
            // 'notes' => 'nullable|string|max:1000', // Optional notes for rejection
        ];
    }
}