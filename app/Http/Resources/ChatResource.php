<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\User;

class ChatResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'participants' => UserResource::collection($this->participantsData),
            'last_message' => new MessageResource($this->last_message),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}