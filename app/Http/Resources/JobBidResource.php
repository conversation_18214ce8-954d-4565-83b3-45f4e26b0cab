<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobBidResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'job_booking_id' => $this->job_booking_id,
            'provider_id' => $this->provider_id,
            'amount' => $this->amount,
            'bid_amount' => $this->amount, // Task requirement alias
            'description' => $this->description,
            'cover_letter' => $this->description, // Task requirement alias
            'status' => $this->status,
            'status_display' => $this->getStatusDisplayName(),
            'estimated_completion_time' => $this->estimated_completion_time?->format('Y-m-d H:i:s'),
            'estimated_duration' => $this->estimated_completion_time?->format('Y-m-d H:i:s'), // Task requirement alias
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'is_winning_bid' => $this->isWinningBid(),
            'is_lowest_bid' => $this->isLowestBid(),
            'ranking' => $this->getRanking(),
            'available_actions' => $this->getAvailableActions(),

            // Job booking information (basic info as per task requirements)
            'job_booking' => $this->whenLoaded('jobBooking', function () {
                return [
                    'id' => $this->jobBooking->id,
                    'job_uuid' => $this->jobBooking->job_uuid,
                    'project_code' => $this->jobBooking->project_code,
                    'title' => $this->jobBooking->service_category,
                    'job_type' => $this->jobBooking->job_type,
                    'service_category' => $this->jobBooking->service_category,
                    'description' => $this->jobBooking->description,
                    'status' => $this->jobBooking->status,
                    'schedule_date' => $this->jobBooking->schedule_date?->format('Y-m-d'),
                    'address' => $this->jobBooking->address,
                    'city' => $this->jobBooking->city,
                    'state' => $this->jobBooking->state,
                ];
            }),

            // Bidder (provider) information as per task requirements
            'bidder' => $this->whenLoaded('provider', function () {
                return [
                    'id' => $this->provider->id,
                    'name' => $this->provider->name,
                    'email' => $this->provider->email,
                    'phone' => $this->provider->phone,
                ];
            }),

            // Keep provider for backward compatibility
            'provider' => $this->whenLoaded('provider', function () {
                return [
                    'id' => $this->provider->id,
                    'name' => $this->provider->name,
                    'email' => $this->provider->email,
                    'phone' => $this->provider->phone,
                ];
            }),
        ];
    }
}
