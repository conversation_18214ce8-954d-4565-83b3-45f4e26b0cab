<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Asset;

class BusinessResource extends JsonResource
{
    public function toArray($request)
    {
        $certificates = [];
        if (is_array($this->certificates) && count($this->certificates)) {
            $assets = Asset::whereIn('uuid', $this->certificates)->get();
            $certificates = $assets->map(function($asset) {
                return [
                    'uuid' => $asset->uuid,
                    'file_name' => $asset->file_name,
                    'url' => $asset->url,
                    'mime_type' => $asset->mime_type,
                    'file_size' => $asset->file_size,
                    'collection_name' => $asset->collection_name,
                    'custom_properties' => $asset->custom_properties,
                ];
            });
        }
        return [
            'businessId' => $this->business_uuid,
            'name' => $this->name,
            'category' => $this->category,
            'location' => $this->location,
            'address' => $this->address,
            'phone' => $this->phone,
            'website' => $this->website,
            'email' => $this->email,
            'hours' => $this->hours,
            'photos' => $this->photos,
            'services' => $this->services,
            'reviews' => $this->reviews,
            'lat' => $this->lat,
            'lng' => $this->lng,
            'createdAt' => $this->created_at,
            'updatedAt' => $this->updated_at,
            'certificates' => $certificates,
            'certificates_status' => $this->certificates_status,
        ];
    }
} 