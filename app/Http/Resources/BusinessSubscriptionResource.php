<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BusinessSubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'business_uuid' => $this->business_uuid,
            'plan_id' => $this->plan_id,
            'provider_id' => $this->provider_id,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'total' => $this->total,
            'allowed_max_services' => $this->allowed_max_services,
            'allowed_max_addresses' => $this->allowed_max_addresses,
            'allowed_max_servicemen' => $this->allowed_max_servicemen,
            'allowed_max_service_packages' => $this->allowed_max_service_packages,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'plan' => $this->when($this->relationLoaded('plan'), function () {
                return new PlanResource($this->plan);
            }),
            'business' => $this->when($this->relationLoaded('business'), function () {
                return new BusinessResource($this->business);
            }),
            'provider' => $this->when($this->relationLoaded('provider'), function () {
                return new UserResource($this->provider);
            }),
            'is_expired' => $this->end_date < now()->toDateString(),
            'days_remaining' => $this->when($this->is_active, function () {
                return now()->diffInDays($this->end_date, false);
            }),
        ];
    }
}