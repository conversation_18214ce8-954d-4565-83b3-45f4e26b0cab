<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Asset;
use App\Http\Resources\BusinessResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $certificates = [];
        if (is_array($this->certificates) && count($this->certificates)) {
            $assets = Asset::whereIn('uuid', $this->certificates)->get();
            $certificates = $assets->map(function($asset) {
                return [
                    'uuid' => $asset->uuid,
                    'file_name' => $asset->file_name,
                    'url' => $asset->url,
                    'mime_type' => $asset->mime_type,
                    'file_size' => $asset->file_size,
                    'collection_name' => $asset->collection_name,
                    'custom_properties' => $asset->custom_properties,
                ];
            });
        }
        $currentSubscription = $this->load(['subscriptions' => function($query) {
            $query->with('plan')->active();
        }])->subscriptions->first();

        return array_merge(parent::toArray($request), [
            'certificates' => $certificates,
            'certificates_status' => $this->certificates_status,
            'business' => $this->when($this->business_uuid, new BusinessResource($this->business)),
            'current_subscription' => $currentSubscription ? [
                'plan' => $currentSubscription->plan,
                'start_date' => $currentSubscription->start_date,
                'end_date' => $currentSubscription->end_date,
                'is_active' => $currentSubscription->is_active,
                'allowed_max_services' => $currentSubscription->allowed_max_services,
                'allowed_max_addresses' => $currentSubscription->allowed_max_addresses,
                'allowed_max_servicemen' => $currentSubscription->allowed_max_servicemen,
                'allowed_max_service_packages' => $currentSubscription->allowed_max_service_packages,
            ] : null,
        ]);
    }
}