<?php

namespace App\Http\Controllers\API\Customer;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\CreateJobBookingRequest;
use App\Http\Requests\API\UpdateJobBookingRequest;
use App\Http\Resources\JobBookingResource;
use App\Http\Resources\JobBidResource;
use App\Models\JobBooking;
use App\Models\Bid;
use App\Enums\RoleEnum;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class JobBookingController extends Controller
{
    public function __construct()
    {
        // Ensure all methods require authentication
        $this->middleware('auth:api');
        
        // Ensure only consumers can access these endpoints
        $this->middleware(function ($request, $next) {
            $user = Auth::guard('api')->user();
            
            if (!$user || !$user->hasRole(RoleEnum::CONSUMER)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'Access denied. Only customers can access this resource.',
                    ]
                ], 403);
            }
            
            return $next($request);
        });
    }

    /**
     * Display a listing of the customer's job bookings.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();
            $perPage = $request->input('per_page', 15);
            
            // Get only the customer's own job bookings
            $query = JobBooking::where('user_id', $user->id)
                ->with(['bids', 'acceptedBid', 'assignedJob']);
            
            // Apply optional filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            
            if ($request->filled('service_category')) {
                $query->where('service_category', $request->input('service_category'));
            }
            
            $jobBookings = $query->latest('created_at')->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => JobBookingResource::collection($jobBookings),
                'pagination' => [
                    'current_page' => $jobBookings->currentPage(),
                    'per_page' => $jobBookings->perPage(),
                    'total' => $jobBookings->total(),
                    'last_page' => $jobBookings->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch customer job bookings: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to fetch job bookings',
                ]
            ], 500);
        }
    }

    /**
     * Store a newly created job booking.
     *
     * @param CreateJobBookingRequest $request
     * @return JsonResponse
     */
    public function store(CreateJobBookingRequest $request): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();
            $jobData = $request->input('jobData');
            
            // Create job booking with authenticated user
            $jobBooking = JobBooking::create([
                'job_type' => $jobData['jobType'] ?? 'send_bids',
                'property_type' => $jobData['property']['type'] ?? null,
                'service_category' => $jobData['service']['category'],
                'service_tasks' => $jobData['service']['tasks'],
                'description' => $jobData['description'] ?? null,
                'schedule_date' => $jobData['schedule']['date'],
                'time_preference' => $jobData['schedule']['timePreference'],
                'frequency' => $jobData['schedule']['frequency'],
                'recurring_frequency' => $jobData['schedule']['recurringFrequency'] ?? null,
                'address' => $jobData['location']['address'],
                'city' => $jobData['location']['city'] ?? null,
                'state' => $jobData['location']['state'] ?? null,
                'zip_code' => $jobData['location']['zipCode'] ?? null,
                'contact_name' => $jobData['contact']['fullName'],
                'contact_email' => $jobData['contact']['email'],
                'contact_phone' => $jobData['contact']['phone'],
                'status' => 'pending',
                'user_id' => $user->id
            ]);
            
            // Handle assets if provided
            if (isset($jobData['assets']) && is_array($jobData['assets'])) {
                $assetIds = collect($jobData['assets'])->pluck('id')->filter();
                if ($assetIds->isNotEmpty()) {
                    $jobBooking->assets()->sync($assetIds);
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($jobBooking),
                'message' => 'Job booking created successfully'
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create customer job booking: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to create job booking',
                ]
            ], 500);
        }
    }

    /**
     * Display the specified job booking.
     *
     * @param string $jobUuid
     * @return JsonResponse
     */
    public function show(string $jobUuid): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();
            
            $jobBooking = JobBooking::where('job_uuid', $jobUuid)
                ->where('user_id', $user->id) // Ensure customer can only view their own
                ->with(['bids', 'acceptedBid', 'assignedJob', 'assets'])
                ->first();
            
            if (!$jobBooking) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_BOOKING_NOT_FOUND',
                        'message' => 'Job booking not found or access denied',
                    ]
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($jobBooking)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch customer job booking: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to fetch job booking',
                ]
            ], 500);
        }
    }

    /**
     * Update the specified job booking.
     *
     * @param UpdateJobBookingRequest $request
     * @param string $jobUuid
     * @return JsonResponse
     */
    public function update(UpdateJobBookingRequest $request, string $jobUuid): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();
            $jobData = $request->input('jobData');
            
            $jobBooking = JobBooking::where('job_uuid', $jobUuid)
                ->where('user_id', $user->id) // Ensure customer can only update their own
                ->first();
            
            if (!$jobBooking) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_BOOKING_NOT_FOUND',
                        'message' => 'Job booking not found or access denied',
                    ]
                ], 404);
            }
            
            // Check if job booking can be updated
            if ($jobBooking->isFinal()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CANNOT_UPDATE',
                        'message' => 'Cannot update job booking in final status',
                    ]
                ], 422);
            }
            
            // Update job booking fields
            $updateData = [];
            if (isset($jobData['service']['category'])) {
                $updateData['service_category'] = $jobData['service']['category'];
            }
            if (isset($jobData['service']['tasks'])) {
                $updateData['service_tasks'] = $jobData['service']['tasks'];
            }
            if (isset($jobData['description'])) {
                $updateData['description'] = $jobData['description'];
            }
            if (isset($jobData['schedule']['date'])) {
                $updateData['schedule_date'] = $jobData['schedule']['date'];
            }
            if (isset($jobData['schedule']['timePreference'])) {
                $updateData['time_preference'] = $jobData['schedule']['timePreference'];
            }
            if (isset($jobData['schedule']['frequency'])) {
                $updateData['frequency'] = $jobData['schedule']['frequency'];
            }
            if (isset($jobData['location']['address'])) {
                $updateData['address'] = $jobData['location']['address'];
            }
            if (isset($jobData['location']['city'])) {
                $updateData['city'] = $jobData['location']['city'];
            }
            if (isset($jobData['location']['state'])) {
                $updateData['state'] = $jobData['location']['state'];
            }
            if (isset($jobData['location']['zipCode'])) {
                $updateData['zip_code'] = $jobData['location']['zipCode'];
            }
            if (isset($jobData['contact']['fullName'])) {
                $updateData['contact_name'] = $jobData['contact']['fullName'];
            }
            if (isset($jobData['contact']['email'])) {
                $updateData['contact_email'] = $jobData['contact']['email'];
            }
            if (isset($jobData['contact']['phone'])) {
                $updateData['contact_phone'] = $jobData['contact']['phone'];
            }
            
            $jobBooking->update($updateData);
            
            // Handle assets if provided
            if (isset($jobData['assets']) && is_array($jobData['assets'])) {
                $assetIds = collect($jobData['assets'])->pluck('id')->filter();
                $jobBooking->assets()->sync($assetIds);
            }
            
            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($jobBooking->fresh(['bids', 'acceptedBid', 'assignedJob', 'assets'])),
                'message' => 'Job booking updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update customer job booking: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to update job booking',
                ]
            ], 500);
        }
    }

    /**
     * Cancel the specified job booking.
     *
     * @param string $jobUuid
     * @return JsonResponse
     */
    public function cancel(string $jobUuid): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();

            $jobBooking = JobBooking::where('job_uuid', $jobUuid)
                ->where('user_id', $user->id) // Ensure customer can only cancel their own
                ->first();

            if (!$jobBooking) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_BOOKING_NOT_FOUND',
                        'message' => 'Job booking not found or access denied',
                    ]
                ], 404);
            }

            // Check if job booking can be cancelled
            if (!$jobBooking->canCancel()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CANNOT_CANCEL',
                        'message' => 'Job booking cannot be cancelled in current status',
                    ]
                ], 422);
            }

            // Cancel the job booking
            $jobBooking->cancel();

            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($jobBooking->fresh(['bids', 'acceptedBid', 'assignedJob'])),
                'message' => 'Job booking cancelled successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to cancel customer job booking: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to cancel job booking',
                ]
            ], 500);
        }
    }

    /**
     * Display bids for the specified job booking.
     *
     * @param string $jobUuid
     * @return JsonResponse
     */
    public function getBids(string $jobUuid): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();

            // Allow any authenticated user to view bids on any job booking
            // since job creation is public
            $jobBooking = JobBooking::where('job_uuid', $jobUuid)->first();

            if (!$jobBooking) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_BOOKING_NOT_FOUND',
                        'message' => 'Job booking not found',
                    ]
                ], 404);
            }

            $bids = $jobBooking->bids()
                ->with(['provider', 'provider.business'])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => JobBidResource::collection($bids),
                'meta' => [
                    'total_bids' => $bids->count(),
                    'pending_bids' => $bids->where('status', 'requested')->count(),
                    'accepted_bids' => $bids->where('status', 'accepted')->count(),
                    'rejected_bids' => $bids->where('status', 'rejected')->count(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch bids for customer job booking: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to fetch bids',
                ]
            ], 500);
        }
    }

    /**
     * Accept a bid for the specified job booking.
     *
     * @param Request $request
     * @param string $jobUuid
     * @param int $bidId
     * @return JsonResponse
     */
    public function acceptBid(Request $request, string $jobUuid, int $bidId): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();

            // Validate request
            $request->validate([
                'notes' => 'nullable|string|max:1000'
            ]);

            $jobBooking = JobBooking::where('job_uuid', $jobUuid)
                ->where('user_id', $user->id) // Ensure customer can only accept bids for their own job bookings
                ->first();

            if (!$jobBooking) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_BOOKING_NOT_FOUND',
                        'message' => 'Job booking not found or access denied',
                    ]
                ], 404);
            }

            // Check if job booking can accept bids
            if (!$jobBooking->canAcceptBids()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CANNOT_ACCEPT_BID',
                        'message' => 'Job booking cannot accept bids in current status',
                    ]
                ], 422);
            }

            // Verify the bid exists and belongs to this job booking
            $bid = $jobBooking->bids()->find($bidId);
            if (!$bid) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'BID_NOT_FOUND',
                        'message' => 'Bid not found for this job booking',
                    ]
                ], 404);
            }

            // Check if bid can be accepted
            if ($bid->status !== 'requested') {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'BID_NOT_ACCEPTABLE',
                        'message' => 'Only pending bids can be accepted',
                    ]
                ], 422);
            }

            // Accept the bid using the model method
            $notes = $request->input('notes');
            $success = $jobBooking->acceptBid($bidId, $notes);

            if ($success) {
                // Reload the job booking with relationships
                $jobBooking->load(['bids', 'acceptedBid', 'assignedJob']);

                return response()->json([
                    'success' => true,
                    'data' => new JobBookingResource($jobBooking),
                    'message' => 'Bid accepted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'ACCEPT_BID_FAILED',
                        'message' => 'Failed to accept bid',
                    ]
                ], 422);
            }
        } catch (\Exception $e) {
            Log::error('Failed to accept bid for customer job booking: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to accept bid',
                ]
            ], 500);
        }
    }
}
