<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\UserStoreRequest;
use App\Http\Requests\API\UserUpdateRequest;
use App\Http\Resources\UserResource;
use App\Repositories\API\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Helpers\Helpers;
use App\Models\User;
use App\Notifications\CertificateStatusNotification;
use App\Mail\CertificateStatusMail;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    protected $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function index(Request $request)
    {
        $users = $this->userRepository->getList($request);
        return UserResource::collection($users);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserStoreRequest $request)
    {
        $user = $this->userRepository->create($request->validated());
        return (new UserResource($user))->response()->setStatusCode(Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $user = $this->userRepository->getShow($id);
        return new UserResource($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserUpdateRequest $request, $id)
    {
        $user = $this->userRepository->update($request->validated(), $id);
        return new UserResource($user);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $this->userRepository->destroy($id);
        return response()->json(['message' => 'User deleted successfully']);
    }

    /**
     * Get count of active providers and customers
     */
    public function countActive(Request $request)
    {
        try {
            $roleId = $request->input('role_id');
            $roleName = null;
            $activeCustomers = 0;

            if ($roleId) {
                $role = \Spatie\Permission\Models\Role::find($roleId);
                if ($role) {
                    $roleName = $role->name;
                    $activeCustomers = $this->userRepository->countActiveByRole($roleName);
                }
            }

            $activeProviders = $this->userRepository->countActiveByRole('provider');

            return response()->json([
                'success' => true,
                'data' => [
                    'role' => $roleName,
                    'activeCustomers' => $activeCustomers,
                    'activeProviders' => $activeProviders
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to get active counts',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * List customers (with pagination, filter by status if needed)
     */
    public function listCustomers(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 15);
            $status = $request->input('status');
            $customers = $this->userRepository->getCustomers($status, $perPage);
            return response()->json([
                'success' => true,
                'data' => $customers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to list customers',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Suspend a provider (set status = 0)
     */
    public function suspendProvider($id)
    {
        try {
            $user = $this->userRepository->suspendProvider($id);
            return response()->json([
                'success' => true,
                'message' => 'Provider suspended successfully',
                'data' => new UserResource($user)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to suspend provider',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Unsuspend a provider (set status = 1)
     */
    public function unsuspendProvider($id)
    {
        try {
            $user = $this->userRepository->unsuspendProvider($id);
            return response()->json([
                'success' => true,
                'message' => 'Provider unsuspended successfully',
                'data' => new UserResource($user)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to unsuspend provider',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Update certificates for a user (upload/update asset UUIDs).
     */
    public function updateCertificates(Request $request)
    {
        $request->validate([
            'certificates' => 'required|array',
            'certificates.*' => 'string',
        ]);
        
        $user = User::find(Helpers::getCurrentUserId());
        
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }
        
        // if ($user->certificates_status === 'approved') {
        //     return response()->json(['success' => false, 'message' => 'Certificates cannot be updated after approval.'], 403);
        // }

        $user->certificates = $request->certificates;
        $user->certificates_status = 'none';
        $user->save();

        return response()->json(['success' => true, 'data' => $user->certificates]);
    }

    /**
     * Request review for certificates (user action).
     */
    public function requestCertificatesReview(Request $request)
    {
        $user = $this->userRepository->getShow(Helpers::getCurrentUserId());

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }

        // if (!in_array($user->certificates_status, ['none', 'rejected'])) {
        //     return response()->json(['success' => false, 'message' => 'Cannot request review in current status.'], 400);
        // }

        $user->certificates_status = 'requested';
        $user->save();

        // Notify all superadmins by email
        $superadmins = \App\Models\User::role('Supreme Admin')->get();
        foreach ($superadmins as $superadmin) {
            if ($superadmin->email) {
                \Mail::to($superadmin->email)->send(new CertificateStatusMail($user, 'request_review', $superadmin));
            }
        }

        return response()->json(['success' => true, 'status' => $user->certificates_status]);
    }

    /**
     * Admin: List all users with certificate review status (optionally filter by status).
     */
    public function listCertificatesReviews(Request $request)
    {
        $status = $request->query('status');
        $perPage = $request->input('per_page', 15);

        $query = \App\Models\User::whereHas('roles', function($q) {
            $q->where('name', 'provider');
        });
        if ($status) {
            $query->where('certificates_status', $status);
        }
        $users = $query->orderByDesc('updated_at')->paginate($perPage);
        return response()->json([
            'success' => true,
            'data' => \App\Http\Resources\UserResource::collection($users),
            'pagination' => [
                'current_page' => $users->currentPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
                'last_page' => $users->lastPage()
            ]
        ]);
    }

    /**
     * Admin: Approve certificates review.
     */
    public function approveCertificatesReview($userId)
    {
        $user = $this->userRepository->getShow($userId);
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }
        // if ($user->certificates_status !== 'requested') {
        //     return response()->json(['success' => false, 'message' => 'Only requested reviews can be approved.'], 400);
        // }
        $user->certificates_status = 'approved';
        $user->save();

        // Notify provider by email
        if ($user->email) {
            \Mail::to($user->email)->send(new CertificateStatusMail($user, 'approved'));
        }

        return response()->json(['success' => true, 'status' => $user->certificates_status]);
    }

    /**
     * Admin: Reject certificates review.
     */
    public function rejectCertificatesReview($userId)
    {
        $user = $this->userRepository->getShow($userId);
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }
        // if ($user->certificates_status !== 'requested') {
        //     return response()->json(['success' => false, 'message' => 'Only requested reviews can be rejected.'], 400);
        // }
        $user->certificates_status = 'rejected';
        $user->save();

        // Notify provider by email
        if ($user->email) {
            \Mail::to($user->email)->send(new CertificateStatusMail($user, 'rejected'));
        }

        return response()->json(['success' => true, 'status' => $user->certificates_status]);
    }
}