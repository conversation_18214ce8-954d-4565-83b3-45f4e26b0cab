<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Repositories\API\RoleRepository;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    public $repository;

    public function __construct(RoleRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $rolesWithPermissions = $this->repository->getRolesWithPermissions();

        return response()->json($rolesWithPermissions);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Public API: Get all roles (id, name, guard_name)
     */
    public function getRolesPublic(Request $request)
    {
        $roles = $this->repository->getAllRolesPublic();
        return response()->json([
            'success' => true,
            'data' => $roles
        ]);
    }
}