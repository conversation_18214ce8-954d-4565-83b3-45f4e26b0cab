<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessResource;
use App\Repositories\API\BusinessRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Jobs\ImportBusinessesJob;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\ImportBusinessRequest;
use App\Http\Requests\API\StoreBusinessRequest;
use App\Http\Requests\API\UpdateBusinessRequest;

class BusinessController extends Controller
{
    protected $repository;

    public function __construct(BusinessRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        try {
            $filters = $request->only(['category', 'location', 'search', 'zip_code']);
            $perPage = $request->input('per_page', 15);
            
            $businesses = $this->repository->getAll($filters, $perPage);
            
            return response()->json([
                'success' => true,
                'data' => BusinessResource::collection($businesses),
                'meta' => [
                    'categories' => $this->repository->getUniqueCategories(),
                    'filters' => $filters
                ],
                'pagination' => [
                    'current_page' => $businesses->currentPage(),
                    'per_page' => $businesses->perPage(),
                    'total' => $businesses->total(),
                    'last_page' => $businesses->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to list businesses: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to list businesses',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    public function show($businessUuid)
    {
        try {
            $business = $this->repository->getByUuid($businessUuid);
            
            if (!$business) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'BUSINESS_NOT_FOUND',
                        'message' => 'Business not found',
                    ]
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => new BusinessResource($business)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch business: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to fetch business',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    public function import(ImportBusinessRequest $request)
    {
        $file = $request->file('file');
        $path = $file->store('imports/businesses', 'public');
        ImportBusinessesJob::dispatch($path, Auth::id());
        return response()->json([
            'message' => 'File uploaded. Import is being processed in the background.'
        ]);
    }

    public function getCategories()
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $this->repository->getUniqueCategories()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get categories: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to get categories',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Store a newly created business in storage.
     */
    public function store(StoreBusinessRequest $request)
    {
        $validated = $request->validated();
        if (isset($validated['lat'])) {
            $validated['lat'] = is_numeric($validated['lat']) ? (float)$validated['lat'] : null;
        }
        if (isset($validated['lng'])) {
            $validated['lng'] = is_numeric($validated['lng']) ? (float)$validated['lng'] : null;
        }
        try {
            $business = $this->repository->create($validated);
            return response()->json([
                'success' => true,
                'data' => new BusinessResource($business)
            ], 201);
        } catch (\Exception $e) {
            \Log::error('Failed to create business: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to create business',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    public function update(UpdateBusinessRequest $request, $businessUuid)
    {
        $validated = $request->validated();
        if (isset($validated['lat'])) {
            $validated['lat'] = is_numeric($validated['lat']) ? (float)$validated['lat'] : null;
        }
        if (isset($validated['lng'])) {
            $validated['lng'] = is_numeric($validated['lng']) ? (float)$validated['lng'] : null;
        }
        try {
            $business = $this->repository->getByUuid($businessUuid);
            if (!$business) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'NOT_FOUND',
                        'message' => 'Business not found'
                    ]
                ], 404);
            }
            $business->update($validated);
            return response()->json([
                'success' => true,
                'data' => new BusinessResource($business)
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to update business: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to update business',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified business from storage.
     */
    public function destroy($businessUuid)
    {
        try {
            $business = $this->repository->getByUuid($businessUuid);
            if (!$business) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'NOT_FOUND',
                        'message' => 'Business not found'
                    ]
                ], 404);
            }
            $business->delete();
            return response()->json([
                'success' => true,
                'message' => 'Business deleted successfully.'
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to delete business: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to delete business',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
} 