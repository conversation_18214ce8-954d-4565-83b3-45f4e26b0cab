<?php

namespace App\Http\Controllers\API\Provider;

use App\Enums\RoleEnum;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\StoreJobBidRequest;
use App\Http\Requests\API\UpdateJobBidRequest;
use App\Http\Resources\JobBidResource;
use App\Http\Resources\JobBookingResource;
use App\Models\Bid;
use App\Models\JobBooking;
use App\Notifications\NewBidPlacedNotification;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class JobBidController extends Controller
{
    public function __construct()
    {
        // Ensure only providers can access these endpoints
        $this->middleware(function ($request, $next) {
            $user = Auth::guard('api')->user();
            if (!$user || !$user->hasRole(RoleEnum::PROVIDER)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHORIZED',
                        'message' => 'Only providers can access this endpoint.',
                    ]
                ], 403);
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the provider's bids.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Get provider ID directly from auth to avoid potential User model loading issues
            $user = Auth::guard('api')->user();
            $providerId = $user->id;
            $perPage = $request->input('per_page', 15);

            // Query bids directly without relying on User model relationships
            $query = Bid::where('provider_id', $providerId)
                ->whereExists(function ($query) use ($providerId) {
                    $query->select(\DB::raw(1))
                          ->from('users')
                          ->whereColumn('users.id', 'bids.provider_id')
                          ->where('users.id', $providerId);
                })
                ->whereExists(function ($query) {
                    $query->select(\DB::raw(1))
                          ->from('job_bookings')
                          ->whereColumn('job_bookings.id', 'bids.job_booking_id');
                })
                ->with(['jobBooking', 'provider']);

            // Apply optional filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('job_booking_id')) {
                $query->where('job_booking_id', $request->input('job_booking_id'));
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->input('date_from'));
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->input('date_to'));
            }

            // Order by creation date (newest first)
            $query->orderBy('created_at', 'desc');

            $bids = $query->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => JobBidResource::collection($bids),
                'pagination' => [
                    'current_page' => $bids->currentPage(),
                    'per_page' => $bids->perPage(),
                    'total' => $bids->total(),
                    'last_page' => $bids->lastPage(),
                    'from' => $bids->firstItem(),
                    'to' => $bids->lastItem(),
                ],
                'meta' => [
                    'total_bids' => $bids->total(),
                    'pending_bids' => Bid::where('provider_id', $providerId)
                        ->whereExists(function ($query) use ($providerId) {
                            $query->select(\DB::raw(1))
                                  ->from('users')
                                  ->whereColumn('users.id', 'bids.provider_id')
                                  ->where('users.id', $providerId);
                        })
                        ->whereExists(function ($query) {
                            $query->select(\DB::raw(1))
                                  ->from('job_bookings')
                                  ->whereColumn('job_bookings.id', 'bids.job_booking_id');
                        })
                        ->where('status', 'requested')->count(),
                    'accepted_bids' => Bid::where('provider_id', $providerId)
                        ->whereExists(function ($query) use ($providerId) {
                            $query->select(\DB::raw(1))
                                  ->from('users')
                                  ->whereColumn('users.id', 'bids.provider_id')
                                  ->where('users.id', $providerId);
                        })
                        ->whereExists(function ($query) {
                            $query->select(\DB::raw(1))
                                  ->from('job_bookings')
                                  ->whereColumn('job_bookings.id', 'bids.job_booking_id');
                        })
                        ->where('status', 'accepted')->count(),
                    'rejected_bids' => Bid::where('provider_id', $providerId)
                        ->whereExists(function ($query) use ($providerId) {
                            $query->select(\DB::raw(1))
                                  ->from('users')
                                  ->whereColumn('users.id', 'bids.provider_id')
                                  ->where('users.id', $providerId);
                        })
                        ->whereExists(function ($query) {
                            $query->select(\DB::raw(1))
                                  ->from('job_bookings')
                                  ->whereColumn('job_bookings.id', 'bids.job_booking_id');
                        })
                        ->where('status', 'rejected')->count(),
                ]
            ]);
        } catch (Exception $e) {
            Log::error('Failed to fetch provider bids: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FETCH_BIDS_FAILED',
                    'message' => 'Failed to fetch your bids.',
                ]
            ], 500);
        }
    }

    /**
     * Store a newly created bid for a job booking.
     *
     * @param StoreJobBidRequest $request
     * @param JobBooking $jobBooking
     * @return JsonResponse
     */
    public function store(StoreJobBidRequest $request, JobBooking $jobBooking): JsonResponse
    {
        try {
            $bid = Bid::create([
                'job_booking_id' => $jobBooking->id,
                'provider_id' => Helpers::getCurrentUserId(),
                'amount' => $request->amount,
                'description' => $request->description,
                'estimated_completion_time' => $request->estimated_completion_time,
                'status' => 'requested',
            ]);

            $bid->load(['provider', 'jobBooking']);

            // Send notification to job booking owner
            try {
                $jobBooking->user->notify(new NewBidPlacedNotification($bid));
            } catch (Exception $notificationException) {
                Log::warning('Failed to send new bid notification: ' . $notificationException->getMessage());
            }

            return response()->json([
                'success' => true,
                'data' => new JobBidResource($bid),
                'message' => 'Bid placed successfully.'
            ], 201);
        } catch (Exception $e) {
            Log::error('Failed to create job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'CREATE_BID_FAILED',
                    'message' => 'Failed to place bid.',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Display the specified bid.
     *
     * @param Bid $bid
     * @return JsonResponse
     */
    public function show(Bid $bid): JsonResponse
    {
        try {
            // Check if the provider owns this bid
            if ($bid->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only view your own bids.',
                    ]
                ], 403);
            }

            $bid->load(['provider', 'jobBooking', 'jobBooking.user']);

            return response()->json([
                'success' => true,
                'data' => new JobBidResource($bid)
            ]);
        } catch (Exception $e) {
            Log::error('Failed to fetch job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FETCH_BID_FAILED',
                    'message' => 'Failed to fetch bid details.',
                ]
            ], 500);
        }
    }

    /**
     * Update the specified bid.
     *
     * @param UpdateJobBidRequest $request
     * @param Bid $bid
     * @return JsonResponse
     */
    public function update(UpdateJobBidRequest $request, Bid $bid): JsonResponse
    {
        try {
            // Check if the provider owns this bid
            if ($bid->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only update your own bids.',
                    ]
                ], 403);
            }

            // Check if bid can be updated
            if (!$bid->canWithdraw()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_STATUS',
                        'message' => 'This bid cannot be updated. Only pending bids can be modified.',
                    ]
                ], 422);
            }

            $bid->update($request->validated());
            $bid->load(['provider', 'jobBooking']);

            return response()->json([
                'success' => true,
                'data' => new JobBidResource($bid),
                'message' => 'Bid updated successfully.'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to update job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UPDATE_BID_FAILED',
                    'message' => 'Failed to update bid.',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified bid (withdraw).
     *
     * @param Bid $bid
     * @return JsonResponse
     */
    public function destroy(Bid $bid): JsonResponse
    {
        try {
            // Check if the provider owns this bid
            if ($bid->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only withdraw your own bids.',
                    ]
                ], 403);
            }

            // Check if bid can be withdrawn
            if (!$bid->canWithdraw()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_STATUS',
                        'message' => 'This bid cannot be withdrawn. Only pending bids can be withdrawn.',
                    ]
                ], 422);
            }

            // Withdraw the bid
            $bid->withdraw();

            return response()->json([
                'success' => true,
                'message' => 'Bid withdrawn successfully.'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to withdraw job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'WITHDRAW_BID_FAILED',
                    'message' => 'Failed to withdraw bid.',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get job booking details with current provider's bid only.
     * This endpoint allows providers to view job booking details and their own bid.
     *
     * @param string $jobId The job UUID
     * @return JsonResponse
     */
    public function getJobBookingWithBids(string $jobId): JsonResponse
    {
        try {
            $currentProviderId = Helpers::getCurrentUserId();

            // Find the job booking by UUID
            $jobBooking = JobBooking::where('job_uuid', $jobId)->first();

            if (!$jobBooking) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_BOOKING_NOT_FOUND',
                        'message' => 'Job booking not found.',
                    ]
                ], 404);
            }

            // Load only the current provider's bid
            $jobBooking->load([
                'assets',
                'acceptedBid',
                'assignedJob'
            ]);

            // Get only the current provider's bid for this job booking
            $providerBid = $jobBooking->bids()
                ->where('provider_id', $currentProviderId)
                ->with(['provider', 'provider.business'])
                ->first();

            // Create a custom response with job booking data and provider's bid
            $jobBookingData = new JobBookingResource($jobBooking);
            $responseData = $jobBookingData->toArray(request());

            // Override the bids data to include only current provider's bid
            $responseData['bids'] = $providerBid ? [new JobBidResource($providerBid)] : [];
            $responseData['provider_bid'] = $providerBid ? new JobBidResource($providerBid) : null;

            // Update bid summary to reflect only current provider's bid
            $responseData['bids_summary'] = [
                'has_bid' => $providerBid !== null,
                'bid_status' => $providerBid ? $providerBid->status : null,
                'bid_amount' => $providerBid ? $providerBid->amount : null,
            ];

            return response()->json([
                'success' => true,
                'data' => $responseData,
                'message' => 'Job booking details with your bid retrieved successfully.'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to fetch job booking with provider bid: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FETCH_JOB_BOOKING_FAILED',
                    'message' => 'Failed to fetch job booking details.',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
}
