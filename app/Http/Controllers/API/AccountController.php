<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\UpdatePasswordRequest;
use App\Http\Requests\API\UpdateProfileRequest;
use App\Repositories\API\AccountRepository;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    protected $repository;

    public function __construct(AccountRepository $repository)
    {
        $this->repository = $repository;
    }

    public function self()
    {
        return $this->repository->self();
    }

    /**
     * Get current user profile (JWT)
     */
    public function getProfile(Request $request)
    {
        try {
            $user = auth('api')->user();
            return response()->json([
                'success' => true,
                'data' => $user
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to get profile',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Update current user profile (JWT)
     */
    public function updateProfile(UpdateProfileRequest $request)
    {
        try {
            $result = $this->repository->updateProfile($request);
            return response()->json([
                'success' => true,
                'data' => $result['user'] ?? null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to update profile',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    public function updatePassword(UpdatePasswordRequest $request)
    {
        return $this->repository->updatePassword($request);
    }

    public function updateUserZone(Request $request)
    {
        return $this->repository->updateUserZone($request);
    }

    public function deleteAccount()
    {
        return $this->repository->deleteAccount();
    }

    public function changePassword(UpdatePasswordRequest $request)
    {
        return $this->repository->updatePassword($request);
    }
}
