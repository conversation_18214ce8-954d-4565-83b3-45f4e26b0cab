<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Repositories\API\BusinessRepository;
use App\DataTables\BusinessDataTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BusinessController extends Controller
{
    protected $repository;

    public function __construct(BusinessRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(BusinessDataTable $dataTable)
    {
        return $dataTable->render('backend.businesses.index');
    }

    public function show($businessUuid)
    {
        try {
            $business = $this->repository->getByUuid($businessUuid);
            
            if (!$business) {
                return redirect()->back()->with('error', 'Business not found.');
            }
            
            return view('backend.businesses.show', compact('business'));
        } catch (\Exception $e) {
            Log::error('Failed to show business: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to fetch business details.');
        }
    }
} 