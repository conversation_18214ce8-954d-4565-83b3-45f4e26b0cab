<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Repositories\API\JobBookingRepository;
use App\DataTables\JobBookingDataTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\JobBooking;
use App\Models\Business;
use App\Services\ProjectCodeGenerator;

class JobBookingController extends Controller
{
    protected $repository;
    protected $projectCodeGenerator;

    public function __construct(JobBookingRepository $repository, ProjectCodeGenerator $projectCodeGenerator)
    {
        $this->repository = $repository;
        $this->projectCodeGenerator = $projectCodeGenerator;
    }

    // public function index(Request $request, JobBookingDataTable $dataTable)
    // {
    //     try {
    //         $filters = $request->only(['status', 'job_type', 'service_category']);
    //         $perPage = $request->input('per_page', 15);
            
    //         $jobs = $this->repository->getAll($filters, $perPage);
            
    //         return view('backend.job-bookings.index', compact('jobs'));
    //     } catch (\Exception $e) {
    //         Log::error('Failed to list jobs: ' . $e->getMessage());
    //         return redirect()->back()->with('error', 'Failed to fetch job bookings.');
    //     }
    // }

    public function index(JobBookingDataTable $dataTable)
    {
        return $dataTable->render('backend.job-bookings.index');
    }

    public function show($jobUuid)
    {
        try {
            $job = $this->repository->getByUuid($jobUuid);
            
            if (!$job) {
                return redirect()->back()->with('error', 'Job booking not found.');
            }
            
            return view('backend.job-bookings.show', compact('job'));
        } catch (\Exception $e) {
            Log::error('Failed to show job: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to fetch job booking details.');
        }
    }

    public function edit($jobUuid)
    {
        try {
            $job = $this->repository->getByUuid($jobUuid);
            
            if (!$job) {
                return redirect()->back()->with('error', 'Job booking not found.');
            }
            
            return view('backend.job-bookings.edit', compact('job'));
        } catch (\Exception $e) {
            Log::error('Failed to edit job: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load job booking.');
        }
    }

    public function update(Request $request, $jobUuid)
    {
        try {
            $job = $this->repository->getByUuid($jobUuid);
            
            if (!$job) {
                return redirect()->back()->with('error', 'Job booking not found.');
            }
            
            $this->repository->updateJob($job, $request->all());
            
            return redirect()->route('admin.job-bookings.index')
                ->with('success', 'Job booking updated successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update job: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update job booking.');
        }
    }

    public function destroy($jobUuid)
    {
        try {
            $job = $this->repository->getByUuid($jobUuid);
            
            if (!$job) {
                return redirect()->back()->with('error', 'Job booking not found.');
            }
            
            $this->repository->deleteJob($job);
            
            return redirect()->route('admin.job-bookings.index')
                ->with('success', 'Job booking deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to delete job: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete job booking.');
        }
    }

    /**
     * Show send to provider page
     */
    public function sendToProviderPage($jobUuid)
    {
        try {
            // Find job with validation
            $job = JobBooking::where('job_uuid', $jobUuid)->firstOrFail();
            
            // Build base query for matching providers
            $query = Business::query();
            
            // Match by service category if available
            if ($job->service_category) {
                $query->where('category', $job->service_category);
            }
            
            // Match by location using proper address parsing
            if ($job->state) {
                $query->where(function($q) use ($job) {
                    $q->where('location', 'LIKE', '%' . $job->state . '%')
                      ->orWhere('address', 'LIKE', '%' . $job->state . '%');
                });
            }

            // Get matching businesses
            $businesses = $query->get();
            
            // Generate project code
            $projectCode = $this->projectCodeGenerator->generateProjectCode($job->job_uuid);
            
            return view('backend.job-bookings.send-to-provider', compact('job', 'businesses', 'projectCode'));

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return redirect()->back()
                ->with('error', 'Job booking not found.');
                
        } catch (\Exception $e) {
            \Log::error('Failed to show send to provider page: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->back()
                ->with('error', 'Failed to load providers. Please try again.');
        }
    }

    /**
     * Process sending to providers
     */
    public function sendProviders(Request $request)
    {
        try {
            $request->validate([
                'job_uuid' => 'required|exists:job_bookings,job_uuid',
                'provider_uuids' => 'required|array|min:1',
                'provider_uuids.*' => 'required|exists:businesses,business_uuid'
            ]);

            $job = JobBooking::where('job_uuid', $request->job_uuid)->firstOrFail();
            $businesses = Business::whereIn('business_uuid', $request->provider_uuids)->get();

            // Get project code for email
            $projectCode = \App\Helpers\ProjectCodeGenerator::fromUuid($job->job_uuid);

            foreach ($businesses as $business) {
                \Mail::to($business->email)
                    ->send(new \App\Mail\NewJobRequest($job, $business, $projectCode));
            }

            return redirect()
                ->route('backend.job-bookings.show', $job->job_uuid)
                ->with('success', 'Project has been sent to ' . $businesses->count() . ' providers.');

        } catch (\Exception $e) {
            \Log::error('Failed to send to providers: ' . $e->getMessage());
            \Log::error('Exception stack trace: ' . $e->getTraceAsString());
            
            return back()
                ->withInput()
                ->with('error', 'Failed to send project to providers. Please try again.');
        }
    }

    /**
     * Search job bookings by project code
     */
    public function searchByProjectCode(Request $request)
    {
        try {
            $projectCode = $request->get('project_code');
            if (!$projectCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Project code is required'
                ], 400);
            }

            $uuid = $this->projectCodeGenerator->decodeProjectCode($projectCode);
            $jobBooking = JobBooking::where('uuid', $uuid)->first();

            if (!$jobBooking) {
                return response()->json([
                    'success' => false, 
                    'message' => 'Job booking not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $jobBooking
            ]);

        } catch (\Exception $e) {
            \Log::error('Error searching job by project code: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error searching job booking'
            ], 500);
        }
    }
} 