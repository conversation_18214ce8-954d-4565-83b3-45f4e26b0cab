<?php

return [
    'name' => 'Install',
    'configuration' => [
        'version' => [
            'PHP >= 8.1' => '8.1',
        ],
        'extensions' => [
            'Bcmath',
            'Ctype',
            'fileinfo',
            'JSON',
            'Mbstring',
            'Openssl',
            'Pdo',
            'Tokenizer',
            'Xml',
        ],
    ],
    'writables' => [
        'storage',
        'bootstrap/cache',
    ],
    'migration' => '.migration',

    'app' => [
        'APP_NAME' => 'Home services',
        'APP_ENV' => '',
        'APP_DEBUG' => 'true',
        'APP_URL' => 'http://127.0.0.1:8000',
    ],

    'locales' => [
        'en' => 'English (en)',
        'af' => 'Afrikaans (af)',
        'sq' => 'Albanian (sq)',
        'am' => 'Amharic (am)',
        'ar' => 'Arabic (ar)',
        'hy' => 'Armenian (hy)',
        'az' => 'Azerbaijani (az)',
        'eu' => 'Basque (eu)',
        'be' => 'Belarusian (be)',
        'bn' => 'Bengali (bn)',
        'bs' => 'Bosnian (bs)',
        'bg' => 'Bulgarian (bg)',
        'ca' => 'Catalan (ca)',
        'ceb' => 'Cebuano (ceb)',
        'ny' => 'Chichewa (ny)',
        'zh' => 'Chinese (Simplified) (zh)',
        'co' => 'Corsican (co)',
        'hr' => 'Croatian (hr)',
        'cs' => 'Czech (cs)',
        'da' => 'Danish (da)',
        'nl' => 'Dutch (nl)',
        'eo' => 'Esperanto (eo)',
        'et' => 'Estonian (et)',
        'tl' => 'Filipino (tl)',
        'fi' => 'Finnish (fi)',
        'fr' => 'French (fr)',
        'fy' => 'Frisian (fy)',
        'gl' => 'Galician (gl)',
        'ka' => 'Georgian (ka)',
        'de' => 'German (de)',
        'el' => 'Greek (el)',
        'gu' => 'Gujarati (gu)',
        'ht' => 'Haitian Creole (ht)',
        'ha' => 'Hausa (ha)',
        'haw' => 'Hawaiian (haw)',
        'iw' => 'Hebrew (iw)',
        'hi' => 'Hindi (hi)',
        'hmn' => 'Hmong (hmn)',
        'hu' => 'Hungarian (hu)',
        'is' => 'Icelandic (is)',
        'ig' => 'Igbo (ig)',
        'id' => 'Indonesian (id)',
        'ga' => 'Irish (ga)',
        'it' => 'Italian (it)',
        'ja' => 'Japanese (ja)',
        'jw' => 'Javanese (jw)',
        'kn' => 'Kannada (kn)',
        'kk' => 'Kazakh (kk)',
        'km' => 'Khmer (km)',
        'rw' => 'Kinyarwanda (rw)',
        'ko' => 'Korean (ko)',
        'ku' => 'Kurdish (ku)',
        'ky' => 'Kyrgyz (ky)',
        'lo' => 'Lao (lo)',
        'la' => 'Latin (la)',
        'lv' => 'Latvian (lv)',
        'lt' => 'Lithuanian (lt)',
        'lb' => 'Luxembourgish (lb)',
        'mk' => 'Macedonian (mk)',
        'mg' => 'Malagasy (mg)',
        'ms' => 'Malay (ms)',
        'ml' => 'Malayalam (ml)',
        'mt' => 'Maltese (mt)',
        'mi' => 'Maori (mi)',
        'mr' => 'Marathi (mr)',
        'mn' => 'Mongolian (mn)',
        'my' => 'Burmese (my)',
        'ne' => 'Nepali (ne)',
        'no' => 'Norwegian (no)',
        'or' => 'Odia (or)',
        'ps' => 'Pashto (ps)',
        'fa' => 'Persian (fa)',
        'pl' => 'Polish (pl)',
        'pt' => 'Portuguese (pt)',
        'pa' => 'Punjabi (pa)',
        'ro' => 'Romanian (ro)',
        'ru' => 'Russian (ru)',
        'sm' => 'Samoan (sm)',
        'gd' => 'Scots Gaelic (gd)',
        'sr' => 'Serbian (sr)',
        'st' => 'Sesotho (st)',
        'sn' => 'Shona (sn)',
        'sd' => 'Sindhi (sd)',
        'si' => 'Sinhala (si)',
        'sk' => 'Slovak (sk)',
        'sl' => 'Slovenian (sl)',
        'so' => 'Somali (so)',
        'es' => 'Spanish (es)',
        'su' => 'Sundanese (su)',
        'sw' => 'Swahili (sw)',
        'sv' => 'Swedish (sv)',
        'tg' => 'Tajik (tg)',
        'ta' => 'Tamil (ta)',
        'te' => 'Telugu (te)',
        'th' => 'Thai (th)',
        'tr' => 'Turkish (tr)',
        'uk' => 'Ukrainian (uk)',
        'ur' => 'Urdu (ur)',
        'ug' => 'Uyghur (ug)',
        'uz' => 'Uzbek (uz)',
        'vi' => 'Vietnamese (vi)',
        'cy' => 'Welsh (cy)',
        'xh' => 'Xhosa (xh)',
        'yi' => 'Yiddish (yi)',
        'yo' => 'Yoruba (yo)',
        'zu' => 'Zulu (zu)',
    ],

    'installation' => 'installation.json',
];
