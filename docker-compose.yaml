version: "3.8"

services:
  db:
    image: mysql:8.0
    container_name: jobon_db
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "33061:3306"
    volumes:
      - db_data:/var/lib/mysql
  redis:
    image: redis:alpine
    container_name: jobon_redis
    ports:
      - "63791:6379"
    volumes:
      - redis_data:/data
  mongodb:
    image: mongo:6.0
    container_name: jobon_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DB_DATABASE}
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  db_data:
  redis_data:
  mongo_data:
