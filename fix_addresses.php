<?php

/**
 * <PERSON><PERSON><PERSON> to fix empty address fields by copying location values
 * This script will update all items in job-on-new-data.json that have empty address fields
 */

// Read the JSON file
$jsonFile = 'job-on-new-data.json';
$jsonContent = file_get_contents($jsonFile);

if ($jsonContent === false) {
    die("Error: Could not read the JSON file.\n");
}

// Decode JSON
$data = json_decode($jsonContent, true);

if ($data === null) {
    die("Error: Invalid JSON format.\n");
}

$updatedCount = 0;
$totalItems = count($data);

echo "Processing {$totalItems} items...\n";
echo "Looking for items with empty address fields...\n\n";

// Process each item
foreach ($data as $index => &$item) {
    // Check if address is empty and location exists
    if (isset($item['address']) && $item['address'] === '' && 
        isset($item['location']) && !empty($item['location'])) {
        
        echo "Fixing item #{$index}: {$item['name']}\n";
        echo "  Location: {$item['location']}\n";
        echo "  Old address: (empty)\n";
        
        // Copy location to address
        $item['address'] = $item['location'];
        
        echo "  New address: {$item['address']}\n\n";
        $updatedCount++;
    }
}

echo "Summary:\n";
echo "- Total items processed: {$totalItems}\n";
echo "- Items updated: {$updatedCount}\n";

if ($updatedCount > 0) {
    // Create backup
    $backupFile = 'job-on-new-data.json.backup.' . date('Y-m-d_H-i-s');
    copy($jsonFile, $backupFile);
    echo "- Backup created: {$backupFile}\n";
    
    // Save updated JSON
    $updatedJson = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    
    if (file_put_contents($jsonFile, $updatedJson) !== false) {
        echo "- Successfully updated {$jsonFile}\n";
    } else {
        echo "- Error: Could not write to {$jsonFile}\n";
    }
} else {
    echo "- No updates needed\n";
}

echo "\nDone!\n";

?>