<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_bookings', function (Blueprint $table) {
            $table->json('service_tasks')->nullable()->after('service_category');
            $table->text('description')->nullable()->after('service_tasks');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_bookings', function (Blueprint $table) {
            $table->dropColumn(['service_tasks', 'description']);
        });
    }
};