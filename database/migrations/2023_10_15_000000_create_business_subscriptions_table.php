<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('business_subscriptions')) {
            Schema::dropIfExists('business_subscriptions');
        }
        Schema::create('business_subscriptions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('business_uuid');
            $table->unsignedBigInteger('plan_id');
            $table->unsignedBigInteger('provider_id')->nullable();
            $table->string('transaction_id')->nullable();
            $table->timestamp('start_date');
            $table->timestamp('end_date');
            $table->integer('max_jobs')->default(0);
            $table->integer('max_providers')->default(0);
            $table->integer('max_services')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys - commented out until referenced tables are created
            // Will be added in separate migrations after all referenced tables exist

            // $table->foreign('business_uuid')
            //       ->references('business_uuid')
            //       ->on('businesses')
            //       ->onDelete('cascade');

            // $table->foreign('plan_id')
            //       ->references('id')
            //       ->on('plans')
            //       ->onDelete('cascade');

            $table->foreign('provider_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_subscriptions');
    }
};