<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Skip if table already exists (created by earlier migration)
        if (Schema::hasTable('business_subscriptions')) {
            return;
        }

        Schema::create('business_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('business_uuid');
            $table->foreignId('plan_id')->nullable();
            $table->foreignId('provider_id')->nullable(); // Provider who assigned the plan
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->decimal('total', 10, 2);
            $table->unsignedInteger('allowed_max_services');
            $table->unsignedInteger('allowed_max_addresses');
            $table->unsignedInteger('allowed_max_servicemen');
            $table->unsignedInteger('allowed_max_service_packages');
            $table->boolean('is_active')->default(false);
            $table->timestamps();

            $table->foreign('business_uuid')->references('business_uuid')->on('businesses')->onDelete('cascade');
            $table->foreign('plan_id')->references('id')->on('plans')->onDelete('cascade');
            $table->foreign('provider_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_subscriptions');
    }
};