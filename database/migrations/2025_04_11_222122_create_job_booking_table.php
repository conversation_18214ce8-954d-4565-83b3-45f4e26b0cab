<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('job_bookings', function (Blueprint $table) {
            $table->id();
            $table->uuid('job_uuid')->unique();
            $table->string('job_type'); // 'send_bids' hoặc 'find_providers'
            $table->string('property_type'); // 'residential', 'commercial', 'industrial'
            $table->string('service_category');
            $table->json('service_tasks')->nullable();
            $table->string('custom_task')->nullable();
            $table->text('description')->nullable();
            $table->date('schedule_date');
            $table->string('time_preference'); // 'morning', 'afternoon', 'evening'
            $table->string('frequency'); // 'one-time', 'recurring'
            $table->string('recurring_frequency')->nullable();
            $table->string('budget')->nullable();
            
            // Location info
            $table->string('address');
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('zip_code')->nullable();
            
            // Contact info
            $table->string('contact_name');
            $table->string('contact_email');
            $table->string('contact_phone');
            
            $table->string('status')->default('pending');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('job_bookings');
    }
};