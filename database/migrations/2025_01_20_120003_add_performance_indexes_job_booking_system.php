<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bids', function (Blueprint $table) {
            // Add composite indexes for common query patterns (only if they don't exist)
            $indexes = collect(Schema::getIndexes('bids'))->pluck('name');

            if (!$indexes->contains('bids_job_booking_status_idx')) {
                $table->index(['job_booking_id', 'status'], 'bids_job_booking_status_idx');
            }
            if (!$indexes->contains('bids_provider_status_idx')) {
                $table->index(['provider_id', 'status'], 'bids_provider_status_idx');
            }
            if (!$indexes->contains('bids_job_booking_provider_idx')) {
                $table->index(['job_booking_id', 'provider_id'], 'bids_job_booking_provider_idx');
            }
        });

        Schema::table('bookings', function (Blueprint $table) {
            // Add composite indexes for common query patterns (only if they don't exist)
            $indexes = collect(Schema::getIndexes('bookings'))->pluck('name');

            if (!$indexes->contains('bookings_job_booking_status_idx')) {
                $table->index(['job_booking_id', 'booking_status_id'], 'bookings_job_booking_status_idx');
            }
            if (!$indexes->contains('bookings_job_booking_provider_idx')) {
                $table->index(['job_booking_id', 'provider_id'], 'bookings_job_booking_provider_idx');
            }
        });

        // Only add indexes to job_bookings if the table exists
        if (Schema::hasTable('job_bookings')) {
            Schema::table('job_bookings', function (Blueprint $table) {
                // Add indexes for common query patterns
                $table->index(['status', 'created_at'], 'job_bookings_status_created_idx');
                $table->index(['user_id', 'status'], 'job_bookings_user_status_idx');
                $table->index(['schedule_date', 'status'], 'job_bookings_schedule_status_idx');
                $table->index(['service_category', 'status'], 'job_bookings_category_status_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bids', function (Blueprint $table) {
            $table->dropIndex('bids_job_booking_status_idx');
            $table->dropIndex('bids_provider_status_idx');
            $table->dropIndex('bids_job_booking_provider_idx');
        });

        Schema::table('bookings', function (Blueprint $table) {
            $table->dropIndex('bookings_job_booking_status_idx');
            $table->dropIndex('bookings_job_booking_provider_idx');
        });

        // Only drop indexes from job_bookings if the table exists
        if (Schema::hasTable('job_bookings')) {
            Schema::table('job_bookings', function (Blueprint $table) {
                $table->dropIndex('job_bookings_status_created_idx');
                $table->dropIndex('job_bookings_user_status_idx');
                $table->dropIndex('job_bookings_schedule_status_idx');
                $table->dropIndex('job_bookings_category_status_idx');
            });
        }
    }
};
