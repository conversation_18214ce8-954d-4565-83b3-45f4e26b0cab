<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add job_booking_id column if it doesn't exist
            if (!Schema::hasColumn('bookings', 'job_booking_id')) {
                $table->unsignedBigInteger('job_booking_id')->nullable()->after('id');
            }

            // Create foreign key constraint to job_bookings table only if it exists
            if (Schema::hasTable('job_bookings')) {
                $table->foreign('job_booking_id')->references('id')->on('job_bookings')->onDelete('set null');
            }

            // Add index for performance
            $table->index('job_booking_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Drop foreign key constraint and index
            $table->dropForeign(['job_booking_id']);
            $table->dropIndex(['job_booking_id']);
            
            // Drop the job_booking_id column
            $table->dropColumn('job_booking_id');
        });
    }
};
