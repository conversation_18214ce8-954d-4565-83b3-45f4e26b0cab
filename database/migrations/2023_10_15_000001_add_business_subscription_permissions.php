<?php

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create permissions
        Permission::firstOrCreate(['name' => 'manage-subscriptions', 'guard_name' => 'api']);

        // Assign to admin role (create if doesn't exist)
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'api']);
        if ($adminRole) {
            $adminRole->givePermissionTo('manage-subscriptions');
        }
        
        // Optionally assign to other roles if needed
        $managerRole = Role::firstOrCreate(['name' => 'Supreme Admin', 'guard_name' => 'api']);
        if ($managerRole) {
            $managerRole->givePermissionTo('manage-subscriptions');
        }

    }
    
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove permissions from roles
        try {
            $adminRole = Role::findByName('admin', 'api');
            if ($adminRole) {
                $adminRole->revokePermissionTo('manage-subscriptions');
            }
        } catch (\Exception $e) {
            // Role doesn't exist, skip
        }
        
        $managerRole = Role::firstOrCreate(['name' => 'Supreme Admin', 'guard_name' => 'api']);
        if ($managerRole) {
            $managerRole->revokePermissionTo('manage-subscriptions');
        }

        
        // Delete the permission
        $permission = Permission::findByName('manage-subscriptions', 'api');
        if ($permission) {
            $permission->delete();
        }
    }
};