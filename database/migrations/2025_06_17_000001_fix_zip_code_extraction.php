<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Drop the existing generated column and index
        try {
            DB::statement('DROP INDEX idx_businesses_zip_code ON businesses');
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            DB::statement('ALTER TABLE businesses DROP COLUMN zip_code');
        } catch (\Exception $e) {
            // Column might not exist, continue
        }

        // Create a more reliable zip code extraction
        // Strategy: Use REVERSE to get the last 5-digit number, which is more likely to be the zip code
        DB::statement("
            ALTER TABLE businesses ADD COLUMN zip_code VARCHAR(5) AS (
                REVERSE(REGEXP_SUBSTR(REVERSE(CONCAT(location, ', ', address)), '[0-9]{5}'))
            ) STORED
        ");

        // Create index on the new column
        DB::statement('CREATE INDEX idx_businesses_zip_code ON businesses(zip_code)');
    }

    public function down()
    {
        // Drop the index and column
        try {
            DB::statement('DROP INDEX idx_businesses_zip_code ON businesses');
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            DB::statement('ALTER TABLE businesses DROP COLUMN zip_code');
        } catch (\Exception $e) {
            // Column might not exist, continue
        }
        
        // Restore the original (problematic) column
        DB::statement(
            'ALTER TABLE businesses ADD COLUMN zip_code VARCHAR(5) AS '
            . '(REGEXP_SUBSTR(CONCAT(location, ", ", address), "[0-9]{5}")) STORED'
        );
        
        DB::statement('CREATE INDEX idx_businesses_zip_code ON businesses(zip_code)');
    }
};
