<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assets', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_key')->unique();
            $table->string('disk')->default('public');
            $table->string('mime_type')->nullable();
            $table->bigInteger('file_size')->nullable();
            $table->string('collection_name')->nullable();
            $table->json('custom_properties')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('job_booking_assets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_booking_id')->constrained()->onDelete('cascade');
            $table->foreignId('asset_id')->constrained()->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assets');
        Schema::dropIfExists('job_booking_assets');
    }
};