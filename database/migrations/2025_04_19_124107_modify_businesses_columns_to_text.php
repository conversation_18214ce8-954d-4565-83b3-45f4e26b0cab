<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->text('name')->nullable()->change();
            $table->text('category')->nullable()->change();
            $table->text('location')->nullable()->change();
            $table->text('phone')->nullable()->change();
            $table->text('website')->nullable()->change();
            $table->text('email')->nullable()->change();
            
            $table->longText('hours')->nullable()->change();
            $table->longText('photos')->nullable()->change();
            $table->longText('services')->nullable()->change();
            $table->longText('reviews')->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
            $table->string('category')->nullable()->change();
            $table->string('location')->nullable()->change();
            $table->string('phone')->nullable()->change();
            $table->string('website')->nullable()->change();
            $table->string('email')->nullable()->change();
            
            $table->json('hours')->nullable()->change();
            $table->json('photos')->nullable()->change();
            $table->json('services')->nullable()->change();
            $table->json('reviews')->nullable()->change();
        });
    }
}; 