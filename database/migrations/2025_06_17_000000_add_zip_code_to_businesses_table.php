<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Add generated zip_code column and create index
        DB::statement(
            'ALTER TABLE businesses ADD COLUMN zip_code VARCHAR(5) AS '
            . '(REGEXP_SUBSTR(CONCAT(location, ", ", address), "[0-9]{5}")) STORED'
        );
        
        DB::statement('CREATE INDEX idx_businesses_zip_code ON businesses(zip_code)');
    }

    public function down()
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropIndex('idx_businesses_zip_code');
            $table->dropColumn('zip_code');
        });
    }
};