<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing foreign key constraints now that all referenced tables exist

        // Business subscriptions table
        if (Schema::hasTable('business_subscriptions') && Schema::hasTable('businesses')) {
            Schema::table('business_subscriptions', function (Blueprint $table) {
                // Check if foreign key doesn't already exist
                $foreignKeys = collect(Schema::getForeignKeys('business_subscriptions'))->pluck('foreign_key');
                if (!$foreignKeys->contains('business_uuid')) {
                    $table->foreign('business_uuid')
                          ->references('business_uuid')
                          ->on('businesses')
                          ->onDelete('cascade');
                }
            });
        }

        if (Schema::hasTable('business_subscriptions') && Schema::hasTable('plans')) {
            Schema::table('business_subscriptions', function (Blueprint $table) {
                $foreignKeys = collect(Schema::getForeignKeys('business_subscriptions'))->pluck('foreign_key');
                if (!$foreignKeys->contains('plan_id')) {
                    $table->foreign('plan_id')
                          ->references('id')
                          ->on('plans')
                          ->onDelete('cascade');
                }
            });
        }

        // Business jobs table
        if (Schema::hasTable('business_jobs') && Schema::hasTable('job_bookings')) {
            Schema::table('business_jobs', function (Blueprint $table) {
                $foreignKeys = collect(Schema::getForeignKeys('business_jobs'))->pluck('foreign_key');
                if (!$foreignKeys->contains('job_booking_id')) {
                    $table->foreign('job_booking_id')
                          ->references('id')
                          ->on('job_bookings')
                          ->onDelete('cascade');
                }
            });
        }

        // Bids table
        if (Schema::hasTable('bids') && Schema::hasTable('job_bookings')) {
            Schema::table('bids', function (Blueprint $table) {
                $foreignKeys = collect(Schema::getForeignKeys('bids'))->pluck('foreign_key');
                if (!$foreignKeys->contains('job_booking_id')) {
                    $table->foreign('job_booking_id')
                          ->references('id')
                          ->on('job_bookings')
                          ->onDelete('cascade');
                }
            });
        }

        // Bookings table
        if (Schema::hasTable('bookings') && Schema::hasTable('job_bookings')) {
            Schema::table('bookings', function (Blueprint $table) {
                $foreignKeys = collect(Schema::getForeignKeys('bookings'))->pluck('foreign_key');
                if (!$foreignKeys->contains('job_booking_id')) {
                    $table->foreign('job_booking_id')
                          ->references('id')
                          ->on('job_bookings')
                          ->onDelete('set null');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop all the foreign key constraints we added

        if (Schema::hasTable('business_subscriptions')) {
            Schema::table('business_subscriptions', function (Blueprint $table) {
                try {
                    $table->dropForeign(['business_uuid']);
                } catch (\Exception $e) {
                    // Foreign key doesn't exist, continue
                }
                try {
                    $table->dropForeign(['plan_id']);
                } catch (\Exception $e) {
                    // Foreign key doesn't exist, continue
                }
            });
        }

        if (Schema::hasTable('business_jobs')) {
            Schema::table('business_jobs', function (Blueprint $table) {
                try {
                    $table->dropForeign(['job_booking_id']);
                } catch (\Exception $e) {
                    // Foreign key doesn't exist, continue
                }
            });
        }

        if (Schema::hasTable('bids')) {
            Schema::table('bids', function (Blueprint $table) {
                try {
                    $table->dropForeign(['job_booking_id']);
                } catch (\Exception $e) {
                    // Foreign key doesn't exist, continue
                }
            });
        }

        if (Schema::hasTable('bookings')) {
            Schema::table('bookings', function (Blueprint $table) {
                try {
                    $table->dropForeign(['job_booking_id']);
                } catch (\Exception $e) {
                    // Foreign key doesn't exist, continue
                }
            });
        }
    }
};
