<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update any existing 'pending' status to 'requested' if they exist
        DB::table('bids')->where('status', 'pending')->update(['status' => 'requested']);
        
        // Modify the enum to include 'requested' status
        DB::statement("ALTER TABLE bids MODIFY COLUMN status ENUM('requested', 'accepted', 'rejected') NOT NULL DEFAULT 'requested'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Update any 'requested' status back to 'pending' before reverting
        DB::table('bids')->where('status', 'requested')->update(['status' => 'pending']);
        
        // Revert the enum to original state
        DB::statement("ALTER TABLE bids MODIFY COLUMN status ENUM('accepted', 'rejected') NOT NULL");
    }
};