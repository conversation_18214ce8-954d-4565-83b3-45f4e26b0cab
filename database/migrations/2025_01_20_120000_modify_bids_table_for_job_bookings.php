<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bids', function (Blueprint $table) {
            // Check if service_request_id column exists before trying to drop it
            if (Schema::hasColumn('bids', 'service_request_id')) {
                // Drop the existing foreign key constraint for service_request_id if it exists
                try {
                    $table->dropForeign(['service_request_id']);
                } catch (\Exception $e) {
                    // Foreign key doesn't exist, continue
                }

                // Drop the service_request_id column
                $table->dropColumn('service_request_id');
            }

            // Add job_booking_id column if it doesn't exist
            if (!Schema::hasColumn('bids', 'job_booking_id')) {
                $table->unsignedBigInteger('job_booking_id')->nullable()->after('id');
            }

            // Only add foreign key if job_bookings table exists
            if (Schema::hasTable('job_bookings')) {
                $table->foreign('job_booking_id')->references('id')->on('job_bookings')->onDelete('cascade');
            }

            // Add estimated_completion_time column if it doesn't exist
            if (!Schema::hasColumn('bids', 'estimated_completion_time')) {
                $table->timestamp('estimated_completion_time')->nullable()->after('description');
            }

            // Add index for performance if it doesn't exist
            if (!Schema::hasColumn('bids', 'job_booking_id') || !collect(Schema::getIndexes('bids'))->contains(function ($index) {
                return in_array('job_booking_id', $index['columns']);
            })) {
                $table->index('job_booking_id');
            }
        });
        
        // Update the status enum to include 'withdrawn' status
        DB::statement("ALTER TABLE bids MODIFY COLUMN status ENUM('requested', 'accepted', 'rejected', 'withdrawn') NOT NULL DEFAULT 'requested'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bids', function (Blueprint $table) {
            // Drop the job_booking_id foreign key and column
            $table->dropForeign(['job_booking_id']);
            $table->dropIndex(['job_booking_id']);
            $table->dropColumn('job_booking_id');
            
            // Drop estimated_completion_time column
            $table->dropColumn('estimated_completion_time');
            
            // Re-add service_request_id column and foreign key
            $table->unsignedBigInteger('service_request_id')->nullable()->after('id');
            $table->foreign('service_request_id')->references('id')->on('service_requests')->onDelete('cascade');
        });
        
        // Revert the status enum to original state
        DB::statement("ALTER TABLE bids MODIFY COLUMN status ENUM('requested', 'accepted', 'rejected') NOT NULL DEFAULT 'requested'");
    }
};
