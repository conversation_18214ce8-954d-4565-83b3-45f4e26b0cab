<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rating', function (Blueprint $table) {
            $table->id();
            $table->integer('rating')->nullable();
            $table->longText('description')->nullable();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('error_type')->nullable();
            $table->bigInteger('consumer_id')->unsigned();
            $table->timestamps();

            $table->foreign('consumer_id')->references('id')->on('users')->onDelete('cascade')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rating');
    }
};
