<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('businesses', function (Blueprint $table) {
            // First modify the column to ensure consistent length
            $table->string('location', 255)->nullable()->change();
            // Then add the index
            $table->index('location', 'idx_business_location');
        });
    }

    public function down()
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropIndex('idx_business_location');
            // Revert the column type change is not necessary as it doesn't affect functionality
        });
    }
};