<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Subscription\Entities\Plan;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing plans if needed
        // Plan::truncate();
        
        // Create the Starter (Free) Plan
        Plan::create([
            'name' => 'Starter',
            'price' => 0, // Free
            'duration' => 'monthly', // Changed from 30 to 'monthly'
            'description' => 'Basic plan for getting started',
            'status' => 1, // Active
            'max_services' => 10, // Set appropriate limits
            'max_addresses' => 5,
            'max_servicemen' => 5,
            'max_service_packages' => 5,
            // Note: created_by will be set by the boot method in the Plan model
            // If running from seeder, you might need to set it manually
            'created_by' => 1, // Assuming admin user has ID 1
        ]);
        
        // Create the Pro Plan
        Plan::create([
            'name' => 'Pro',
            'price' => 159, // $159
            'duration' => 'monthly', // Changed from 30 to 'monthly'
            'description' => 'Professional plan with enhanced features',
            'status' => 1, // Active
            'max_services' => 50,
            'max_addresses' => 20,
            'max_servicemen' => 20,
            'max_service_packages' => 20,
            'created_by' => 1,
        ]);
        
        // Create the Elite Plan
        Plan::create([
            'name' => 'Elite',
            'price' => 299, // $299
            'duration' => 'monthly', // Changed from 30 to 'monthly'
            'description' => 'Premium plan with unlimited features',
            'status' => 1, // Active
            'max_services' => 100,
            'max_addresses' => 50,
            'max_servicemen' => 50,
            'max_service_packages' => 50,
            'created_by' => 1,
        ]);
    }
}