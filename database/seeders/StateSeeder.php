<?php

namespace Database\Seeders;

use App\Models\State;
use Illuminate\Database\Seeder;

class StateSeeder extends Seeder
{
    private $states = [
        ['name' => 'Andaman and Nicobar Islands', 'country_id' => 356],
        ['name' => 'Andhra Pradesh', 'country_id' => 356],
        ['name' => 'Arunachal Pradesh', 'country_id' => 356],
        ['name' => 'Assam', 'country_id' => 356],
        ['name' => 'Bihar', 'country_id' => 356],
        ['name' => 'Chandigarh', 'country_id' => 356],
        ['name' => 'Chhattisgarh', 'country_id' => 356],
        ['name' => 'Dadra and Nagar Haveli', 'country_id' => 356],
        ['name' => 'Daman and Diu', 'country_id' => 356],
        ['name' => 'Delhi', 'country_id' => 356],
        ['name' => 'Goa', 'country_id' => 356],
        ['name' => 'Gujarat', 'country_id' => 356],
        ['name' => 'Haryana', 'country_id' => 356],
        ['name' => 'Himachal Pradesh', 'country_id' => 356],
        ['name' => 'Jammu and Kashmir', 'country_id' => 356],
        ['name' => 'Jharkhand', 'country_id' => 356],
        ['name' => 'Karnataka', 'country_id' => 356],
        ['name' => 'Kenmore', 'country_id' => 356],
        ['name' => 'Kerala', 'country_id' => 356],
        ['name' => 'Lakshadweep', 'country_id' => 356],
        ['name' => 'Madhya Pradesh', 'country_id' => 356],
        ['name' => 'Maharashtra', 'country_id' => 356],
        ['name' => 'Manipur', 'country_id' => 356],
        ['name' => 'Meghalaya', 'country_id' => 356],
        ['name' => 'Mizoram', 'country_id' => 356],
        ['name' => 'Nagaland', 'country_id' => 356],
        ['name' => 'Narora', 'country_id' => 356],
        ['name' => 'Natwar', 'country_id' => 356],
        ['name' => 'Odisha', 'country_id' => 356],
        ['name' => 'Paschim Medinipur', 'country_id' => 356],
        ['name' => 'Pondicherry', 'country_id' => 356],
        ['name' => 'Punjab', 'country_id' => 356],
        ['name' => 'Rajasthan', 'country_id' => 356],
        ['name' => 'Sikkim', 'country_id' => 356],
        ['name' => 'Tamil Nadu', 'country_id' => 356],
        ['name' => 'Telangana', 'country_id' => 356],
        ['name' => 'Tripura', 'country_id' => 356],
        ['name' => 'Uttar Pradesh', 'country_id' => 356],
        ['name' => 'Uttarakhand', 'country_id' => 356],
        ['name' => 'Vaishali', 'country_id' => 356],
        ['name' => 'West Bengal', 'country_id' => 356],
        ['name' => 'Badakhshan', 'country_id' => 4],
        ['name' => 'Badgis', 'country_id' => 4],
        ['name' => 'Baglan', 'country_id' => 4],
        ['name' => 'Balkh', 'country_id' => 4],
        ['name' => 'Bamiyan', 'country_id' => 4],
        ['name' => 'Farah', 'country_id' => 4],
        ['name' => 'Faryab', 'country_id' => 4],
        ['name' => 'Gawr', 'country_id' => 4],
        ['name' => 'Gazni', 'country_id' => 4],
        ['name' => 'Herat', 'country_id' => 4],
        ['name' => 'Hilmand', 'country_id' => 4],
        ['name' => 'Jawzjan', 'country_id' => 4],
        ['name' => 'Kabul', 'country_id' => 4],
        ['name' => 'Kapisa', 'country_id' => 4],
        ['name' => 'Khawst', 'country_id' => 4],
        ['name' => 'Kunar', 'country_id' => 4],
        ['name' => 'Lagman', 'country_id' => 4],
        ['name' => 'Lawghar', 'country_id' => 4],
        ['name' => 'Nangarhar', 'country_id' => 4],
        ['name' => 'Nimruz', 'country_id' => 4],
        ['name' => 'Nuristan', 'country_id' => 4],
        ['name' => 'Paktika', 'country_id' => 4],
        ['name' => 'Paktiya', 'country_id' => 4],
        ['name' => 'Parwan', 'country_id' => 4],
        ['name' => 'Qandahar', 'country_id' => 4],
        ['name' => 'Qunduz', 'country_id' => 4],
        ['name' => 'Samangan', 'country_id' => 4],
        ['name' => 'Sar-e Pul', 'country_id' => 4],
        ['name' => 'Takhar', 'country_id' => 4],
        ['name' => 'Uruzgan', 'country_id' => 4],
        ['name' => 'Wardak', 'country_id' => 4],
        ['name' => 'Zabul', 'country_id' => 4],
        ['name' => 'Berat', 'country_id' => 8],
        ['name' => 'Bulqize', 'country_id' => 8],
        ['name' => 'Delvine', 'country_id' => 8],
        ['name' => 'Devoll', 'country_id' => 8],
        ['name' => 'Dibre', 'country_id' => 8],
        ['name' => 'Durres', 'country_id' => 8],
        ['name' => 'Elbasan', 'country_id' => 8],
        ['name' => 'Fier', 'country_id' => 8],
        ['name' => 'Gjirokaster', 'country_id' => 8],
        ['name' => 'Gramsh', 'country_id' => 8],
        ['name' => 'Has', 'country_id' => 8],
        ['name' => 'Kavaje', 'country_id' => 8],
        ['name' => 'Kolonje', 'country_id' => 8],
        ['name' => 'Korce', 'country_id' => 8],
        ['name' => 'Kruje', 'country_id' => 8],
        ['name' => 'Kucove', 'country_id' => 8],
        ['name' => 'Kukes', 'country_id' => 8],
        ['name' => 'Kurbin', 'country_id' => 8],
        ['name' => 'Lezhe', 'country_id' => 8],
        ['name' => 'Librazhd', 'country_id' => 8],
        ['name' => 'Lushnje', 'country_id' => 8],
        ['name' => 'Mallakaster', 'country_id' => 8],
        ['name' => 'Malsi e Madhe', 'country_id' => 8],
        ['name' => 'Mat', 'country_id' => 8],
        ['name' => 'Mirdite', 'country_id' => 8],
        ['name' => 'Peqin', 'country_id' => 8],
        ['name' => 'Permet', 'country_id' => 8],
        ['name' => 'Pogradec', 'country_id' => 8],
        ['name' => 'Puke', 'country_id' => 8],
        ['name' => 'Sarande', 'country_id' => 8],
        ['name' => 'Shkoder', 'country_id' => 8],
        ['name' => 'Skrapar', 'country_id' => 8],
        ['name' => 'Tepelene', 'country_id' => 8],
        ['name' => 'Tirane', 'country_id' => 8],
        ['name' => 'Tropoje', 'country_id' => 8],
        ['name' => 'Vlore', 'country_id' => 8],
        ['name' => "'Ayn Daflah", 'country_id' => 12],
        ['name' => "'Ayn Tamushanat", 'country_id' => 12],
        ['name' => 'Adrar', 'country_id' => 12],
        ['name' => 'Algiers', 'country_id' => 12],
        ['name' => 'Annabah', 'country_id' => 12],
        ['name' => 'Bashshar', 'country_id' => 12],
        ['name' => 'Batnah', 'country_id' => 12],
        ['name' => 'Bijayah', 'country_id' => 12],
        ['name' => 'Biskrah', 'country_id' => 12],
        ['name' => 'Blidah', 'country_id' => 12],
        ['name' => 'Buirah', 'country_id' => 12],
        ['name' => 'Bumardas', 'country_id' => 12],
        ['name' => 'Burj Bu Arririj', 'country_id' => 12],
        ['name' => 'Ghalizan', 'country_id' => 12],
        ['name' => 'Ghardayah', 'country_id' => 12],
        ['name' => 'Ilizi', 'country_id' => 12],
        ['name' => 'Jijili', 'country_id' => 12],
        ['name' => 'Jilfah', 'country_id' => 12],
        ['name' => 'Khanshalah', 'country_id' => 12],
        ['name' => 'Masilah', 'country_id' => 12],
        ['name' => 'Midyah', 'country_id' => 12],
        ['name' => 'Milah', 'country_id' => 12],
        ['name' => 'Muaskar', 'country_id' => 12],
        ['name' => 'Mustaghanam', 'country_id' => 12],
        ['name' => 'Naama', 'country_id' => 12],
        ['name' => 'Oran', 'country_id' => 12],
        ['name' => 'Ouargla', 'country_id' => 12],
        ['name' => 'Qalmah', 'country_id' => 12],
        ['name' => 'Qustantinah', 'country_id' => 12],
        ['name' => 'Sakikdah', 'country_id' => 12],
        ['name' => 'Satif', 'country_id' => 12],
        ['name' => "Sayda'", 'country_id' => 12],
        ['name' => "Sidi ban-al-'Abbas", 'country_id' => 12],
        ['name' => 'Suq Ahras', 'country_id' => 12],
        ['name' => 'Tamanghasat', 'country_id' => 12],
        ['name' => 'Tibazah', 'country_id' => 12],
        ['name' => 'Tibissah', 'country_id' => 12],
        ['name' => 'Tilimsan', 'country_id' => 12],
        ['name' => 'Tinduf', 'country_id' => 12],
        ['name' => 'Tisamsilt', 'country_id' => 12],
        ['name' => 'Tiyarat', 'country_id' => 12],
        ['name' => 'Tizi Wazu', 'country_id' => 12],
        ['name' => 'Umm-al-Bawaghi', 'country_id' => 12],
        ['name' => 'Wahran', 'country_id' => 12],
        ['name' => 'Warqla', 'country_id' => 12],
        ['name' => 'Wilaya d Alger', 'country_id' => 12],
        ['name' => 'Wilaya de Bejaia', 'country_id' => 12],
        ['name' => 'Wilaya de Constantine', 'country_id' => 12],
        ['name' => 'al-Aghwat', 'country_id' => 12],
        ['name' => 'al-Bayadh', 'country_id' => 12],
        ['name' => "al-Jaza'ir", 'country_id' => 12],
        ['name' => 'al-Wad', 'country_id' => 12],
        ['name' => 'ash-Shalif', 'country_id' => 12],
        ['name' => 'at-Tarif', 'country_id' => 12],
        ['name' => 'Eastern', 'country_id' => 16],
        ['name' => "Manu'a", 'country_id' => 16],
        ['name' => 'Swains Island', 'country_id' => 16],
        ['name' => 'Western', 'country_id' => 16],
        ['name' => 'Andorra la Vella', 'country_id' => 20],
        ['name' => 'Canillo', 'country_id' => 20],
        ['name' => 'Encamp', 'country_id' => 20],
        ['name' => 'La Massana', 'country_id' => 20],
        ['name' => 'Les Escaldes', 'country_id' => 20],
        ['name' => 'Ordino', 'country_id' => 20],
        ['name' => 'Sant Julia de Loria', 'country_id' => 20],
        ['name' => 'Bengo', 'country_id' => 24],
        ['name' => 'Benguela', 'country_id' => 24],
        ['name' => 'Bie', 'country_id' => 24],
        ['name' => 'Cabinda', 'country_id' => 24],
        ['name' => 'Cunene', 'country_id' => 24],
        ['name' => 'Huambo', 'country_id' => 24],
        ['name' => 'Huila', 'country_id' => 24],
        ['name' => 'Kuando-Kubango', 'country_id' => 24],
        ['name' => 'Kwanza Norte', 'country_id' => 24],
        ['name' => 'Kwanza Sul', 'country_id' => 24],
        ['name' => 'Luanda', 'country_id' => 24],
        ['name' => 'Lunda Norte', 'country_id' => 24],
        ['name' => 'Lunda Sul', 'country_id' => 24],
        ['name' => 'Malanje', 'country_id' => 24],
        ['name' => 'Moxico', 'country_id' => 24],
        ['name' => 'Namibe', 'country_id' => 24],
        ['name' => 'Uige', 'country_id' => 24],
        ['name' => 'Zaire', 'country_id' => 24],
        ['name' => 'Other Provinces', 'country_id' => 24],
        ['name' => 'Sector claimed by Argentina/Ch', 'country_id' => 10],
        ['name' => 'Sector claimed by Argentina/UK', 'country_id' => 10],
        ['name' => 'Sector claimed by Australia', 'country_id' => 10],
        ['name' => 'Sector claimed by France', 'country_id' => 10],
        ['name' => 'Sector claimed by New Zealand', 'country_id' => 10],
        ['name' => 'Sector claimed by Norway', 'country_id' => 10],
        ['name' => 'Unclaimed Sector', 'country_id' => 10],
        ['name' => 'Barbuda', 'country_id' => 28],
        ['name' => 'Saint George', 'country_id' => 28],
        ['name' => 'Saint John', 'country_id' => 28],
        ['name' => 'Saint Mary', 'country_id' => 28],
        ['name' => 'Saint Paul', 'country_id' => 28],
        ['name' => 'Saint Peter', 'country_id' => 28],
        ['name' => 'Saint Philip', 'country_id' => 28],
        ['name' => 'Buenos Aires', 'country_id' => 32],
        ['name' => 'Catamarca', 'country_id' => 32],
        ['name' => 'Chaco', 'country_id' => 32],
        ['name' => 'Chubut', 'country_id' => 32],
        ['name' => 'Cordoba', 'country_id' => 32],
        ['name' => 'Corrientes', 'country_id' => 32],
        ['name' => 'Distrito Federal', 'country_id' => 32],
        ['name' => 'Entre Rios', 'country_id' => 32],
        ['name' => 'Formosa', 'country_id' => 32],
        ['name' => 'Jujuy', 'country_id' => 32],
        ['name' => 'La Pampa', 'country_id' => 32],
        ['name' => 'La Rioja', 'country_id' => 32],
        ['name' => 'Mendoza', 'country_id' => 32],
        ['name' => 'Misiones', 'country_id' => 32],
        ['name' => 'Neuquen', 'country_id' => 32],
        ['name' => 'Rio Negro', 'country_id' => 32],
        ['name' => 'Salta', 'country_id' => 32],
        ['name' => 'San Juan', 'country_id' => 32],
        ['name' => 'San Luis', 'country_id' => 32],
        ['name' => 'Santa Cruz', 'country_id' => 32],
        ['name' => 'Santa Fe', 'country_id' => 32],
        ['name' => 'Santiago del Estero', 'country_id' => 32],
        ['name' => 'Tierra del Fuego', 'country_id' => 32],
        ['name' => 'Tucuman', 'country_id' => 32],
        ['name' => 'Aragatsotn', 'country_id' => 51],
        ['name' => 'Ararat', 'country_id' => 51],
        ['name' => 'Armavir', 'country_id' => 51],
        ['name' => 'Gegharkunik', 'country_id' => 51],
        ['name' => 'Kotaik', 'country_id' => 51],
        ['name' => 'Lori', 'country_id' => 51],
        ['name' => 'Shirak', 'country_id' => 51],
        ['name' => 'Stepanakert', 'country_id' => 51],
        ['name' => 'Syunik', 'country_id' => 51],
        ['name' => 'Tavush', 'country_id' => 51],
        ['name' => 'Vayots Dzor', 'country_id' => 51],
        ['name' => 'Yerevan', 'country_id' => 51],
        ['name' => 'Aruba', 'country_id' => 533],
        ['name' => 'Auckland', 'country_id' => 36],
        ['name' => 'Australian Capital Territory', 'country_id' => 36],
        ['name' => 'Balgowlah', 'country_id' => 36],
        ['name' => 'Balmain', 'country_id' => 36],
        ['name' => 'Bankstown', 'country_id' => 36],
        ['name' => 'Baulkham Hills', 'country_id' => 36],
        ['name' => 'Bonnet Bay', 'country_id' => 36],
        ['name' => 'Camberwell', 'country_id' => 36],
        ['name' => 'Carole Park', 'country_id' => 36],
        ['name' => 'Castle Hill', 'country_id' => 36],
        ['name' => 'Caulfield', 'country_id' => 36],
        ['name' => 'Chatswood', 'country_id' => 36],
        ['name' => 'Cheltenham', 'country_id' => 36],
        ['name' => 'Cherrybrook', 'country_id' => 36],
        ['name' => 'Clayton', 'country_id' => 36],
        ['name' => 'Collingwood', 'country_id' => 36],
        ['name' => 'Frenchs Forest', 'country_id' => 36],
        ['name' => 'Hawthorn', 'country_id' => 36],
        ['name' => 'Jannnali', 'country_id' => 36],
        ['name' => 'Knoxfield', 'country_id' => 36],
        ['name' => 'Melbourne', 'country_id' => 36],
        ['name' => 'New South Wales', 'country_id' => 36],
        ['name' => 'Northern Territory', 'country_id' => 36],
        ['name' => 'Perth', 'country_id' => 36],
        ['name' => 'Queensland', 'country_id' => 36],
        ['name' => 'South Australia', 'country_id' => 36],
        ['name' => 'Tasmania', 'country_id' => 36],
        ['name' => 'Templestowe', 'country_id' => 36],
        ['name' => 'Victoria', 'country_id' => 36],
        ['name' => 'Werribee south', 'country_id' => 36],
        ['name' => 'Western Australia', 'country_id' => 36],
        ['name' => 'Wheeler', 'country_id' => 36],
        ['name' => 'Bundesland Salzburg', 'country_id' => 40],
        ['name' => 'Bundesland Steiermark', 'country_id' => 40],
        ['name' => 'Bundesland Tirol', 'country_id' => 40],
        ['name' => 'Burgenland', 'country_id' => 40],
        ['name' => 'Carinthia', 'country_id' => 40],
        ['name' => 'Karnten', 'country_id' => 40],
        ['name' => 'Liezen', 'country_id' => 40],
        ['name' => 'Lower Austria', 'country_id' => 40],
        ['name' => 'Niederosterreich', 'country_id' => 40],
        ['name' => 'Oberosterreich', 'country_id' => 40],
        ['name' => 'Salzburg', 'country_id' => 40],
        ['name' => 'Schleswig-Holstein', 'country_id' => 40],
        ['name' => 'Steiermark', 'country_id' => 40],
        ['name' => 'Styria', 'country_id' => 40],
        ['name' => 'Tirol', 'country_id' => 40],
        ['name' => 'Upper Austria', 'country_id' => 40],
        ['name' => 'Vorarlberg', 'country_id' => 40],
        ['name' => 'Wien', 'country_id' => 40],
        ['name' => 'Abseron', 'country_id' => 31],
        ['name' => 'Baki Sahari', 'country_id' => 31],
        ['name' => 'Ganca', 'country_id' => 31],
        ['name' => 'Ganja', 'country_id' => 31],
        ['name' => 'Kalbacar', 'country_id' => 31],
        ['name' => 'Lankaran', 'country_id' => 31],
        ['name' => 'Mil-Qarabax', 'country_id' => 31],
        ['name' => 'Mugan-Salyan', 'country_id' => 31],
        ['name' => 'Nagorni-Qarabax', 'country_id' => 31],
        ['name' => 'Naxcivan', 'country_id' => 31],
        ['name' => 'Priaraks', 'country_id' => 31],
        ['name' => 'Qazax', 'country_id' => 31],
        ['name' => 'Saki', 'country_id' => 31],
        ['name' => 'Sirvan', 'country_id' => 31],
        ['name' => 'Xacmaz', 'country_id' => 31],
        ['name' => 'Abaco', 'country_id' => 44],
        ['name' => 'Acklins Island', 'country_id' => 44],
        ['name' => 'Andros', 'country_id' => 44],
        ['name' => 'Berry Islands', 'country_id' => 44],
        ['name' => 'Biminis', 'country_id' => 44],
        ['name' => 'Cat Island', 'country_id' => 44],
        ['name' => 'Crooked Island', 'country_id' => 44],
        ['name' => 'Eleuthera', 'country_id' => 44],
        ['name' => 'Exuma and Cays', 'country_id' => 44],
        ['name' => 'Grand Bahama', 'country_id' => 44],
        ['name' => 'Inagua Islands', 'country_id' => 44],
        ['name' => 'Long Island', 'country_id' => 44],
        ['name' => 'Mayaguana', 'country_id' => 44],
        ['name' => 'New Providence', 'country_id' => 44],
        ['name' => 'Ragged Island', 'country_id' => 44],
        ['name' => 'Rum Cay', 'country_id' => 44],
        ['name' => 'San Salvador', 'country_id' => 44],
        ['name' => "'Isa", 'country_id' => 48],
        ['name' => 'Badiyah', 'country_id' => 48],
        ['name' => 'Hidd', 'country_id' => 48],
        ['name' => 'Jidd Hafs', 'country_id' => 48],
        ['name' => 'Mahama', 'country_id' => 48],
        ['name' => 'Manama', 'country_id' => 48],
        ['name' => 'Sitrah', 'country_id' => 48],
        ['name' => 'al-Manamah', 'country_id' => 48],
        ['name' => 'al-Muharraq', 'country_id' => 48],
        ['name' => "ar-Rifa'a", 'country_id' => 48],
        ['name' => 'Bagar Hat', 'country_id' => 50],
        ['name' => 'Bandarban', 'country_id' => 50],
        ['name' => 'Barguna', 'country_id' => 50],
        ['name' => 'Barisal', 'country_id' => 50],
        ['name' => 'Bhola', 'country_id' => 50],
        ['name' => 'Bogora', 'country_id' => 50],
        ['name' => 'Brahman Bariya', 'country_id' => 50],
        ['name' => 'Chandpur', 'country_id' => 50],
        ['name' => 'Chattagam', 'country_id' => 50],
        ['name' => 'Chittagong Division', 'country_id' => 50],
        ['name' => 'Chuadanga', 'country_id' => 50],
        ['name' => 'Dhaka', 'country_id' => 50],
        ['name' => 'Dinajpur', 'country_id' => 50],
        ['name' => 'Faridpur', 'country_id' => 50],
        ['name' => 'Feni', 'country_id' => 50],
        ['name' => 'Gaybanda', 'country_id' => 50],
        ['name' => 'Gazipur', 'country_id' => 50],
        ['name' => 'Gopalganj', 'country_id' => 50],
        ['name' => 'Habiganj', 'country_id' => 50],
        ['name' => 'Jaipur Hat', 'country_id' => 50],
        ['name' => 'Jamalpur', 'country_id' => 50],
        ['name' => 'Jessor', 'country_id' => 50],
        ['name' => 'Jhalakati', 'country_id' => 50],
        ['name' => 'Jhanaydah', 'country_id' => 50],
        ['name' => 'Khagrachhari', 'country_id' => 50],
        ['name' => 'Khulna', 'country_id' => 50],
        ['name' => 'Kishorganj', 'country_id' => 50],
        ['name' => 'Koks Bazar', 'country_id' => 50],
        ['name' => 'Komilla', 'country_id' => 50],
        ['name' => 'Kurigram', 'country_id' => 50],
        ['name' => 'Kushtiya', 'country_id' => 50],
        ['name' => 'Lakshmipur', 'country_id' => 50],
        ['name' => 'Lalmanir Hat', 'country_id' => 50],
        ['name' => 'Madaripur', 'country_id' => 50],
        ['name' => 'Magura', 'country_id' => 50],
        ['name' => 'Maimansingh', 'country_id' => 50],
        ['name' => 'Manikganj', 'country_id' => 50],
        ['name' => 'Maulvi Bazar', 'country_id' => 50],
        ['name' => 'Meherpur', 'country_id' => 50],
        ['name' => 'Munshiganj', 'country_id' => 50],
        ['name' => 'Naral', 'country_id' => 50],
        ['name' => 'Narayanganj', 'country_id' => 50],
        ['name' => 'Narsingdi', 'country_id' => 50],
        ['name' => 'Nator', 'country_id' => 50],
        ['name' => 'Naugaon', 'country_id' => 50],
        ['name' => 'Nawabganj', 'country_id' => 50],
        ['name' => 'Netrakona', 'country_id' => 50],
        ['name' => 'Nilphamari', 'country_id' => 50],
        ['name' => 'Noakhali', 'country_id' => 50],
        ['name' => 'Pabna', 'country_id' => 50],
        ['name' => 'Panchagarh', 'country_id' => 50],
        ['name' => 'Patuakhali', 'country_id' => 50],
        ['name' => 'Pirojpur', 'country_id' => 50],
        ['name' => 'Rajbari', 'country_id' => 50],
        ['name' => 'Rajshahi', 'country_id' => 50],
        ['name' => 'Rangamati', 'country_id' => 50],
        ['name' => 'Rangpur', 'country_id' => 50],
        ['name' => 'Satkhira', 'country_id' => 50],
        ['name' => 'Shariatpur', 'country_id' => 50],
        ['name' => 'Sherpur', 'country_id' => 50],
        ['name' => 'Silhat', 'country_id' => 50],
        ['name' => 'Sirajganj', 'country_id' => 50],
        ['name' => 'Sunamganj', 'country_id' => 50],
        ['name' => 'Tangayal', 'country_id' => 50],
        ['name' => 'Thakurgaon', 'country_id' => 50],
        ['name' => 'Christ Church', 'country_id' => 52],
        ['name' => 'Saint Andrew', 'country_id' => 52],
        ['name' => 'Saint George', 'country_id' => 52],
        ['name' => 'Saint James', 'country_id' => 52],
        ['name' => 'Saint John', 'country_id' => 52],
        ['name' => 'Saint Joseph', 'country_id' => 52],
        ['name' => 'Saint Lucy', 'country_id' => 52],
        ['name' => 'Saint Michael', 'country_id' => 52],
        ['name' => 'Saint Peter', 'country_id' => 52],
        ['name' => 'Saint Philip', 'country_id' => 52],
        ['name' => 'Saint Thomas', 'country_id' => 52],
        ['name' => 'Brest', 'country_id' => 112],
        ['name' => "Homjel'", 'country_id' => 112],
        ['name' => 'Hrodna', 'country_id' => 112],
        ['name' => 'Mahiljow', 'country_id' => 112],
        ['name' => 'Mahilyowskaya Voblasts', 'country_id' => 112],
        ['name' => 'Minsk', 'country_id' => 112],
        ['name' => "Minskaja Voblasts'", 'country_id' => 112],
        ['name' => 'Petrik', 'country_id' => 112],
        ['name' => 'Vicebsk', 'country_id' => 112],
        ['name' => 'Antwerpen', 'country_id' => 56],
        ['name' => 'Berchem', 'country_id' => 56],
        ['name' => 'Brabant', 'country_id' => 56],
        ['name' => 'Brabant Wallon', 'country_id' => 56],
        ['name' => 'Brussel', 'country_id' => 56],
        ['name' => 'East Flanders', 'country_id' => 56],
        ['name' => 'Hainaut', 'country_id' => 56],
        ['name' => 'Liege', 'country_id' => 56],
        ['name' => 'Limburg', 'country_id' => 56],
        ['name' => 'Luxembourg', 'country_id' => 56],
        ['name' => 'Namur', 'country_id' => 56],
        ['name' => 'Ontario', 'country_id' => 56],
        ['name' => 'Oost-Vlaanderen', 'country_id' => 56],
        ['name' => 'Provincie Brabant', 'country_id' => 56],
        ['name' => 'Vlaams-Brabant', 'country_id' => 56],
        ['name' => 'Wallonne', 'country_id' => 56],
        ['name' => 'West-Vlaanderen', 'country_id' => 56],
        ['name' => 'Belize', 'country_id' => 84],
        ['name' => 'Cayo', 'country_id' => 84],
        ['name' => 'Corozal', 'country_id' => 84],
        ['name' => 'Orange Walk', 'country_id' => 84],
        ['name' => 'Stann Creek', 'country_id' => 84],
        ['name' => 'Toledo', 'country_id' => 84],
        ['name' => 'Alibori', 'country_id' => 204],
        ['name' => 'Atacora', 'country_id' => 204],
        ['name' => 'Atlantique', 'country_id' => 204],
        ['name' => 'Borgou', 'country_id' => 204],
        ['name' => 'Collines', 'country_id' => 204],
        ['name' => 'Couffo', 'country_id' => 204],
        ['name' => 'Donga', 'country_id' => 204],
        ['name' => 'Littoral', 'country_id' => 204],
        ['name' => 'Mono', 'country_id' => 204],
        ['name' => 'Oueme', 'country_id' => 204],
        ['name' => 'Plateau', 'country_id' => 204],
        ['name' => 'Zou', 'country_id' => 204],
        ['name' => 'Hamilton', 'country_id' => 60],
        ['name' => 'Saint George', 'country_id' => 60],
        ['name' => 'Bumthang', 'country_id' => 64],
        ['name' => 'Chhukha', 'country_id' => 64],
        ['name' => 'Chirang', 'country_id' => 64],
        ['name' => 'Daga', 'country_id' => 64],
        ['name' => 'Geylegphug', 'country_id' => 64],
        ['name' => 'Ha', 'country_id' => 64],
        ['name' => 'Lhuntshi', 'country_id' => 64],
        ['name' => 'Mongar', 'country_id' => 64],
        ['name' => 'Pemagatsel', 'country_id' => 64],
        ['name' => 'Punakha', 'country_id' => 64],
        ['name' => 'Rinpung', 'country_id' => 64],
        ['name' => 'Samchi', 'country_id' => 64],
        ['name' => 'Samdrup Jongkhar', 'country_id' => 64],
        ['name' => 'Shemgang', 'country_id' => 64],
        ['name' => 'Tashigang', 'country_id' => 64],
        ['name' => 'Timphu', 'country_id' => 64],
        ['name' => 'Tongsa', 'country_id' => 64],
        ['name' => 'Wangdiphodrang', 'country_id' => 64],
        ['name' => 'Beni', 'country_id' => 68],
        ['name' => 'Chuquisaca', 'country_id' => 68],
        ['name' => 'Cochabamba', 'country_id' => 68],
        ['name' => 'La Paz', 'country_id' => 68],
        ['name' => 'Oruro', 'country_id' => 68],
        ['name' => 'Pando', 'country_id' => 68],
        ['name' => 'Potosi', 'country_id' => 68],
        ['name' => 'Santa Cruz', 'country_id' => 68],
        ['name' => 'Tarija', 'country_id' => 68],
        ['name' => 'Federacija Bosna i Hercegovina', 'country_id' => 70],
        ['name' => 'Republika Srpska', 'country_id' => 70],
        ['name' => 'Central Bobonong', 'country_id' => 72],
        ['name' => 'Central Boteti', 'country_id' => 72],
        ['name' => 'Central Mahalapye', 'country_id' => 72],
        ['name' => 'Central Serowe-Palapye', 'country_id' => 72],
        ['name' => 'Central Tutume', 'country_id' => 72],
        ['name' => 'Chobe', 'country_id' => 72],
        ['name' => 'Francistown', 'country_id' => 72],
        ['name' => 'Gaborone', 'country_id' => 72],
        ['name' => 'Ghanzi', 'country_id' => 72],
        ['name' => 'Jwaneng', 'country_id' => 72],
        ['name' => 'Kgalagadi North', 'country_id' => 72],
        ['name' => 'Kgalagadi South', 'country_id' => 72],
        ['name' => 'Kgatleng', 'country_id' => 72],
        ['name' => 'Kweneng', 'country_id' => 72],
        ['name' => 'Lobatse', 'country_id' => 72],
        ['name' => 'Ngamiland', 'country_id' => 72],
        ['name' => 'Ngwaketse', 'country_id' => 72],
        ['name' => 'North East', 'country_id' => 72],
        ['name' => 'Okavango', 'country_id' => 72],
        ['name' => 'Orapa', 'country_id' => 72],
        ['name' => 'Selibe Phikwe', 'country_id' => 72],
        ['name' => 'South East', 'country_id' => 72],
        ['name' => 'Sowa', 'country_id' => 72],
        ['name' => 'Bouvet Island', 'country_id' => 74],
        ['name' => 'Acre', 'country_id' => 76],
        ['name' => 'Alagoas', 'country_id' => 76],
        ['name' => 'Amapa', 'country_id' => 76],
        ['name' => 'Amazonas', 'country_id' => 76],
        ['name' => 'Bahia', 'country_id' => 76],
        ['name' => 'Ceara', 'country_id' => 76],
        ['name' => 'Distrito Federal', 'country_id' => 76],
        ['name' => 'Espirito Santo', 'country_id' => 76],
        ['name' => 'Estado de Sao Paulo', 'country_id' => 76],
        ['name' => 'Goias', 'country_id' => 76],
        ['name' => 'Maranhao', 'country_id' => 76],
        ['name' => 'Mato Grosso', 'country_id' => 76],
        ['name' => 'Mato Grosso do Sul', 'country_id' => 76],
        ['name' => 'Minas Gerais', 'country_id' => 76],
        ['name' => 'Para', 'country_id' => 76],
        ['name' => 'Paraiba', 'country_id' => 76],
        ['name' => 'Parana', 'country_id' => 76],
        ['name' => 'Pernambuco', 'country_id' => 76],
        ['name' => 'Piaui', 'country_id' => 76],
        ['name' => 'Rio Grande do Norte', 'country_id' => 76],
        ['name' => 'Rio Grande do Sul', 'country_id' => 76],
        ['name' => 'Rio de Janeiro', 'country_id' => 76],
        ['name' => 'Rondonia', 'country_id' => 76],
        ['name' => 'Roraima', 'country_id' => 76],
        ['name' => 'Santa Catarina', 'country_id' => 76],
        ['name' => 'Sao Paulo', 'country_id' => 76],
        ['name' => 'Sergipe', 'country_id' => 76],
        ['name' => 'Tocantins', 'country_id' => 76],
        ['name' => 'British Indian Ocean Territory', 'country_id' => 86],
        ['name' => 'Belait', 'country_id' => 96],
        ['name' => 'Brunei-Muara', 'country_id' => 96],
        ['name' => 'Temburong', 'country_id' => 96],
        ['name' => 'Tutong', 'country_id' => 96],
        ['name' => 'Blagoevgrad', 'country_id' => 100],
        ['name' => 'Burgas', 'country_id' => 100],
        ['name' => 'Dobrich', 'country_id' => 100],
        ['name' => 'Gabrovo', 'country_id' => 100],
        ['name' => 'Haskovo', 'country_id' => 100],
        ['name' => 'Jambol', 'country_id' => 100],
        ['name' => 'Kardzhali', 'country_id' => 100],
        ['name' => 'Kjustendil', 'country_id' => 100],
        ['name' => 'Lovech', 'country_id' => 100],
        ['name' => 'Montana', 'country_id' => 100],
        ['name' => 'Oblast Sofiya-Grad', 'country_id' => 100],
        ['name' => 'Pazardzhik', 'country_id' => 100],
        ['name' => 'Pernik', 'country_id' => 100],
        ['name' => 'Pleven', 'country_id' => 100],
        ['name' => 'Plovdiv', 'country_id' => 100],
        ['name' => 'Razgrad', 'country_id' => 100],
        ['name' => 'Ruse', 'country_id' => 100],
        ['name' => 'Shumen', 'country_id' => 100],
        ['name' => 'Silistra', 'country_id' => 100],
        ['name' => 'Sliven', 'country_id' => 100],
        ['name' => 'Smoljan', 'country_id' => 100],
        ['name' => 'Sofija grad', 'country_id' => 100],
        ['name' => 'Sofijska oblast', 'country_id' => 100],
        ['name' => 'Stara Zagora', 'country_id' => 100],
        ['name' => 'Targovishte', 'country_id' => 100],
        ['name' => 'Varna', 'country_id' => 100],
        ['name' => 'Veliko Tarnovo', 'country_id' => 100],
        ['name' => 'Vidin', 'country_id' => 100],
        ['name' => 'Vraca', 'country_id' => 100],
        ['name' => 'Yablaniza', 'country_id' => 100],
        ['name' => 'Bale', 'country_id' => 854],
        ['name' => 'Bam', 'country_id' => 854],
        ['name' => 'Bazega', 'country_id' => 854],
        ['name' => 'Bougouriba', 'country_id' => 854],
        ['name' => 'Boulgou', 'country_id' => 854],
        ['name' => 'Boulkiemde', 'country_id' => 854],
        ['name' => 'Comoe', 'country_id' => 854],
        ['name' => 'Ganzourgou', 'country_id' => 854],
        ['name' => 'Gnagna', 'country_id' => 854],
        ['name' => 'Gourma', 'country_id' => 854],
        ['name' => 'Houet', 'country_id' => 854],
        ['name' => 'Ioba', 'country_id' => 854],
        ['name' => 'Kadiogo', 'country_id' => 854],
        ['name' => 'Kenedougou', 'country_id' => 854],
        ['name' => 'Komandjari', 'country_id' => 854],
        ['name' => 'Kompienga', 'country_id' => 854],
        ['name' => 'Kossi', 'country_id' => 854],
        ['name' => 'Kouritenga', 'country_id' => 854],
        ['name' => 'Kourweogo', 'country_id' => 854],
        ['name' => 'Leraba', 'country_id' => 854],
        ['name' => 'Mouhoun', 'country_id' => 854],
        ['name' => 'Nahouri', 'country_id' => 854],
        ['name' => 'Namentenga', 'country_id' => 854],
        ['name' => 'Noumbiel', 'country_id' => 854],
        ['name' => 'Oubritenga', 'country_id' => 854],
        ['name' => 'Oudalan', 'country_id' => 854],
        ['name' => 'Passore', 'country_id' => 854],
        ['name' => 'Poni', 'country_id' => 854],
        ['name' => 'Sanguie', 'country_id' => 854],
        ['name' => 'Sanmatenga', 'country_id' => 854],
        ['name' => 'Seno', 'country_id' => 854],
        ['name' => 'Sissili', 'country_id' => 854],
        ['name' => 'Soum', 'country_id' => 854],
        ['name' => 'Sourou', 'country_id' => 854],
        ['name' => 'Tapoa', 'country_id' => 854],
        ['name' => 'Tuy', 'country_id' => 854],
        ['name' => 'Yatenga', 'country_id' => 854],
        ['name' => 'Zondoma', 'country_id' => 854],
        ['name' => 'Zoundweogo', 'country_id' => 854],
        ['name' => 'Bubanza', 'country_id' => 108],
        ['name' => 'Bujumbura', 'country_id' => 108],
        ['name' => 'Bururi', 'country_id' => 108],
        ['name' => 'Cankuzo', 'country_id' => 108],
        ['name' => 'Cibitoke', 'country_id' => 108],
        ['name' => 'Gitega', 'country_id' => 108],
        ['name' => 'Karuzi', 'country_id' => 108],
        ['name' => 'Kayanza', 'country_id' => 108],
        ['name' => 'Kirundo', 'country_id' => 108],
        ['name' => 'Makamba', 'country_id' => 108],
        ['name' => 'Muramvya', 'country_id' => 108],
        ['name' => 'Muyinga', 'country_id' => 108],
        ['name' => 'Ngozi', 'country_id' => 108],
        ['name' => 'Rutana', 'country_id' => 108],
        ['name' => 'Ruyigi', 'country_id' => 108],
        ['name' => 'Banteay Mean Chey', 'country_id' => 116],
        ['name' => 'Bat Dambang', 'country_id' => 116],
        ['name' => 'Kampong Cham', 'country_id' => 116],
        ['name' => 'Kampong Chhnang', 'country_id' => 116],
        ['name' => 'Kampong Spoeu', 'country_id' => 116],
        ['name' => 'Kampong Thum', 'country_id' => 116],
        ['name' => 'Kampot', 'country_id' => 116],
        ['name' => 'Kandal', 'country_id' => 116],
        ['name' => 'Kaoh Kong', 'country_id' => 116],
        ['name' => 'Kracheh', 'country_id' => 116],
        ['name' => 'Krong Kaeb', 'country_id' => 116],
        ['name' => 'Krong Pailin', 'country_id' => 116],
        ['name' => 'Krong Preah Sihanouk', 'country_id' => 116],
        ['name' => 'Mondol Kiri', 'country_id' => 116],
        ['name' => 'Otdar Mean Chey', 'country_id' => 116],
        ['name' => 'Phnum Penh', 'country_id' => 116],
        ['name' => 'Pousat', 'country_id' => 116],
        ['name' => 'Preah Vihear', 'country_id' => 116],
        ['name' => 'Prey Veaeng', 'country_id' => 116],
        ['name' => 'Rotanak Kiri', 'country_id' => 116],
        ['name' => 'Siem Reab', 'country_id' => 116],
        ['name' => 'Stueng Traeng', 'country_id' => 116],
        ['name' => 'Svay Rieng', 'country_id' => 116],
        ['name' => 'Takaev', 'country_id' => 116],
        ['name' => 'Adamaoua', 'country_id' => 120],
        ['name' => 'Centre', 'country_id' => 120],
        ['name' => 'Est', 'country_id' => 120],
        ['name' => 'Littoral', 'country_id' => 120],
        ['name' => 'Nord', 'country_id' => 120],
        ['name' => 'Nord Extreme', 'country_id' => 120],
        ['name' => 'Nordouest', 'country_id' => 120],
        ['name' => 'Ouest', 'country_id' => 120],
        ['name' => 'Sud', 'country_id' => 120],
        ['name' => 'Sudouest', 'country_id' => 120],
        ['name' => 'Alberta', 'country_id' => 124],
        ['name' => 'British Columbia', 'country_id' => 124],
        ['name' => 'Manitoba', 'country_id' => 124],
        ['name' => 'New Brunswick', 'country_id' => 124],
        ['name' => 'Newfoundland and Labrador', 'country_id' => 124],
        ['name' => 'Northwest Territories', 'country_id' => 124],
        ['name' => 'Nova Scotia', 'country_id' => 124],
        ['name' => 'Nunavut', 'country_id' => 124],
        ['name' => 'Ontario', 'country_id' => 124],
        ['name' => 'Prince Edward Island', 'country_id' => 124],
        ['name' => 'Quebec', 'country_id' => 124],
        ['name' => 'Saskatchewan', 'country_id' => 124],
        ['name' => 'Yukon', 'country_id' => 124],
        ['name' => 'Boavista', 'country_id' => 132],
        ['name' => 'Brava', 'country_id' => 132],
        ['name' => 'Fogo', 'country_id' => 132],
        ['name' => 'Maio', 'country_id' => 132],
        ['name' => 'Sal', 'country_id' => 132],
        ['name' => 'Santo Antao', 'country_id' => 132],
        ['name' => 'Sao Nicolau', 'country_id' => 132],
        ['name' => 'Sao Tiago', 'country_id' => 132],
        ['name' => 'Sao Vicente', 'country_id' => 132],
        ['name' => 'Grand Cayman', 'country_id' => 136],
        ['name' => 'Bamingui-Bangoran', 'country_id' => 140],
        ['name' => 'Bangui', 'country_id' => 140],
        ['name' => 'Basse-Kotto', 'country_id' => 140],
        ['name' => 'Haut-Mbomou', 'country_id' => 140],
        ['name' => 'Haute-Kotto', 'country_id' => 140],
        ['name' => 'Kemo', 'country_id' => 140],
        ['name' => 'Lobaye', 'country_id' => 140],
        ['name' => 'Mambere-Kadei', 'country_id' => 140],
        ['name' => 'Mbomou', 'country_id' => 140],
        ['name' => 'Nana-Gribizi', 'country_id' => 140],
        ['name' => 'Nana-Mambere', 'country_id' => 140],
        ['name' => 'Ombella Mpoko', 'country_id' => 140],
        ['name' => 'Ouaka', 'country_id' => 140],
        ['name' => 'Ouham', 'country_id' => 140],
        ['name' => 'Ouham-Pende', 'country_id' => 140],
        ['name' => 'Sangha-Mbaere', 'country_id' => 140],
        ['name' => 'Vakaga', 'country_id' => 140],
        ['name' => 'Batha', 'country_id' => 148],
        ['name' => 'Biltine', 'country_id' => 148],
        ['name' => 'Bourkou-Ennedi-Tibesti', 'country_id' => 148],
        ['name' => 'Chari-Baguirmi', 'country_id' => 148],
        ['name' => 'Guera', 'country_id' => 148],
        ['name' => 'Kanem', 'country_id' => 148],
        ['name' => 'Lac', 'country_id' => 148],
        ['name' => 'Logone Occidental', 'country_id' => 148],
        ['name' => 'Logone Oriental', 'country_id' => 148],
        ['name' => 'Mayo-Kebbi', 'country_id' => 148],
        ['name' => 'Moyen-Chari', 'country_id' => 148],
        ['name' => 'Ouaddai', 'country_id' => 148],
        ['name' => 'Salamat', 'country_id' => 148],
        ['name' => 'Tandjile', 'country_id' => 148],
        ['name' => 'Aisen', 'country_id' => 152],
        ['name' => 'Antofagasta', 'country_id' => 152],
        ['name' => 'Araucania', 'country_id' => 152],
        ['name' => 'Atacama', 'country_id' => 152],
        ['name' => 'Bio Bio', 'country_id' => 152],
        ['name' => 'Coquimbo', 'country_id' => 152],
        ['name' => "Libertador General Bernardo O'", 'country_id' => 152],
        ['name' => 'Los Lagos', 'country_id' => 152],
        ['name' => 'Magellanes', 'country_id' => 152],
        ['name' => 'Maule', 'country_id' => 152],
        ['name' => 'Metropolitana', 'country_id' => 152],
        ['name' => 'Metropolitana de Santiago', 'country_id' => 152],
        ['name' => 'Tarapaca', 'country_id' => 152],
        ['name' => 'Valparaiso', 'country_id' => 152],
        ['name' => 'Anhui', 'country_id' => 156],
        ['name' => 'Anhui Province', 'country_id' => 156],
        ['name' => 'Anhui Sheng', 'country_id' => 156],
        ['name' => 'Aomen', 'country_id' => 156],
        ['name' => 'Beijing', 'country_id' => 156],
        ['name' => 'Beijing Shi', 'country_id' => 156],
        ['name' => 'Chongqing', 'country_id' => 156],
        ['name' => 'Fujian', 'country_id' => 156],
        ['name' => 'Fujian Sheng', 'country_id' => 156],
        ['name' => 'Gansu', 'country_id' => 156],
        ['name' => 'Guangdong', 'country_id' => 156],
        ['name' => 'Guangdong Sheng', 'country_id' => 156],
        ['name' => 'Guangxi', 'country_id' => 156],
        ['name' => 'Guizhou', 'country_id' => 156],
        ['name' => 'Hainan', 'country_id' => 156],
        ['name' => 'Hebei', 'country_id' => 156],
        ['name' => 'Heilongjiang', 'country_id' => 156],
        ['name' => 'Henan', 'country_id' => 156],
        ['name' => 'Hubei', 'country_id' => 156],
        ['name' => 'Hunan', 'country_id' => 156],
        ['name' => 'Jiangsu', 'country_id' => 156],
        ['name' => 'Jiangsu Sheng', 'country_id' => 156],
        ['name' => 'Jiangxi', 'country_id' => 156],
        ['name' => 'Jilin', 'country_id' => 156],
        ['name' => 'Liaoning', 'country_id' => 156],
        ['name' => 'Liaoning Sheng', 'country_id' => 156],
        ['name' => 'Nei Monggol', 'country_id' => 156],
        ['name' => 'Ningxia Hui', 'country_id' => 156],
        ['name' => 'Qinghai', 'country_id' => 156],
        ['name' => 'Shaanxi', 'country_id' => 156],
        ['name' => 'Shandong', 'country_id' => 156],
        ['name' => 'Shandong Sheng', 'country_id' => 156],
        ['name' => 'Shanghai', 'country_id' => 156],
        ['name' => 'Shanxi', 'country_id' => 156],
        ['name' => 'Sichuan', 'country_id' => 156],
        ['name' => 'Tianjin', 'country_id' => 156],
        ['name' => 'Xianggang', 'country_id' => 156],
        ['name' => 'Xinjiang', 'country_id' => 156],
        ['name' => 'Xizang', 'country_id' => 156],
        ['name' => 'Yunnan', 'country_id' => 156],
        ['name' => 'Zhejiang', 'country_id' => 156],
        ['name' => 'Zhejiang Sheng', 'country_id' => 156],
        ['name' => 'Christmas Island', 'country_id' => 162],
        ['name' => 'Cocos (Keeling) Islands', 'country_id' => 166],
        ['name' => 'Amazonas', 'country_id' => 170],
        ['name' => 'Antioquia', 'country_id' => 170],
        ['name' => 'Arauca', 'country_id' => 170],
        ['name' => 'Atlantico', 'country_id' => 170],
        ['name' => 'Bogota', 'country_id' => 170],
        ['name' => 'Bolivar', 'country_id' => 170],
        ['name' => 'Boyaca', 'country_id' => 170],
        ['name' => 'Caldas', 'country_id' => 170],
        ['name' => 'Caqueta', 'country_id' => 170],
        ['name' => 'Casanare', 'country_id' => 170],
        ['name' => 'Cauca', 'country_id' => 170],
        ['name' => 'Cesar', 'country_id' => 170],
        ['name' => 'Choco', 'country_id' => 170],
        ['name' => 'Cordoba', 'country_id' => 170],
        ['name' => 'Cundinamarca', 'country_id' => 170],
        ['name' => 'Guainia', 'country_id' => 170],
        ['name' => 'Guaviare', 'country_id' => 170],
        ['name' => 'Huila', 'country_id' => 170],
        ['name' => 'La Guajira', 'country_id' => 170],
        ['name' => 'Magdalena', 'country_id' => 170],
        ['name' => 'Meta', 'country_id' => 170],
        ['name' => 'Narino', 'country_id' => 170],
        ['name' => 'Norte de Santander', 'country_id' => 170],
        ['name' => 'Putumayo', 'country_id' => 170],
        ['name' => 'Quindio', 'country_id' => 170],
        ['name' => 'Risaralda', 'country_id' => 170],
        ['name' => 'San Andres y Providencia', 'country_id' => 170],
        ['name' => 'Santander', 'country_id' => 170],
        ['name' => 'Sucre', 'country_id' => 170],
        ['name' => 'Tolima', 'country_id' => 170],
        ['name' => 'Valle del Cauca', 'country_id' => 170],
        ['name' => 'Vaupes', 'country_id' => 170],
        ['name' => 'Vichada', 'country_id' => 170],
        ['name' => 'Mwali', 'country_id' => 174],
        ['name' => 'Njazidja', 'country_id' => 174],
        ['name' => 'Nzwani', 'country_id' => 174],
        ['name' => 'Bouenza', 'country_id' => 178],
        ['name' => 'Brazzaville', 'country_id' => 178],
        ['name' => 'Cuvette', 'country_id' => 178],
        ['name' => 'Kouilou', 'country_id' => 178],
        ['name' => 'Lekoumou', 'country_id' => 178],
        ['name' => 'Likouala', 'country_id' => 178],
        ['name' => 'Niari', 'country_id' => 178],
        ['name' => 'Plateaux', 'country_id' => 178],
        ['name' => 'Pool', 'country_id' => 178],
        ['name' => 'Sangha', 'country_id' => 178],
        ['name' => 'Bandundu', 'country_id' => 180],
        ['name' => 'Bas-Congo', 'country_id' => 180],
        ['name' => 'Equateur', 'country_id' => 180],
        ['name' => 'Haut-Congo', 'country_id' => 180],
        ['name' => 'Kasai-Occidental', 'country_id' => 180],
        ['name' => 'Kasai-Oriental', 'country_id' => 180],
        ['name' => 'Katanga', 'country_id' => 180],
        ['name' => 'Kinshasa', 'country_id' => 180],
        ['name' => 'Maniema', 'country_id' => 180],
        ['name' => 'Nord-Kivu', 'country_id' => 180],
        ['name' => 'Sud-Kivu', 'country_id' => 180],
        ['name' => 'Aitutaki', 'country_id' => 184],
        ['name' => 'Atiu', 'country_id' => 184],
        ['name' => 'Mangaia', 'country_id' => 184],
        ['name' => 'Manihiki', 'country_id' => 184],
        ['name' => 'Mauke', 'country_id' => 184],
        ['name' => 'Mitiaro', 'country_id' => 184],
        ['name' => 'Nassau', 'country_id' => 184],
        ['name' => 'Pukapuka', 'country_id' => 184],
        ['name' => 'Rakahanga', 'country_id' => 184],
        ['name' => 'Rarotonga', 'country_id' => 184],
        ['name' => 'Tongareva', 'country_id' => 184],
        ['name' => 'Alajuela', 'country_id' => 188],
        ['name' => 'Cartago', 'country_id' => 188],
        ['name' => 'Guanacaste', 'country_id' => 188],
        ['name' => 'Heredia', 'country_id' => 188],
        ['name' => 'Limon', 'country_id' => 188],
        ['name' => 'Puntarenas', 'country_id' => 188],
        ['name' => 'San Jose', 'country_id' => 188],
        ['name' => 'Abidjan', 'country_id' => 384],
        ['name' => 'Agneby', 'country_id' => 384],
        ['name' => 'Bafing', 'country_id' => 384],
        ['name' => 'Denguele', 'country_id' => 384],
        ['name' => 'Dix-huit Montagnes', 'country_id' => 384],
        ['name' => 'Fromager', 'country_id' => 384],
        ['name' => 'Haut-Sassandra', 'country_id' => 384],
        ['name' => 'Lacs', 'country_id' => 384],
        ['name' => 'Lagunes', 'country_id' => 384],
        ['name' => 'Marahoue', 'country_id' => 384],
        ['name' => 'Moyen-Cavally', 'country_id' => 384],
        ['name' => 'Moyen-Comoe', 'country_id' => 384],
        ['name' => "N'zi-Comoe", 'country_id' => 384],
        ['name' => 'Sassandra', 'country_id' => 384],
        ['name' => 'Savanes', 'country_id' => 384],
        ['name' => 'Sud-Bandama', 'country_id' => 384],
        ['name' => 'Sud-Comoe', 'country_id' => 384],
        ['name' => 'Vallee du Bandama', 'country_id' => 384],
        ['name' => 'Worodougou', 'country_id' => 384],
        ['name' => 'Zanzan', 'country_id' => 384],
        ['name' => 'Bjelovar-Bilogora', 'country_id' => 191],
        ['name' => 'Dubrovnik-Neretva', 'country_id' => 191],
        ['name' => 'Grad Zagreb', 'country_id' => 191],
        ['name' => 'Istra', 'country_id' => 191],
        ['name' => 'Karlovac', 'country_id' => 191],
        ['name' => 'Koprivnica-Krizhevci', 'country_id' => 191],
        ['name' => 'Krapina-Zagorje', 'country_id' => 191],
        ['name' => 'Lika-Senj', 'country_id' => 191],
        ['name' => 'Medhimurje', 'country_id' => 191],
        ['name' => 'Medimurska Zupanija', 'country_id' => 191],
        ['name' => 'Osijek-Baranja', 'country_id' => 191],
        ['name' => 'Osjecko-Baranjska Zupanija', 'country_id' => 191],
        ['name' => 'Pozhega-Slavonija', 'country_id' => 191],
        ['name' => 'Primorje-Gorski Kotar', 'country_id' => 191],
        ['name' => 'Shibenik-Knin', 'country_id' => 191],
        ['name' => 'Sisak-Moslavina', 'country_id' => 191],
        ['name' => 'Slavonski Brod-Posavina', 'country_id' => 191],
        ['name' => 'Split-Dalmacija', 'country_id' => 191],
        ['name' => 'Varazhdin', 'country_id' => 191],
        ['name' => 'Virovitica-Podravina', 'country_id' => 191],
        ['name' => 'Vukovar-Srijem', 'country_id' => 191],
        ['name' => 'Zadar', 'country_id' => 191],
        ['name' => 'Zagreb', 'country_id' => 191],
        ['name' => 'Camaguey', 'country_id' => 192],
        ['name' => 'Ciego de Avila', 'country_id' => 192],
        ['name' => 'Cienfuegos', 'country_id' => 192],
        ['name' => 'Ciudad de la Habana', 'country_id' => 192],
        ['name' => 'Granma', 'country_id' => 192],
        ['name' => 'Guantanamo', 'country_id' => 192],
        ['name' => 'Habana', 'country_id' => 192],
        ['name' => 'Holguin', 'country_id' => 192],
        ['name' => 'Isla de la Juventud', 'country_id' => 192],
        ['name' => 'La Habana', 'country_id' => 192],
        ['name' => 'Las Tunas', 'country_id' => 192],
        ['name' => 'Matanzas', 'country_id' => 192],
        ['name' => 'Pinar del Rio', 'country_id' => 192],
        ['name' => 'Sancti Spiritus', 'country_id' => 192],
        ['name' => 'Santiago de Cuba', 'country_id' => 192],
        ['name' => 'Villa Clara', 'country_id' => 192],
        ['name' => 'Government controlled area', 'country_id' => 196],
        ['name' => 'Limassol', 'country_id' => 196],
        ['name' => 'Nicosia District', 'country_id' => 196],
        ['name' => 'Paphos', 'country_id' => 196],
        ['name' => 'Turkish controlled area', 'country_id' => 196],
        ['name' => 'Central Bohemian', 'country_id' => 203],
        ['name' => 'Frycovice', 'country_id' => 203],
        ['name' => 'Jihocesky Kraj', 'country_id' => 203],
        ['name' => 'Jihochesky', 'country_id' => 203],
        ['name' => 'Jihomoravsky', 'country_id' => 203],
        ['name' => 'Karlovarsky', 'country_id' => 203],
        ['name' => 'Klecany', 'country_id' => 203],
        ['name' => 'Kralovehradecky', 'country_id' => 203],
        ['name' => 'Liberecky', 'country_id' => 203],
        ['name' => 'Lipov', 'country_id' => 203],
        ['name' => 'Moravskoslezsky', 'country_id' => 203],
        ['name' => 'Olomoucky', 'country_id' => 203],
        ['name' => 'Olomoucky Kraj', 'country_id' => 203],
        ['name' => 'Pardubicky', 'country_id' => 203],
        ['name' => 'Plzensky', 'country_id' => 203],
        ['name' => 'Praha', 'country_id' => 203],
        ['name' => 'Rajhrad', 'country_id' => 203],
        ['name' => 'Smirice', 'country_id' => 203],
        ['name' => 'South Moravian', 'country_id' => 203],
        ['name' => 'Straz nad Nisou', 'country_id' => 203],
        ['name' => 'Stredochesky', 'country_id' => 203],
        ['name' => 'Unicov', 'country_id' => 203],
        ['name' => 'Ustecky', 'country_id' => 203],
        ['name' => 'Valletta', 'country_id' => 203],
        ['name' => 'Velesin', 'country_id' => 203],
        ['name' => 'Vysochina', 'country_id' => 203],
        ['name' => 'Zlinsky', 'country_id' => 203],
        ['name' => 'Arhus', 'country_id' => 208],
        ['name' => 'Bornholm', 'country_id' => 208],
        ['name' => 'Frederiksborg', 'country_id' => 208],
        ['name' => 'Fyn', 'country_id' => 208],
        ['name' => 'Hovedstaden', 'country_id' => 208],
        ['name' => 'Kobenhavn', 'country_id' => 208],
        ['name' => 'Kobenhavns Amt', 'country_id' => 208],
        ['name' => 'Kobenhavns Kommune', 'country_id' => 208],
        ['name' => 'Nordjylland', 'country_id' => 208],
        ['name' => 'Ribe', 'country_id' => 208],
        ['name' => 'Ringkobing', 'country_id' => 208],
        ['name' => 'Roervig', 'country_id' => 208],
        ['name' => 'Roskilde', 'country_id' => 208],
        ['name' => 'Roslev', 'country_id' => 208],
        ['name' => 'Sjaelland', 'country_id' => 208],
        ['name' => 'Soeborg', 'country_id' => 208],
        ['name' => 'Sonderjylland', 'country_id' => 208],
        ['name' => 'Storstrom', 'country_id' => 208],
        ['name' => 'Syddanmark', 'country_id' => 208],
        ['name' => 'Toelloese', 'country_id' => 208],
        ['name' => 'Vejle', 'country_id' => 208],
        ['name' => 'Vestsjalland', 'country_id' => 208],
        ['name' => 'Viborg', 'country_id' => 208],
        ['name' => "'Ali Sabih", 'country_id' => 262],
        ['name' => 'Dikhil', 'country_id' => 262],
        ['name' => 'Jibuti', 'country_id' => 262],
        ['name' => 'Tajurah', 'country_id' => 262],
        ['name' => 'Ubuk', 'country_id' => 262],
        ['name' => 'Saint Andrew', 'country_id' => 212],
        ['name' => 'Saint David', 'country_id' => 212],
        ['name' => 'Saint George', 'country_id' => 212],
        ['name' => 'Saint John', 'country_id' => 212],
        ['name' => 'Saint Joseph', 'country_id' => 212],
        ['name' => 'Saint Luke', 'country_id' => 212],
        ['name' => 'Saint Mark', 'country_id' => 212],
        ['name' => 'Saint Patrick', 'country_id' => 212],
        ['name' => 'Saint Paul', 'country_id' => 212],
        ['name' => 'Saint Peter', 'country_id' => 212],
        ['name' => 'Azua', 'country_id' => 214],
        ['name' => 'Bahoruco', 'country_id' => 214],
        ['name' => 'Barahona', 'country_id' => 214],
        ['name' => 'Dajabon', 'country_id' => 214],
        ['name' => 'Distrito Nacional', 'country_id' => 214],
        ['name' => 'Duarte', 'country_id' => 214],
        ['name' => 'El Seybo', 'country_id' => 214],
        ['name' => 'Elias Pina', 'country_id' => 214],
        ['name' => 'Espaillat', 'country_id' => 214],
        ['name' => 'Hato Mayor', 'country_id' => 214],
        ['name' => 'Independencia', 'country_id' => 214],
        ['name' => 'La Altagracia', 'country_id' => 214],
        ['name' => 'La Romana', 'country_id' => 214],
        ['name' => 'La Vega', 'country_id' => 214],
        ['name' => 'Maria Trinidad Sanchez', 'country_id' => 214],
        ['name' => 'Monsenor Nouel', 'country_id' => 214],
        ['name' => 'Monte Cristi', 'country_id' => 214],
        ['name' => 'Monte Plata', 'country_id' => 214],
        ['name' => 'Pedernales', 'country_id' => 214],
        ['name' => 'Peravia', 'country_id' => 214],
        ['name' => 'Puerto Plata', 'country_id' => 214],
        ['name' => 'Salcedo', 'country_id' => 214],
        ['name' => 'Samana', 'country_id' => 214],
        ['name' => 'San Cristobal', 'country_id' => 214],
        ['name' => 'San Juan', 'country_id' => 214],
        ['name' => 'San Pedro de Macoris', 'country_id' => 214],
        ['name' => 'Sanchez Ramirez', 'country_id' => 214],
        ['name' => 'Santiago', 'country_id' => 214],
        ['name' => 'Santiago Rodriguez', 'country_id' => 214],
        ['name' => 'Valverde', 'country_id' => 214],
        ['name' => 'Azuay', 'country_id' => 218],
        ['name' => 'Bolivar', 'country_id' => 218],
        ['name' => 'Canar', 'country_id' => 218],
        ['name' => 'Carchi', 'country_id' => 218],
        ['name' => 'Chimborazo', 'country_id' => 218],
        ['name' => 'Cotopaxi', 'country_id' => 218],
        ['name' => 'El Oro', 'country_id' => 218],
        ['name' => 'Esmeraldas', 'country_id' => 218],
        ['name' => 'Galapagos', 'country_id' => 218],
        ['name' => 'Guayas', 'country_id' => 218],
        ['name' => 'Imbabura', 'country_id' => 218],
        ['name' => 'Loja', 'country_id' => 218],
        ['name' => 'Los Rios', 'country_id' => 218],
        ['name' => 'Manabi', 'country_id' => 218],
        ['name' => 'Morona Santiago', 'country_id' => 218],
        ['name' => 'Napo', 'country_id' => 218],
        ['name' => 'Orellana', 'country_id' => 218],
        ['name' => 'Pastaza', 'country_id' => 218],
        ['name' => 'Pichincha', 'country_id' => 218],
        ['name' => 'Sucumbios', 'country_id' => 218],
        ['name' => 'Tungurahua', 'country_id' => 218],
        ['name' => 'Zamora Chinchipe', 'country_id' => 218],
        ['name' => 'Aswan', 'country_id' => 818],
        ['name' => 'Asyut', 'country_id' => 818],
        ['name' => 'Bani Suwayf', 'country_id' => 818],
        ['name' => "Bur Sa'id", 'country_id' => 818],
        ['name' => 'Cairo', 'country_id' => 818],
        ['name' => 'Dumyat', 'country_id' => 818],
        ['name' => 'Kafr-ash-Shaykh', 'country_id' => 818],
        ['name' => 'Matruh', 'country_id' => 818],
        ['name' => 'Muhafazat ad Daqahliyah', 'country_id' => 818],
        ['name' => 'Muhafazat al Fayyum', 'country_id' => 818],
        ['name' => 'Muhafazat al Gharbiyah', 'country_id' => 818],
        ['name' => 'Muhafazat al Iskandariyah', 'country_id' => 818],
        ['name' => 'Muhafazat al Qahirah', 'country_id' => 818],
        ['name' => 'Qina', 'country_id' => 818],
        ['name' => 'Sawhaj', 'country_id' => 818],
        ['name' => 'Sina al-Janubiyah', 'country_id' => 818],
        ['name' => 'Sina ash-Shamaliyah', 'country_id' => 818],
        ['name' => 'ad-Daqahliyah', 'country_id' => 818],
        ['name' => 'al-Bahr-al-Ahmar', 'country_id' => 818],
        ['name' => 'al-Buhayrah', 'country_id' => 818],
        ['name' => 'al-Fayyum', 'country_id' => 818],
        ['name' => 'al-Gharbiyah', 'country_id' => 818],
        ['name' => 'al-Iskandariyah', 'country_id' => 818],
        ['name' => 'al-Ismailiyah', 'country_id' => 818],
        ['name' => 'al-Jizah', 'country_id' => 818],
        ['name' => 'al-Minufiyah', 'country_id' => 818],
        ['name' => 'al-Minya', 'country_id' => 818],
        ['name' => 'al-Qahira', 'country_id' => 818],
        ['name' => 'al-Qalyubiyah', 'country_id' => 818],
        ['name' => 'al-Uqsur', 'country_id' => 818],
        ['name' => 'al-Wadi al-Jadid', 'country_id' => 818],
        ['name' => 'as-Suways', 'country_id' => 818],
        ['name' => 'ash-Sharqiyah', 'country_id' => 818],
        ['name' => 'Ahuachapan', 'country_id' => 222],
        ['name' => 'Cabanas', 'country_id' => 222],
        ['name' => 'Chalatenango', 'country_id' => 222],
        ['name' => 'Cuscatlan', 'country_id' => 222],
        ['name' => 'La Libertad', 'country_id' => 222],
        ['name' => 'La Paz', 'country_id' => 222],
        ['name' => 'La Union', 'country_id' => 222],
        ['name' => 'Morazan', 'country_id' => 222],
        ['name' => 'San Miguel', 'country_id' => 222],
        ['name' => 'San Salvador', 'country_id' => 222],
        ['name' => 'San Vicente', 'country_id' => 222],
        ['name' => 'Santa Ana', 'country_id' => 222],
        ['name' => 'Sonsonate', 'country_id' => 222],
        ['name' => 'Usulutan', 'country_id' => 222],
        ['name' => 'Annobon', 'country_id' => 226],
        ['name' => 'Bioko Norte', 'country_id' => 226],
        ['name' => 'Bioko Sur', 'country_id' => 226],
        ['name' => 'Centro Sur', 'country_id' => 226],
        ['name' => 'Kie-Ntem', 'country_id' => 226],
        ['name' => 'Litoral', 'country_id' => 226],
        ['name' => 'Wele-Nzas', 'country_id' => 226],
        ['name' => 'Anseba', 'country_id' => 232],
        ['name' => 'Debub', 'country_id' => 232],
        ['name' => 'Debub-Keih-Bahri', 'country_id' => 232],
        ['name' => 'Gash-Barka', 'country_id' => 232],
        ['name' => 'Maekel', 'country_id' => 232],
        ['name' => 'Semien-Keih-Bahri', 'country_id' => 232],
        ['name' => 'Harju', 'country_id' => 233],
        ['name' => 'Hiiu', 'country_id' => 233],
        ['name' => 'Ida-Viru', 'country_id' => 233],
        ['name' => 'Jarva', 'country_id' => 233],
        ['name' => 'Jogeva', 'country_id' => 233],
        ['name' => 'Laane', 'country_id' => 233],
        ['name' => 'Laane-Viru', 'country_id' => 233],
        ['name' => 'Parnu', 'country_id' => 233],
        ['name' => 'Polva', 'country_id' => 233],
        ['name' => 'Rapla', 'country_id' => 233],
        ['name' => 'Saare', 'country_id' => 233],
        ['name' => 'Tartu', 'country_id' => 233],
        ['name' => 'Valga', 'country_id' => 233],
        ['name' => 'Viljandi', 'country_id' => 233],
        ['name' => 'Voru', 'country_id' => 233],
        ['name' => 'Addis Abeba', 'country_id' => 231],
        ['name' => 'Afar', 'country_id' => 231],
        ['name' => 'Amhara', 'country_id' => 231],
        ['name' => 'Benishangul', 'country_id' => 231],
        ['name' => 'Diredawa', 'country_id' => 231],
        ['name' => 'Gambella', 'country_id' => 231],
        ['name' => 'Harar', 'country_id' => 231],
        ['name' => 'Jigjiga', 'country_id' => 231],
        ['name' => 'Mekele', 'country_id' => 231],
        ['name' => 'Oromia', 'country_id' => 231],
        ['name' => 'Somali', 'country_id' => 231],
        ['name' => 'Southern', 'country_id' => 231],
        ['name' => 'Tigray', 'country_id' => 231],
        ['name' => 'Falkland Islands', 'country_id' => 238],
        ['name' => 'South Georgia', 'country_id' => 238],
        ['name' => 'Klaksvik', 'country_id' => 234],
        ['name' => 'Nor ara Eysturoy', 'country_id' => 234],
        ['name' => 'Nor oy', 'country_id' => 234],
        ['name' => 'Sandoy', 'country_id' => 234],
        ['name' => 'Streymoy', 'country_id' => 234],
        ['name' => 'Su uroy', 'country_id' => 234],
        ['name' => 'Sy ra Eysturoy', 'country_id' => 234],
        ['name' => 'Torshavn', 'country_id' => 234],
        ['name' => 'Vaga', 'country_id' => 234],
        ['name' => 'Central', 'country_id' => 242],
        ['name' => 'Eastern', 'country_id' => 242],
        ['name' => 'Northern', 'country_id' => 242],
        ['name' => 'South Pacific', 'country_id' => 242],
        ['name' => 'Western', 'country_id' => 242],
        ['name' => 'Ahvenanmaa', 'country_id' => 246],
        ['name' => 'Etela-Karjala', 'country_id' => 246],
        ['name' => 'Etela-Pohjanmaa', 'country_id' => 246],
        ['name' => 'Etela-Savo', 'country_id' => 246],
        ['name' => 'Etela-Suomen Laani', 'country_id' => 246],
        ['name' => 'Ita-Suomen Laani', 'country_id' => 246],
        ['name' => 'Ita-Uusimaa', 'country_id' => 246],
        ['name' => 'Kainuu', 'country_id' => 246],
        ['name' => 'Kanta-Hame', 'country_id' => 246],
        ['name' => 'Keski-Pohjanmaa', 'country_id' => 246],
        ['name' => 'Keski-Suomi', 'country_id' => 246],
        ['name' => 'Kymenlaakso', 'country_id' => 246],
        ['name' => 'Lansi-Suomen Laani', 'country_id' => 246],
        ['name' => 'Lappi', 'country_id' => 246],
        ['name' => 'Northern Savonia', 'country_id' => 246],
        ['name' => 'Ostrobothnia', 'country_id' => 246],
        ['name' => 'Oulun Laani', 'country_id' => 246],
        ['name' => 'Paijat-Hame', 'country_id' => 246],
        ['name' => 'Pirkanmaa', 'country_id' => 246],
        ['name' => 'Pohjanmaa', 'country_id' => 246],
        ['name' => 'Pohjois-Karjala', 'country_id' => 246],
        ['name' => 'Pohjois-Pohjanmaa', 'country_id' => 246],
        ['name' => 'Pohjois-Savo', 'country_id' => 246],
        ['name' => 'Saarijarvi', 'country_id' => 246],
        ['name' => 'Satakunta', 'country_id' => 246],
        ['name' => 'Southern Savonia', 'country_id' => 246],
        ['name' => 'Tavastia Proper', 'country_id' => 246],
        ['name' => 'Uleaborgs Lan', 'country_id' => 246],
        ['name' => 'Uusimaa', 'country_id' => 246],
        ['name' => 'Varsinais-Suomi', 'country_id' => 246],
        ['name' => 'Ain', 'country_id' => 250],
        ['name' => 'Aisne', 'country_id' => 250],
        ['name' => 'Albi Le Sequestre', 'country_id' => 250],
        ['name' => 'Allier', 'country_id' => 250],
        ['name' => 'Alpes-Cote dAzur', 'country_id' => 250],
        ['name' => 'Alpes-Maritimes', 'country_id' => 250],
        ['name' => 'Alpes-de-Haute-Provence', 'country_id' => 250],
        ['name' => 'Alsace', 'country_id' => 250],
        ['name' => 'Aquitaine', 'country_id' => 250],
        ['name' => 'Ardeche', 'country_id' => 250],
        ['name' => 'Ardennes', 'country_id' => 250],
        ['name' => 'Ariege', 'country_id' => 250],
        ['name' => 'Aube', 'country_id' => 250],
        ['name' => 'Aude', 'country_id' => 250],
        ['name' => 'Auvergne', 'country_id' => 250],
        ['name' => 'Aveyron', 'country_id' => 250],
        ['name' => 'Bas-Rhin', 'country_id' => 250],
        ['name' => 'Basse-Normandie', 'country_id' => 250],
        ['name' => 'Bouches-du-Rhone', 'country_id' => 250],
        ['name' => 'Bourgogne', 'country_id' => 250],
        ['name' => 'Bretagne', 'country_id' => 250],
        ['name' => 'Brittany', 'country_id' => 250],
        ['name' => 'Burgundy', 'country_id' => 250],
        ['name' => 'Calvados', 'country_id' => 250],
        ['name' => 'Cantal', 'country_id' => 250],
        ['name' => 'Cedex', 'country_id' => 250],
        ['name' => 'Centre', 'country_id' => 250],
        ['name' => 'Charente', 'country_id' => 250],
        ['name' => 'Charente-Maritime', 'country_id' => 250],
        ['name' => 'Cher', 'country_id' => 250],
        ['name' => 'Correze', 'country_id' => 250],
        ['name' => 'Corse-du-Sud', 'country_id' => 250],
        ['name' => "Cote-d'Or", 'country_id' => 250],
        ['name' => "Cotes-d'Armor", 'country_id' => 250],
        ['name' => 'Creuse', 'country_id' => 250],
        ['name' => 'Crolles', 'country_id' => 250],
        ['name' => 'Deux-Sevres', 'country_id' => 250],
        ['name' => 'Dordogne', 'country_id' => 250],
        ['name' => 'Doubs', 'country_id' => 250],
        ['name' => 'Drome', 'country_id' => 250],
        ['name' => 'Essonne', 'country_id' => 250],
        ['name' => 'Eure', 'country_id' => 250],
        ['name' => 'Eure-et-Loir', 'country_id' => 250],
        ['name' => 'Feucherolles', 'country_id' => 250],
        ['name' => 'Finistere', 'country_id' => 250],
        ['name' => 'Franche-Comte', 'country_id' => 250],
        ['name' => 'Gard', 'country_id' => 250],
        ['name' => 'Gers', 'country_id' => 250],
        ['name' => 'Gironde', 'country_id' => 250],
        ['name' => 'Haut-Rhin', 'country_id' => 250],
        ['name' => 'Haute-Corse', 'country_id' => 250],
        ['name' => 'Haute-Garonne', 'country_id' => 250],
        ['name' => 'Haute-Loire', 'country_id' => 250],
        ['name' => 'Haute-Marne', 'country_id' => 250],
        ['name' => 'Haute-Saone', 'country_id' => 250],
        ['name' => 'Haute-Savoie', 'country_id' => 250],
        ['name' => 'Haute-Vienne', 'country_id' => 250],
        ['name' => 'Hautes-Alpes', 'country_id' => 250],
        ['name' => 'Hautes-Pyrenees', 'country_id' => 250],
        ['name' => 'Hauts-de-Seine', 'country_id' => 250],
        ['name' => 'Herault', 'country_id' => 250],
        ['name' => 'Ile-de-France', 'country_id' => 250],
        ['name' => 'Ille-et-Vilaine', 'country_id' => 250],
        ['name' => 'Indre', 'country_id' => 250],
        ['name' => 'Indre-et-Loire', 'country_id' => 250],
        ['name' => 'Isere', 'country_id' => 250],
        ['name' => 'Jura', 'country_id' => 250],
        ['name' => 'Klagenfurt', 'country_id' => 250],
        ['name' => 'Landes', 'country_id' => 250],
        ['name' => 'Languedoc-Roussillon', 'country_id' => 250],
        ['name' => 'Larcay', 'country_id' => 250],
        ['name' => 'Le Castellet', 'country_id' => 250],
        ['name' => 'Le Creusot', 'country_id' => 250],
        ['name' => 'Limousin', 'country_id' => 250],
        ['name' => 'Loir-et-Cher', 'country_id' => 250],
        ['name' => 'Loire', 'country_id' => 250],
        ['name' => 'Loire-Atlantique', 'country_id' => 250],
        ['name' => 'Loiret', 'country_id' => 250],
        ['name' => 'Lorraine', 'country_id' => 250],
        ['name' => 'Lot', 'country_id' => 250],
        ['name' => 'Lot-et-Garonne', 'country_id' => 250],
        ['name' => 'Lower Normandy', 'country_id' => 250],
        ['name' => 'Lozere', 'country_id' => 250],
        ['name' => 'Maine-et-Loire', 'country_id' => 250],
        ['name' => 'Manche', 'country_id' => 250],
        ['name' => 'Marne', 'country_id' => 250],
        ['name' => 'Mayenne', 'country_id' => 250],
        ['name' => 'Meurthe-et-Moselle', 'country_id' => 250],
        ['name' => 'Meuse', 'country_id' => 250],
        ['name' => 'Midi-Pyrenees', 'country_id' => 250],
        ['name' => 'Morbihan', 'country_id' => 250],
        ['name' => 'Moselle', 'country_id' => 250],
        ['name' => 'Nievre', 'country_id' => 250],
        ['name' => 'Nord', 'country_id' => 250],
        ['name' => 'Nord-Pas-de-Calais', 'country_id' => 250],
        ['name' => 'Oise', 'country_id' => 250],
        ['name' => 'Orne', 'country_id' => 250],
        ['name' => 'Paris', 'country_id' => 250],
        ['name' => 'Pas-de-Calais', 'country_id' => 250],
        ['name' => 'Pays de la Loire', 'country_id' => 250],
        ['name' => 'Pays-de-la-Loire', 'country_id' => 250],
        ['name' => 'Picardy', 'country_id' => 250],
        ['name' => 'Puy-de-Dome', 'country_id' => 250],
        ['name' => 'Pyrenees-Atlantiques', 'country_id' => 250],
        ['name' => 'Pyrenees-Orientales', 'country_id' => 250],
        ['name' => 'Quelmes', 'country_id' => 250],
        ['name' => 'Rhone', 'country_id' => 250],
        ['name' => 'Rhone-Alpes', 'country_id' => 250],
        ['name' => 'Saint Ouen', 'country_id' => 250],
        ['name' => 'Saint Viatre', 'country_id' => 250],
        ['name' => 'Saone-et-Loire', 'country_id' => 250],
        ['name' => 'Sarthe', 'country_id' => 250],
        ['name' => 'Savoie', 'country_id' => 250],
        ['name' => 'Seine-Maritime', 'country_id' => 250],
        ['name' => 'Seine-Saint-Denis', 'country_id' => 250],
        ['name' => 'Seine-et-Marne', 'country_id' => 250],
        ['name' => 'Somme', 'country_id' => 250],
        ['name' => 'Sophia Antipolis', 'country_id' => 250],
        ['name' => 'Souvans', 'country_id' => 250],
        ['name' => 'Tarn', 'country_id' => 250],
        ['name' => 'Tarn-et-Garonne', 'country_id' => 250],
        ['name' => 'Territoire de Belfort', 'country_id' => 250],
        ['name' => 'Treignac', 'country_id' => 250],
        ['name' => 'Upper Normandy', 'country_id' => 250],
        ['name' => "Val-d'Oise", 'country_id' => 250],
        ['name' => 'Val-de-Marne', 'country_id' => 250],
        ['name' => 'Var', 'country_id' => 250],
        ['name' => 'Vaucluse', 'country_id' => 250],
        ['name' => 'Vellise', 'country_id' => 250],
        ['name' => 'Vendee', 'country_id' => 250],
        ['name' => 'Vienne', 'country_id' => 250],
        ['name' => 'Vosges', 'country_id' => 250],
        ['name' => 'Yonne', 'country_id' => 250],
        ['name' => 'Yvelines', 'country_id' => 250],
        ['name' => 'Cayenne', 'country_id' => 254],
        ['name' => 'Saint-Laurent-du-Maroni', 'country_id' => 254],
        ['name' => 'Iles du Vent', 'country_id' => 258],
        ['name' => 'Iles sous le Vent', 'country_id' => 258],
        ['name' => 'Marquesas', 'country_id' => 258],
        ['name' => 'Tuamotu', 'country_id' => 258],
        ['name' => 'Tubuai', 'country_id' => 258],
        ['name' => 'Amsterdam', 'country_id' => 260],
        ['name' => 'Crozet Islands', 'country_id' => 260],
        ['name' => 'Kerguelen', 'country_id' => 260],
        ['name' => 'Estuaire', 'country_id' => 266],
        ['name' => 'Haut-Ogooue', 'country_id' => 266],
        ['name' => 'Moyen-Ogooue', 'country_id' => 266],
        ['name' => 'Ngounie', 'country_id' => 266],
        ['name' => 'Nyanga', 'country_id' => 266],
        ['name' => 'Ogooue-Ivindo', 'country_id' => 266],
        ['name' => 'Ogooue-Lolo', 'country_id' => 266],
        ['name' => 'Ogooue-Maritime', 'country_id' => 266],
        ['name' => 'Woleu-Ntem', 'country_id' => 266],
        ['name' => 'Banjul', 'country_id' => 270],
        ['name' => 'Basse', 'country_id' => 270],
        ['name' => 'Brikama', 'country_id' => 270],
        ['name' => 'Janjanbureh', 'country_id' => 270],
        ['name' => 'Kanifing', 'country_id' => 270],
        ['name' => 'Kerewan', 'country_id' => 270],
        ['name' => 'Kuntaur', 'country_id' => 270],
        ['name' => 'Mansakonko', 'country_id' => 270],
        ['name' => 'Abhasia', 'country_id' => 268],
        ['name' => 'Ajaria', 'country_id' => 268],
        ['name' => 'Guria', 'country_id' => 268],
        ['name' => 'Imereti', 'country_id' => 268],
        ['name' => 'Kaheti', 'country_id' => 268],
        ['name' => 'Kvemo Kartli', 'country_id' => 268],
        ['name' => 'Mcheta-Mtianeti', 'country_id' => 268],
        ['name' => 'Racha', 'country_id' => 268],
        ['name' => 'Samagrelo-Zemo Svaneti', 'country_id' => 268],
        ['name' => 'Samche-Zhavaheti', 'country_id' => 268],
        ['name' => 'Shida Kartli', 'country_id' => 268],
        ['name' => 'Tbilisi', 'country_id' => 268],
        ['name' => 'Auvergne', 'country_id' => 276],
        ['name' => 'Baden-Wurttemberg', 'country_id' => 276],
        ['name' => 'Bavaria', 'country_id' => 276],
        ['name' => 'Bayern', 'country_id' => 276],
        ['name' => 'Beilstein Wurtt', 'country_id' => 276],
        ['name' => 'Berlin', 'country_id' => 276],
        ['name' => 'Brandenburg', 'country_id' => 276],
        ['name' => 'Bremen', 'country_id' => 276],
        ['name' => 'Dreisbach', 'country_id' => 276],
        ['name' => 'Freistaat Bayern', 'country_id' => 276],
        ['name' => 'Hamburg', 'country_id' => 276],
        ['name' => 'Hannover', 'country_id' => 276],
        ['name' => 'Heroldstatt', 'country_id' => 276],
        ['name' => 'Hessen', 'country_id' => 276],
        ['name' => 'Kortenberg', 'country_id' => 276],
        ['name' => 'Laasdorf', 'country_id' => 276],
        ['name' => 'Land Baden-Wurttemberg', 'country_id' => 276],
        ['name' => 'Land Bayern', 'country_id' => 276],
        ['name' => 'Land Brandenburg', 'country_id' => 276],
        ['name' => 'Land Hessen', 'country_id' => 276],
        ['name' => 'Land Mecklenburg-Vorpommern', 'country_id' => 276],
        ['name' => 'Land Nordrhein-Westfalen', 'country_id' => 276],
        ['name' => 'Land Rheinland-Pfalz', 'country_id' => 276],
        ['name' => 'Land Sachsen', 'country_id' => 276],
        ['name' => 'Land Sachsen-Anhalt', 'country_id' => 276],
        ['name' => 'Land Thuringen', 'country_id' => 276],
        ['name' => 'Lower Saxony', 'country_id' => 276],
        ['name' => 'Mecklenburg-Vorpommern', 'country_id' => 276],
        ['name' => 'Mulfingen', 'country_id' => 276],
        ['name' => 'Munich', 'country_id' => 276],
        ['name' => 'Neubeuern', 'country_id' => 276],
        ['name' => 'Niedersachsen', 'country_id' => 276],
        ['name' => 'Noord-Holland', 'country_id' => 276],
        ['name' => 'Nordrhein-Westfalen', 'country_id' => 276],
        ['name' => 'North Rhine-Westphalia', 'country_id' => 276],
        ['name' => 'Osterode', 'country_id' => 276],
        ['name' => 'Rheinland-Pfalz', 'country_id' => 276],
        ['name' => 'Rhineland-Palatinate', 'country_id' => 276],
        ['name' => 'Saarland', 'country_id' => 276],
        ['name' => 'Sachsen', 'country_id' => 276],
        ['name' => 'Sachsen-Anhalt', 'country_id' => 276],
        ['name' => 'Saxony', 'country_id' => 276],
        ['name' => 'Schleswig-Holstein', 'country_id' => 276],
        ['name' => 'Thuringia', 'country_id' => 276],
        ['name' => 'Webling', 'country_id' => 276],
        ['name' => 'Weinstrabe', 'country_id' => 276],
        ['name' => 'schlobborn', 'country_id' => 276],
        ['name' => 'Ashanti', 'country_id' => 288],
        ['name' => 'Brong-Ahafo', 'country_id' => 288],
        ['name' => 'Central', 'country_id' => 288],
        ['name' => 'Eastern', 'country_id' => 288],
        ['name' => 'Greater Accra', 'country_id' => 288],
        ['name' => 'Northern', 'country_id' => 288],
        ['name' => 'Upper East', 'country_id' => 288],
        ['name' => 'Upper West', 'country_id' => 288],
        ['name' => 'Volta', 'country_id' => 288],
        ['name' => 'Western', 'country_id' => 288],
        ['name' => 'Gibraltar', 'country_id' => 292],
        ['name' => 'Acharnes', 'country_id' => 300],
        ['name' => 'Ahaia', 'country_id' => 300],
        ['name' => 'Aitolia kai Akarnania', 'country_id' => 300],
        ['name' => 'Argolis', 'country_id' => 300],
        ['name' => 'Arkadia', 'country_id' => 300],
        ['name' => 'Arta', 'country_id' => 300],
        ['name' => 'Attica', 'country_id' => 300],
        ['name' => 'Attiki', 'country_id' => 300],
        ['name' => 'Ayion Oros', 'country_id' => 300],
        ['name' => 'Crete', 'country_id' => 300],
        ['name' => 'Dodekanisos', 'country_id' => 300],
        ['name' => 'Drama', 'country_id' => 300],
        ['name' => 'Evia', 'country_id' => 300],
        ['name' => 'Evritania', 'country_id' => 300],
        ['name' => 'Evros', 'country_id' => 300],
        ['name' => 'Evvoia', 'country_id' => 300],
        ['name' => 'Florina', 'country_id' => 300],
        ['name' => 'Fokis', 'country_id' => 300],
        ['name' => 'Fthiotis', 'country_id' => 300],
        ['name' => 'Grevena', 'country_id' => 300],
        ['name' => 'Halandri', 'country_id' => 300],
        ['name' => 'Halkidiki', 'country_id' => 300],
        ['name' => 'Hania', 'country_id' => 300],
        ['name' => 'Heraklion', 'country_id' => 300],
        ['name' => 'Hios', 'country_id' => 300],
        ['name' => 'Ilia', 'country_id' => 300],
        ['name' => 'Imathia', 'country_id' => 300],
        ['name' => 'Ioannina', 'country_id' => 300],
        ['name' => 'Iraklion', 'country_id' => 300],
        ['name' => 'Karditsa', 'country_id' => 300],
        ['name' => 'Kastoria', 'country_id' => 300],
        ['name' => 'Kavala', 'country_id' => 300],
        ['name' => 'Kefallinia', 'country_id' => 300],
        ['name' => 'Kerkira', 'country_id' => 300],
        ['name' => 'Kiklades', 'country_id' => 300],
        ['name' => 'Kilkis', 'country_id' => 300],
        ['name' => 'Korinthia', 'country_id' => 300],
        ['name' => 'Kozani', 'country_id' => 300],
        ['name' => 'Lakonia', 'country_id' => 300],
        ['name' => 'Larisa', 'country_id' => 300],
        ['name' => 'Lasithi', 'country_id' => 300],
        ['name' => 'Lesvos', 'country_id' => 300],
        ['name' => 'Levkas', 'country_id' => 300],
        ['name' => 'Magnisia', 'country_id' => 300],
        ['name' => 'Messinia', 'country_id' => 300],
        ['name' => 'Nomos Attikis', 'country_id' => 300],
        ['name' => 'Nomos Zakynthou', 'country_id' => 300],
        ['name' => 'Pella', 'country_id' => 300],
        ['name' => 'Pieria', 'country_id' => 300],
        ['name' => 'Piraios', 'country_id' => 300],
        ['name' => 'Preveza', 'country_id' => 300],
        ['name' => 'Rethimni', 'country_id' => 300],
        ['name' => 'Rodopi', 'country_id' => 300],
        ['name' => 'Samos', 'country_id' => 300],
        ['name' => 'Serrai', 'country_id' => 300],
        ['name' => 'Thesprotia', 'country_id' => 300],
        ['name' => 'Thessaloniki', 'country_id' => 300],
        ['name' => 'Trikala', 'country_id' => 300],
        ['name' => 'Voiotia', 'country_id' => 300],
        ['name' => 'West Greece', 'country_id' => 300],
        ['name' => 'Xanthi', 'country_id' => 300],
        ['name' => 'Zakinthos', 'country_id' => 300],
        ['name' => 'Aasiaat', 'country_id' => 304],
        ['name' => 'Ammassalik', 'country_id' => 304],
        ['name' => 'Illoqqortoormiut', 'country_id' => 304],
        ['name' => 'Ilulissat', 'country_id' => 304],
        ['name' => 'Ivittuut', 'country_id' => 304],
        ['name' => 'Kangaatsiaq', 'country_id' => 304],
        ['name' => 'Maniitsoq', 'country_id' => 304],
        ['name' => 'Nanortalik', 'country_id' => 304],
        ['name' => 'Narsaq', 'country_id' => 304],
        ['name' => 'Nuuk', 'country_id' => 304],
        ['name' => 'Paamiut', 'country_id' => 304],
        ['name' => 'Qaanaaq', 'country_id' => 304],
        ['name' => 'Qaqortoq', 'country_id' => 304],
        ['name' => 'Qasigiannguit', 'country_id' => 304],
        ['name' => 'Qeqertarsuaq', 'country_id' => 304],
        ['name' => 'Sisimiut', 'country_id' => 304],
        ['name' => 'Udenfor kommunal inddeling', 'country_id' => 304],
        ['name' => 'Upernavik', 'country_id' => 304],
        ['name' => 'Uummannaq', 'country_id' => 304],
        ['name' => 'Carriacou-Petite Martinique', 'country_id' => 308],
        ['name' => 'Saint Andrew', 'country_id' => 308],
        ['name' => 'Saint Davids', 'country_id' => 308],
        ['name' => "Saint George's", 'country_id' => 308],
        ['name' => 'Saint John', 'country_id' => 308],
        ['name' => 'Saint Mark', 'country_id' => 308],
        ['name' => 'Saint Patrick', 'country_id' => 308],
        ['name' => 'Basse-Terre', 'country_id' => 312],
        ['name' => 'Grande-Terre', 'country_id' => 312],
        ['name' => 'Iles des Saintes', 'country_id' => 312],
        ['name' => 'La Desirade', 'country_id' => 312],
        ['name' => 'Marie-Galante', 'country_id' => 312],
        ['name' => 'Saint Barthelemy', 'country_id' => 312],
        ['name' => 'Saint Martin', 'country_id' => 312],
        ['name' => 'Agana Heights', 'country_id' => 316],
        ['name' => 'Agat', 'country_id' => 316],
        ['name' => 'Barrigada', 'country_id' => 316],
        ['name' => 'Chalan-Pago-Ordot', 'country_id' => 316],
        ['name' => 'Dededo', 'country_id' => 316],
        ['name' => 'Hagatna', 'country_id' => 316],
        ['name' => 'Inarajan', 'country_id' => 316],
        ['name' => 'Mangilao', 'country_id' => 316],
        ['name' => 'Merizo', 'country_id' => 316],
        ['name' => 'Mongmong-Toto-Maite', 'country_id' => 316],
        ['name' => 'Santa Rita', 'country_id' => 316],
        ['name' => 'Sinajana', 'country_id' => 316],
        ['name' => 'Talofofo', 'country_id' => 316],
        ['name' => 'Tamuning', 'country_id' => 316],
        ['name' => 'Yigo', 'country_id' => 316],
        ['name' => 'Yona', 'country_id' => 316],
        ['name' => 'Alta Verapaz', 'country_id' => 320],
        ['name' => 'Baja Verapaz', 'country_id' => 320],
        ['name' => 'Chimaltenango', 'country_id' => 320],
        ['name' => 'Chiquimula', 'country_id' => 320],
        ['name' => 'El Progreso', 'country_id' => 320],
        ['name' => 'Escuintla', 'country_id' => 320],
        ['name' => 'Guatemala', 'country_id' => 320],
        ['name' => 'Huehuetenango', 'country_id' => 320],
        ['name' => 'Izabal', 'country_id' => 320],
        ['name' => 'Jalapa', 'country_id' => 320],
        ['name' => 'Jutiapa', 'country_id' => 320],
        ['name' => 'Peten', 'country_id' => 320],
        ['name' => 'Quezaltenango', 'country_id' => 320],
        ['name' => 'Quiche', 'country_id' => 320],
        ['name' => 'Retalhuleu', 'country_id' => 320],
        ['name' => 'Sacatepequez', 'country_id' => 320],
        ['name' => 'San Marcos', 'country_id' => 320],
        ['name' => 'Santa Rosa', 'country_id' => 320],
        ['name' => 'Solola', 'country_id' => 320],
        ['name' => 'Suchitepequez', 'country_id' => 320],
        ['name' => 'Totonicapan', 'country_id' => 320],
        ['name' => 'Zacapa', 'country_id' => 320],
        ['name' => 'Beyla', 'country_id' => 324],
        ['name' => 'Boffa', 'country_id' => 324],
        ['name' => 'Boke', 'country_id' => 324],
        ['name' => 'Conakry', 'country_id' => 324],
        ['name' => 'Coyah', 'country_id' => 324],
        ['name' => 'Dabola', 'country_id' => 324],
        ['name' => 'Dalaba', 'country_id' => 324],
        ['name' => 'Dinguiraye', 'country_id' => 324],
        ['name' => 'Faranah', 'country_id' => 324],
        ['name' => 'Forecariah', 'country_id' => 324],
        ['name' => 'Fria', 'country_id' => 324],
        ['name' => 'Gaoual', 'country_id' => 324],
        ['name' => 'Gueckedou', 'country_id' => 324],
        ['name' => 'Kankan', 'country_id' => 324],
        ['name' => 'Kerouane', 'country_id' => 324],
        ['name' => 'Kindia', 'country_id' => 324],
        ['name' => 'Kissidougou', 'country_id' => 324],
        ['name' => 'Koubia', 'country_id' => 324],
        ['name' => 'Koundara', 'country_id' => 324],
        ['name' => 'Kouroussa', 'country_id' => 324],
        ['name' => 'Labe', 'country_id' => 324],
        ['name' => 'Lola', 'country_id' => 324],
        ['name' => 'Macenta', 'country_id' => 324],
        ['name' => 'Mali', 'country_id' => 324],
        ['name' => 'Mamou', 'country_id' => 324],
        ['name' => 'Mandiana', 'country_id' => 324],
        ['name' => 'Nzerekore', 'country_id' => 324],
        ['name' => 'Pita', 'country_id' => 324],
        ['name' => 'Siguiri', 'country_id' => 324],
        ['name' => 'Telimele', 'country_id' => 324],
        ['name' => 'Tougue', 'country_id' => 324],
        ['name' => 'Yomou', 'country_id' => 324],
        ['name' => 'Bafata', 'country_id' => 624],
        ['name' => 'Bissau', 'country_id' => 624],
        ['name' => 'Bolama', 'country_id' => 624],
        ['name' => 'Cacheu', 'country_id' => 624],
        ['name' => 'Gabu', 'country_id' => 624],
        ['name' => 'Oio', 'country_id' => 624],
        ['name' => 'Quinara', 'country_id' => 624],
        ['name' => 'Tombali', 'country_id' => 624],
        ['name' => 'Barima-Waini', 'country_id' => 328],
        ['name' => 'Cuyuni-Mazaruni', 'country_id' => 328],
        ['name' => 'Demerara-Mahaica', 'country_id' => 328],
        ['name' => 'East Berbice-Corentyne', 'country_id' => 328],
        ['name' => 'Essequibo Islands-West Demerar', 'country_id' => 328],
        ['name' => 'Mahaica-Berbice', 'country_id' => 328],
        ['name' => 'Pomeroon-Supenaam', 'country_id' => 328],
        ['name' => 'Potaro-Siparuni', 'country_id' => 328],
        ['name' => 'Upper Demerara-Berbice', 'country_id' => 328],
        ['name' => 'Upper Takutu-Upper Essequibo', 'country_id' => 328],
        ['name' => 'Artibonite', 'country_id' => 332],
        ['name' => 'Centre', 'country_id' => 332],
        ['name' => "Grand'Anse", 'country_id' => 332],
        ['name' => 'Nord', 'country_id' => 332],
        ['name' => 'Nord-Est', 'country_id' => 332],
        ['name' => 'Nord-Ouest', 'country_id' => 332],
        ['name' => 'Ouest', 'country_id' => 332],
        ['name' => 'Sud', 'country_id' => 332],
        ['name' => 'Sud-Est', 'country_id' => 332],
        ['name' => 'Heard and McDonald Islands', 'country_id' => 334],
        ['name' => 'Atlantida', 'country_id' => 340],
        ['name' => 'Choluteca', 'country_id' => 340],
        ['name' => 'Colon', 'country_id' => 340],
        ['name' => 'Comayagua', 'country_id' => 340],
        ['name' => 'Copan', 'country_id' => 340],
        ['name' => 'Cortes', 'country_id' => 340],
        ['name' => 'Distrito Central', 'country_id' => 340],
        ['name' => 'El Paraiso', 'country_id' => 340],
        ['name' => 'Francisco Morazan', 'country_id' => 340],
        ['name' => 'Gracias a Dios', 'country_id' => 340],
        ['name' => 'Intibuca', 'country_id' => 340],
        ['name' => 'Islas de la Bahia', 'country_id' => 340],
        ['name' => 'La Paz', 'country_id' => 340],
        ['name' => 'Lempira', 'country_id' => 340],
        ['name' => 'Ocotepeque', 'country_id' => 340],
        ['name' => 'Olancho', 'country_id' => 340],
        ['name' => 'Santa Barbara', 'country_id' => 340],
        ['name' => 'Valle', 'country_id' => 340],
        ['name' => 'Yoro', 'country_id' => 340],
        ['name' => 'Hong Kong', 'country_id' => 344],
        ['name' => 'Bacs-Kiskun', 'country_id' => 348],
        ['name' => 'Baranya', 'country_id' => 348],
        ['name' => 'Bekes', 'country_id' => 348],
        ['name' => 'Borsod-Abauj-Zemplen', 'country_id' => 348],
        ['name' => 'Budapest', 'country_id' => 348],
        ['name' => 'Csongrad', 'country_id' => 348],
        ['name' => 'Fejer', 'country_id' => 348],
        ['name' => 'Gyor-Moson-Sopron', 'country_id' => 348],
        ['name' => 'Hajdu-Bihar', 'country_id' => 348],
        ['name' => 'Heves', 'country_id' => 348],
        ['name' => 'Jasz-Nagykun-Szolnok', 'country_id' => 348],
        ['name' => 'Komarom-Esztergom', 'country_id' => 348],
        ['name' => 'Nograd', 'country_id' => 348],
        ['name' => 'Pest', 'country_id' => 348],
        ['name' => 'Somogy', 'country_id' => 348],
        ['name' => 'Szabolcs-Szatmar-Bereg', 'country_id' => 348],
        ['name' => 'Tolna', 'country_id' => 348],
        ['name' => 'Vas', 'country_id' => 348],
        ['name' => 'Veszprem', 'country_id' => 348],
        ['name' => 'Zala', 'country_id' => 348],
        ['name' => 'Austurland', 'country_id' => 352],
        ['name' => 'Gullbringusysla', 'country_id' => 352],
        ['name' => 'Hofu borgarsva i', 'country_id' => 352],
        ['name' => 'Nor urland eystra', 'country_id' => 352],
        ['name' => 'Nor urland vestra', 'country_id' => 352],
        ['name' => 'Su urland', 'country_id' => 352],
        ['name' => 'Su urnes', 'country_id' => 352],
        ['name' => 'Vestfir ir', 'country_id' => 352],
        ['name' => 'Vesturland', 'country_id' => 352],
        ['name' => 'Aceh', 'country_id' => 360],
        ['name' => 'Bali', 'country_id' => 360],
        ['name' => 'Bangka-Belitung', 'country_id' => 360],
        ['name' => 'Banten', 'country_id' => 360],
        ['name' => 'Bengkulu', 'country_id' => 360],
        ['name' => 'Gandaria', 'country_id' => 360],
        ['name' => 'Gorontalo', 'country_id' => 360],
        ['name' => 'Jakarta', 'country_id' => 360],
        ['name' => 'Jambi', 'country_id' => 360],
        ['name' => 'Jawa Barat', 'country_id' => 360],
        ['name' => 'Jawa Tengah', 'country_id' => 360],
        ['name' => 'Jawa Timur', 'country_id' => 360],
        ['name' => 'Kalimantan Barat', 'country_id' => 360],
        ['name' => 'Kalimantan Selatan', 'country_id' => 360],
        ['name' => 'Kalimantan Tengah', 'country_id' => 360],
        ['name' => 'Kalimantan Timur', 'country_id' => 360],
        ['name' => 'Kendal', 'country_id' => 360],
        ['name' => 'Lampung', 'country_id' => 360],
        ['name' => 'Maluku', 'country_id' => 360],
        ['name' => 'Maluku Utara', 'country_id' => 360],
        ['name' => 'Nusa Tenggara Barat', 'country_id' => 360],
        ['name' => 'Nusa Tenggara Timur', 'country_id' => 360],
        ['name' => 'Papua', 'country_id' => 360],
        ['name' => 'Riau', 'country_id' => 360],
        ['name' => 'Riau Kepulauan', 'country_id' => 360],
        ['name' => 'Solo', 'country_id' => 360],
        ['name' => 'Sulawesi Selatan', 'country_id' => 360],
        ['name' => 'Sulawesi Tengah', 'country_id' => 360],
        ['name' => 'Sulawesi Tenggara', 'country_id' => 360],
        ['name' => 'Sulawesi Utara', 'country_id' => 360],
        ['name' => 'Sumatera Barat', 'country_id' => 360],
        ['name' => 'Sumatera Selatan', 'country_id' => 360],
        ['name' => 'Sumatera Utara', 'country_id' => 360],
        ['name' => 'Yogyakarta', 'country_id' => 360],
        ['name' => 'Ardabil', 'country_id' => 364],
        ['name' => 'Azarbayjan-e Bakhtari', 'country_id' => 364],
        ['name' => 'Azarbayjan-e Khavari', 'country_id' => 364],
        ['name' => 'Bushehr', 'country_id' => 364],
        ['name' => 'Chahar Mahal-e Bakhtiari', 'country_id' => 364],
        ['name' => 'Esfahan', 'country_id' => 364],
        ['name' => 'Fars', 'country_id' => 364],
        ['name' => 'Gilan', 'country_id' => 364],
        ['name' => 'Golestan', 'country_id' => 364],
        ['name' => 'Hamadan', 'country_id' => 364],
        ['name' => 'Hormozgan', 'country_id' => 364],
        ['name' => 'Ilam', 'country_id' => 364],
        ['name' => 'Kerman', 'country_id' => 364],
        ['name' => 'Kermanshah', 'country_id' => 364],
        ['name' => 'Khorasan', 'country_id' => 364],
        ['name' => 'Khuzestan', 'country_id' => 364],
        ['name' => 'Kohgiluyeh-e Boyerahmad', 'country_id' => 364],
        ['name' => 'Kordestan', 'country_id' => 364],
        ['name' => 'Lorestan', 'country_id' => 364],
        ['name' => 'Markazi', 'country_id' => 364],
        ['name' => 'Mazandaran', 'country_id' => 364],
        ['name' => 'Ostan-e Esfahan', 'country_id' => 364],
        ['name' => 'Qazvin', 'country_id' => 364],
        ['name' => 'Qom', 'country_id' => 364],
        ['name' => 'Semnan', 'country_id' => 364],
        ['name' => 'Sistan-e Baluchestan', 'country_id' => 364],
        ['name' => 'Tehran', 'country_id' => 364],
        ['name' => 'Yazd', 'country_id' => 364],
        ['name' => 'Zanjan', 'country_id' => 364],
        ['name' => 'Babil', 'country_id' => 368],
        ['name' => 'Baghdad', 'country_id' => 368],
        ['name' => 'Dahuk', 'country_id' => 368],
        ['name' => 'Dhi Qar', 'country_id' => 368],
        ['name' => 'Diyala', 'country_id' => 368],
        ['name' => 'Erbil', 'country_id' => 368],
        ['name' => 'Irbil', 'country_id' => 368],
        ['name' => 'Karbala', 'country_id' => 368],
        ['name' => 'Kurdistan', 'country_id' => 368],
        ['name' => 'Maysan', 'country_id' => 368],
        ['name' => 'Ninawa', 'country_id' => 368],
        ['name' => 'Salah-ad-Din', 'country_id' => 368],
        ['name' => 'Wasit', 'country_id' => 368],
        ['name' => 'al-Anbar', 'country_id' => 368],
        ['name' => 'al-Basrah', 'country_id' => 368],
        ['name' => 'al-Muthanna', 'country_id' => 368],
        ['name' => 'al-Qadisiyah', 'country_id' => 368],
        ['name' => 'an-Najaf', 'country_id' => 368],
        ['name' => 'as-Sulaymaniyah', 'country_id' => 368],
        ['name' => "at-Ta'mim", 'country_id' => 368],
        ['name' => 'Armagh', 'country_id' => 372],
        ['name' => 'Carlow', 'country_id' => 372],
        ['name' => 'Cavan', 'country_id' => 372],
        ['name' => 'Clare', 'country_id' => 372],
        ['name' => 'Cork', 'country_id' => 372],
        ['name' => 'Donegal', 'country_id' => 372],
        ['name' => 'Dublin', 'country_id' => 372],
        ['name' => 'Galway', 'country_id' => 372],
        ['name' => 'Kerry', 'country_id' => 372],
        ['name' => 'Kildare', 'country_id' => 372],
        ['name' => 'Kilkenny', 'country_id' => 372],
        ['name' => 'Laois', 'country_id' => 372],
        ['name' => 'Leinster', 'country_id' => 372],
        ['name' => 'Leitrim', 'country_id' => 372],
        ['name' => 'Limerick', 'country_id' => 372],
        ['name' => 'Loch Garman', 'country_id' => 372],
        ['name' => 'Longford', 'country_id' => 372],
        ['name' => 'Louth', 'country_id' => 372],
        ['name' => 'Mayo', 'country_id' => 372],
        ['name' => 'Meath', 'country_id' => 372],
        ['name' => 'Monaghan', 'country_id' => 372],
        ['name' => 'Offaly', 'country_id' => 372],
        ['name' => 'Roscommon', 'country_id' => 372],
        ['name' => 'Sligo', 'country_id' => 372],
        ['name' => 'Tipperary North Riding', 'country_id' => 372],
        ['name' => 'Tipperary South Riding', 'country_id' => 372],
        ['name' => 'Ulster', 'country_id' => 372],
        ['name' => 'Waterford', 'country_id' => 372],
        ['name' => 'Westmeath', 'country_id' => 372],
        ['name' => 'Wexford', 'country_id' => 372],
        ['name' => 'Wicklow', 'country_id' => 372],
        ['name' => 'Beit Hanania', 'country_id' => 376],
        ['name' => 'Ben Gurion Airport', 'country_id' => 376],
        ['name' => 'Bethlehem', 'country_id' => 376],
        ['name' => 'Caesarea', 'country_id' => 376],
        ['name' => 'Centre', 'country_id' => 376],
        ['name' => 'Gaza', 'country_id' => 376],
        ['name' => 'Hadaron', 'country_id' => 376],
        ['name' => 'Haifa District', 'country_id' => 376],
        ['name' => 'Hamerkaz', 'country_id' => 376],
        ['name' => 'Hazafon', 'country_id' => 376],
        ['name' => 'Hebron', 'country_id' => 376],
        ['name' => 'Jaffa', 'country_id' => 376],
        ['name' => 'Jerusalem', 'country_id' => 376],
        ['name' => 'Khefa', 'country_id' => 376],
        ['name' => 'Kiryat Yam', 'country_id' => 376],
        ['name' => 'Lower Galilee', 'country_id' => 376],
        ['name' => 'Qalqilya', 'country_id' => 376],
        ['name' => 'Talme Elazar', 'country_id' => 376],
        ['name' => 'Tel Aviv', 'country_id' => 376],
        ['name' => 'Tsafon', 'country_id' => 376],
        ['name' => 'Umm El Fahem', 'country_id' => 376],
        ['name' => 'Yerushalayim', 'country_id' => 376],
        ['name' => 'Abruzzi', 'country_id' => 380],
        ['name' => 'Abruzzo', 'country_id' => 380],
        ['name' => 'Agrigento', 'country_id' => 380],
        ['name' => 'Alessandria', 'country_id' => 380],
        ['name' => 'Ancona', 'country_id' => 380],
        ['name' => 'Arezzo', 'country_id' => 380],
        ['name' => 'Ascoli Piceno', 'country_id' => 380],
        ['name' => 'Asti', 'country_id' => 380],
        ['name' => 'Avellino', 'country_id' => 380],
        ['name' => 'Bari', 'country_id' => 380],
        ['name' => 'Basilicata', 'country_id' => 380],
        ['name' => 'Belluno', 'country_id' => 380],
        ['name' => 'Benevento', 'country_id' => 380],
        ['name' => 'Bergamo', 'country_id' => 380],
        ['name' => 'Biella', 'country_id' => 380],
        ['name' => 'Bologna', 'country_id' => 380],
        ['name' => 'Bolzano', 'country_id' => 380],
        ['name' => 'Brescia', 'country_id' => 380],
        ['name' => 'Brindisi', 'country_id' => 380],
        ['name' => 'Calabria', 'country_id' => 380],
        ['name' => 'Campania', 'country_id' => 380],
        ['name' => 'Cartoceto', 'country_id' => 380],
        ['name' => 'Caserta', 'country_id' => 380],
        ['name' => 'Catania', 'country_id' => 380],
        ['name' => 'Chieti', 'country_id' => 380],
        ['name' => 'Como', 'country_id' => 380],
        ['name' => 'Cosenza', 'country_id' => 380],
        ['name' => 'Cremona', 'country_id' => 380],
        ['name' => 'Cuneo', 'country_id' => 380],
        ['name' => 'Emilia-Romagna', 'country_id' => 380],
        ['name' => 'Ferrara', 'country_id' => 380],
        ['name' => 'Firenze', 'country_id' => 380],
        ['name' => 'Florence', 'country_id' => 380],
        ['name' => 'Forli-Cesena ', 'country_id' => 380],
        ['name' => 'Friuli-Venezia Giulia', 'country_id' => 380],
        ['name' => 'Frosinone', 'country_id' => 380],
        ['name' => 'Genoa', 'country_id' => 380],
        ['name' => 'Gorizia', 'country_id' => 380],
        ['name' => "L'Aquila", 'country_id' => 380],
        ['name' => 'Lazio', 'country_id' => 380],
        ['name' => 'Lecce', 'country_id' => 380],
        ['name' => 'Lecco', 'country_id' => 380],
        ['name' => 'Lecco Province', 'country_id' => 380],
        ['name' => 'Liguria', 'country_id' => 380],
        ['name' => 'Lodi', 'country_id' => 380],
        ['name' => 'Lombardia', 'country_id' => 380],
        ['name' => 'Lombardy', 'country_id' => 380],
        ['name' => 'Macerata', 'country_id' => 380],
        ['name' => 'Mantova', 'country_id' => 380],
        ['name' => 'Marche', 'country_id' => 380],
        ['name' => 'Messina', 'country_id' => 380],
        ['name' => 'Milan', 'country_id' => 380],
        ['name' => 'Modena', 'country_id' => 380],
        ['name' => 'Molise', 'country_id' => 380],
        ['name' => 'Molteno', 'country_id' => 380],
        ['name' => 'Montenegro', 'country_id' => 380],
        ['name' => 'Monza and Brianza', 'country_id' => 380],
        ['name' => 'Naples', 'country_id' => 380],
        ['name' => 'Novara', 'country_id' => 380],
        ['name' => 'Padova', 'country_id' => 380],
        ['name' => 'Parma', 'country_id' => 380],
        ['name' => 'Pavia', 'country_id' => 380],
        ['name' => 'Perugia', 'country_id' => 380],
        ['name' => 'Pesaro-Urbino', 'country_id' => 380],
        ['name' => 'Piacenza', 'country_id' => 380],
        ['name' => 'Piedmont', 'country_id' => 380],
        ['name' => 'Piemonte', 'country_id' => 380],
        ['name' => 'Pisa', 'country_id' => 380],
        ['name' => 'Pordenone', 'country_id' => 380],
        ['name' => 'Potenza', 'country_id' => 380],
        ['name' => 'Puglia', 'country_id' => 380],
        ['name' => 'Reggio Emilia', 'country_id' => 380],
        ['name' => 'Rimini', 'country_id' => 380],
        ['name' => 'Roma', 'country_id' => 380],
        ['name' => 'Salerno', 'country_id' => 380],
        ['name' => 'Sardegna', 'country_id' => 380],
        ['name' => 'Sassari', 'country_id' => 380],
        ['name' => 'Savona', 'country_id' => 380],
        ['name' => 'Sicilia', 'country_id' => 380],
        ['name' => 'Siena', 'country_id' => 380],
        ['name' => 'Sondrio', 'country_id' => 380],
        ['name' => 'South Tyrol', 'country_id' => 380],
        ['name' => 'Taranto', 'country_id' => 380],
        ['name' => 'Teramo', 'country_id' => 380],
        ['name' => 'Torino', 'country_id' => 380],
        ['name' => 'Toscana', 'country_id' => 380],
        ['name' => 'Trapani', 'country_id' => 380],
        ['name' => 'Trentino-Alto Adige', 'country_id' => 380],
        ['name' => 'Trento', 'country_id' => 380],
        ['name' => 'Treviso', 'country_id' => 380],
        ['name' => 'Udine', 'country_id' => 380],
        ['name' => 'Umbria', 'country_id' => 380],
        ['name' => "Valle d'Aosta", 'country_id' => 380],
        ['name' => 'Varese', 'country_id' => 380],
        ['name' => 'Veneto', 'country_id' => 380],
        ['name' => 'Venezia', 'country_id' => 380],
        ['name' => 'Verbano-Cusio-Ossola', 'country_id' => 380],
        ['name' => 'Vercelli', 'country_id' => 380],
        ['name' => 'Verona', 'country_id' => 380],
        ['name' => 'Vicenza', 'country_id' => 380],
        ['name' => 'Viterbo', 'country_id' => 380],
        ['name' => 'Buxoro Viloyati', 'country_id' => 388],
        ['name' => 'Clarendon', 'country_id' => 388],
        ['name' => 'Hanover', 'country_id' => 388],
        ['name' => 'Kingston', 'country_id' => 388],
        ['name' => 'Manchester', 'country_id' => 388],
        ['name' => 'Portland', 'country_id' => 388],
        ['name' => 'Saint Andrews', 'country_id' => 388],
        ['name' => 'Saint Ann', 'country_id' => 388],
        ['name' => 'Saint Catherine', 'country_id' => 388],
        ['name' => 'Saint Elizabeth', 'country_id' => 388],
        ['name' => 'Saint James', 'country_id' => 388],
        ['name' => 'Saint Mary', 'country_id' => 388],
        ['name' => 'Saint Thomas', 'country_id' => 388],
        ['name' => 'Trelawney', 'country_id' => 388],
        ['name' => 'Westmoreland', 'country_id' => 388],
        ['name' => 'Aichi', 'country_id' => 392],
        ['name' => 'Akita', 'country_id' => 392],
        ['name' => 'Aomori', 'country_id' => 392],
        ['name' => 'Chiba', 'country_id' => 392],
        ['name' => 'Ehime', 'country_id' => 392],
        ['name' => 'Fukui', 'country_id' => 392],
        ['name' => 'Fukuoka', 'country_id' => 392],
        ['name' => 'Fukushima', 'country_id' => 392],
        ['name' => 'Gifu', 'country_id' => 392],
        ['name' => 'Gumma', 'country_id' => 392],
        ['name' => 'Hiroshima', 'country_id' => 392],
        ['name' => 'Hokkaido', 'country_id' => 392],
        ['name' => 'Hyogo', 'country_id' => 392],
        ['name' => 'Ibaraki', 'country_id' => 392],
        ['name' => 'Ishikawa', 'country_id' => 392],
        ['name' => 'Iwate', 'country_id' => 392],
        ['name' => 'Kagawa', 'country_id' => 392],
        ['name' => 'Kagoshima', 'country_id' => 392],
        ['name' => 'Kanagawa', 'country_id' => 392],
        ['name' => 'Kanto', 'country_id' => 392],
        ['name' => 'Kochi', 'country_id' => 392],
        ['name' => 'Kumamoto', 'country_id' => 392],
        ['name' => 'Kyoto', 'country_id' => 392],
        ['name' => 'Mie', 'country_id' => 392],
        ['name' => 'Miyagi', 'country_id' => 392],
        ['name' => 'Miyazaki', 'country_id' => 392],
        ['name' => 'Nagano', 'country_id' => 392],
        ['name' => 'Nagasaki', 'country_id' => 392],
        ['name' => 'Nara', 'country_id' => 392],
        ['name' => 'Niigata', 'country_id' => 392],
        ['name' => 'Oita', 'country_id' => 392],
        ['name' => 'Okayama', 'country_id' => 392],
        ['name' => 'Okinawa', 'country_id' => 392],
        ['name' => 'Osaka', 'country_id' => 392],
        ['name' => 'Saga', 'country_id' => 392],
        ['name' => 'Saitama', 'country_id' => 392],
        ['name' => 'Shiga', 'country_id' => 392],
        ['name' => 'Shimane', 'country_id' => 392],
        ['name' => 'Shizuoka', 'country_id' => 392],
        ['name' => 'Tochigi', 'country_id' => 392],
        ['name' => 'Tokushima', 'country_id' => 392],
        ['name' => 'Tokyo', 'country_id' => 392],
        ['name' => 'Tottori', 'country_id' => 392],
        ['name' => 'Toyama', 'country_id' => 392],
        ['name' => 'Wakayama', 'country_id' => 392],
        ['name' => 'Yamagata', 'country_id' => 392],
        ['name' => 'Yamaguchi', 'country_id' => 392],
        ['name' => 'Yamanashi', 'country_id' => 392],
        ['name' => "'Ajlun", 'country_id' => 400],
        ['name' => 'Amman', 'country_id' => 400],
        ['name' => 'Irbid', 'country_id' => 400],
        ['name' => 'Jarash', 'country_id' => 400],
        ['name' => "Ma'an", 'country_id' => 400],
        ['name' => 'Madaba', 'country_id' => 400],
        ['name' => "al-'Aqabah", 'country_id' => 400],
        ['name' => "al-Balqa'", 'country_id' => 400],
        ['name' => 'al-Karak', 'country_id' => 400],
        ['name' => 'al-Mafraq', 'country_id' => 400],
        ['name' => 'at-Tafilah', 'country_id' => 400],
        ['name' => "az-Zarqa'", 'country_id' => 400],
        ['name' => 'Akmecet', 'country_id' => 398],
        ['name' => 'Akmola', 'country_id' => 398],
        ['name' => 'Aktobe', 'country_id' => 398],
        ['name' => 'Almati', 'country_id' => 398],
        ['name' => 'Atirau', 'country_id' => 398],
        ['name' => 'Batis Kazakstan', 'country_id' => 398],
        ['name' => 'Burlinsky Region', 'country_id' => 398],
        ['name' => 'Karagandi', 'country_id' => 398],
        ['name' => 'Kostanay', 'country_id' => 398],
        ['name' => 'Mankistau', 'country_id' => 398],
        ['name' => 'Ontustik Kazakstan', 'country_id' => 398],
        ['name' => 'Pavlodar', 'country_id' => 398],
        ['name' => 'Sigis Kazakstan', 'country_id' => 398],
        ['name' => 'Soltustik Kazakstan', 'country_id' => 398],
        ['name' => 'Taraz', 'country_id' => 398],
        ['name' => 'Central', 'country_id' => 404],
        ['name' => 'Coast', 'country_id' => 404],
        ['name' => 'Eastern', 'country_id' => 404],
        ['name' => 'Nairobi', 'country_id' => 404],
        ['name' => 'North Eastern', 'country_id' => 404],
        ['name' => 'Nyanza', 'country_id' => 404],
        ['name' => 'Rift Valley', 'country_id' => 404],
        ['name' => 'Western', 'country_id' => 404],
        ['name' => 'Abaiang', 'country_id' => 296],
        ['name' => 'Abemana', 'country_id' => 296],
        ['name' => 'Aranuka', 'country_id' => 296],
        ['name' => 'Arorae', 'country_id' => 296],
        ['name' => 'Banaba', 'country_id' => 296],
        ['name' => 'Beru', 'country_id' => 296],
        ['name' => 'Butaritari', 'country_id' => 296],
        ['name' => 'Kiritimati', 'country_id' => 296],
        ['name' => 'Kuria', 'country_id' => 296],
        ['name' => 'Maiana', 'country_id' => 296],
        ['name' => 'Makin', 'country_id' => 296],
        ['name' => 'Marakei', 'country_id' => 296],
        ['name' => 'Nikunau', 'country_id' => 296],
        ['name' => 'Nonouti', 'country_id' => 296],
        ['name' => 'Onotoa', 'country_id' => 296],
        ['name' => 'Phoenix Islands', 'country_id' => 296],
        ['name' => 'Tabiteuea North', 'country_id' => 296],
        ['name' => 'Tabiteuea South', 'country_id' => 296],
        ['name' => 'Tabuaeran', 'country_id' => 296],
        ['name' => 'Tamana', 'country_id' => 296],
        ['name' => 'Tarawa North', 'country_id' => 296],
        ['name' => 'Tarawa South', 'country_id' => 296],
        ['name' => 'Teraina', 'country_id' => 296],
        ['name' => 'Chagangdo', 'country_id' => 408],
        ['name' => 'Hamgyeongbukto', 'country_id' => 408],
        ['name' => 'Hamgyeongnamdo', 'country_id' => 408],
        ['name' => 'Hwanghaebukto', 'country_id' => 408],
        ['name' => 'Hwanghaenamdo', 'country_id' => 408],
        ['name' => 'Kaeseong', 'country_id' => 408],
        ['name' => 'Kangweon', 'country_id' => 408],
        ['name' => 'Nampo', 'country_id' => 408],
        ['name' => 'Pyeonganbukto', 'country_id' => 408],
        ['name' => 'Pyeongannamdo', 'country_id' => 408],
        ['name' => 'Pyeongyang', 'country_id' => 408],
        ['name' => 'Yanggang', 'country_id' => 408],
        ['name' => 'Busan', 'country_id' => 410],
        ['name' => 'Cheju', 'country_id' => 410],
        ['name' => 'Chollabuk', 'country_id' => 410],
        ['name' => 'Chollanam', 'country_id' => 410],
        ['name' => 'Chungbuk', 'country_id' => 410],
        ['name' => 'Chungcheongbuk', 'country_id' => 410],
        ['name' => 'Chungcheongnam', 'country_id' => 410],
        ['name' => 'Chungnam', 'country_id' => 410],
        ['name' => 'Daegu', 'country_id' => 410],
        ['name' => 'Gangwon-do', 'country_id' => 410],
        ['name' => 'Goyang-si', 'country_id' => 410],
        ['name' => 'Gyeonggi-do', 'country_id' => 410],
        ['name' => 'Gyeongsang ', 'country_id' => 410],
        ['name' => 'Gyeongsangnam-do', 'country_id' => 410],
        ['name' => 'Incheon', 'country_id' => 410],
        ['name' => 'Jeju-Si', 'country_id' => 410],
        ['name' => 'Jeonbuk', 'country_id' => 410],
        ['name' => 'Kangweon', 'country_id' => 410],
        ['name' => 'Kwangju', 'country_id' => 410],
        ['name' => 'Kyeonggi', 'country_id' => 410],
        ['name' => 'Kyeongsangbuk', 'country_id' => 410],
        ['name' => 'Kyeongsangnam', 'country_id' => 410],
        ['name' => 'Kyonggi-do', 'country_id' => 410],
        ['name' => 'Kyungbuk-Do', 'country_id' => 410],
        ['name' => 'Kyunggi-Do', 'country_id' => 410],
        ['name' => 'Kyunggi-do', 'country_id' => 410],
        ['name' => 'Pusan', 'country_id' => 410],
        ['name' => 'Seoul', 'country_id' => 410],
        ['name' => 'Sudogwon', 'country_id' => 410],
        ['name' => 'Taegu', 'country_id' => 410],
        ['name' => 'Taejeon', 'country_id' => 410],
        ['name' => 'Taejon-gwangyoksi', 'country_id' => 410],
        ['name' => 'Ulsan', 'country_id' => 410],
        ['name' => 'Wonju', 'country_id' => 410],
        ['name' => 'gwangyoksi', 'country_id' => 410],
        ['name' => 'Al Asimah', 'country_id' => 414],
        ['name' => 'Hawalli', 'country_id' => 414],
        ['name' => 'Mishref', 'country_id' => 414],
        ['name' => 'Qadesiya', 'country_id' => 414],
        ['name' => 'Safat', 'country_id' => 414],
        ['name' => 'Salmiya', 'country_id' => 414],
        ['name' => 'al-Ahmadi', 'country_id' => 414],
        ['name' => 'al-Farwaniyah', 'country_id' => 414],
        ['name' => 'al-Jahra', 'country_id' => 414],
        ['name' => 'al-Kuwayt', 'country_id' => 414],
        ['name' => 'Batken', 'country_id' => 417],
        ['name' => 'Bishkek', 'country_id' => 417],
        ['name' => 'Chui', 'country_id' => 417],
        ['name' => 'Issyk-Kul', 'country_id' => 417],
        ['name' => 'Jalal-Abad', 'country_id' => 417],
        ['name' => 'Naryn', 'country_id' => 417],
        ['name' => 'Osh', 'country_id' => 417],
        ['name' => 'Talas', 'country_id' => 417],
        ['name' => 'Attopu', 'country_id' => 418],
        ['name' => 'Bokeo', 'country_id' => 418],
        ['name' => 'Bolikhamsay', 'country_id' => 418],
        ['name' => 'Champasak', 'country_id' => 418],
        ['name' => 'Houaphanh', 'country_id' => 418],
        ['name' => 'Khammouane', 'country_id' => 418],
        ['name' => 'Luang Nam Tha', 'country_id' => 418],
        ['name' => 'Luang Prabang', 'country_id' => 418],
        ['name' => 'Oudomxay', 'country_id' => 418],
        ['name' => 'Phongsaly', 'country_id' => 418],
        ['name' => 'Saravan', 'country_id' => 418],
        ['name' => 'Savannakhet', 'country_id' => 418],
        ['name' => 'Sekong', 'country_id' => 418],
        ['name' => 'Viangchan Prefecture', 'country_id' => 418],
        ['name' => 'Viangchan Province', 'country_id' => 418],
        ['name' => 'Xaignabury', 'country_id' => 418],
        ['name' => 'Xiang Khuang', 'country_id' => 418],
        ['name' => 'Aizkraukles', 'country_id' => 428],
        ['name' => 'Aluksnes', 'country_id' => 428],
        ['name' => 'Balvu', 'country_id' => 428],
        ['name' => 'Bauskas', 'country_id' => 428],
        ['name' => 'Cesu', 'country_id' => 428],
        ['name' => 'Daugavpils', 'country_id' => 428],
        ['name' => 'Daugavpils City', 'country_id' => 428],
        ['name' => 'Dobeles', 'country_id' => 428],
        ['name' => 'Gulbenes', 'country_id' => 428],
        ['name' => 'Jekabspils', 'country_id' => 428],
        ['name' => 'Jelgava', 'country_id' => 428],
        ['name' => 'Jelgavas', 'country_id' => 428],
        ['name' => 'Jurmala City', 'country_id' => 428],
        ['name' => 'Kraslavas', 'country_id' => 428],
        ['name' => 'Kuldigas', 'country_id' => 428],
        ['name' => 'Liepaja', 'country_id' => 428],
        ['name' => 'Liepajas', 'country_id' => 428],
        ['name' => 'Limbazhu', 'country_id' => 428],
        ['name' => 'Ludzas', 'country_id' => 428],
        ['name' => 'Madonas', 'country_id' => 428],
        ['name' => 'Ogres', 'country_id' => 428],
        ['name' => 'Preilu', 'country_id' => 428],
        ['name' => 'Rezekne', 'country_id' => 428],
        ['name' => 'Rezeknes', 'country_id' => 428],
        ['name' => 'Riga', 'country_id' => 428],
        ['name' => 'Rigas', 'country_id' => 428],
        ['name' => 'Saldus', 'country_id' => 428],
        ['name' => 'Talsu', 'country_id' => 428],
        ['name' => 'Tukuma', 'country_id' => 428],
        ['name' => 'Valkas', 'country_id' => 428],
        ['name' => 'Valmieras', 'country_id' => 428],
        ['name' => 'Ventspils', 'country_id' => 428],
        ['name' => 'Ventspils City', 'country_id' => 428],
        ['name' => 'Beirut', 'country_id' => 422],
        ['name' => 'Jabal Lubnan', 'country_id' => 422],
        ['name' => 'Mohafazat Liban-Nord', 'country_id' => 422],
        ['name' => 'Mohafazat Mont-Liban', 'country_id' => 422],
        ['name' => 'Sidon', 'country_id' => 422],
        ['name' => 'al-Biqa', 'country_id' => 422],
        ['name' => 'al-Janub', 'country_id' => 422],
        ['name' => 'an-Nabatiyah', 'country_id' => 422],
        ['name' => 'ash-Shamal', 'country_id' => 422],
        ['name' => 'Berea', 'country_id' => 426],
        ['name' => 'Butha-Buthe', 'country_id' => 426],
        ['name' => 'Leribe', 'country_id' => 426],
        ['name' => 'Mafeteng', 'country_id' => 426],
        ['name' => 'Maseru', 'country_id' => 426],
        ['name' => "Mohale's Hoek", 'country_id' => 426],
        ['name' => 'Mokhotlong', 'country_id' => 426],
        ['name' => "Qacha's Nek", 'country_id' => 426],
        ['name' => 'Quthing', 'country_id' => 426],
        ['name' => 'Thaba-Tseka', 'country_id' => 426],
        ['name' => 'Bomi', 'country_id' => 430],
        ['name' => 'Bong', 'country_id' => 430],
        ['name' => 'Grand Bassa', 'country_id' => 430],
        ['name' => 'Grand Cape Mount', 'country_id' => 430],
        ['name' => 'Grand Gedeh', 'country_id' => 430],
        ['name' => 'Loffa', 'country_id' => 430],
        ['name' => 'Margibi', 'country_id' => 430],
        ['name' => 'Maryland and Grand Kru', 'country_id' => 430],
        ['name' => 'Montserrado', 'country_id' => 430],
        ['name' => 'Nimba', 'country_id' => 430],
        ['name' => 'Rivercess', 'country_id' => 430],
        ['name' => 'Sinoe', 'country_id' => 430],
        ['name' => 'Ajdabiya', 'country_id' => 434],
        ['name' => 'Fezzan', 'country_id' => 434],
        ['name' => 'Banghazi', 'country_id' => 434],
        ['name' => 'Darnah', 'country_id' => 434],
        ['name' => 'Ghadamis', 'country_id' => 434],
        ['name' => 'Gharyan', 'country_id' => 434],
        ['name' => 'Misratah', 'country_id' => 434],
        ['name' => 'Murzuq', 'country_id' => 434],
        ['name' => 'Sabha', 'country_id' => 434],
        ['name' => 'Sawfajjin', 'country_id' => 434],
        ['name' => 'Surt', 'country_id' => 434],
        ['name' => 'Tarabulus', 'country_id' => 434],
        ['name' => 'Tarhunah', 'country_id' => 434],
        ['name' => 'Tripolitania', 'country_id' => 434],
        ['name' => 'Tubruq', 'country_id' => 434],
        ['name' => 'Yafran', 'country_id' => 434],
        ['name' => 'Zlitan', 'country_id' => 434],
        ['name' => "al-'Aziziyah", 'country_id' => 434],
        ['name' => 'al-Fatih', 'country_id' => 434],
        ['name' => 'al-Jabal al Akhdar', 'country_id' => 434],
        ['name' => 'al-Jufrah', 'country_id' => 434],
        ['name' => 'al-Khums', 'country_id' => 434],
        ['name' => 'al-Kufrah', 'country_id' => 434],
        ['name' => 'an-Nuqat al-Khams', 'country_id' => 434],
        ['name' => "ash-Shati'", 'country_id' => 434],
        ['name' => 'az-Zawiyah', 'country_id' => 434],
        ['name' => 'Balzers', 'country_id' => 438],
        ['name' => 'Eschen', 'country_id' => 438],
        ['name' => 'Gamprin', 'country_id' => 438],
        ['name' => 'Mauren', 'country_id' => 438],
        ['name' => 'Planken', 'country_id' => 438],
        ['name' => 'Ruggell', 'country_id' => 438],
        ['name' => 'Schaan', 'country_id' => 438],
        ['name' => 'Schellenberg', 'country_id' => 438],
        ['name' => 'Triesen', 'country_id' => 438],
        ['name' => 'Triesenberg', 'country_id' => 438],
        ['name' => 'Vaduz', 'country_id' => 438],
        ['name' => 'Alytaus', 'country_id' => 440],
        ['name' => 'Anyksciai', 'country_id' => 440],
        ['name' => 'Kauno', 'country_id' => 440],
        ['name' => 'Klaipedos', 'country_id' => 440],
        ['name' => 'Marijampoles', 'country_id' => 440],
        ['name' => 'Panevezhio', 'country_id' => 440],
        ['name' => 'Panevezys', 'country_id' => 440],
        ['name' => 'Shiauliu', 'country_id' => 440],
        ['name' => 'Taurages', 'country_id' => 440],
        ['name' => 'Telshiu', 'country_id' => 440],
        ['name' => 'Telsiai', 'country_id' => 440],
        ['name' => 'Utenos', 'country_id' => 440],
        ['name' => 'Vilniaus', 'country_id' => 440],
        ['name' => 'Capellen', 'country_id' => 442],
        ['name' => 'Clervaux', 'country_id' => 442],
        ['name' => 'Diekirch', 'country_id' => 442],
        ['name' => 'Echternach', 'country_id' => 442],
        ['name' => 'Esch-sur-Alzette', 'country_id' => 442],
        ['name' => 'Grevenmacher', 'country_id' => 442],
        ['name' => 'Luxembourg', 'country_id' => 442],
        ['name' => 'Mersch', 'country_id' => 442],
        ['name' => 'Redange', 'country_id' => 442],
        ['name' => 'Remich', 'country_id' => 442],
        ['name' => 'Vianden', 'country_id' => 442],
        ['name' => 'Wiltz', 'country_id' => 442],
        ['name' => 'Macau', 'country_id' => 446],
        ['name' => 'Berovo', 'country_id' => 807],
        ['name' => 'Bitola', 'country_id' => 807],
        ['name' => 'Brod', 'country_id' => 807],
        ['name' => 'Debar', 'country_id' => 807],
        ['name' => 'Delchevo', 'country_id' => 807],
        ['name' => 'Demir Hisar', 'country_id' => 807],
        ['name' => 'Gevgelija', 'country_id' => 807],
        ['name' => 'Gostivar', 'country_id' => 807],
        ['name' => 'Kavadarci', 'country_id' => 807],
        ['name' => 'Kichevo', 'country_id' => 807],
        ['name' => 'Kochani', 'country_id' => 807],
        ['name' => 'Kratovo', 'country_id' => 807],
        ['name' => 'Kriva Palanka', 'country_id' => 807],
        ['name' => 'Krushevo', 'country_id' => 807],
        ['name' => 'Kumanovo', 'country_id' => 807],
        ['name' => 'Negotino', 'country_id' => 807],
        ['name' => 'Ohrid', 'country_id' => 807],
        ['name' => 'Prilep', 'country_id' => 807],
        ['name' => 'Probishtip', 'country_id' => 807],
        ['name' => 'Radovish', 'country_id' => 807],
        ['name' => 'Resen', 'country_id' => 807],
        ['name' => 'Shtip', 'country_id' => 807],
        ['name' => 'Skopje', 'country_id' => 807],
        ['name' => 'Struga', 'country_id' => 807],
        ['name' => 'Strumica', 'country_id' => 807],
        ['name' => 'Sveti Nikole', 'country_id' => 807],
        ['name' => 'Tetovo', 'country_id' => 807],
        ['name' => 'Valandovo', 'country_id' => 807],
        ['name' => 'Veles', 'country_id' => 807],
        ['name' => 'Vinica', 'country_id' => 807],
        ['name' => 'Antananarivo', 'country_id' => 450],
        ['name' => 'Antsiranana', 'country_id' => 450],
        ['name' => 'Fianarantsoa', 'country_id' => 450],
        ['name' => 'Mahajanga', 'country_id' => 450],
        ['name' => 'Toamasina', 'country_id' => 450],
        ['name' => 'Toliary', 'country_id' => 450],
        ['name' => 'Balaka', 'country_id' => 454],
        ['name' => 'Blantyre City', 'country_id' => 454],
        ['name' => 'Chikwawa', 'country_id' => 454],
        ['name' => 'Chiradzulu', 'country_id' => 454],
        ['name' => 'Chitipa', 'country_id' => 454],
        ['name' => 'Dedza', 'country_id' => 454],
        ['name' => 'Dowa', 'country_id' => 454],
        ['name' => 'Karonga', 'country_id' => 454],
        ['name' => 'Kasungu', 'country_id' => 454],
        ['name' => 'Lilongwe City', 'country_id' => 454],
        ['name' => 'Machinga', 'country_id' => 454],
        ['name' => 'Mangochi', 'country_id' => 454],
        ['name' => 'Mchinji', 'country_id' => 454],
        ['name' => 'Mulanje', 'country_id' => 454],
        ['name' => 'Mwanza', 'country_id' => 454],
        ['name' => 'Mzimba', 'country_id' => 454],
        ['name' => 'Mzuzu City', 'country_id' => 454],
        ['name' => 'Nkhata Bay', 'country_id' => 454],
        ['name' => 'Nkhotakota', 'country_id' => 454],
        ['name' => 'Nsanje', 'country_id' => 454],
        ['name' => 'Ntcheu', 'country_id' => 454],
        ['name' => 'Ntchisi', 'country_id' => 454],
        ['name' => 'Phalombe', 'country_id' => 454],
        ['name' => 'Rumphi', 'country_id' => 454],
        ['name' => 'Salima', 'country_id' => 454],
        ['name' => 'Thyolo', 'country_id' => 454],
        ['name' => 'Zomba Municipality', 'country_id' => 454],
        ['name' => 'Johor', 'country_id' => 458],
        ['name' => 'Kedah', 'country_id' => 458],
        ['name' => 'Kelantan', 'country_id' => 458],
        ['name' => 'Kuala Lumpur', 'country_id' => 458],
        ['name' => 'Labuan', 'country_id' => 458],
        ['name' => 'Melaka', 'country_id' => 458],
        ['name' => 'Negeri Johor', 'country_id' => 458],
        ['name' => 'Negeri Sembilan', 'country_id' => 458],
        ['name' => 'Pahang', 'country_id' => 458],
        ['name' => 'Penang', 'country_id' => 458],
        ['name' => 'Perak', 'country_id' => 458],
        ['name' => 'Perlis', 'country_id' => 458],
        ['name' => 'Pulau Pinang', 'country_id' => 458],
        ['name' => 'Sabah', 'country_id' => 458],
        ['name' => 'Sarawak', 'country_id' => 458],
        ['name' => 'Selangor', 'country_id' => 458],
        ['name' => 'Sembilan', 'country_id' => 458],
        ['name' => 'Terengganu', 'country_id' => 458],
        ['name' => 'Alif Alif', 'country_id' => 462],
        ['name' => 'Alif Dhaal', 'country_id' => 462],
        ['name' => 'Baa', 'country_id' => 462],
        ['name' => 'Dhaal', 'country_id' => 462],
        ['name' => 'Faaf', 'country_id' => 462],
        ['name' => 'Gaaf Alif', 'country_id' => 462],
        ['name' => 'Gaaf Dhaal', 'country_id' => 462],
        ['name' => 'Ghaviyani', 'country_id' => 462],
        ['name' => 'Haa Alif', 'country_id' => 462],
        ['name' => 'Haa Dhaal', 'country_id' => 462],
        ['name' => 'Kaaf', 'country_id' => 462],
        ['name' => 'Laam', 'country_id' => 462],
        ['name' => 'Lhaviyani', 'country_id' => 462],
        ['name' => 'Male', 'country_id' => 462],
        ['name' => 'Miim', 'country_id' => 462],
        ['name' => 'Nuun', 'country_id' => 462],
        ['name' => 'Raa', 'country_id' => 462],
        ['name' => 'Shaviyani', 'country_id' => 462],
        ['name' => 'Siin', 'country_id' => 462],
        ['name' => 'Thaa', 'country_id' => 462],
        ['name' => 'Vaav', 'country_id' => 462],
        ['name' => 'Bamako', 'country_id' => 466],
        ['name' => 'Gao', 'country_id' => 466],
        ['name' => 'Kayes', 'country_id' => 466],
        ['name' => 'Kidal', 'country_id' => 466],
        ['name' => 'Koulikoro', 'country_id' => 466],
        ['name' => 'Mopti', 'country_id' => 466],
        ['name' => 'Segou', 'country_id' => 466],
        ['name' => 'Sikasso', 'country_id' => 466],
        ['name' => 'Tombouctou', 'country_id' => 466],
        ['name' => 'Gozo and Comino', 'country_id' => 470],
        ['name' => 'Inner Harbour', 'country_id' => 470],
        ['name' => 'Northern', 'country_id' => 470],
        ['name' => 'Outer Harbour', 'country_id' => 470],
        ['name' => 'South Eastern', 'country_id' => 470],
        ['name' => 'Valletta', 'country_id' => 470],
        ['name' => 'Western', 'country_id' => 470],
        ['name' => 'Ailinlaplap', 'country_id' => 584],
        ['name' => 'Ailuk', 'country_id' => 584],
        ['name' => 'Arno', 'country_id' => 584],
        ['name' => 'Aur', 'country_id' => 584],
        ['name' => 'Bikini', 'country_id' => 584],
        ['name' => 'Ebon', 'country_id' => 584],
        ['name' => 'Enewetak', 'country_id' => 584],
        ['name' => 'Jabat', 'country_id' => 584],
        ['name' => 'Jaluit', 'country_id' => 584],
        ['name' => 'Kili', 'country_id' => 584],
        ['name' => 'Kwajalein', 'country_id' => 584],
        ['name' => 'Lae', 'country_id' => 584],
        ['name' => 'Lib', 'country_id' => 584],
        ['name' => 'Likiep', 'country_id' => 584],
        ['name' => 'Majuro', 'country_id' => 584],
        ['name' => 'Maloelap', 'country_id' => 584],
        ['name' => 'Mejit', 'country_id' => 584],
        ['name' => 'Mili', 'country_id' => 584],
        ['name' => 'Namorik', 'country_id' => 584],
        ['name' => 'Namu', 'country_id' => 584],
        ['name' => 'Rongelap', 'country_id' => 584],
        ['name' => 'Ujae', 'country_id' => 584],
        ['name' => 'Utrik', 'country_id' => 584],
        ['name' => 'Wotho', 'country_id' => 584],
        ['name' => 'Wotje', 'country_id' => 584],
        ['name' => 'Fort-de-France', 'country_id' => 474],
        ['name' => 'La Trinite', 'country_id' => 474],
        ['name' => 'Le Marin', 'country_id' => 474],
        ['name' => 'Saint-Pierre', 'country_id' => 474],
        ['name' => 'Adrar', 'country_id' => 478],
        ['name' => 'Assaba', 'country_id' => 478],
        ['name' => 'Brakna', 'country_id' => 478],
        ['name' => 'Dhakhlat Nawadibu', 'country_id' => 478],
        ['name' => 'Hudh-al-Gharbi', 'country_id' => 478],
        ['name' => 'Hudh-ash-Sharqi', 'country_id' => 478],
        ['name' => 'Inshiri', 'country_id' => 478],
        ['name' => 'Nawakshut', 'country_id' => 478],
        ['name' => 'Qidimagha', 'country_id' => 478],
        ['name' => 'Qurqul', 'country_id' => 478],
        ['name' => 'Taqant', 'country_id' => 478],
        ['name' => 'Tiris Zammur', 'country_id' => 478],
        ['name' => 'Trarza', 'country_id' => 478],
        ['name' => 'Black River', 'country_id' => 480],
        ['name' => 'Eau Coulee', 'country_id' => 480],
        ['name' => 'Flacq', 'country_id' => 480],
        ['name' => 'Floreal', 'country_id' => 480],
        ['name' => 'Grand Port', 'country_id' => 480],
        ['name' => 'Moka', 'country_id' => 480],
        ['name' => 'Pamplempousses', 'country_id' => 480],
        ['name' => 'Plaines Wilhelm', 'country_id' => 480],
        ['name' => 'Port Louis', 'country_id' => 480],
        ['name' => 'Riviere du Rempart', 'country_id' => 480],
        ['name' => 'Rodrigues', 'country_id' => 480],
        ['name' => 'Rose Hill', 'country_id' => 480],
        ['name' => 'Savanne', 'country_id' => 480],
        ['name' => 'Mayotte', 'country_id' => 175],
        ['name' => 'Pamanzi', 'country_id' => 175],
        ['name' => 'Aguascalientes', 'country_id' => 484],
        ['name' => 'Baja California', 'country_id' => 484],
        ['name' => 'Baja California Sur', 'country_id' => 484],
        ['name' => 'Campeche', 'country_id' => 484],
        ['name' => 'Chiapas', 'country_id' => 484],
        ['name' => 'Chihuahua', 'country_id' => 484],
        ['name' => 'Coahuila', 'country_id' => 484],
        ['name' => 'Colima', 'country_id' => 484],
        ['name' => 'Distrito Federal', 'country_id' => 484],
        ['name' => 'Durango', 'country_id' => 484],
        ['name' => 'Estado de Mexico', 'country_id' => 484],
        ['name' => 'Guanajuato', 'country_id' => 484],
        ['name' => 'Guerrero', 'country_id' => 484],
        ['name' => 'Hidalgo', 'country_id' => 484],
        ['name' => 'Jalisco', 'country_id' => 484],
        ['name' => 'Mexico', 'country_id' => 484],
        ['name' => 'Michoacan', 'country_id' => 484],
        ['name' => 'Morelos', 'country_id' => 484],
        ['name' => 'Nayarit', 'country_id' => 484],
        ['name' => 'Nuevo Leon', 'country_id' => 484],
        ['name' => 'Oaxaca', 'country_id' => 484],
        ['name' => 'Puebla', 'country_id' => 484],
        ['name' => 'Queretaro', 'country_id' => 484],
        ['name' => 'Quintana Roo', 'country_id' => 484],
        ['name' => 'San Luis Potosi', 'country_id' => 484],
        ['name' => 'Sinaloa', 'country_id' => 484],
        ['name' => 'Sonora', 'country_id' => 484],
        ['name' => 'Tabasco', 'country_id' => 484],
        ['name' => 'Tamaulipas', 'country_id' => 484],
        ['name' => 'Tlaxcala', 'country_id' => 484],
        ['name' => 'Veracruz', 'country_id' => 484],
        ['name' => 'Yucatan', 'country_id' => 484],
        ['name' => 'Zacatecas', 'country_id' => 484],
        ['name' => 'Chuuk', 'country_id' => 583],
        ['name' => 'Kusaie', 'country_id' => 583],
        ['name' => 'Pohnpei', 'country_id' => 583],
        ['name' => 'Yap', 'country_id' => 583],
        ['name' => 'Balti', 'country_id' => 498],
        ['name' => 'Cahul', 'country_id' => 498],
        ['name' => 'Chisinau', 'country_id' => 498],
        ['name' => 'Chisinau Oras', 'country_id' => 498],
        ['name' => 'Edinet', 'country_id' => 498],
        ['name' => 'Gagauzia', 'country_id' => 498],
        ['name' => 'Lapusna', 'country_id' => 498],
        ['name' => 'Orhei', 'country_id' => 498],
        ['name' => 'Soroca', 'country_id' => 498],
        ['name' => 'Taraclia', 'country_id' => 498],
        ['name' => 'Tighina', 'country_id' => 498],
        ['name' => 'Transnistria', 'country_id' => 498],
        ['name' => 'Ungheni', 'country_id' => 498],
        ['name' => 'Fontvieille', 'country_id' => 492],
        ['name' => 'La Condamine', 'country_id' => 492],
        ['name' => 'Monaco-Ville', 'country_id' => 492],
        ['name' => 'Monte Carlo', 'country_id' => 492],
        ['name' => 'Arhangaj', 'country_id' => 496],
        ['name' => 'Bajan-Olgij', 'country_id' => 496],
        ['name' => 'Bajanhongor', 'country_id' => 496],
        ['name' => 'Bulgan', 'country_id' => 496],
        ['name' => 'Darhan-Uul', 'country_id' => 496],
        ['name' => 'Dornod', 'country_id' => 496],
        ['name' => 'Dornogovi', 'country_id' => 496],
        ['name' => 'Dundgovi', 'country_id' => 496],
        ['name' => 'Govi-Altaj', 'country_id' => 496],
        ['name' => 'Govisumber', 'country_id' => 496],
        ['name' => 'Hentij', 'country_id' => 496],
        ['name' => 'Hovd', 'country_id' => 496],
        ['name' => 'Hovsgol', 'country_id' => 496],
        ['name' => 'Omnogovi', 'country_id' => 496],
        ['name' => 'Orhon', 'country_id' => 496],
        ['name' => 'Ovorhangaj', 'country_id' => 496],
        ['name' => 'Selenge', 'country_id' => 496],
        ['name' => 'Suhbaatar', 'country_id' => 496],
        ['name' => 'Tov', 'country_id' => 496],
        ['name' => 'Ulaanbaatar', 'country_id' => 496],
        ['name' => 'Uvs', 'country_id' => 496],
        ['name' => 'Zavhan', 'country_id' => 496],
        ['name' => 'Montserrat', 'country_id' => 500],
        ['name' => 'Agadir', 'country_id' => 504],
        ['name' => 'Casablanca', 'country_id' => 504],
        ['name' => 'Chaouia-Ouardigha', 'country_id' => 504],
        ['name' => 'Doukkala-Abda', 'country_id' => 504],
        ['name' => 'Fes-Boulemane', 'country_id' => 504],
        ['name' => 'Gharb-Chrarda-Beni Hssen', 'country_id' => 504],
        ['name' => 'Guelmim', 'country_id' => 504],
        ['name' => 'Kenitra', 'country_id' => 504],
        ['name' => 'Marrakech-Tensift-Al Haouz', 'country_id' => 504],
        ['name' => 'Meknes-Tafilalet', 'country_id' => 504],
        ['name' => 'Oriental', 'country_id' => 504],
        ['name' => 'Oujda', 'country_id' => 504],
        ['name' => 'Province de Tanger', 'country_id' => 504],
        ['name' => 'Rabat-Sale-Zammour-Zaer', 'country_id' => 504],
        ['name' => 'Sala Al Jadida', 'country_id' => 504],
        ['name' => 'Settat', 'country_id' => 504],
        ['name' => 'Souss Massa-Draa', 'country_id' => 504],
        ['name' => 'Tadla-Azilal', 'country_id' => 504],
        ['name' => 'Tangier-Tetouan', 'country_id' => 504],
        ['name' => 'Taza-Al Hoceima-Taounate', 'country_id' => 504],
        ['name' => 'Wilaya de Casablanca', 'country_id' => 504],
        ['name' => 'Wilaya de Rabat-Sale', 'country_id' => 504],
        ['name' => 'Cabo Delgado', 'country_id' => 508],
        ['name' => 'Gaza', 'country_id' => 508],
        ['name' => 'Inhambane', 'country_id' => 508],
        ['name' => 'Manica', 'country_id' => 508],
        ['name' => 'Maputo', 'country_id' => 508],
        ['name' => 'Maputo Provincia', 'country_id' => 508],
        ['name' => 'Nampula', 'country_id' => 508],
        ['name' => 'Niassa', 'country_id' => 508],
        ['name' => 'Sofala', 'country_id' => 508],
        ['name' => 'Tete', 'country_id' => 508],
        ['name' => 'Zambezia', 'country_id' => 508],
        ['name' => 'Ayeyarwady', 'country_id' => 104],
        ['name' => 'Bago', 'country_id' => 104],
        ['name' => 'Chin', 'country_id' => 104],
        ['name' => 'Kachin', 'country_id' => 104],
        ['name' => 'Kayah', 'country_id' => 104],
        ['name' => 'Kayin', 'country_id' => 104],
        ['name' => 'Magway', 'country_id' => 104],
        ['name' => 'Mandalay', 'country_id' => 104],
        ['name' => 'Mon', 'country_id' => 104],
        ['name' => 'Nay Pyi Taw', 'country_id' => 104],
        ['name' => 'Rakhine', 'country_id' => 104],
        ['name' => 'Sagaing', 'country_id' => 104],
        ['name' => 'Shan', 'country_id' => 104],
        ['name' => 'Tanintharyi', 'country_id' => 104],
        ['name' => 'Yangon', 'country_id' => 104],
        ['name' => 'Caprivi', 'country_id' => 516],
        ['name' => 'Erongo', 'country_id' => 516],
        ['name' => 'Hardap', 'country_id' => 516],
        ['name' => 'Karas', 'country_id' => 516],
        ['name' => 'Kavango', 'country_id' => 516],
        ['name' => 'Khomas', 'country_id' => 516],
        ['name' => 'Kunene', 'country_id' => 516],
        ['name' => 'Ohangwena', 'country_id' => 516],
        ['name' => 'Omaheke', 'country_id' => 516],
        ['name' => 'Omusati', 'country_id' => 516],
        ['name' => 'Oshana', 'country_id' => 516],
        ['name' => 'Oshikoto', 'country_id' => 516],
        ['name' => 'Otjozondjupa', 'country_id' => 516],
        ['name' => 'Yaren', 'country_id' => 520],
        ['name' => 'Bagmati', 'country_id' => 524],
        ['name' => 'Bheri', 'country_id' => 524],
        ['name' => 'Dhawalagiri', 'country_id' => 524],
        ['name' => 'Gandaki', 'country_id' => 524],
        ['name' => 'Janakpur', 'country_id' => 524],
        ['name' => 'Karnali', 'country_id' => 524],
        ['name' => 'Koshi', 'country_id' => 524],
        ['name' => 'Lumbini', 'country_id' => 524],
        ['name' => 'Mahakali', 'country_id' => 524],
        ['name' => 'Mechi', 'country_id' => 524],
        ['name' => 'Narayani', 'country_id' => 524],
        ['name' => 'Rapti', 'country_id' => 524],
        ['name' => 'Sagarmatha', 'country_id' => 524],
        ['name' => 'Seti', 'country_id' => 524],
        ['name' => 'Amsterdam', 'country_id' => 528],
        ['name' => 'Benelux', 'country_id' => 528],
        ['name' => 'Drenthe', 'country_id' => 528],
        ['name' => 'Flevoland', 'country_id' => 528],
        ['name' => 'Friesland', 'country_id' => 528],
        ['name' => 'Gelderland', 'country_id' => 528],
        ['name' => 'Groningen', 'country_id' => 528],
        ['name' => 'Limburg', 'country_id' => 528],
        ['name' => 'Noord-Brabant', 'country_id' => 528],
        ['name' => 'Noord-Holland', 'country_id' => 528],
        ['name' => 'Overijssel', 'country_id' => 528],
        ['name' => 'South Holland', 'country_id' => 528],
        ['name' => 'Utrecht', 'country_id' => 528],
        ['name' => 'Zeeland', 'country_id' => 528],
        ['name' => 'Zuid-Holland', 'country_id' => 528],
        ['name' => 'Iles', 'country_id' => 540],
        ['name' => 'Nord', 'country_id' => 540],
        ['name' => 'Sud', 'country_id' => 540],
        ['name' => 'Area Outside Region', 'country_id' => 554],
        ['name' => 'Auckland', 'country_id' => 554],
        ['name' => 'Bay of Plenty', 'country_id' => 554],
        ['name' => 'Canterbury', 'country_id' => 554],
        ['name' => 'Christchurch', 'country_id' => 554],
        ['name' => 'Gisborne', 'country_id' => 554],
        ['name' => "Hawke's Bay", 'country_id' => 554],
        ['name' => 'Manawatu-Wanganui', 'country_id' => 554],
        ['name' => 'Marlborough', 'country_id' => 554],
        ['name' => 'Nelson', 'country_id' => 554],
        ['name' => 'Northland', 'country_id' => 554],
        ['name' => 'Otago', 'country_id' => 554],
        ['name' => 'Rodney', 'country_id' => 554],
        ['name' => 'Southland', 'country_id' => 554],
        ['name' => 'Taranaki', 'country_id' => 554],
        ['name' => 'Tasman', 'country_id' => 554],
        ['name' => 'Waikato', 'country_id' => 554],
        ['name' => 'Wellington', 'country_id' => 554],
        ['name' => 'West Coast', 'country_id' => 554],
        ['name' => 'Atlantico Norte', 'country_id' => 558],
        ['name' => 'Atlantico Sur', 'country_id' => 558],
        ['name' => 'Boaco', 'country_id' => 558],
        ['name' => 'Carazo', 'country_id' => 558],
        ['name' => 'Chinandega', 'country_id' => 558],
        ['name' => 'Chontales', 'country_id' => 558],
        ['name' => 'Esteli', 'country_id' => 558],
        ['name' => 'Granada', 'country_id' => 558],
        ['name' => 'Jinotega', 'country_id' => 558],
        ['name' => 'Leon', 'country_id' => 558],
        ['name' => 'Madriz', 'country_id' => 558],
        ['name' => 'Managua', 'country_id' => 558],
        ['name' => 'Masaya', 'country_id' => 558],
        ['name' => 'Matagalpa', 'country_id' => 558],
        ['name' => 'Nueva Segovia', 'country_id' => 558],
        ['name' => 'Rio San Juan', 'country_id' => 558],
        ['name' => 'Rivas', 'country_id' => 558],
        ['name' => 'Agadez', 'country_id' => 562],
        ['name' => 'Diffa', 'country_id' => 562],
        ['name' => 'Dosso', 'country_id' => 562],
        ['name' => 'Maradi', 'country_id' => 562],
        ['name' => 'Niamey', 'country_id' => 562],
        ['name' => 'Tahoua', 'country_id' => 562],
        ['name' => 'Tillabery', 'country_id' => 562],
        ['name' => 'Zinder', 'country_id' => 562],
        ['name' => 'Abia', 'country_id' => 566],
        ['name' => 'Abuja Federal Capital Territor', 'country_id' => 566],
        ['name' => 'Adamawa', 'country_id' => 566],
        ['name' => 'Akwa Ibom', 'country_id' => 566],
        ['name' => 'Anambra', 'country_id' => 566],
        ['name' => 'Bauchi', 'country_id' => 566],
        ['name' => 'Bayelsa', 'country_id' => 566],
        ['name' => 'Benue', 'country_id' => 566],
        ['name' => 'Borno', 'country_id' => 566],
        ['name' => 'Cross River', 'country_id' => 566],
        ['name' => 'Delta', 'country_id' => 566],
        ['name' => 'Ebonyi', 'country_id' => 566],
        ['name' => 'Edo', 'country_id' => 566],
        ['name' => 'Ekiti', 'country_id' => 566],
        ['name' => 'Enugu', 'country_id' => 566],
        ['name' => 'Gombe', 'country_id' => 566],
        ['name' => 'Imo', 'country_id' => 566],
        ['name' => 'Jigawa', 'country_id' => 566],
        ['name' => 'Kaduna', 'country_id' => 566],
        ['name' => 'Kano', 'country_id' => 566],
        ['name' => 'Katsina', 'country_id' => 566],
        ['name' => 'Kebbi', 'country_id' => 566],
        ['name' => 'Kogi', 'country_id' => 566],
        ['name' => 'Kwara', 'country_id' => 566],
        ['name' => 'Lagos', 'country_id' => 566],
        ['name' => 'Nassarawa', 'country_id' => 566],
        ['name' => 'Niger', 'country_id' => 566],
        ['name' => 'Ogun', 'country_id' => 566],
        ['name' => 'Ondo', 'country_id' => 566],
        ['name' => 'Osun', 'country_id' => 566],
        ['name' => 'Oyo', 'country_id' => 566],
        ['name' => 'Plateau', 'country_id' => 566],
        ['name' => 'Rivers', 'country_id' => 566],
        ['name' => 'Sokoto', 'country_id' => 566],
        ['name' => 'Taraba', 'country_id' => 566],
        ['name' => 'Yobe', 'country_id' => 566],
        ['name' => 'Zamfara', 'country_id' => 566],
        ['name' => 'Niue', 'country_id' => 570],
        ['name' => 'Norfolk Island', 'country_id' => 574],
        ['name' => 'Northern Islands', 'country_id' => 580],
        ['name' => 'Rota', 'country_id' => 580],
        ['name' => 'Saipan', 'country_id' => 580],
        ['name' => 'Tinian', 'country_id' => 580],
        ['name' => 'Akershus', 'country_id' => 578],
        ['name' => 'Aust Agder', 'country_id' => 578],
        ['name' => 'Bergen', 'country_id' => 578],
        ['name' => 'Buskerud', 'country_id' => 578],
        ['name' => 'Finnmark', 'country_id' => 578],
        ['name' => 'Hedmark', 'country_id' => 578],
        ['name' => 'Hordaland', 'country_id' => 578],
        ['name' => 'Moere og Romsdal', 'country_id' => 578],
        ['name' => 'Nord Trondelag', 'country_id' => 578],
        ['name' => 'Nordland', 'country_id' => 578],
        ['name' => 'Oestfold', 'country_id' => 578],
        ['name' => 'Oppland', 'country_id' => 578],
        ['name' => 'Oslo', 'country_id' => 578],
        ['name' => 'Rogaland', 'country_id' => 578],
        ['name' => 'Soer Troendelag', 'country_id' => 578],
        ['name' => 'Sogn og Fjordane', 'country_id' => 578],
        ['name' => 'Stavern', 'country_id' => 578],
        ['name' => 'Sykkylven', 'country_id' => 578],
        ['name' => 'Telemark', 'country_id' => 578],
        ['name' => 'Troms', 'country_id' => 578],
        ['name' => 'Vest Agder', 'country_id' => 578],
        ['name' => 'Vestfold', 'country_id' => 578],
        ['name' => 'ÃƒÂ˜stfold', 'country_id' => 578],
        ['name' => 'Al Buraimi', 'country_id' => 512],
        ['name' => 'Dhufar', 'country_id' => 512],
        ['name' => 'Masqat', 'country_id' => 512],
        ['name' => 'Musandam', 'country_id' => 512],
        ['name' => 'Rusayl', 'country_id' => 512],
        ['name' => 'Wadi Kabir', 'country_id' => 512],
        ['name' => 'ad-Dakhiliyah', 'country_id' => 512],
        ['name' => 'adh-Dhahirah', 'country_id' => 512],
        ['name' => 'al-Batinah', 'country_id' => 512],
        ['name' => 'ash-Sharqiyah', 'country_id' => 512],
        ['name' => 'Azad kashmir', 'country_id' => 586],
        ['name' => 'Balochistan', 'country_id' => 586],
        ['name' => 'Fata', 'country_id' => 586],
        ['name' => 'Gilgit–baltistan', 'country_id' => 586],
        ['name' => 'Islamabad capital territory', 'country_id' => 586],
        ['name' => 'Khyber Pakhtunkhwa', 'country_id' => 586],
        ['name' => 'Punjab', 'country_id' => 586],
        ['name' => 'Sindh', 'country_id' => 586],
        ['name' => 'Aimeliik', 'country_id' => 585],
        ['name' => 'Airai', 'country_id' => 585],
        ['name' => 'Angaur', 'country_id' => 585],
        ['name' => 'Hatobohei', 'country_id' => 585],
        ['name' => 'Kayangel', 'country_id' => 585],
        ['name' => 'Koror', 'country_id' => 585],
        ['name' => 'Melekeok', 'country_id' => 585],
        ['name' => 'Ngaraard', 'country_id' => 585],
        ['name' => 'Ngardmau', 'country_id' => 585],
        ['name' => 'Ngaremlengui', 'country_id' => 585],
        ['name' => 'Ngatpang', 'country_id' => 585],
        ['name' => 'Ngchesar', 'country_id' => 585],
        ['name' => 'Ngerchelong', 'country_id' => 585],
        ['name' => 'Ngiwal', 'country_id' => 585],
        ['name' => 'Peleliu', 'country_id' => 585],
        ['name' => 'Sonsorol', 'country_id' => 585],
        ['name' => 'Ariha', 'country_id' => 275],
        ['name' => 'Bayt Lahm', 'country_id' => 275],
        ['name' => 'Bethlehem', 'country_id' => 275],
        ['name' => 'Dayr-al-Balah', 'country_id' => 275],
        ['name' => 'Ghazzah', 'country_id' => 275],
        ['name' => 'Ghazzah ash-Shamaliyah', 'country_id' => 275],
        ['name' => 'Janin', 'country_id' => 275],
        ['name' => 'Khan Yunis', 'country_id' => 275],
        ['name' => 'Nabulus', 'country_id' => 275],
        ['name' => 'Qalqilyah', 'country_id' => 275],
        ['name' => 'Rafah', 'country_id' => 275],
        ['name' => 'Ram Allah wal-Birah', 'country_id' => 275],
        ['name' => 'Salfit', 'country_id' => 275],
        ['name' => 'Tubas', 'country_id' => 275],
        ['name' => 'Tulkarm', 'country_id' => 275],
        ['name' => 'al-Khalil', 'country_id' => 275],
        ['name' => 'al-Quds', 'country_id' => 275],
        ['name' => 'Bocas del Toro', 'country_id' => 591],
        ['name' => 'Chiriqui', 'country_id' => 591],
        ['name' => 'Cocle', 'country_id' => 591],
        ['name' => 'Colon', 'country_id' => 591],
        ['name' => 'Darien', 'country_id' => 591],
        ['name' => 'Embera', 'country_id' => 591],
        ['name' => 'Herrera', 'country_id' => 591],
        ['name' => 'Kuna Yala', 'country_id' => 591],
        ['name' => 'Los Santos', 'country_id' => 591],
        ['name' => 'Ngobe Bugle', 'country_id' => 591],
        ['name' => 'Panama', 'country_id' => 591],
        ['name' => 'Veraguas', 'country_id' => 591],
        ['name' => 'East New Britain', 'country_id' => 598],
        ['name' => 'East Sepik', 'country_id' => 598],
        ['name' => 'Eastern Highlands', 'country_id' => 598],
        ['name' => 'Enga', 'country_id' => 598],
        ['name' => 'Fly River', 'country_id' => 598],
        ['name' => 'Gulf', 'country_id' => 598],
        ['name' => 'Madang', 'country_id' => 598],
        ['name' => 'Manus', 'country_id' => 598],
        ['name' => 'Milne Bay', 'country_id' => 598],
        ['name' => 'Morobe', 'country_id' => 598],
        ['name' => 'National Capital District', 'country_id' => 598],
        ['name' => 'New Ireland', 'country_id' => 598],
        ['name' => 'North Solomons', 'country_id' => 598],
        ['name' => 'Oro', 'country_id' => 598],
        ['name' => 'Sandaun', 'country_id' => 598],
        ['name' => 'Simbu', 'country_id' => 598],
        ['name' => 'Southern Highlands', 'country_id' => 598],
        ['name' => 'West New Britain', 'country_id' => 598],
        ['name' => 'Western Highlands', 'country_id' => 598],
        ['name' => 'Alto Paraguay', 'country_id' => 600],
        ['name' => 'Alto Parana', 'country_id' => 600],
        ['name' => 'Amambay', 'country_id' => 600],
        ['name' => 'Asuncion', 'country_id' => 600],
        ['name' => 'Boqueron', 'country_id' => 600],
        ['name' => 'Caaguazu', 'country_id' => 600],
        ['name' => 'Caazapa', 'country_id' => 600],
        ['name' => 'Canendiyu', 'country_id' => 600],
        ['name' => 'Central', 'country_id' => 600],
        ['name' => 'Concepcion', 'country_id' => 600],
        ['name' => 'Cordillera', 'country_id' => 600],
        ['name' => 'Guaira', 'country_id' => 600],
        ['name' => 'Itapua', 'country_id' => 600],
        ['name' => 'Misiones', 'country_id' => 600],
        ['name' => 'Neembucu', 'country_id' => 600],
        ['name' => 'Paraguari', 'country_id' => 600],
        ['name' => 'Presidente Hayes', 'country_id' => 600],
        ['name' => 'San Pedro', 'country_id' => 600],
        ['name' => 'Amazonas', 'country_id' => 604],
        ['name' => 'Ancash', 'country_id' => 604],
        ['name' => 'Apurimac', 'country_id' => 604],
        ['name' => 'Arequipa', 'country_id' => 604],
        ['name' => 'Ayacucho', 'country_id' => 604],
        ['name' => 'Cajamarca', 'country_id' => 604],
        ['name' => 'Cusco', 'country_id' => 604],
        ['name' => 'Huancavelica', 'country_id' => 604],
        ['name' => 'Huanuco', 'country_id' => 604],
        ['name' => 'Ica', 'country_id' => 604],
        ['name' => 'Junin', 'country_id' => 604],
        ['name' => 'La Libertad', 'country_id' => 604],
        ['name' => 'Lambayeque', 'country_id' => 604],
        ['name' => 'Lima y Callao', 'country_id' => 604],
        ['name' => 'Loreto', 'country_id' => 604],
        ['name' => 'Madre de Dios', 'country_id' => 604],
        ['name' => 'Moquegua', 'country_id' => 604],
        ['name' => 'Pasco', 'country_id' => 604],
        ['name' => 'Piura', 'country_id' => 604],
        ['name' => 'Puno', 'country_id' => 604],
        ['name' => 'San Martin', 'country_id' => 604],
        ['name' => 'Tacna', 'country_id' => 604],
        ['name' => 'Tumbes', 'country_id' => 604],
        ['name' => 'Ucayali', 'country_id' => 604],
        ['name' => 'Batangas', 'country_id' => 608],
        ['name' => 'Bicol', 'country_id' => 608],
        ['name' => 'Bulacan', 'country_id' => 608],
        ['name' => 'Cagayan', 'country_id' => 608],
        ['name' => 'Caraga', 'country_id' => 608],
        ['name' => 'Central Luzon', 'country_id' => 608],
        ['name' => 'Central Mindanao', 'country_id' => 608],
        ['name' => 'Central Visayas', 'country_id' => 608],
        ['name' => 'Cordillera', 'country_id' => 608],
        ['name' => 'Davao', 'country_id' => 608],
        ['name' => 'Eastern Visayas', 'country_id' => 608],
        ['name' => 'Greater Metropolitan Area', 'country_id' => 608],
        ['name' => 'Ilocos', 'country_id' => 608],
        ['name' => 'Laguna', 'country_id' => 608],
        ['name' => 'Luzon', 'country_id' => 608],
        ['name' => 'Mactan', 'country_id' => 608],
        ['name' => 'Metropolitan Manila Area', 'country_id' => 608],
        ['name' => 'Muslim Mindanao', 'country_id' => 608],
        ['name' => 'Northern Mindanao', 'country_id' => 608],
        ['name' => 'Southern Mindanao', 'country_id' => 608],
        ['name' => 'Southern Tagalog', 'country_id' => 608],
        ['name' => 'Western Mindanao', 'country_id' => 608],
        ['name' => 'Western Visayas', 'country_id' => 608],
        ['name' => 'Pitcairn Island', 'country_id' => 612],
        ['name' => 'Biale Blota', 'country_id' => 616],
        ['name' => 'Dobroszyce', 'country_id' => 616],
        ['name' => 'Dolnoslaskie', 'country_id' => 616],
        ['name' => 'Dziekanow Lesny', 'country_id' => 616],
        ['name' => 'Hopowo', 'country_id' => 616],
        ['name' => 'Kartuzy', 'country_id' => 616],
        ['name' => 'Koscian', 'country_id' => 616],
        ['name' => 'Krakow', 'country_id' => 616],
        ['name' => 'Kujawsko-Pomorskie', 'country_id' => 616],
        ['name' => 'Lodzkie', 'country_id' => 616],
        ['name' => 'Lubelskie', 'country_id' => 616],
        ['name' => 'Lubuskie', 'country_id' => 616],
        ['name' => 'Malomice', 'country_id' => 616],
        ['name' => 'Malopolskie', 'country_id' => 616],
        ['name' => 'Mazowieckie', 'country_id' => 616],
        ['name' => 'Mirkow', 'country_id' => 616],
        ['name' => 'Opolskie', 'country_id' => 616],
        ['name' => 'Ostrowiec', 'country_id' => 616],
        ['name' => 'Podkarpackie', 'country_id' => 616],
        ['name' => 'Podlaskie', 'country_id' => 616],
        ['name' => 'Polska', 'country_id' => 616],
        ['name' => 'Pomorskie', 'country_id' => 616],
        ['name' => 'Poznan', 'country_id' => 616],
        ['name' => 'Pruszkow', 'country_id' => 616],
        ['name' => 'Rymanowska', 'country_id' => 616],
        ['name' => 'Rzeszow', 'country_id' => 616],
        ['name' => 'Slaskie', 'country_id' => 616],
        ['name' => 'Stare Pole', 'country_id' => 616],
        ['name' => 'Swietokrzyskie', 'country_id' => 616],
        ['name' => 'Warminsko-Mazurskie', 'country_id' => 616],
        ['name' => 'Warsaw', 'country_id' => 616],
        ['name' => 'Wejherowo', 'country_id' => 616],
        ['name' => 'Wielkopolskie', 'country_id' => 616],
        ['name' => 'Wroclaw', 'country_id' => 616],
        ['name' => 'Zachodnio-Pomorskie', 'country_id' => 616],
        ['name' => 'Zukowo', 'country_id' => 616],
        ['name' => 'Abrantes', 'country_id' => 620],
        ['name' => 'Acores', 'country_id' => 620],
        ['name' => 'Alentejo', 'country_id' => 620],
        ['name' => 'Algarve', 'country_id' => 620],
        ['name' => 'Braga', 'country_id' => 620],
        ['name' => 'Centro', 'country_id' => 620],
        ['name' => 'Distrito de Leiria', 'country_id' => 620],
        ['name' => 'Distrito de Viana do Castelo', 'country_id' => 620],
        ['name' => 'Distrito de Vila Real', 'country_id' => 620],
        ['name' => 'Distrito do Porto', 'country_id' => 620],
        ['name' => 'Lisboa e Vale do Tejo', 'country_id' => 620],
        ['name' => 'Madeira', 'country_id' => 620],
        ['name' => 'Norte', 'country_id' => 620],
        ['name' => 'Paivas', 'country_id' => 620],
        ['name' => 'Arecibo', 'country_id' => 630],
        ['name' => 'Bayamon', 'country_id' => 630],
        ['name' => 'Carolina', 'country_id' => 630],
        ['name' => 'Florida', 'country_id' => 630],
        ['name' => 'Guayama', 'country_id' => 630],
        ['name' => 'Humacao', 'country_id' => 630],
        ['name' => 'Mayaguez-Aguadilla', 'country_id' => 630],
        ['name' => 'Ponce', 'country_id' => 630],
        ['name' => 'Salinas', 'country_id' => 630],
        ['name' => 'San Juan', 'country_id' => 630],
        ['name' => 'Doha', 'country_id' => 634],
        ['name' => 'Jarian-al-Batnah', 'country_id' => 634],
        ['name' => 'Umm Salal', 'country_id' => 634],
        ['name' => 'ad-Dawhah', 'country_id' => 634],
        ['name' => 'al-Ghuwayriyah', 'country_id' => 634],
        ['name' => 'al-Jumayliyah', 'country_id' => 634],
        ['name' => 'al-Khawr', 'country_id' => 634],
        ['name' => 'al-Wakrah', 'country_id' => 634],
        ['name' => 'ar-Rayyan', 'country_id' => 634],
        ['name' => 'ash-Shamal', 'country_id' => 634],
        ['name' => 'Saint-Benoit', 'country_id' => 638],
        ['name' => 'Saint-Denis', 'country_id' => 638],
        ['name' => 'Saint-Paul', 'country_id' => 638],
        ['name' => 'Saint-Pierre', 'country_id' => 638],
        ['name' => 'Alba', 'country_id' => 642],
        ['name' => 'Arad', 'country_id' => 642],
        ['name' => 'Arges', 'country_id' => 642],
        ['name' => 'Bacau', 'country_id' => 642],
        ['name' => 'Bihor', 'country_id' => 642],
        ['name' => 'Bistrita-Nasaud', 'country_id' => 642],
        ['name' => 'Botosani', 'country_id' => 642],
        ['name' => 'Braila', 'country_id' => 642],
        ['name' => 'Brasov', 'country_id' => 642],
        ['name' => 'Bucuresti', 'country_id' => 642],
        ['name' => 'Buzau', 'country_id' => 642],
        ['name' => 'Calarasi', 'country_id' => 642],
        ['name' => 'Caras-Severin', 'country_id' => 642],
        ['name' => 'Cluj', 'country_id' => 642],
        ['name' => 'Constanta', 'country_id' => 642],
        ['name' => 'Covasna', 'country_id' => 642],
        ['name' => 'Dambovita', 'country_id' => 642],
        ['name' => 'Dolj', 'country_id' => 642],
        ['name' => 'Galati', 'country_id' => 642],
        ['name' => 'Giurgiu', 'country_id' => 642],
        ['name' => 'Gorj', 'country_id' => 642],
        ['name' => 'Harghita', 'country_id' => 642],
        ['name' => 'Hunedoara', 'country_id' => 642],
        ['name' => 'Ialomita', 'country_id' => 642],
        ['name' => 'Iasi', 'country_id' => 642],
        ['name' => 'Ilfov', 'country_id' => 642],
        ['name' => 'Maramures', 'country_id' => 642],
        ['name' => 'Mehedinti', 'country_id' => 642],
        ['name' => 'Mures', 'country_id' => 642],
        ['name' => 'Neamt', 'country_id' => 642],
        ['name' => 'Olt', 'country_id' => 642],
        ['name' => 'Prahova', 'country_id' => 642],
        ['name' => 'Salaj', 'country_id' => 642],
        ['name' => 'Satu Mare', 'country_id' => 642],
        ['name' => 'Sibiu', 'country_id' => 642],
        ['name' => 'Sondelor', 'country_id' => 642],
        ['name' => 'Suceava', 'country_id' => 642],
        ['name' => 'Teleorman', 'country_id' => 642],
        ['name' => 'Timis', 'country_id' => 642],
        ['name' => 'Tulcea', 'country_id' => 642],
        ['name' => 'Valcea', 'country_id' => 642],
        ['name' => 'Vaslui', 'country_id' => 642],
        ['name' => 'Vrancea', 'country_id' => 642],
        ['name' => 'Adygeja', 'country_id' => 643],
        ['name' => 'Aga', 'country_id' => 643],
        ['name' => 'Alanija', 'country_id' => 643],
        ['name' => 'Altaj', 'country_id' => 643],
        ['name' => 'Amur', 'country_id' => 643],
        ['name' => 'Arhangelsk', 'country_id' => 643],
        ['name' => 'Astrahan', 'country_id' => 643],
        ['name' => 'Bashkortostan', 'country_id' => 643],
        ['name' => 'Belgorod', 'country_id' => 643],
        ['name' => 'Brjansk', 'country_id' => 643],
        ['name' => 'Burjatija', 'country_id' => 643],
        ['name' => 'Chechenija', 'country_id' => 643],
        ['name' => 'Cheljabinsk', 'country_id' => 643],
        ['name' => 'Chita', 'country_id' => 643],
        ['name' => 'Chukotka', 'country_id' => 643],
        ['name' => 'Chuvashija', 'country_id' => 643],
        ['name' => 'Dagestan', 'country_id' => 643],
        ['name' => 'Evenkija', 'country_id' => 643],
        ['name' => 'Gorno-Altaj', 'country_id' => 643],
        ['name' => 'Habarovsk', 'country_id' => 643],
        ['name' => 'Hakasija', 'country_id' => 643],
        ['name' => 'Hanty-Mansija', 'country_id' => 643],
        ['name' => 'Ingusetija', 'country_id' => 643],
        ['name' => 'Irkutsk', 'country_id' => 643],
        ['name' => 'Ivanovo', 'country_id' => 643],
        ['name' => 'Jamalo-Nenets', 'country_id' => 643],
        ['name' => 'Jaroslavl', 'country_id' => 643],
        ['name' => 'Jevrej', 'country_id' => 643],
        ['name' => 'Kabardino-Balkarija', 'country_id' => 643],
        ['name' => 'Kaliningrad', 'country_id' => 643],
        ['name' => 'Kalmykija', 'country_id' => 643],
        ['name' => 'Kaluga', 'country_id' => 643],
        ['name' => 'Kamchatka', 'country_id' => 643],
        ['name' => 'Karachaj-Cherkessija', 'country_id' => 643],
        ['name' => 'Karelija', 'country_id' => 643],
        ['name' => 'Kemerovo', 'country_id' => 643],
        ['name' => 'Khabarovskiy Kray', 'country_id' => 643],
        ['name' => 'Kirov', 'country_id' => 643],
        ['name' => 'Komi', 'country_id' => 643],
        ['name' => 'Komi-Permjakija', 'country_id' => 643],
        ['name' => 'Korjakija', 'country_id' => 643],
        ['name' => 'Kostroma', 'country_id' => 643],
        ['name' => 'Krasnodar', 'country_id' => 643],
        ['name' => 'Krasnojarsk', 'country_id' => 643],
        ['name' => 'Krasnoyarskiy Kray', 'country_id' => 643],
        ['name' => 'Kurgan', 'country_id' => 643],
        ['name' => 'Kursk', 'country_id' => 643],
        ['name' => 'Leningrad', 'country_id' => 643],
        ['name' => 'Lipeck', 'country_id' => 643],
        ['name' => 'Magadan', 'country_id' => 643],
        ['name' => 'Marij El', 'country_id' => 643],
        ['name' => 'Mordovija', 'country_id' => 643],
        ['name' => 'Moscow', 'country_id' => 643],
        ['name' => 'Moskovskaja Oblast', 'country_id' => 643],
        ['name' => 'Moskovskaya Oblast', 'country_id' => 643],
        ['name' => 'Moskva', 'country_id' => 643],
        ['name' => 'Murmansk', 'country_id' => 643],
        ['name' => 'Nenets', 'country_id' => 643],
        ['name' => 'Nizhnij Novgorod', 'country_id' => 643],
        ['name' => 'Novgorod', 'country_id' => 643],
        ['name' => 'Novokusnezk', 'country_id' => 643],
        ['name' => 'Novosibirsk', 'country_id' => 643],
        ['name' => 'Omsk', 'country_id' => 643],
        ['name' => 'Orenburg', 'country_id' => 643],
        ['name' => 'Orjol', 'country_id' => 643],
        ['name' => 'Penza', 'country_id' => 643],
        ['name' => 'Perm', 'country_id' => 643],
        ['name' => 'Primorje', 'country_id' => 643],
        ['name' => 'Pskov', 'country_id' => 643],
        ['name' => 'Pskovskaya Oblast', 'country_id' => 643],
        ['name' => 'Rjazan', 'country_id' => 643],
        ['name' => 'Rostov', 'country_id' => 643],
        ['name' => 'Saha', 'country_id' => 643],
        ['name' => 'Sahalin', 'country_id' => 643],
        ['name' => 'Samara', 'country_id' => 643],
        ['name' => 'Samarskaya', 'country_id' => 643],
        ['name' => 'Sankt-Peterburg', 'country_id' => 643],
        ['name' => 'Saratov', 'country_id' => 643],
        ['name' => 'Smolensk', 'country_id' => 643],
        ['name' => 'Stavropol', 'country_id' => 643],
        ['name' => 'Sverdlovsk', 'country_id' => 643],
        ['name' => 'Tajmyrija', 'country_id' => 643],
        ['name' => 'Tambov', 'country_id' => 643],
        ['name' => 'Tatarstan', 'country_id' => 643],
        ['name' => 'Tjumen', 'country_id' => 643],
        ['name' => 'Tomsk', 'country_id' => 643],
        ['name' => 'Tula', 'country_id' => 643],
        ['name' => 'Tver', 'country_id' => 643],
        ['name' => 'Tyva', 'country_id' => 643],
        ['name' => 'Udmurtija', 'country_id' => 643],
        ['name' => 'Uljanovsk', 'country_id' => 643],
        ['name' => 'Ulyanovskaya Oblast', 'country_id' => 643],
        ['name' => 'Ust-Orda', 'country_id' => 643],
        ['name' => 'Vladimir', 'country_id' => 643],
        ['name' => 'Volgograd', 'country_id' => 643],
        ['name' => 'Vologda', 'country_id' => 643],
        ['name' => 'Voronezh', 'country_id' => 643],
        ['name' => 'Butare', 'country_id' => 646],
        ['name' => 'Byumba', 'country_id' => 646],
        ['name' => 'Cyangugu', 'country_id' => 646],
        ['name' => 'Gikongoro', 'country_id' => 646],
        ['name' => 'Gisenyi', 'country_id' => 646],
        ['name' => 'Gitarama', 'country_id' => 646],
        ['name' => 'Kibungo', 'country_id' => 646],
        ['name' => 'Kibuye', 'country_id' => 646],
        ['name' => 'Kigali-ngali', 'country_id' => 646],
        ['name' => 'Ruhengeri', 'country_id' => 646],
        ['name' => 'Ascension', 'country_id' => 654],
        ['name' => 'Gough Island', 'country_id' => 654],
        ['name' => 'Saint Helena', 'country_id' => 654],
        ['name' => 'Tristan da Cunha', 'country_id' => 654],
        ['name' => 'Christ Church Nichola Town', 'country_id' => 659],
        ['name' => 'Saint Anne Sandy Point', 'country_id' => 659],
        ['name' => 'Saint George Basseterre', 'country_id' => 659],
        ['name' => 'Saint George Gingerland', 'country_id' => 659],
        ['name' => 'Saint James Windward', 'country_id' => 659],
        ['name' => 'Saint John Capesterre', 'country_id' => 659],
        ['name' => 'Saint John Figtree', 'country_id' => 659],
        ['name' => 'Saint Mary Cayon', 'country_id' => 659],
        ['name' => 'Saint Paul Capesterre', 'country_id' => 659],
        ['name' => 'Saint Paul Charlestown', 'country_id' => 659],
        ['name' => 'Saint Peter Basseterre', 'country_id' => 659],
        ['name' => 'Saint Thomas Lowland', 'country_id' => 659],
        ['name' => 'Saint Thomas Middle Island', 'country_id' => 659],
        ['name' => 'Trinity Palmetto Point', 'country_id' => 659],
        ['name' => 'Anse-la-Raye', 'country_id' => 662],
        ['name' => 'Canaries', 'country_id' => 662],
        ['name' => 'Castries', 'country_id' => 662],
        ['name' => 'Choiseul', 'country_id' => 662],
        ['name' => 'Dennery', 'country_id' => 662],
        ['name' => 'Gros Inlet', 'country_id' => 662],
        ['name' => 'Laborie', 'country_id' => 662],
        ['name' => 'Micoud', 'country_id' => 662],
        ['name' => 'Soufriere', 'country_id' => 662],
        ['name' => 'Vieux Fort', 'country_id' => 662],
        ['name' => 'Miquelon-Langlade', 'country_id' => 666],
        ['name' => 'Saint-Pierre', 'country_id' => 666],
        ['name' => 'Charlotte', 'country_id' => 670],
        ['name' => 'Grenadines', 'country_id' => 670],
        ['name' => 'Saint Andrew', 'country_id' => 670],
        ['name' => 'Saint David', 'country_id' => 670],
        ['name' => 'Saint George', 'country_id' => 670],
        ['name' => 'Saint Patrick', 'country_id' => 670],
        ['name' => "A'ana", 'country_id' => 882],
        ['name' => 'Aiga-i-le-Tai', 'country_id' => 882],
        ['name' => 'Atua', 'country_id' => 882],
        ['name' => "Fa'asaleleaga", 'country_id' => 882],
        ['name' => "Gaga'emauga", 'country_id' => 882],
        ['name' => 'Gagaifomauga', 'country_id' => 882],
        ['name' => 'Palauli', 'country_id' => 882],
        ['name' => "Satupa'itea", 'country_id' => 882],
        ['name' => 'Tuamasaga', 'country_id' => 882],
        ['name' => "Va'a-o-Fonoti", 'country_id' => 882],
        ['name' => 'Vaisigano', 'country_id' => 882],
        ['name' => 'Acquaviva', 'country_id' => 674],
        ['name' => 'Borgo Maggiore', 'country_id' => 674],
        ['name' => 'Chiesanuova', 'country_id' => 674],
        ['name' => 'Domagnano', 'country_id' => 674],
        ['name' => 'Faetano', 'country_id' => 674],
        ['name' => 'Fiorentino', 'country_id' => 674],
        ['name' => 'Montegiardino', 'country_id' => 674],
        ['name' => 'San Marino', 'country_id' => 674],
        ['name' => 'Serravalle', 'country_id' => 674],
        ['name' => 'Agua Grande', 'country_id' => 678],
        ['name' => 'Cantagalo', 'country_id' => 678],
        ['name' => 'Lemba', 'country_id' => 678],
        ['name' => 'Lobata', 'country_id' => 678],
        ['name' => 'Me-Zochi', 'country_id' => 678],
        ['name' => 'Pague', 'country_id' => 678],
        ['name' => 'Al Khobar', 'country_id' => 682],
        ['name' => 'Aseer', 'country_id' => 682],
        ['name' => 'Ash Sharqiyah', 'country_id' => 682],
        ['name' => 'Asir', 'country_id' => 682],
        ['name' => 'Central Province', 'country_id' => 682],
        ['name' => 'Eastern Province', 'country_id' => 682],
        ['name' => "Ha'il", 'country_id' => 682],
        ['name' => 'Jawf', 'country_id' => 682],
        ['name' => 'Jizan', 'country_id' => 682],
        ['name' => 'Makkah', 'country_id' => 682],
        ['name' => 'Najran', 'country_id' => 682],
        ['name' => 'Qasim', 'country_id' => 682],
        ['name' => 'Tabuk', 'country_id' => 682],
        ['name' => 'Western Province', 'country_id' => 682],
        ['name' => 'al-Bahah', 'country_id' => 682],
        ['name' => 'al-Hudud-ash-Shamaliyah', 'country_id' => 682],
        ['name' => 'al-Madinah', 'country_id' => 682],
        ['name' => 'ar-Riyad', 'country_id' => 682],
        ['name' => 'Dakar', 'country_id' => 686],
        ['name' => 'Diourbel', 'country_id' => 686],
        ['name' => 'Fatick', 'country_id' => 686],
        ['name' => 'Kaolack', 'country_id' => 686],
        ['name' => 'Kolda', 'country_id' => 686],
        ['name' => 'Louga', 'country_id' => 686],
        ['name' => 'Saint-Louis', 'country_id' => 686],
        ['name' => 'Tambacounda', 'country_id' => 686],
        ['name' => 'Thies', 'country_id' => 686],
        ['name' => 'Ziguinchor', 'country_id' => 686],
        ['name' => 'Central Serbia', 'country_id' => 688],
        ['name' => 'Kosovo and Metohija', 'country_id' => 688],
        ['name' => 'Vojvodina', 'country_id' => 688],
        ['name' => 'Anse Boileau', 'country_id' => 690],
        ['name' => 'Anse Royale', 'country_id' => 690],
        ['name' => 'Cascade', 'country_id' => 690],
        ['name' => 'Takamaka', 'country_id' => 690],
        ['name' => 'Victoria', 'country_id' => 690],
        ['name' => 'Eastern', 'country_id' => 694],
        ['name' => 'Northern', 'country_id' => 694],
        ['name' => 'Southern', 'country_id' => 694],
        ['name' => 'Western', 'country_id' => 694],
        ['name' => 'Singapore', 'country_id' => 702],
        ['name' => 'Banskobystricky', 'country_id' => 703],
        ['name' => 'Bratislavsky', 'country_id' => 703],
        ['name' => 'Kosicky', 'country_id' => 703],
        ['name' => 'Nitriansky', 'country_id' => 703],
        ['name' => 'Presovsky', 'country_id' => 703],
        ['name' => 'Trenciansky', 'country_id' => 703],
        ['name' => 'Trnavsky', 'country_id' => 703],
        ['name' => 'Zilinsky', 'country_id' => 703],
        ['name' => 'Benedikt', 'country_id' => 705],
        ['name' => 'Gorenjska', 'country_id' => 705],
        ['name' => 'Gorishka', 'country_id' => 705],
        ['name' => 'Jugovzhodna Slovenija', 'country_id' => 705],
        ['name' => 'Koroshka', 'country_id' => 705],
        ['name' => 'Notranjsko-krashka', 'country_id' => 705],
        ['name' => 'Obalno-krashka', 'country_id' => 705],
        ['name' => 'Obcina Domzale', 'country_id' => 705],
        ['name' => 'Obcina Vitanje', 'country_id' => 705],
        ['name' => 'Osrednjeslovenska', 'country_id' => 705],
        ['name' => 'Podravska', 'country_id' => 705],
        ['name' => 'Pomurska', 'country_id' => 705],
        ['name' => 'Savinjska', 'country_id' => 705],
        ['name' => 'Slovenian Littoral', 'country_id' => 705],
        ['name' => 'Spodnjeposavska', 'country_id' => 705],
        ['name' => 'Zasavska', 'country_id' => 705],
        ['name' => 'Central', 'country_id' => 90],
        ['name' => 'Choiseul', 'country_id' => 90],
        ['name' => 'Guadalcanal', 'country_id' => 90],
        ['name' => 'Isabel', 'country_id' => 90],
        ['name' => 'Makira and Ulawa', 'country_id' => 90],
        ['name' => 'Malaita', 'country_id' => 90],
        ['name' => 'Rennell and Bellona', 'country_id' => 90],
        ['name' => 'Temotu', 'country_id' => 90],
        ['name' => 'Western', 'country_id' => 90],
        ['name' => 'Awdal', 'country_id' => 706],
        ['name' => 'Bakol', 'country_id' => 706],
        ['name' => 'Banadir', 'country_id' => 706],
        ['name' => 'Bari', 'country_id' => 706],
        ['name' => 'Bay', 'country_id' => 706],
        ['name' => 'Galgudug', 'country_id' => 706],
        ['name' => 'Gedo', 'country_id' => 706],
        ['name' => 'Hiran', 'country_id' => 706],
        ['name' => 'Jubbada Hose', 'country_id' => 706],
        ['name' => 'Jubbadha Dexe', 'country_id' => 706],
        ['name' => 'Mudug', 'country_id' => 706],
        ['name' => 'Nugal', 'country_id' => 706],
        ['name' => 'Sanag', 'country_id' => 706],
        ['name' => 'Shabellaha Dhexe', 'country_id' => 706],
        ['name' => 'Shabellaha Hose', 'country_id' => 706],
        ['name' => 'Togdher', 'country_id' => 706],
        ['name' => 'Woqoyi Galbed', 'country_id' => 706],
        ['name' => 'Eastern Cape', 'country_id' => 710],
        ['name' => 'Free State', 'country_id' => 710],
        ['name' => 'Gauteng', 'country_id' => 710],
        ['name' => 'Kempton Park', 'country_id' => 710],
        ['name' => 'Kramerville', 'country_id' => 710],
        ['name' => 'KwaZulu Natal', 'country_id' => 710],
        ['name' => 'Limpopo', 'country_id' => 710],
        ['name' => 'Mpumalanga', 'country_id' => 710],
        ['name' => 'North West', 'country_id' => 710],
        ['name' => 'Northern Cape', 'country_id' => 710],
        ['name' => 'Parow', 'country_id' => 710],
        ['name' => 'Table View', 'country_id' => 710],
        ['name' => 'Umtentweni', 'country_id' => 710],
        ['name' => 'Western Cape', 'country_id' => 710],
        ['name' => 'South Georgia', 'country_id' => 239],
        ['name' => 'Central Equatoria', 'country_id' => 728],
        ['name' => 'A Coruna', 'country_id' => 724],
        ['name' => 'Alacant', 'country_id' => 724],
        ['name' => 'Alava', 'country_id' => 724],
        ['name' => 'Albacete', 'country_id' => 724],
        ['name' => 'Almeria', 'country_id' => 724],
        ['name' => 'Andalucia', 'country_id' => 724],
        ['name' => 'Asturias', 'country_id' => 724],
        ['name' => 'Avila', 'country_id' => 724],
        ['name' => 'Badajoz', 'country_id' => 724],
        ['name' => 'Balears', 'country_id' => 724],
        ['name' => 'Barcelona', 'country_id' => 724],
        ['name' => 'Bertamirans', 'country_id' => 724],
        ['name' => 'Biscay', 'country_id' => 724],
        ['name' => 'Burgos', 'country_id' => 724],
        ['name' => 'Caceres', 'country_id' => 724],
        ['name' => 'Cadiz', 'country_id' => 724],
        ['name' => 'Cantabria', 'country_id' => 724],
        ['name' => 'Castello', 'country_id' => 724],
        ['name' => 'Catalunya', 'country_id' => 724],
        ['name' => 'Ceuta', 'country_id' => 724],
        ['name' => 'Ciudad Real', 'country_id' => 724],
        ['name' => 'Comunidad Autonoma de Canarias', 'country_id' => 724],
        ['name' => 'Comunidad Autonoma de Cataluna', 'country_id' => 724],
        ['name' => 'Comunidad Autonoma de Galicia', 'country_id' => 724],
        ['name' => 'Comunidad Autonoma de las Isla', 'country_id' => 724],
        ['name' => 'Comunidad Autonoma del Princip', 'country_id' => 724],
        ['name' => 'Comunidad Valenciana', 'country_id' => 724],
        ['name' => 'Cordoba', 'country_id' => 724],
        ['name' => 'Cuenca', 'country_id' => 724],
        ['name' => 'Gipuzkoa', 'country_id' => 724],
        ['name' => 'Girona', 'country_id' => 724],
        ['name' => 'Granada', 'country_id' => 724],
        ['name' => 'Guadalajara', 'country_id' => 724],
        ['name' => 'Guipuzcoa', 'country_id' => 724],
        ['name' => 'Huelva', 'country_id' => 724],
        ['name' => 'Huesca', 'country_id' => 724],
        ['name' => 'Jaen', 'country_id' => 724],
        ['name' => 'La Rioja', 'country_id' => 724],
        ['name' => 'Las Palmas', 'country_id' => 724],
        ['name' => 'Leon', 'country_id' => 724],
        ['name' => 'Lerida', 'country_id' => 724],
        ['name' => 'Lleida', 'country_id' => 724],
        ['name' => 'Lugo', 'country_id' => 724],
        ['name' => 'Madrid', 'country_id' => 724],
        ['name' => 'Malaga', 'country_id' => 724],
        ['name' => 'Melilla', 'country_id' => 724],
        ['name' => 'Murcia', 'country_id' => 724],
        ['name' => 'Navarra', 'country_id' => 724],
        ['name' => 'Ourense', 'country_id' => 724],
        ['name' => 'Pais Vasco', 'country_id' => 724],
        ['name' => 'Palencia', 'country_id' => 724],
        ['name' => 'Pontevedra', 'country_id' => 724],
        ['name' => 'Salamanca', 'country_id' => 724],
        ['name' => 'Santa Cruz de Tenerife', 'country_id' => 724],
        ['name' => 'Segovia', 'country_id' => 724],
        ['name' => 'Sevilla', 'country_id' => 724],
        ['name' => 'Soria', 'country_id' => 724],
        ['name' => 'Tarragona', 'country_id' => 724],
        ['name' => 'Tenerife', 'country_id' => 724],
        ['name' => 'Teruel', 'country_id' => 724],
        ['name' => 'Toledo', 'country_id' => 724],
        ['name' => 'Valencia', 'country_id' => 724],
        ['name' => 'Valladolid', 'country_id' => 724],
        ['name' => 'Vizcaya', 'country_id' => 724],
        ['name' => 'Zamora', 'country_id' => 724],
        ['name' => 'Zaragoza', 'country_id' => 724],
        ['name' => 'Amparai', 'country_id' => 144],
        ['name' => 'Anuradhapuraya', 'country_id' => 144],
        ['name' => 'Badulla', 'country_id' => 144],
        ['name' => 'Boralesgamuwa', 'country_id' => 144],
        ['name' => 'Colombo', 'country_id' => 144],
        ['name' => 'Galla', 'country_id' => 144],
        ['name' => 'Gampaha', 'country_id' => 144],
        ['name' => 'Hambantota', 'country_id' => 144],
        ['name' => 'Kalatura', 'country_id' => 144],
        ['name' => 'Kegalla', 'country_id' => 144],
        ['name' => 'Kilinochchi', 'country_id' => 144],
        ['name' => 'Kurunegala', 'country_id' => 144],
        ['name' => 'Madakalpuwa', 'country_id' => 144],
        ['name' => 'Maha Nuwara', 'country_id' => 144],
        ['name' => 'Malwana', 'country_id' => 144],
        ['name' => 'Mannarama', 'country_id' => 144],
        ['name' => 'Matale', 'country_id' => 144],
        ['name' => 'Matara', 'country_id' => 144],
        ['name' => 'Monaragala', 'country_id' => 144],
        ['name' => 'Mullaitivu', 'country_id' => 144],
        ['name' => 'North Eastern Province', 'country_id' => 144],
        ['name' => 'North Western Province', 'country_id' => 144],
        ['name' => 'Nuwara Eliya', 'country_id' => 144],
        ['name' => 'Polonnaruwa', 'country_id' => 144],
        ['name' => 'Puttalama', 'country_id' => 144],
        ['name' => 'Ratnapuraya', 'country_id' => 144],
        ['name' => 'Southern Province', 'country_id' => 144],
        ['name' => 'Tirikunamalaya', 'country_id' => 144],
        ['name' => 'Tuscany', 'country_id' => 144],
        ['name' => 'Vavuniyawa', 'country_id' => 144],
        ['name' => 'Western Province', 'country_id' => 144],
        ['name' => 'Yapanaya', 'country_id' => 144],
        ['name' => 'kadawatha', 'country_id' => 144],
        ['name' => "A'ali-an-Nil", 'country_id' => 729],
        ['name' => 'Bahr-al-Jabal', 'country_id' => 729],
        ['name' => 'Central Equatoria', 'country_id' => 729],
        ['name' => 'Gharb Bahr-al-Ghazal', 'country_id' => 729],
        ['name' => 'Gharb Darfur', 'country_id' => 729],
        ['name' => 'Gharb Kurdufan', 'country_id' => 729],
        ['name' => "Gharb-al-Istiwa'iyah", 'country_id' => 729],
        ['name' => 'Janub Darfur', 'country_id' => 729],
        ['name' => 'Janub Kurdufan', 'country_id' => 729],
        ['name' => 'Junqali', 'country_id' => 729],
        ['name' => 'Kassala', 'country_id' => 729],
        ['name' => 'Nahr-an-Nil', 'country_id' => 729],
        ['name' => 'Shamal Bahr-al-Ghazal', 'country_id' => 729],
        ['name' => 'Shamal Darfur', 'country_id' => 729],
        ['name' => 'Shamal Kurdufan', 'country_id' => 729],
        ['name' => "Sharq-al-Istiwa'iyah", 'country_id' => 729],
        ['name' => 'Sinnar', 'country_id' => 729],
        ['name' => 'Warab', 'country_id' => 729],
        ['name' => 'Wilayat al Khartum', 'country_id' => 729],
        ['name' => 'al-Bahr-al-Ahmar', 'country_id' => 729],
        ['name' => 'al-Buhayrat', 'country_id' => 729],
        ['name' => 'al-Jazirah', 'country_id' => 729],
        ['name' => 'al-Khartum', 'country_id' => 729],
        ['name' => 'al-Qadarif', 'country_id' => 729],
        ['name' => 'al-Wahdah', 'country_id' => 729],
        ['name' => 'an-Nil-al-Abyad', 'country_id' => 729],
        ['name' => 'an-Nil-al-Azraq', 'country_id' => 729],
        ['name' => 'ash-Shamaliyah', 'country_id' => 729],
        ['name' => 'Brokopondo', 'country_id' => 740],
        ['name' => 'Commewijne', 'country_id' => 740],
        ['name' => 'Coronie', 'country_id' => 740],
        ['name' => 'Marowijne', 'country_id' => 740],
        ['name' => 'Nickerie', 'country_id' => 740],
        ['name' => 'Para', 'country_id' => 740],
        ['name' => 'Paramaribo', 'country_id' => 740],
        ['name' => 'Saramacca', 'country_id' => 740],
        ['name' => 'Wanica', 'country_id' => 740],
        ['name' => 'Svalbard', 'country_id' => 744],
        ['name' => 'Hhohho', 'country_id' => 748],
        ['name' => 'Lubombo', 'country_id' => 748],
        ['name' => 'Manzini', 'country_id' => 748],
        ['name' => 'Shiselweni', 'country_id' => 748],
        ['name' => 'Alvsborgs Lan', 'country_id' => 752],
        ['name' => 'Angermanland', 'country_id' => 752],
        ['name' => 'Blekinge', 'country_id' => 752],
        ['name' => 'Bohuslan', 'country_id' => 752],
        ['name' => 'Dalarna', 'country_id' => 752],
        ['name' => 'Gavleborg', 'country_id' => 752],
        ['name' => 'Gaza', 'country_id' => 752],
        ['name' => 'Gotland', 'country_id' => 752],
        ['name' => 'Halland', 'country_id' => 752],
        ['name' => 'Jamtland', 'country_id' => 752],
        ['name' => 'Jonkoping', 'country_id' => 752],
        ['name' => 'Kalmar', 'country_id' => 752],
        ['name' => 'Kristianstads', 'country_id' => 752],
        ['name' => 'Kronoberg', 'country_id' => 752],
        ['name' => 'Norrbotten', 'country_id' => 752],
        ['name' => 'Orebro', 'country_id' => 752],
        ['name' => 'Ostergotland', 'country_id' => 752],
        ['name' => 'Saltsjo-Boo', 'country_id' => 752],
        ['name' => 'Skane', 'country_id' => 752],
        ['name' => 'Smaland', 'country_id' => 752],
        ['name' => 'Sodermanland', 'country_id' => 752],
        ['name' => 'Stockholm', 'country_id' => 752],
        ['name' => 'Uppsala', 'country_id' => 752],
        ['name' => 'Varmland', 'country_id' => 752],
        ['name' => 'Vasterbotten', 'country_id' => 752],
        ['name' => 'Vastergotland', 'country_id' => 752],
        ['name' => 'Vasternorrland', 'country_id' => 752],
        ['name' => 'Vastmanland', 'country_id' => 752],
        ['name' => 'Vastra Gotaland', 'country_id' => 752],
        ['name' => 'Aargau', 'country_id' => 756],
        ['name' => 'Appenzell Inner-Rhoden', 'country_id' => 756],
        ['name' => 'Appenzell-Ausser Rhoden', 'country_id' => 756],
        ['name' => 'Basel-Landschaft', 'country_id' => 756],
        ['name' => 'Basel-Stadt', 'country_id' => 756],
        ['name' => 'Bern', 'country_id' => 756],
        ['name' => 'Canton Ticino', 'country_id' => 756],
        ['name' => 'Fribourg', 'country_id' => 756],
        ['name' => 'Geneve', 'country_id' => 756],
        ['name' => 'Glarus', 'country_id' => 756],
        ['name' => 'Graubunden', 'country_id' => 756],
        ['name' => 'Heerbrugg', 'country_id' => 756],
        ['name' => 'Jura', 'country_id' => 756],
        ['name' => 'Kanton Aargau', 'country_id' => 756],
        ['name' => 'Luzern', 'country_id' => 756],
        ['name' => 'Morbio Inferiore', 'country_id' => 756],
        ['name' => 'Muhen', 'country_id' => 756],
        ['name' => 'Neuchatel', 'country_id' => 756],
        ['name' => 'Nidwalden', 'country_id' => 756],
        ['name' => 'Obwalden', 'country_id' => 756],
        ['name' => 'Sankt Gallen', 'country_id' => 756],
        ['name' => 'Schaffhausen', 'country_id' => 756],
        ['name' => 'Schwyz', 'country_id' => 756],
        ['name' => 'Solothurn', 'country_id' => 756],
        ['name' => 'Thurgau', 'country_id' => 756],
        ['name' => 'Ticino', 'country_id' => 756],
        ['name' => 'Uri', 'country_id' => 756],
        ['name' => 'Valais', 'country_id' => 756],
        ['name' => 'Vaud', 'country_id' => 756],
        ['name' => 'Vauffelin', 'country_id' => 756],
        ['name' => 'Zug', 'country_id' => 756],
        ['name' => 'Zurich', 'country_id' => 756],
        ['name' => 'Aleppo', 'country_id' => 760],
        ['name' => "Dar'a", 'country_id' => 760],
        ['name' => 'Dayr-az-Zawr', 'country_id' => 760],
        ['name' => 'Dimashq', 'country_id' => 760],
        ['name' => 'Halab', 'country_id' => 760],
        ['name' => 'Hamah', 'country_id' => 760],
        ['name' => 'Hims', 'country_id' => 760],
        ['name' => 'Idlib', 'country_id' => 760],
        ['name' => 'Madinat Dimashq', 'country_id' => 760],
        ['name' => 'Tartus', 'country_id' => 760],
        ['name' => 'al-Hasakah', 'country_id' => 760],
        ['name' => 'al-Ladhiqiyah', 'country_id' => 760],
        ['name' => 'al-Qunaytirah', 'country_id' => 760],
        ['name' => 'ar-Raqqah', 'country_id' => 760],
        ['name' => 'as-Suwayda', 'country_id' => 760],
        ['name' => 'Changhwa', 'country_id' => 158],
        ['name' => 'Chiayi Hsien', 'country_id' => 158],
        ['name' => 'Chiayi Shih', 'country_id' => 158],
        ['name' => 'Eastern Taipei', 'country_id' => 158],
        ['name' => 'Hsinchu Hsien', 'country_id' => 158],
        ['name' => 'Hsinchu Shih', 'country_id' => 158],
        ['name' => 'Hualien', 'country_id' => 158],
        ['name' => 'Ilan', 'country_id' => 158],
        ['name' => 'Kaohsiung Hsien', 'country_id' => 158],
        ['name' => 'Kaohsiung Shih', 'country_id' => 158],
        ['name' => 'Keelung Shih', 'country_id' => 158],
        ['name' => 'Kinmen', 'country_id' => 158],
        ['name' => 'Miaoli', 'country_id' => 158],
        ['name' => 'Nantou', 'country_id' => 158],
        ['name' => 'Northern Taiwan', 'country_id' => 158],
        ['name' => 'Penghu', 'country_id' => 158],
        ['name' => 'Pingtung', 'country_id' => 158],
        ['name' => 'Taichung', 'country_id' => 158],
        ['name' => 'Taichung Hsien', 'country_id' => 158],
        ['name' => 'Taichung Shih', 'country_id' => 158],
        ['name' => 'Tainan Hsien', 'country_id' => 158],
        ['name' => 'Tainan Shih', 'country_id' => 158],
        ['name' => 'Taipei Hsien', 'country_id' => 158],
        ['name' => 'Taipei Shih / Taipei Hsien', 'country_id' => 158],
        ['name' => 'Taitung', 'country_id' => 158],
        ['name' => 'Taoyuan', 'country_id' => 158],
        ['name' => 'Yilan', 'country_id' => 158],
        ['name' => 'Yun-Lin Hsien', 'country_id' => 158],
        ['name' => 'Yunlin', 'country_id' => 158],
        ['name' => 'Dushanbe', 'country_id' => 762],
        ['name' => 'Gorno-Badakhshan', 'country_id' => 762],
        ['name' => 'Karotegin', 'country_id' => 762],
        ['name' => 'Khatlon', 'country_id' => 762],
        ['name' => 'Sughd', 'country_id' => 762],
        ['name' => 'Arusha', 'country_id' => 834],
        ['name' => 'Dar es Salaam', 'country_id' => 834],
        ['name' => 'Dodoma', 'country_id' => 834],
        ['name' => 'Iringa', 'country_id' => 834],
        ['name' => 'Kagera', 'country_id' => 834],
        ['name' => 'Kigoma', 'country_id' => 834],
        ['name' => 'Kilimanjaro', 'country_id' => 834],
        ['name' => 'Lindi', 'country_id' => 834],
        ['name' => 'Mara', 'country_id' => 834],
        ['name' => 'Mbeya', 'country_id' => 834],
        ['name' => 'Morogoro', 'country_id' => 834],
        ['name' => 'Mtwara', 'country_id' => 834],
        ['name' => 'Mwanza', 'country_id' => 834],
        ['name' => 'Pwani', 'country_id' => 834],
        ['name' => 'Rukwa', 'country_id' => 834],
        ['name' => 'Ruvuma', 'country_id' => 834],
        ['name' => 'Shinyanga', 'country_id' => 834],
        ['name' => 'Singida', 'country_id' => 834],
        ['name' => 'Tabora', 'country_id' => 834],
        ['name' => 'Tanga', 'country_id' => 834],
        ['name' => 'Zanzibar and Pemba', 'country_id' => 834],
        ['name' => 'Amnat Charoen', 'country_id' => 764],
        ['name' => 'Ang Thong', 'country_id' => 764],
        ['name' => 'Bangkok', 'country_id' => 764],
        ['name' => 'Buri Ram', 'country_id' => 764],
        ['name' => 'Chachoengsao', 'country_id' => 764],
        ['name' => 'Chai Nat', 'country_id' => 764],
        ['name' => 'Chaiyaphum', 'country_id' => 764],
        ['name' => 'Changwat Chaiyaphum', 'country_id' => 764],
        ['name' => 'Chanthaburi', 'country_id' => 764],
        ['name' => 'Chiang Mai', 'country_id' => 764],
        ['name' => 'Chiang Rai', 'country_id' => 764],
        ['name' => 'Chon Buri', 'country_id' => 764],
        ['name' => 'Chumphon', 'country_id' => 764],
        ['name' => 'Kalasin', 'country_id' => 764],
        ['name' => 'Kamphaeng Phet', 'country_id' => 764],
        ['name' => 'Kanchanaburi', 'country_id' => 764],
        ['name' => 'Khon Kaen', 'country_id' => 764],
        ['name' => 'Krabi', 'country_id' => 764],
        ['name' => 'Krung Thep', 'country_id' => 764],
        ['name' => 'Lampang', 'country_id' => 764],
        ['name' => 'Lamphun', 'country_id' => 764],
        ['name' => 'Loei', 'country_id' => 764],
        ['name' => 'Lop Buri', 'country_id' => 764],
        ['name' => 'Mae Hong Son', 'country_id' => 764],
        ['name' => 'Maha Sarakham', 'country_id' => 764],
        ['name' => 'Mukdahan', 'country_id' => 764],
        ['name' => 'Nakhon Nayok', 'country_id' => 764],
        ['name' => 'Nakhon Pathom', 'country_id' => 764],
        ['name' => 'Nakhon Phanom', 'country_id' => 764],
        ['name' => 'Nakhon Ratchasima', 'country_id' => 764],
        ['name' => 'Nakhon Sawan', 'country_id' => 764],
        ['name' => 'Nakhon Si Thammarat', 'country_id' => 764],
        ['name' => 'Nan', 'country_id' => 764],
        ['name' => 'Narathiwat', 'country_id' => 764],
        ['name' => 'Nong Bua Lam Phu', 'country_id' => 764],
        ['name' => 'Nong Khai', 'country_id' => 764],
        ['name' => 'Nonthaburi', 'country_id' => 764],
        ['name' => 'Pathum Thani', 'country_id' => 764],
        ['name' => 'Pattani', 'country_id' => 764],
        ['name' => 'Phangnga', 'country_id' => 764],
        ['name' => 'Phatthalung', 'country_id' => 764],
        ['name' => 'Phayao', 'country_id' => 764],
        ['name' => 'Phetchabun', 'country_id' => 764],
        ['name' => 'Phetchaburi', 'country_id' => 764],
        ['name' => 'Phichit', 'country_id' => 764],
        ['name' => 'Phitsanulok', 'country_id' => 764],
        ['name' => 'Phra Nakhon Si Ayutthaya', 'country_id' => 764],
        ['name' => 'Phrae', 'country_id' => 764],
        ['name' => 'Phuket', 'country_id' => 764],
        ['name' => 'Prachin Buri', 'country_id' => 764],
        ['name' => 'Prachuap Khiri Khan', 'country_id' => 764],
        ['name' => 'Ranong', 'country_id' => 764],
        ['name' => 'Ratchaburi', 'country_id' => 764],
        ['name' => 'Rayong', 'country_id' => 764],
        ['name' => 'Roi Et', 'country_id' => 764],
        ['name' => 'Sa Kaeo', 'country_id' => 764],
        ['name' => 'Sakon Nakhon', 'country_id' => 764],
        ['name' => 'Samut Prakan', 'country_id' => 764],
        ['name' => 'Samut Sakhon', 'country_id' => 764],
        ['name' => 'Samut Songkhran', 'country_id' => 764],
        ['name' => 'Saraburi', 'country_id' => 764],
        ['name' => 'Satun', 'country_id' => 764],
        ['name' => 'Si Sa Ket', 'country_id' => 764],
        ['name' => 'Sing Buri', 'country_id' => 764],
        ['name' => 'Songkhla', 'country_id' => 764],
        ['name' => 'Sukhothai', 'country_id' => 764],
        ['name' => 'Suphan Buri', 'country_id' => 764],
        ['name' => 'Surat Thani', 'country_id' => 764],
        ['name' => 'Surin', 'country_id' => 764],
        ['name' => 'Tak', 'country_id' => 764],
        ['name' => 'Trang', 'country_id' => 764],
        ['name' => 'Trat', 'country_id' => 764],
        ['name' => 'Ubon Ratchathani', 'country_id' => 764],
        ['name' => 'Udon Thani', 'country_id' => 764],
        ['name' => 'Uthai Thani', 'country_id' => 764],
        ['name' => 'Uttaradit', 'country_id' => 764],
        ['name' => 'Yala', 'country_id' => 764],
        ['name' => 'Yasothon', 'country_id' => 764],
        ['name' => 'Centre', 'country_id' => 768],
        ['name' => 'Kara', 'country_id' => 768],
        ['name' => 'Maritime', 'country_id' => 768],
        ['name' => 'Plateaux', 'country_id' => 768],
        ['name' => 'Savanes', 'country_id' => 768],
        ['name' => 'Atafu', 'country_id' => 772],
        ['name' => 'Fakaofo', 'country_id' => 772],
        ['name' => 'Nukunonu', 'country_id' => 772],
        ['name' => 'Eua', 'country_id' => 776],
        ['name' => "Ha'apai", 'country_id' => 776],
        ['name' => 'Niuas', 'country_id' => 776],
        ['name' => 'Tongatapu', 'country_id' => 776],
        ['name' => "Vava'u", 'country_id' => 776],
        ['name' => 'Arima-Tunapuna-Piarco', 'country_id' => 780],
        ['name' => 'Caroni', 'country_id' => 780],
        ['name' => 'Chaguanas', 'country_id' => 780],
        ['name' => 'Couva-Tabaquite-Talparo', 'country_id' => 780],
        ['name' => 'Diego Martin', 'country_id' => 780],
        ['name' => 'Glencoe', 'country_id' => 780],
        ['name' => 'Penal Debe', 'country_id' => 780],
        ['name' => 'Point Fortin', 'country_id' => 780],
        ['name' => 'Port of Spain', 'country_id' => 780],
        ['name' => 'Princes Town', 'country_id' => 780],
        ['name' => 'Saint George', 'country_id' => 780],
        ['name' => 'San Fernando', 'country_id' => 780],
        ['name' => 'San Juan', 'country_id' => 780],
        ['name' => 'Sangre Grande', 'country_id' => 780],
        ['name' => 'Siparia', 'country_id' => 780],
        ['name' => 'Tobago', 'country_id' => 780],
        ['name' => 'Aryanah', 'country_id' => 788],
        ['name' => 'Bajah', 'country_id' => 788],
        ['name' => "Bin 'Arus", 'country_id' => 788],
        ['name' => 'Binzart', 'country_id' => 788],
        ['name' => 'Gouvernorat de Ariana', 'country_id' => 788],
        ['name' => 'Gouvernorat de Nabeul', 'country_id' => 788],
        ['name' => 'Gouvernorat de Sousse', 'country_id' => 788],
        ['name' => 'Hammamet Yasmine', 'country_id' => 788],
        ['name' => 'Jundubah', 'country_id' => 788],
        ['name' => 'Madaniyin', 'country_id' => 788],
        ['name' => 'Manubah', 'country_id' => 788],
        ['name' => 'Monastir', 'country_id' => 788],
        ['name' => 'Nabul', 'country_id' => 788],
        ['name' => 'Qabis', 'country_id' => 788],
        ['name' => 'Qafsah', 'country_id' => 788],
        ['name' => 'Qibili', 'country_id' => 788],
        ['name' => 'Safaqis', 'country_id' => 788],
        ['name' => 'Sfax', 'country_id' => 788],
        ['name' => 'Sidi Bu Zayd', 'country_id' => 788],
        ['name' => 'Silyanah', 'country_id' => 788],
        ['name' => 'Susah', 'country_id' => 788],
        ['name' => 'Tatawin', 'country_id' => 788],
        ['name' => 'Tawzar', 'country_id' => 788],
        ['name' => 'Tunis', 'country_id' => 788],
        ['name' => 'Zaghwan', 'country_id' => 788],
        ['name' => 'al-Kaf', 'country_id' => 788],
        ['name' => 'al-Mahdiyah', 'country_id' => 788],
        ['name' => 'al-Munastir', 'country_id' => 788],
        ['name' => 'al-Qasrayn', 'country_id' => 788],
        ['name' => 'al-Qayrawan', 'country_id' => 788],
        ['name' => 'Adana', 'country_id' => 792],
        ['name' => 'Adiyaman', 'country_id' => 792],
        ['name' => 'Afyon', 'country_id' => 792],
        ['name' => 'Agri', 'country_id' => 792],
        ['name' => 'Aksaray', 'country_id' => 792],
        ['name' => 'Amasya', 'country_id' => 792],
        ['name' => 'Ankara', 'country_id' => 792],
        ['name' => 'Antalya', 'country_id' => 792],
        ['name' => 'Ardahan', 'country_id' => 792],
        ['name' => 'Artvin', 'country_id' => 792],
        ['name' => 'Aydin', 'country_id' => 792],
        ['name' => 'Balikesir', 'country_id' => 792],
        ['name' => 'Bartin', 'country_id' => 792],
        ['name' => 'Batman', 'country_id' => 792],
        ['name' => 'Bayburt', 'country_id' => 792],
        ['name' => 'Bilecik', 'country_id' => 792],
        ['name' => 'Bingol', 'country_id' => 792],
        ['name' => 'Bitlis', 'country_id' => 792],
        ['name' => 'Bolu', 'country_id' => 792],
        ['name' => 'Burdur', 'country_id' => 792],
        ['name' => 'Bursa', 'country_id' => 792],
        ['name' => 'Canakkale', 'country_id' => 792],
        ['name' => 'Cankiri', 'country_id' => 792],
        ['name' => 'Corum', 'country_id' => 792],
        ['name' => 'Denizli', 'country_id' => 792],
        ['name' => 'Diyarbakir', 'country_id' => 792],
        ['name' => 'Duzce', 'country_id' => 792],
        ['name' => 'Edirne', 'country_id' => 792],
        ['name' => 'Elazig', 'country_id' => 792],
        ['name' => 'Erzincan', 'country_id' => 792],
        ['name' => 'Erzurum', 'country_id' => 792],
        ['name' => 'Eskisehir', 'country_id' => 792],
        ['name' => 'Gaziantep', 'country_id' => 792],
        ['name' => 'Giresun', 'country_id' => 792],
        ['name' => 'Gumushane', 'country_id' => 792],
        ['name' => 'Hakkari', 'country_id' => 792],
        ['name' => 'Hatay', 'country_id' => 792],
        ['name' => 'Icel', 'country_id' => 792],
        ['name' => 'Igdir', 'country_id' => 792],
        ['name' => 'Isparta', 'country_id' => 792],
        ['name' => 'Istanbul', 'country_id' => 792],
        ['name' => 'Izmir', 'country_id' => 792],
        ['name' => 'Kahramanmaras', 'country_id' => 792],
        ['name' => 'Karabuk', 'country_id' => 792],
        ['name' => 'Karaman', 'country_id' => 792],
        ['name' => 'Kars', 'country_id' => 792],
        ['name' => 'Karsiyaka', 'country_id' => 792],
        ['name' => 'Kastamonu', 'country_id' => 792],
        ['name' => 'Kayseri', 'country_id' => 792],
        ['name' => 'Kilis', 'country_id' => 792],
        ['name' => 'Kirikkale', 'country_id' => 792],
        ['name' => 'Kirklareli', 'country_id' => 792],
        ['name' => 'Kirsehir', 'country_id' => 792],
        ['name' => 'Kocaeli', 'country_id' => 792],
        ['name' => 'Konya', 'country_id' => 792],
        ['name' => 'Kutahya', 'country_id' => 792],
        ['name' => 'Lefkosa', 'country_id' => 792],
        ['name' => 'Malatya', 'country_id' => 792],
        ['name' => 'Manisa', 'country_id' => 792],
        ['name' => 'Mardin', 'country_id' => 792],
        ['name' => 'Mugla', 'country_id' => 792],
        ['name' => 'Mus', 'country_id' => 792],
        ['name' => 'Nevsehir', 'country_id' => 792],
        ['name' => 'Nigde', 'country_id' => 792],
        ['name' => 'Ordu', 'country_id' => 792],
        ['name' => 'Osmaniye', 'country_id' => 792],
        ['name' => 'Rize', 'country_id' => 792],
        ['name' => 'Sakarya', 'country_id' => 792],
        ['name' => 'Samsun', 'country_id' => 792],
        ['name' => 'Sanliurfa', 'country_id' => 792],
        ['name' => 'Siirt', 'country_id' => 792],
        ['name' => 'Sinop', 'country_id' => 792],
        ['name' => 'Sirnak', 'country_id' => 792],
        ['name' => 'Sivas', 'country_id' => 792],
        ['name' => 'Tekirdag', 'country_id' => 792],
        ['name' => 'Tokat', 'country_id' => 792],
        ['name' => 'Trabzon', 'country_id' => 792],
        ['name' => 'Tunceli', 'country_id' => 792],
        ['name' => 'Usak', 'country_id' => 792],
        ['name' => 'Van', 'country_id' => 792],
        ['name' => 'Yalova', 'country_id' => 792],
        ['name' => 'Yozgat', 'country_id' => 792],
        ['name' => 'Zonguldak', 'country_id' => 792],
        ['name' => 'Ahal', 'country_id' => 795],
        ['name' => 'Asgabat', 'country_id' => 795],
        ['name' => 'Balkan', 'country_id' => 795],
        ['name' => 'Dasoguz', 'country_id' => 795],
        ['name' => 'Lebap', 'country_id' => 795],
        ['name' => 'Mari', 'country_id' => 795],
        ['name' => 'Grand Turk', 'country_id' => 796],
        ['name' => 'South Caicos and East Caicos', 'country_id' => 796],
        ['name' => 'Funafuti', 'country_id' => 798],
        ['name' => 'Nanumanga', 'country_id' => 798],
        ['name' => 'Nanumea', 'country_id' => 798],
        ['name' => 'Niutao', 'country_id' => 798],
        ['name' => 'Nui', 'country_id' => 798],
        ['name' => 'Nukufetau', 'country_id' => 798],
        ['name' => 'Nukulaelae', 'country_id' => 798],
        ['name' => 'Vaitupu', 'country_id' => 798],
        ['name' => 'Central', 'country_id' => 800],
        ['name' => 'Eastern', 'country_id' => 800],
        ['name' => 'Northern', 'country_id' => 800],
        ['name' => 'Western', 'country_id' => 800],
        ['name' => "Cherkas'ka", 'country_id' => 804],
        ['name' => "Chernihivs'ka", 'country_id' => 804],
        ['name' => "Chernivets'ka", 'country_id' => 804],
        ['name' => 'Crimea', 'country_id' => 804],
        ['name' => 'Dnipropetrovska', 'country_id' => 804],
        ['name' => "Donets'ka", 'country_id' => 804],
        ['name' => "Ivano-Frankivs'ka", 'country_id' => 804],
        ['name' => 'Kharkiv', 'country_id' => 804],
        ['name' => 'Kharkov', 'country_id' => 804],
        ['name' => 'Khersonska', 'country_id' => 804],
        ['name' => "Khmel'nyts'ka", 'country_id' => 804],
        ['name' => 'Kirovohrad', 'country_id' => 804],
        ['name' => 'Krym', 'country_id' => 804],
        ['name' => 'Kyyiv', 'country_id' => 804],
        ['name' => "Kyyivs'ka", 'country_id' => 804],
        ['name' => "L'vivs'ka", 'country_id' => 804],
        ['name' => "Luhans'ka", 'country_id' => 804],
        ['name' => "Mykolayivs'ka", 'country_id' => 804],
        ['name' => "Odes'ka", 'country_id' => 804],
        ['name' => 'Odessa', 'country_id' => 804],
        ['name' => "Poltavs'ka", 'country_id' => 804],
        ['name' => "Rivnens'ka", 'country_id' => 804],
        ['name' => "Sevastopol'", 'country_id' => 804],
        ['name' => "Sums'ka", 'country_id' => 804],
        ['name' => "Ternopil's'ka", 'country_id' => 804],
        ['name' => "Volyns'ka", 'country_id' => 804],
        ['name' => "Vynnyts'ka", 'country_id' => 804],
        ['name' => "Zakarpats'ka", 'country_id' => 804],
        ['name' => 'Zaporizhia', 'country_id' => 804],
        ['name' => "Zhytomyrs'ka", 'country_id' => 804],
        ['name' => 'Abu Zabi', 'country_id' => 784],
        ['name' => 'Ajman', 'country_id' => 784],
        ['name' => 'Dubai', 'country_id' => 784],
        ['name' => 'Ras al-Khaymah', 'country_id' => 784],
        ['name' => 'Sharjah', 'country_id' => 784],
        ['name' => 'Sharjha', 'country_id' => 784],
        ['name' => 'Umm al Qaywayn', 'country_id' => 784],
        ['name' => 'al-Fujayrah', 'country_id' => 784],
        ['name' => 'ash-Shariqah', 'country_id' => 784],
        ['name' => 'Aberdeen', 'country_id' => 818],
        ['name' => 'Aberdeenshire', 'country_id' => 818],
        ['name' => 'Argyll', 'country_id' => 818],
        ['name' => 'Armagh', 'country_id' => 818],
        ['name' => 'Bedfordshire', 'country_id' => 818],
        ['name' => 'Belfast', 'country_id' => 818],
        ['name' => 'Berkshire', 'country_id' => 818],
        ['name' => 'Birmingham', 'country_id' => 818],
        ['name' => 'Brechin', 'country_id' => 818],
        ['name' => 'Bridgnorth', 'country_id' => 818],
        ['name' => 'Bristol', 'country_id' => 818],
        ['name' => 'Buckinghamshire', 'country_id' => 818],
        ['name' => 'Cambridge', 'country_id' => 818],
        ['name' => 'Cambridgeshire', 'country_id' => 818],
        ['name' => 'Channel Islands', 'country_id' => 818],
        ['name' => 'Cheshire', 'country_id' => 818],
        ['name' => 'Cleveland', 'country_id' => 818],
        ['name' => 'Co Fermanagh', 'country_id' => 818],
        ['name' => 'Conwy', 'country_id' => 818],
        ['name' => 'Cornwall', 'country_id' => 818],
        ['name' => 'Coventry', 'country_id' => 818],
        ['name' => 'Craven Arms', 'country_id' => 818],
        ['name' => 'Cumbria', 'country_id' => 818],
        ['name' => 'Denbighshire', 'country_id' => 818],
        ['name' => 'Derby', 'country_id' => 818],
        ['name' => 'Derbyshire', 'country_id' => 818],
        ['name' => 'Devon', 'country_id' => 818],
        ['name' => 'Dial Code Dungannon', 'country_id' => 818],
        ['name' => 'Didcot', 'country_id' => 818],
        ['name' => 'Dorset', 'country_id' => 818],
        ['name' => 'Dunbartonshire', 'country_id' => 818],
        ['name' => 'Durham', 'country_id' => 818],
        ['name' => 'East Dunbartonshire', 'country_id' => 818],
        ['name' => 'East Lothian', 'country_id' => 818],
        ['name' => 'East Midlands', 'country_id' => 818],
        ['name' => 'East Sussex', 'country_id' => 818],
        ['name' => 'East Yorkshire', 'country_id' => 818],
        ['name' => 'England', 'country_id' => 818],
        ['name' => 'Essex', 'country_id' => 818],
        ['name' => 'Fermanagh', 'country_id' => 818],
        ['name' => 'Fife', 'country_id' => 818],
        ['name' => 'Flintshire', 'country_id' => 818],
        ['name' => 'Fulham', 'country_id' => 818],
        ['name' => 'Gainsborough', 'country_id' => 818],
        ['name' => 'Glocestershire', 'country_id' => 818],
        ['name' => 'Gwent', 'country_id' => 818],
        ['name' => 'Hampshire', 'country_id' => 818],
        ['name' => 'Hants', 'country_id' => 818],
        ['name' => 'Herefordshire', 'country_id' => 818],
        ['name' => 'Hertfordshire', 'country_id' => 818],
        ['name' => 'Ireland', 'country_id' => 818],
        ['name' => 'Isle Of Man', 'country_id' => 818],
        ['name' => 'Isle of Wight', 'country_id' => 818],
        ['name' => 'Kenford', 'country_id' => 818],
        ['name' => 'Kent', 'country_id' => 818],
        ['name' => 'Kilmarnock', 'country_id' => 818],
        ['name' => 'Lanarkshire', 'country_id' => 818],
        ['name' => 'Lancashire', 'country_id' => 818],
        ['name' => 'Leicestershire', 'country_id' => 818],
        ['name' => 'Lincolnshire', 'country_id' => 818],
        ['name' => 'Llanymynech', 'country_id' => 818],
        ['name' => 'London', 'country_id' => 818],
        ['name' => 'Ludlow', 'country_id' => 818],
        ['name' => 'Manchester', 'country_id' => 818],
        ['name' => 'Mayfair', 'country_id' => 818],
        ['name' => 'Merseyside', 'country_id' => 818],
        ['name' => 'Mid Glamorgan', 'country_id' => 818],
        ['name' => 'Middlesex', 'country_id' => 818],
        ['name' => 'Mildenhall', 'country_id' => 818],
        ['name' => 'Monmouthshire', 'country_id' => 818],
        ['name' => 'Newton Stewart', 'country_id' => 818],
        ['name' => 'Norfolk', 'country_id' => 818],
        ['name' => 'North Humberside', 'country_id' => 818],
        ['name' => 'North Yorkshire', 'country_id' => 818],
        ['name' => 'Northamptonshire', 'country_id' => 818],
        ['name' => 'Northants', 'country_id' => 818],
        ['name' => 'Northern Ireland', 'country_id' => 818],
        ['name' => 'Northumberland', 'country_id' => 818],
        ['name' => 'Nottinghamshire', 'country_id' => 818],
        ['name' => 'Oxford', 'country_id' => 818],
        ['name' => 'Powys', 'country_id' => 818],
        ['name' => 'Roos-shire', 'country_id' => 818],
        ['name' => 'SUSSEX', 'country_id' => 818],
        ['name' => 'Sark', 'country_id' => 818],
        ['name' => 'Scotland', 'country_id' => 818],
        ['name' => 'Scottish Borders', 'country_id' => 818],
        ['name' => 'Shropshire', 'country_id' => 818],
        ['name' => 'Somerset', 'country_id' => 818],
        ['name' => 'South Glamorgan', 'country_id' => 818],
        ['name' => 'South Wales', 'country_id' => 818],
        ['name' => 'South Yorkshire', 'country_id' => 818],
        ['name' => 'Southwell', 'country_id' => 818],
        ['name' => 'Staffordshire', 'country_id' => 818],
        ['name' => 'Strabane', 'country_id' => 818],
        ['name' => 'Suffolk', 'country_id' => 818],
        ['name' => 'Surrey', 'country_id' => 818],
        ['name' => 'Sussex', 'country_id' => 818],
        ['name' => 'Twickenham', 'country_id' => 818],
        ['name' => 'Tyne and Wear', 'country_id' => 818],
        ['name' => 'Tyrone', 'country_id' => 818],
        ['name' => 'Utah', 'country_id' => 818],
        ['name' => 'Wales', 'country_id' => 818],
        ['name' => 'Warwickshire', 'country_id' => 818],
        ['name' => 'West Lothian', 'country_id' => 818],
        ['name' => 'West Midlands', 'country_id' => 818],
        ['name' => 'West Sussex', 'country_id' => 818],
        ['name' => 'West Yorkshire', 'country_id' => 818],
        ['name' => 'Whissendine', 'country_id' => 818],
        ['name' => 'Wiltshire', 'country_id' => 818],
        ['name' => 'Wokingham', 'country_id' => 818],
        ['name' => 'Worcestershire', 'country_id' => 818],
        ['name' => 'Wrexham', 'country_id' => 818],
        ['name' => 'Wurttemberg', 'country_id' => 818],
        ['name' => 'Yorkshire', 'country_id' => 818],
        ['name' => 'Alabama', 'country_id' => 840],
        ['name' => 'Alaska', 'country_id' => 840],
        ['name' => 'Arizona', 'country_id' => 840],
        ['name' => 'Arkansas', 'country_id' => 840],
        ['name' => 'Byram', 'country_id' => 840],
        ['name' => 'California', 'country_id' => 840],
        ['name' => 'Cokato', 'country_id' => 840],
        ['name' => 'Colorado', 'country_id' => 840],
        ['name' => 'Connecticut', 'country_id' => 840],
        ['name' => 'Delaware', 'country_id' => 840],
        ['name' => 'District of Columbia', 'country_id' => 840],
        ['name' => 'Florida', 'country_id' => 840],
        ['name' => 'Georgia', 'country_id' => 840],
        ['name' => 'Hawaii', 'country_id' => 840],
        ['name' => 'Idaho', 'country_id' => 840],
        ['name' => 'Illinois', 'country_id' => 840],
        ['name' => 'Indiana', 'country_id' => 840],
        ['name' => 'Iowa', 'country_id' => 840],
        ['name' => 'Kansas', 'country_id' => 840],
        ['name' => 'Kentucky', 'country_id' => 840],
        ['name' => 'Louisiana', 'country_id' => 840],
        ['name' => 'Lowa', 'country_id' => 840],
        ['name' => 'Maine', 'country_id' => 840],
        ['name' => 'Maryland', 'country_id' => 840],
        ['name' => 'Massachusetts', 'country_id' => 840],
        ['name' => 'Medfield', 'country_id' => 840],
        ['name' => 'Michigan', 'country_id' => 840],
        ['name' => 'Minnesota', 'country_id' => 840],
        ['name' => 'Mississippi', 'country_id' => 840],
        ['name' => 'Missouri', 'country_id' => 840],
        ['name' => 'Montana', 'country_id' => 840],
        ['name' => 'Nebraska', 'country_id' => 840],
        ['name' => 'Nevada', 'country_id' => 840],
        ['name' => 'New Hampshire', 'country_id' => 840],
        ['name' => 'New Jersey', 'country_id' => 840],
        ['name' => 'New Jersy', 'country_id' => 840],
        ['name' => 'New Mexico', 'country_id' => 840],
        ['name' => 'New York', 'country_id' => 840],
        ['name' => 'North Carolina', 'country_id' => 840],
        ['name' => 'North Dakota', 'country_id' => 840],
        ['name' => 'Ohio', 'country_id' => 840],
        ['name' => 'Oklahoma', 'country_id' => 840],
        ['name' => 'Ontario', 'country_id' => 840],
        ['name' => 'Oregon', 'country_id' => 840],
        ['name' => 'Pennsylvania', 'country_id' => 840],
        ['name' => 'Ramey', 'country_id' => 840],
        ['name' => 'Rhode Island', 'country_id' => 840],
        ['name' => 'South Carolina', 'country_id' => 840],
        ['name' => 'South Dakota', 'country_id' => 840],
        ['name' => 'Sublimity', 'country_id' => 840],
        ['name' => 'Tennessee', 'country_id' => 840],
        ['name' => 'Texas', 'country_id' => 840],
        ['name' => 'Trimble', 'country_id' => 840],
        ['name' => 'Utah', 'country_id' => 840],
        ['name' => 'Vermont', 'country_id' => 840],
        ['name' => 'Virginia', 'country_id' => 840],
        ['name' => 'Washington', 'country_id' => 840],
        ['name' => 'West Virginia', 'country_id' => 840],
        ['name' => 'Wisconsin', 'country_id' => 840],
        ['name' => 'Wyoming', 'country_id' => 840],
        ['name' => 'United States Minor Outlying I', 'country_id' => 581],
        ['name' => 'Artigas', 'country_id' => 858],
        ['name' => 'Canelones', 'country_id' => 858],
        ['name' => 'Cerro Largo', 'country_id' => 858],
        ['name' => 'Colonia', 'country_id' => 858],
        ['name' => 'Durazno', 'country_id' => 858],
        ['name' => 'FLorida', 'country_id' => 858],
        ['name' => 'Flores', 'country_id' => 858],
        ['name' => 'Lavalleja', 'country_id' => 858],
        ['name' => 'Maldonado', 'country_id' => 858],
        ['name' => 'Montevideo', 'country_id' => 858],
        ['name' => 'Paysandu', 'country_id' => 858],
        ['name' => 'Rio Negro', 'country_id' => 858],
        ['name' => 'Rivera', 'country_id' => 858],
        ['name' => 'Rocha', 'country_id' => 858],
        ['name' => 'Salto', 'country_id' => 858],
        ['name' => 'San Jose', 'country_id' => 858],
        ['name' => 'Soriano', 'country_id' => 858],
        ['name' => 'Tacuarembo', 'country_id' => 858],
        ['name' => 'Treinta y Tres', 'country_id' => 858],
        ['name' => 'Andijon', 'country_id' => 860],
        ['name' => 'Buhoro', 'country_id' => 860],
        ['name' => 'Buxoro Viloyati', 'country_id' => 860],
        ['name' => 'Cizah', 'country_id' => 860],
        ['name' => 'Fargona', 'country_id' => 860],
        ['name' => 'Horazm', 'country_id' => 860],
        ['name' => 'Kaskadar', 'country_id' => 860],
        ['name' => 'Korakalpogiston', 'country_id' => 860],
        ['name' => 'Namangan', 'country_id' => 860],
        ['name' => 'Navoi', 'country_id' => 860],
        ['name' => 'Samarkand', 'country_id' => 860],
        ['name' => 'Sirdare', 'country_id' => 860],
        ['name' => 'Surhondar', 'country_id' => 860],
        ['name' => 'Toskent', 'country_id' => 860],
        ['name' => 'Malampa', 'country_id' => 548],
        ['name' => 'Penama', 'country_id' => 548],
        ['name' => 'Sanma', 'country_id' => 548],
        ['name' => 'Shefa', 'country_id' => 548],
        ['name' => 'Tafea', 'country_id' => 548],
        ['name' => 'Torba', 'country_id' => 548],
        ['name' => 'Vatican City State (Holy See)', 'country_id' => 336],
        ['name' => 'Amazonas', 'country_id' => 862],
        ['name' => 'Anzoategui', 'country_id' => 862],
        ['name' => 'Apure', 'country_id' => 862],
        ['name' => 'Aragua', 'country_id' => 862],
        ['name' => 'Barinas', 'country_id' => 862],
        ['name' => 'Bolivar', 'country_id' => 862],
        ['name' => 'Carabobo', 'country_id' => 862],
        ['name' => 'Cojedes', 'country_id' => 862],
        ['name' => 'Delta Amacuro', 'country_id' => 862],
        ['name' => 'Distrito Federal', 'country_id' => 862],
        ['name' => 'Falcon', 'country_id' => 862],
        ['name' => 'Guarico', 'country_id' => 862],
        ['name' => 'Lara', 'country_id' => 862],
        ['name' => 'Merida', 'country_id' => 862],
        ['name' => 'Miranda', 'country_id' => 862],
        ['name' => 'Monagas', 'country_id' => 862],
        ['name' => 'Nueva Esparta', 'country_id' => 862],
        ['name' => 'Portuguesa', 'country_id' => 862],
        ['name' => 'Sucre', 'country_id' => 862],
        ['name' => 'Tachira', 'country_id' => 862],
        ['name' => 'Trujillo', 'country_id' => 862],
        ['name' => 'Vargas', 'country_id' => 862],
        ['name' => 'Yaracuy', 'country_id' => 862],
        ['name' => 'Zulia', 'country_id' => 862],
        ['name' => 'Bac Giang', 'country_id' => 704],
        ['name' => 'Binh Dinh', 'country_id' => 704],
        ['name' => 'Binh Duong', 'country_id' => 704],
        ['name' => 'Da Nang', 'country_id' => 704],
        ['name' => 'Dong Bang Song Cuu Long', 'country_id' => 704],
        ['name' => 'Dong Bang Song Hong', 'country_id' => 704],
        ['name' => 'Dong Nai', 'country_id' => 704],
        ['name' => 'Dong Nam Bo', 'country_id' => 704],
        ['name' => 'Duyen Hai Mien Trung', 'country_id' => 704],
        ['name' => 'Hanoi', 'country_id' => 704],
        ['name' => 'Hung Yen', 'country_id' => 704],
        ['name' => 'Khu Bon Cu', 'country_id' => 704],
        ['name' => 'Long An', 'country_id' => 704],
        ['name' => 'Mien Nui Va Trung Du', 'country_id' => 704],
        ['name' => 'Thai Nguyen', 'country_id' => 704],
        ['name' => 'Thanh Pho Ho Chi Minh', 'country_id' => 704],
        ['name' => 'Thu Do Ha Noi', 'country_id' => 704],
        ['name' => 'Tinh Can Tho', 'country_id' => 704],
        ['name' => 'Tinh Da Nang', 'country_id' => 704],
        ['name' => 'Tinh Gia Lai', 'country_id' => 704],
        ['name' => 'Anegada', 'country_id' => 92],
        ['name' => 'Jost van Dyke', 'country_id' => 92],
        ['name' => 'Tortola', 'country_id' => 92],
        ['name' => 'Saint Croix', 'country_id' => 850],
        ['name' => 'Saint John', 'country_id' => 850],
        ['name' => 'Saint Thomas', 'country_id' => 850],
        ['name' => 'Alo', 'country_id' => 876],
        ['name' => 'Singave', 'country_id' => 876],
        ['name' => 'Wallis', 'country_id' => 876],
        ['name' => 'Bu Jaydur', 'country_id' => 732],
        ['name' => 'Wad-adh-Dhahab', 'country_id' => 732],
        ['name' => "al-'Ayun", 'country_id' => 732],
        ['name' => 'as-Samarah', 'country_id' => 732],
        ['name' => "'Adan", 'country_id' => 887],
        ['name' => 'Abyan', 'country_id' => 887],
        ['name' => 'Dhamar', 'country_id' => 887],
        ['name' => 'Hadramaut', 'country_id' => 887],
        ['name' => 'Hajjah', 'country_id' => 887],
        ['name' => 'Hudaydah', 'country_id' => 887],
        ['name' => 'Ibb', 'country_id' => 887],
        ['name' => 'Lahij', 'country_id' => 887],
        ['name' => "Ma'rib", 'country_id' => 887],
        ['name' => "Madinat San'a", 'country_id' => 887],
        ['name' => "Sa'dah", 'country_id' => 887],
        ['name' => 'Sana', 'country_id' => 887],
        ['name' => 'Shabwah', 'country_id' => 887],
        ['name' => "Ta'izz", 'country_id' => 887],
        ['name' => 'al-Bayda', 'country_id' => 887],
        ['name' => 'al-Hudaydah', 'country_id' => 887],
        ['name' => 'al-Jawf', 'country_id' => 887],
        ['name' => 'al-Mahrah', 'country_id' => 887],
        ['name' => 'al-Mahwit', 'country_id' => 887],
        ['name' => 'Central', 'country_id' => 894],
        ['name' => 'Copperbelt', 'country_id' => 894],
        ['name' => 'Eastern', 'country_id' => 894],
        ['name' => 'Luapala', 'country_id' => 894],
        ['name' => 'Lusaka', 'country_id' => 894],
        ['name' => 'North-Western', 'country_id' => 894],
        ['name' => 'Northern', 'country_id' => 894],
        ['name' => 'Southern', 'country_id' => 894],
        ['name' => 'Western', 'country_id' => 894],
        ['name' => 'Bulawayo', 'country_id' => 716],
        ['name' => 'Harare', 'country_id' => 716],
        ['name' => 'Manicaland', 'country_id' => 716],
        ['name' => 'Mashonaland Central', 'country_id' => 716],
        ['name' => 'Mashonaland East', 'country_id' => 716],
        ['name' => 'Mashonaland West', 'country_id' => 716],
        ['name' => 'Masvingo', 'country_id' => 716],
        ['name' => 'Matabeleland North', 'country_id' => 716],
        ['name' => 'Matabeleland South', 'country_id' => 716],
        ['name' => 'Midlands', 'country_id' => 716],

    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach ($this->states as $key => $state) {

            if (! State::where('name', $state['name'])->first()) {

                State::create([
                    'name' => $state['name'],
                    'country_id' => $state['country_id'],
                ]);
            }

        }
    }
}
