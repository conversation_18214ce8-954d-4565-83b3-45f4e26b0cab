<?php

namespace Database\Seeders;

ini_set('memory_limit', '512M');

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ZipcodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $file = app_path('Services/ZipCode/zipcodes.csv');
        
        if (!file_exists($file)) {
            $this->command->error('File zipcodes.csv not found!');
            return;
        }

        DB::table('zip_codes')->truncate();

        $handle = fopen($file, 'r');
        $header = fgetcsv($handle);
        $batchSize = 500;
        $batch = [];
        $count = 0;

        $this->command->info('Importing zipcodes...');

        while (($data = fgetcsv($handle)) !== FALSE) {
            if (empty($data[0])) continue;
            
            $zipCode = trim($data[0], '"');
            $lat = trim($data[1], '"');
            $lng = trim($data[2], '"');
            
            if (empty($zipCode) || empty($lat) || empty($lng)) continue;
            
            $batch[] = [
                'zip_code' => $zipCode,
                'lat' => $lat,
                'lng' => $lng,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            if (count($batch) >= $batchSize) {
                try {
                    DB::table('zip_codes')->insert($batch);
                    $count += count($batch);
                    $this->command->info("Imported {$count} records");
                } catch (\Exception $e) {
                    $this->command->error("Error importing batch: " . $e->getMessage());
                }
                
                $batch = [];
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
        }

        if (!empty($batch)) {
            try {
                DB::table('zip_codes')->insert($batch);
                $count += count($batch);
                $this->command->info("Imported " . count($batch) . " records");
            } catch (\Exception $e) {
                $this->command->error("Error importing batch: " . $e->getMessage());
            }
        }

        fclose($handle);
        $this->command->info("Import completed! Total records: {$count}");
    }
}