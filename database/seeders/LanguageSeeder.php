<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $locales = [
            'Afrikaans' => 'Afrikaans',
            'English' => 'English',
            'Albanian' => 'Albanian',
            'Amharic' => 'Amharic',
            'Arabic' => 'Arabic',
            'Armenian' => 'Armenian',
            'Azerbaijani' => 'Azerbaijani',
            'Basque' => 'Basque',
            'Belarusian' => 'Belarusian',
            'Bengali' => 'Bengali',
            'Bosnian' => 'Bosnian',
            'Bulgarian' => 'Bulgarian',
            'Catalan' => 'Catalan',
            'Cebuano' => 'Cebuano',
            'Chichewa' => 'Chichewa',
            'Chinese' => 'Chinese',
            'Corsican' => 'Corsican',
            'Croatian' => 'Croatian',
            'Czech' => 'Czech',
            'Danish' => 'Danish',
            'Dutch' => 'Dutch',
            'Esperanto' => 'Esperanto',
            'Estonian' => 'Estonian',
            'Filipino' => 'Filipino',
            'Finnish' => 'Finnish',
            'French' => 'French',
            'Frisian' => 'Frisian',
            'Galician' => 'Galician',
            'Georgian' => 'Georgian',
            'German' => 'German',
            'Greek' => 'Greek',
            'Gujarati' => 'Gujarati',
            'Haitian Creole' => 'Haitian Creole',
            'Hausa' => 'Hausa',
            'Hawaiian' => 'Hawaiian',
            'Hebrew' => 'Hebrew',
            'Hindi' => 'Hindi',
            'Hmong' => 'Hmong',
            'Hungarian' => 'Hungarian',
            'Icelandic' => 'Icelandic',
            'Igbo' => 'Igbo',
            'Indonesian' => 'Indonesian',
            'Irish' => 'Irish',
            'Italian' => 'Italian',
            'Japanese' => 'Japanese',
            'Javanese' => 'Javanese',
            'Kannada' => 'Kannada',
            'Kazakh' => 'Kazakh',
            'Khmer' => 'Khmer',
            'Kinyarwanda' => 'Kinyarwanda',
            'Korean' => 'Korean',
            'Kurdish' => 'Kurdish',
            'Kyrgyz' => 'Kyrgyz',
            'Lao' => 'Lao',
            'Latin' => 'Latin',
            'Latvian' => 'Latvian',
            'Lithuanian' => 'Lithuanian',
            'Luxembourgish' => 'Luxembourgish',
            'Macedonian' => 'Macedonian',
            'Malagasy' => 'Malagasy',
            'Malay' => 'Malay',
            'Malayalam' => 'Malayalam',
            'Maltese' => 'Maltese',
            'Maori' => 'Maori',
            'Marathi' => 'Marathi',
            'Mongolian' => 'Mongolian',
            'Burmese' => 'Burmese',
            'Nepali' => 'Nepali',
            'Norwegian' => 'Norwegian',
            'Odia' => 'Odia',
            'Pashto' => 'Pashto',
            'Persian' => 'Persian',
            'Polish' => 'Polish',
            'Portuguese' => 'Portuguese',
            'Punjabi' => 'Punjabi',
            'Romanian' => 'Romanian',
            'Russian' => 'Russian',
            'Samoan' => 'Samoan',
            'Scots Gaelic' => 'Scots Gaelic',
            'Serbian' => 'Serbian',
            'Sesotho' => 'Sesotho',
            'Shona' => 'Shona',
            'Sindhi' => 'Sindhi',
            'Sinhala' => 'Sinhala',
            'Slovak' => 'Slovak',
            'Slovenian' => 'Slovenian',
            'Somali' => 'Somali',
            'Spanish' => 'Spanish',
            'Sundanese' => 'Sundanese',
            'Swahili' => 'Swahili',
            'Swedish' => 'Swedish',
            'Tajik' => 'Tajik',
            'Tamil' => 'Tamil',
            'Telugu' => 'Telugu',
            'Thai' => 'Thai',
            'Turkish' => 'Turkish',
            'Ukrainian' => 'Ukrainian',
            'Urdu' => 'Urdu',
            'Uyghur' => 'Uyghur',
            'Uzbek' => 'Uzbek',
            'Vietnamese' => 'Vietnamese',
            'Welsh' => 'Welsh',
            'Xhosa' => 'Xhosa',
            'Yiddish' => 'Yiddish',
            'Yoruba' => 'Yoruba',
            'Zulu' => 'Zulu',
        ];

        foreach ($locales as $key => $value) {
            DB::table('languages')->insert([
                'key' => $key,
                'value' => $value,
            ]);
        }
    }
}
