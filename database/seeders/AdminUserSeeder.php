<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class AdminUserSeeder extends Seeder
{
    public function run()
    {
        // Create users with hashed passwords
        $user1 = User::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('oLgIPEHDCbLU6Fzg'),
            'name' => 'Huy Admin',
        ]);
        
        $user2 = User::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('{EhS7U47^91@'),
            'name' => 'Jimmy Admin',
        ]);
        
        // Assign roles
        $adminRole = Role::where('name', 'Supreme Admin')->first();
        $user1->assignRole($adminRole);
        $user2->assignRole($adminRole);
    }
}