<?php
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use React\Http\HttpServer;
use React\EventLoop\Factory;
use React\Socket\SocketServer;
use Psr\Http\Message\ServerRequestInterface;

require __DIR__ . '/vendor/autoload.php';

class Chat implements MessageComponentInterface {
    protected $clients;
    protected $clientChats = []; // resourceId => chatId

    public function __construct() {
        $this->clients = new \SplObjectStorage;
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "New connection! ({$conn->resourceId})\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);
        if (!$data) return;

        if ($data['action'] === 'join') {
            $this->clientChats[$from->resourceId] = $data['chat_id'];
        }
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        unset($this->clientChats[$conn->resourceId]);
        echo "Connection {$conn->resourceId} has disconnected\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }

    // Broadcast to all clients in a chat
    public function broadcastToChat($chatId, $payload) {
        foreach ($this->clients as $client) {
            if (
                isset($this->clientChats[$client->resourceId]) &&
                $this->clientChats[$client->resourceId] == $chatId
            ) {
                $client->send(json_encode($payload));
            }
        }
    }
}

// Setup event loop
$loop = Factory::create();
$chatComponent = new Chat();

// WebSocket server
$wsServer = new Ratchet\Server\IoServer(
    new Ratchet\Http\HttpServer(
        new Ratchet\WebSocket\WsServer($chatComponent)
    ),
    new React\Socket\SocketServer('0.0.0.0:8080', [], $loop)
);

// HTTP server for broadcast
$httpServer = new HttpServer(function (ServerRequestInterface $request) use ($chatComponent) {
    if ($request->getMethod() === 'POST' && $request->getUri()->getPath() === '/broadcast') {
        $data = json_decode((string)$request->getBody(), true);
        if (
            !isset($data['chat_id']) ||
            !isset($data['message'])
        ) {
            return new React\Http\Message\Response(400, [], 'Invalid payload');
        }
        $chatComponent->broadcastToChat($data['chat_id'], [
            'action' => 'message_sent',
            'data' => $data['message']
        ]);
        return new React\Http\Message\Response(200, ['Content-Type' => 'application/json'], json_encode(['status' => 'ok']));
    }
    return new React\Http\Message\Response(404);
});
$socket = new SocketServer('0.0.0.0:8081', [], $loop);
$httpServer->listen($socket);

$loop->run();