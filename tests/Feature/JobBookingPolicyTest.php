<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\JobBooking;
use App\Enums\RoleEnum;
use App\Enums\JobBookingStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

class JobBookingPolicyTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => RoleEnum::CONSUMER, 'guard_name' => 'api']);
        Role::create(['name' => RoleEnum::PROVIDER, 'guard_name' => 'api']);
        Role::create(['name' => RoleEnum::ADMIN, 'guard_name' => 'api']);
    }

    /** @test */
    public function consumer_can_view_their_own_job_booking()
    {
        // Create a consumer user
        $consumer = User::factory()->create();
        $consumer->assignRole(RoleEnum::CONSUMER);

        // Create a job booking owned by the consumer
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer->id,
            'status' => JobBookingStatusEnum::PENDING
        ]);

        // Consumer should be able to view their own job booking
        $this->assertTrue($consumer->can('view', $jobBooking));
    }

    /** @test */
    public function consumer_cannot_view_other_users_job_booking()
    {
        // Create two consumer users
        $consumer1 = User::factory()->create();
        $consumer1->assignRole(RoleEnum::CONSUMER);
        
        $consumer2 = User::factory()->create();
        $consumer2->assignRole(RoleEnum::CONSUMER);

        // Create a job booking owned by consumer1
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer1->id,
            'status' => JobBookingStatusEnum::PENDING
        ]);

        // Consumer2 should NOT be able to view consumer1's job booking
        $this->assertFalse($consumer2->can('view', $jobBooking));
    }

    /** @test */
    public function provider_can_view_open_job_booking()
    {
        // Create a provider user
        $provider = User::factory()->create();
        $provider->assignRole(RoleEnum::PROVIDER);

        // Create a consumer user
        $consumer = User::factory()->create();
        $consumer->assignRole(RoleEnum::CONSUMER);

        // Create an open job booking
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer->id,
            'status' => JobBookingStatusEnum::OPEN
        ]);

        // Provider should be able to view open job booking (to place bids)
        $this->assertTrue($provider->can('view', $jobBooking));
    }

    /** @test */
    public function provider_can_view_pending_job_booking()
    {
        // Create a provider user
        $provider = User::factory()->create();
        $provider->assignRole(RoleEnum::PROVIDER);

        // Create a consumer user
        $consumer = User::factory()->create();
        $consumer->assignRole(RoleEnum::CONSUMER);

        // Create a pending job booking
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer->id,
            'status' => JobBookingStatusEnum::PENDING
        ]);

        // Provider should be able to view pending job booking (to place bids)
        $this->assertTrue($provider->can('view', $jobBooking));
    }

    /** @test */
    public function provider_cannot_view_completed_job_booking_they_are_not_involved_in()
    {
        // Create a provider user
        $provider = User::factory()->create();
        $provider->assignRole(RoleEnum::PROVIDER);

        // Create a consumer user
        $consumer = User::factory()->create();
        $consumer->assignRole(RoleEnum::CONSUMER);

        // Create a completed job booking (not allowing bidding)
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer->id,
            'status' => JobBookingStatusEnum::COMPLETED
        ]);

        // Provider should NOT be able to view completed job booking they're not involved in
        $this->assertFalse($provider->can('view', $jobBooking));
    }

    /** @test */
    public function admin_can_view_any_job_booking()
    {
        // Create an admin user
        $admin = User::factory()->create();
        $admin->assignRole(RoleEnum::ADMIN);

        // Create a consumer user
        $consumer = User::factory()->create();
        $consumer->assignRole(RoleEnum::CONSUMER);

        // Create a job booking
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer->id,
            'status' => JobBookingStatusEnum::PENDING
        ]);

        // Admin should be able to view any job booking
        $this->assertTrue($admin->can('view', $jobBooking));
    }
}
